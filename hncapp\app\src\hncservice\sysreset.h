﻿/*!
 * @file sysreset.h
 * @brief 用于启动复位和获取相应通道复位状态
 * @note  ResetStart(ch[]):启动复位
 *        ResetOnMsg(ch, code):处理定时器刷新或获取复位过程中的状态变化
 *        ResetFinish():检测所有通道是否复位完成
 *        GetResetState(ch):获取某个通道的状态
 *        GetResetError(ch):获取某个通道产生的异常
 *
 * @version V1.00
 * @date 2022/12/20
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */
#ifndef SYSRESET_H
#define SYSRESET_H

#include "common.h"

class SysReset
{
public:

    SysReset(void);
    ~SysReset(void);
    // 复位状态
    typedef enum _STATE_
    {
        FAILED = -2,      // 复位失败
        FREE = -1,         // 空闲状态
        PROGRESS,         // 复位中
        SUCCESS,          // 复位成功

    }ENState;

    typedef enum _ERROR_
    {
        ERR_NORMAL = 0,      // 无错误
        ERR_RIDCYC,           // 刚性攻丝
        ERR_THREAD,           // 螺纹加工
        ERR_HOMING,           // 回零第二阶段
        ERR_ESTOP ,           // 该通道处于急停中
        ERR_UNDONE,           // 上一次复位未完成
        ERR_FORBID,           // 禁止复位
    }ENError;



    static Bit32 ResetStart(const Bit32 ch[SYS_CHAN_NUM]);  // 复位多个通道
    static SysReset::ENError ResetStart(Bit32 ch); // 复位单个通道

    static SysReset::ENState ResetOnMsg(Bit32 ch = -1, uBit16 code = 0xFFFF); //ch为-1 并且 code为0xFFFF，代表为定时消息

    static void ResetFinish(); // 系统复位完成
    static SysReset::ENState GetResetState(Bit32 ch); // 获取某个通道的状态
    static SysReset::ENError GetResetError(Bit32 ch);

private:
    static time_t m_resetTime; // 计时
    static ENState m_nResetState[SYS_CHAN_NUM]; // 状态机
    static ENError m_nResetError[SYS_CHAN_NUM];

    static SysReset::ENState SysResetRefresh(void); // 复位状态检测
    static SysReset::ENState SysResetOnChan(Bit32 ch); // 某通道复位开始


    static ENError CheckSysReset(Bit32 ch);   // 能否复位

    static bool IsAllSysResetFinish();    // 检测所有通道是否复位结束
    static void ResetFinishClearFlag();   // 清除所有标记
    static void ClrProgMdiResetStep(Bit32 ch); // 设置mdi或prog复位下的状态机
    static void ClrMulChanResetFlag(); // 清除复位标记

    static void ChangeChStateReset(Bit32 ch, bool flag);

    static ENState Bit32ToENState(Bit32 ret);
};

#endif // SysReset_H
