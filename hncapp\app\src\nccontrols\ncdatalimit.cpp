﻿#include "ncdatalimit.h"

#include "hncsys.h"


NcDataLimit DataTypeLimit(Bit32 ebxType, Bit32 len, Bit32 prec)
{
    NcDataLimit dataLimit;

    switch (ebxType)
    {
    case DTYPE_INT: // 整型
        dataLimit.dateType = DTYPE_INT;
        dataLimit.mixInt = -2147483647;  // 负2的31次方
        dataLimit.maxInt = 2147483647;   // 2的31次方
        if (len < 0)
        {
            len = 11;
        }
        dataLimit.len = len;
        break;
    case DTYPE_UINT: // 无符号整型
        dataLimit.dateType = DTYPE_INT;
        dataLimit.mixInt = 0;  // 0
        dataLimit.maxInt = 2147483647;   // 2的31次方
        if (len < 0)
        {
            len = 10;
        }
        dataLimit.len = len;
        break;
    case DTYPE_INTEXP: // 表达式整型
    case DTYPE_FLOAT: // 浮点型
        dataLimit.dateType = DTYPE_FLOAT;
        dataLimit.mixFloat = -1.0e20;
        dataLimit.maxFloat = 1.0e20;
        if (len < 0)
        {
            len = 22;
        }
        dataLimit.len = len;
        if (prec < 0)
        {
            HNC_SystemGetValue(HNC_SYS_PREC, &prec);
        }
        dataLimit.prec = prec;
        break;
    case DTYPE_STRING: // 字符串型
        dataLimit.dateType = DTYPE_STRING;
        if (len < 0)
        {
            len = 128;
        }
        dataLimit.len = len;
        break;
    case DTYPE_NULL: // 空类型
        if (len < 0)
        {
            len = 128;
        }
        dataLimit.len = len;
        dataLimit.dateType = DTYPE_NULL;
        break;
    case DTYPE_BOOL: // 布尔型
        dataLimit.dateType = DTYPE_BOOL;
        dataLimit.mixInt = 0;
        dataLimit.maxInt = 1;
        break;
    case DTYPE_HEX4: // 十六进制格式
        dataLimit.dateType = DTYPE_HEX4;
        if (len < 0)
        {
            len = 8;
        }
        dataLimit.len = len;
        break;
    case DTYPE_BYTE: // 数组格式
        dataLimit.dateType = DTYPE_BYTE;
        if (len < 0)
        {
            len = 16 * 2 -1;
        }
        dataLimit.len = len;
        break;
    default:
        if (len < 0)
        {
            len = 128;
        }
        dataLimit.len = len;
        dataLimit.dateType = DTYPE_STRING;
        break;
    }

    return dataLimit;
}

// 整型
NcDataLimit DataTypeLimitInt(Bit32 mix, Bit32 max, uBit8 len)
{
    NcDataLimit dataLimit;

    dataLimit.dateType = DTYPE_INT;
    dataLimit.mixInt = mix;
    dataLimit.maxInt = max;
    dataLimit.len = len;

    return dataLimit;
}

// 无符号整形
NcDataLimit DataTypeLimitUInt(Bit32 mix, Bit32 max, uBit8 len)
{
    if (mix < 0)
    {
        mix = 0;
    }
    return DataTypeLimitInt(mix, max, len);
}

// 实型
NcDataLimit DataTypeLimitFloat(fBit64 mix, fBit64 max, uBit8 len, uBit8 prec)
{
    NcDataLimit dataLimit;

    dataLimit.dateType = DTYPE_FLOAT;
    dataLimit.mixFloat = mix;
    dataLimit.maxFloat = max;
    dataLimit.len = len;

    dataLimit.prec = prec;

    return dataLimit;
}

// 无符号实型
NcDataLimit DataTypeLimitUFloat(fBit64 mix, fBit64 max, uBit8 len, uBit8 prec)
{
    if (mix < 0)
    {
        mix = 0;
    }
    return DataTypeLimitFloat(mix, max, len, prec);
}

// 布尔
NcDataLimit DataTypeLimitBool()
{
    NcDataLimit dataLimit;

    dataLimit.dateType = DTYPE_BOOL;

    dataLimit.mixInt = 0;
    dataLimit.maxInt = 1;

    return dataLimit;
}

// 字符串
NcDataLimit DataTypeLimitStr(uBit8 len)
{
    NcDataLimit dataLimit;

    dataLimit.dateType = DTYPE_STRING;
    dataLimit.len = len;

    return dataLimit;
}

// 十六进制格式输入设置
NcDataLimit DataTypeLimitHex(uBit8 len)
{
    NcDataLimit dataLimit;

    dataLimit.dateType = DTYPE_HEX4;
    dataLimit.len = len;

    return dataLimit;
}

// 数组格式输入设置
NcDataLimit DataTypeLimitByte(uBit8 len)
{
    NcDataLimit dataLimit;

    dataLimit.dateType = DTYPE_ARR;
    dataLimit.len = len;

    return dataLimit;
}
