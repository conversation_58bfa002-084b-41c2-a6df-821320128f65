﻿#ifndef OSCWAVE_H
#define OSCWAVE_H

#include "hmioscservo.h"
#include "qcustomplot.h"

namespace Ui {
class OscWave;
}

QT_BEGIN_NAMESPACE
class QWidget;
class QCustomPlot;
class QCPCurve;
QT_END_NAMESPACE

class OscWave : public QWidget
{
    Q_OBJECT

public:
    explicit OscWave(QWidget *parent = 0, HmiOscServo::OscServoType type = HmiOscServo::OSC_SERVO_SPE, QString name = "", QString title = "");
    ~OscWave();

    typedef enum _OscSelMoveDir
    {
        OSC_SEL_MOVE_LEFT = 0, // 向左
        OSC_SEL_MOVE_RIGHT,	   // 向右
        OSC_SEL_MOVE_UP,	   // 向上
        OSC_SEL_MOVE_DOWN,	   // 向下
    }OscSelMoveDir;

    typedef enum _TYPE_
    {
        BACKGROUND = 0,         // 背景
        GRID,                   // 网格
        FOREGROUND,             // 前景
        CURVE1,                 // 曲线1
        CURVE2,                 // 曲线2
        CURVE3,                 // 曲线3
        CURVE4,                 // 曲线4
        CURVE5,                 // 曲线5
        CURVE6,                 // 曲线6
        CURVE7,                 // 曲线7
        CURVE8,                 // 曲线8
        CURVE9,                 // 曲线9
        CURVE10,                // 曲线10
        CURVE11,                // 曲线11
        CURVE12,                // 曲线12
        CURVE13,                // 曲线13
        CURVE14,                // 曲线14
        CURVE15,                // 曲线15

        TYPE_COUNT
    }ENType;

    void SetZoomXUp();
    void SetZoomXDn();
    void SetZoomYUp();
    void SetZoomYDn();

    void SetZoomXLeft();
    void SetZoomXRight();

    void SetRasterZoomYUp();
    void SetRasterZoomYDn();
    void SetRasterZoomXUp();
    void SetRasterZoomXDn();

    void ShowSelArea();
    void HideSelArea();
    Bit32 MoveSelArea(OscSelMoveDir dir);
    Bit32 GetSelCount();
    void GetCurSelAreaInfo(Bit32 *xPos, Bit32 *yPos, Bit32 *xLength, Bit32 *yHeight);
    void SetCurSelAreaInfo(Bit32 xPos, Bit32 yPos, Bit32 xLength, Bit32 yHeight);
    Bit32 SelAreaZoomUp();
    Bit32 SelAreaZoomDown();

    void LineAddPoint(Bit32 index, QVector<double>x, QVector<double>y, Bit32 flag = 0, bool displayName = true);             // 添加曲线数据
    void LineZeroAddPoint(const QVector<double> x, const QVector<double> y, Bit32 flag = 0); // 添加曲线0数据
    void ClearPoint(); // 清除所有曲线数据
    void ClearPoint(Bit32 plotIdx); // 清除指定曲线数据
    void LineOneAddPoint(const QVector<double> x, const QVector<double> y, Bit32 flag = 0); // 添加曲线1数据
    void SetColor(QColor &bk, QColor &gd, QColor &ft, QColor &c1, QColor &c2, QColor &c3, QColor &c4); // 设置颜色(背景，坐标值，曲线0，1,2,3)
    void SetColor(ENType type, QColor &color); // 设置颜色
    void SetGraphColor(Bit32 curveNo); // 设置图形颜色
    void SetAxisName(QStringList strList); // 设置坐标轴名
    void UpdateYAxisName(QString strName, QString waveName); // 更新纵轴轴名

    void PlotRestore();
    void SelAreaDataRestore();
    Bit32 SelAreaZoomFullSrceen();
    void SetYTickPrec(Bit32 prec);

    void XAxisGetTickParm(fBit64 *lower, fBit64 *upper, fBit64 *step);
    void XAxisSetTickParm(fBit64 lower, fBit64 upper, fBit64 step);
    void YAxisGetTickParm(fBit64 *lower, fBit64 *upper, fBit64 *step);
    void YAxisSetTickParm(fBit64 lower, fBit64 upper, fBit64 step);

    void SetYAxis2Visible(bool visible);

    void WaveReplot();
    QCustomPlot *GetWavePlot();
    void CreateGroup(QCustomPlot *plot);
    void AutoRangParm();
    void AutoRangParm(bool xRang, bool yRang);
    void SetXRange(fBit64 rangLower, fBit64 rangUpper, fBit64 rangStep);
    void SetYRange(fBit64 rangLower, fBit64 rangUpper, fBit64 rangStep);
    void SetXMaxMin(fBit64 max, fBit64 min);
    void SetYMaxMin(fBit64 max, fBit64 min);

    void ClearCurveName();                  // 清除曲线名称
    void CircleAddPoint(Bit32 index, QVector<double> x, QVector<double> y, bool showPoint = true, bool showCurveName = false, bool xInvert = false);

    void YAxisGetDataMaxAndMin(Bit32 index, fBit64& yMin, fBit64& yMax);
    void RasterSpecialSetWave();

    void RasterAddPoint(QVector<fBit64> x, QVector<fBit64> y);
    void SetWaveTitle(const QString& title);
signals:
    void WaveWigdetFocusIn();
    void WaveWigdetZoomChangeByWheel();

private:
    Ui::OscWave *ui;

    HmiOscServo::OscServoType oscType;
    QString waveName;

	// 主界面的标尺
    fBit64 baseXRangLower;
    fBit64 baseXRangUpper;
    fBit64 baseXStep;
    fBit64 baseYRangLower;
    fBit64 baseYRangUpper;
    fBit64 baseYStep;

	// 操作界面的初始化标尺(【还原】菜单使用)
    fBit64 optXRangLower;
    fBit64 optXRangUpper;
    fBit64 optXStep;
    fBit64 optYRangLower;
    fBit64 optYRangUpper;
    fBit64 optYStep;

    // 缩放限制
    fBit64 plotXStepMix;
    fBit64 plotXStepMax;
    fBit64 plotYStepMix;
    fBit64 plotYStepMax;

	// y轴数据的范围
    fBit64 yMin;
    fBit64 yMax;

    Bit32 xStepNum;
    Bit32 yStepNum;

    Bit32 allLength;    // 显示区域总长度
    Bit32 allHeight;    // 显示区域总宽度
    Bit32 selAreaLength;// 选择框长度
    Bit32 selAreaHeight;// 选择框宽度
    Bit32 selAreaPosX;  // 选择框左上角X坐标
    Bit32 selAreaPosY;  // 选择框右上角Y坐标

    Bit32 yTickPrec; // y轴刻度的精度，整数为1，一位小数位10，两位小数位100，依次类推

    QVector<QCPCurve *>m_vecpGraphSel;
    QVector<QCPCurve *>m_vecpGraphSelOp;
    QVector<double> graphSelX, graphSelY;
    QVector<ENType> m_vecWaveCurve;            // QVector索引值与ENType中CURVE一一对应
    QMap<ENType, QColor> m_mapCurveColor;       // 曲线颜色

    bool autoRangParmFlag;
    QVector<QLabel*> m_vecYNameLabel;                       // y轴名称容器

    fBit64 m_dbHisMaxX;
    fBit64 m_dbHisMinX;
    fBit64 m_dbHisMaxY;
    fBit64 m_dbHisMinY;

    bool m_nShowYAxis2; // 右侧Y轴是否显示

    void SetupPlot(); // 初始设置
    void CalSelArea();
    fBit64 OscAdaptStep(fBit64 step);
    bool eventFilter(QObject *wg, QEvent *event);
    void SetCurveName(Bit32 no);                            // 设置曲线名称及颜色
    void GetScaleParameter(fBit64 max, fBit64 min, Bit32 rfStepNum, fBit64 &lower, fBit64 &upper, fBit64 &stepValue);
    void GetDataLimit(const QVector<double> &vecValue, fBit64 &min, fBit64 &max);
private slots:
    void WaveWigdetMouseWheel();
};

#endif // OSCWAVE_H
