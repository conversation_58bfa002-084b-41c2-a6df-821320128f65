﻿
#include <qmath.h>
#include <QPainter>
#include <hncmath.h>

#include "common.h"
#include "hmioscservo.h"
#include "staticdata.h"
#include "hmipaintercolor.h"

#include "roundscale.h"
#include "ui_roundscale.h"

const Bit32 SCALE_COUNT = 40;

RoundScale::RoundScale(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::RoundScale)
{
    ui->setupUi(this);
    this->centrePoint=QPoint(size().width() / 2, this->size().height() / 2);

    m_dbScaleDist = 10;

    graph0 = new RoundScaleGraph(this);
    graph1 = new RoundScaleGraph(this);
    graph2 = new RoundScaleGraph(this);
    gList.append(graph0);
    gList.append(graph1);
    gList.append(graph2);

//    fontcolor = StaticData2::Instance().painterColor->GetColor(HmiPainterColor::OSCSERVO_BLACK_FONT);
    fontcolor = HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_BLACK_FONT);

    m_bShowScaleAreaFlag = false;
}

RoundScale::~RoundScale()
{
    delete ui;
}

void RoundScale::paintEvent(QPaintEvent *)
{
    QPainter painter;
    painter.begin(this);
    painter.setRenderHint(QPainter::Antialiasing);

    ScaleRedraw(painter);
    CordRedraw(painter);
    GraphRedraw(painter);

    painter.end();
}

void RoundScale::resizeEvent(QResizeEvent *)
{
    this->resizeCof = this->height() / 100;
    this->centrePoint=QPoint(size().width() / 2, this->size().height() / 2);
}

void RoundScale::replot()
{
    this->repaint();
}

void RoundScale::SetColor(const QColor &bg, const QColor &ft,
                         const QColor &c1, const QColor &c2, const QColor &c3)
{
    crdcolor = bg; // 背景色
    fontcolor = ft; // 文本色
    graph0->curColor = c1; // 实际位置曲线颜色
    graph1->curColor = c2; // 指令位置曲线颜色
    graph2->curColor = c3; // 十字线颜色
}

void RoundScale::LineSetRadius(double r)
{ // 设置圆半径
    this->radius = r;
}

bool RoundScale::CrdTran(double x, double y, double &outx, double &outy)
{
    double r = sqrt(x * x + y * y);
    double diffDist = (r - this->radius) * 1000; // 半径偏差，单位微米
    double diff = 0.0;
    double plotR = 0.0; // 绘图半径

    if (this->m_dbScaleDist == 0 || r == 0)
    {
        return false;
    }

    if (diffDist > this->m_dbScaleDist * 5)
    {
        diffDist = this->m_dbScaleDist * 5;
    }
    if (diffDist < this->m_dbScaleDist * (-5))
    {
        diffDist = this->m_dbScaleDist * (-5);
    }

    diff = diffDist / this->m_dbScaleDist;

    plotR = diff + 25;//this->m_dbStdCirRadius;

    outx = mcX(plotR * x / r);
    outy =  mcY(plotR * -y / r);

    return true;
}

void RoundScale::GraphRedraw(QPainter &painter)
{
    painter.save();

    Bit32 height = this->size().height();
    Bit32 width = this->size().width();

    // 曲线1
    for (Bit32 i = 0; i < gList.count(); i++)
    {
        for (Bit32 m = 0; m < gList.at(i)->vecX.count() && m < gList.at(i)->vecY.count(); m++)
        {
            painter.setPen(QPen(gList.at(i)->curColor, 1, Qt::SolidLine, Qt::RoundCap));
            Bit32 xcount = gList.at(i)->vecX.at(m).count();
            Bit32 ycount = gList.at(i)->vecY.at(m).count();

            for (Bit32 j = 1; j<xcount && j < ycount;j++)
            {
                painter.drawLine(QPointF(gList.at(i)->vecX.at(m).at(j-1) * width, gList.at(i)->vecY.at(m).at(j-1) * height), QPointF(gList.at(i)->vecX.at(m).at(j) * width, gList.at(i)->vecY.at(m).at(j) * height));
            }
        }
    }

    painter.restore();
}

void RoundScale::CordRedraw(QPainter &painter)
{ // 坐标绘制
    painter.save();

    Bit32 harfLen = 8; // 短线一半长度2
    double gap = (double)(this->height()) / SCALE_COUNT;

    double centerWidth = this->width() / 2.0;
    double centerHeight = this->height() / 2.0;

    // 十字线
    painter.setPen(QPen(HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_CIRGRID), 1, Qt::DashLine, Qt::RoundCap));
    painter.drawLine(QPointF(centerWidth, 0), QPointF(centerWidth, this->height()));
    painter.drawLine(QPointF(0, centerHeight), QPointF(this->width(), centerHeight));

    // 中心点
    painter.setPen(QPen(HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_CIRGRID), 1, Qt::SolidLine, Qt::RoundCap));

    for (int i = 0; i <= SCALE_COUNT; i++)
    {
        if (i % 10 == 0)
        {
            harfLen = 16;
        }
        else
        {
            harfLen = 8;
        }

        if (i >= (SCALE_COUNT/ 2 - 1) && i <= (SCALE_COUNT/ 2 + 1))
        {
            harfLen = 4;
        }

        painter.drawLine(QPointF(centerWidth, i * gap), QPointF(centerWidth + harfLen, i * gap));
        painter.drawLine(QPointF(i * gap, centerHeight - harfLen), QPointF(i * gap, centerHeight));
    }

    painter.restore();
}

void RoundScale::ScaleRedraw(QPainter &painter)
{
    painter.save();
    QFont f(FONT_TYPE);
    f.setPixelSize(13 * FONT_RATIO);
    painter.setPen(QPen(fontcolor, 0.5, Qt::SolidLine, Qt::RoundCap));
    painter.setFont(f);
    QString str = QString("%1 um/div").arg(this->m_dbScaleDist / 10);
    Bit32 len = GetStringPixedWidth(f, str);
    painter.drawText(0, 0, len, 25,Qt::AlignCenter, str);

    painter.restore();
}

//void RoundScale::LineZeroAddPoint(const QVector<double>x, const QVector<double>y)
//{
//    this->graph0->x =x;
//    this->graph0->y =y;
//}

//void RoundScale::LineOneAddPoint(const QVector<double>x, const QVector<double>y)
//{
//    this->graph1->x = x;
//    this->graph1->y = y;
//}

void RoundScale::LineZeroAddPoint(const QVector<QVector<double>> vecx, const QVector<QVector<double>> vecy)
{
    this->graph0->vecX = vecx;
    this->graph0->vecY = vecy;
}

void RoundScale::LineOneAddPoint(const QVector<QVector<double>> vecx, const QVector<QVector<double>> vecy)
{
    this->graph1->vecX = vecx;
    this->graph1->vecY = vecy;
}

void RoundScale::LineCrossAddPoint(const QVector<QVector<double>> vecx, const QVector<QVector<double>> vecy)
{
    this->graph2->vecX = vecx;
    this->graph2->vecY = vecy;
}

void RoundScale::ClearData()
{
//    this->graph0->x.clear();
//    this->graph0->y.clear();
//    this->graph1->x.clear();
//    this->graph1->y.clear();

    this->graph0->vecX.clear();
    this->graph0->vecY.clear();
    this->graph1->vecX.clear();
    this->graph1->vecY.clear();
    this->graph2->vecX.clear();
    this->graph2->vecY.clear();

    this->repaint();

}

void RoundScale::SetScaleDist(double dist)
{
    m_dbScaleDist = dist;
    this->repaint();
}
