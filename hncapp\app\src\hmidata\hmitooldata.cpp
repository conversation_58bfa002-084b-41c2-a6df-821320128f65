﻿#include <math.h>

#include "hncsys.h"
#include "hncmodal.h"
#include "hncdatadef.h"
#include "hncchan.h"
#include "hncvar.h"
#include "hnctool.h"
#include "hncmath.h"
#include "hmiparaman.h"
#include "hmiparset.h"


#include "corddata.h"

#include "hmitooldata.h"
#include"datadef.h"
const Bit32 ToolMaxEdge = 9;
const Bit32 ToolEdgeAddrBias = 100; // 刀沿地址偏移量

static Bit32 m_nToolEdgeNum = 0;
static QList <Bit32> m_pLockToolNo;
static Bit32 m_nCurMag = 1;

void InitEdgeNum()
{
    Bit32 chNum = 0;
    Bit32 toolEdgeNum = 0;

    ParaGetIntVal(PARAMAN_FILE_MAC, 0, PAR_MAC_CHAN_NUM, &chNum);
	ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_TOOL_HEAD_NUM, &toolEdgeNum);
    if(toolEdgeNum > ToolMaxEdge)
    {
        m_nToolEdgeNum = ToolMaxEdge;
    }
    else
    {
        m_nToolEdgeNum = toolEdgeNum;
    }
}

Bit32 GetEdgeNum(Bit32 ch)
{
    if (ch < 0 || ch >= SYS_CHAN_NUM)
    {
        return 0;
    }

    return m_nToolEdgeNum;
}

fBit64 GetTurnToolOffValLimit(void) //见bug #5882
{
    fBit64 limit = 214740.0;
    Bit32 moveUnit = 0;
    HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &moveUnit);
    if (moveUnit >= 100000)
    {
        limit = 21474.0;
    }
    else if (moveUnit <= 1000)
    {
        limit = 2174700.0;
    }

    return limit;
}

fBit64 ToolfrmGetPulseUnit(void)
{
    fBit64 k = 1.0;
    Bit32 metric = GetMetricDisp();
    k = (1 == metric) ? 1.0 : METRIC_DISP_COEF; // 公制/英制编程(1/0)

    return k;
}

// 车床刀补,磨损,半径是否累加
Bit8 IsTurnToolCount(void)
{
    Bit32 num = 0;

    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_TABRA_ADD_EN, &num);

    if (num != 1)
    {
        num = 0;
    }

    return (Bit8)num;
}

fBit64 ToolDiameterTrans(Bit32 ax)
{
    Bit32 v = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_TDIA_SHOW_EN, &v);

    if ((v & (0x01 << ax)) == (0x01 << ax))
    {
        return 2.0;
    }
    else
    {
        return 1.0;
    }
}

Bit32 TurningHaveYComp(Bit32 ch)
{
    Bit32 ret = 0;
    Bit32 axNo = 0;

    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_YINDEX, &axNo);
    if (axNo != -1)
    {
        ret = 1;
    }

    return ret;
}

// 取G54Px中的x值
Bit32 TransMdlToG54P(Bit32 mdl, Bit32 *x)
{
    Bit8 buf[16] = {0};
    Bit32 len = 0;

    HNC_ModalGetModeStr(mdl, 11, buf);
    len = strlen(buf);
    if (buf[len - 2] != '.')
    {
        *x = buf[len - 1] - '0' + (buf[len - 2] - '0') * 10;
    }
    else
    {
        *x = buf[len - 1] - '0';
    }
    return 1;
}

void ToolFauncOff(Bit32 ch, fBit64 *gAxis)
{
    Bit32 gCodeFanuc = 0;
    Bit32 tmpVal = 0;
    Bit32 mdl = 0;
    Bit32 p = 0;
    Bit32 G5x = 0;
    fBit64 crdOffset[3] = {0.0};
    fBit64 g5xAxis[3] = {0.0};
    Bit32 i = 0;
    Bit32 macType = 0;
    Bit32 macCurType = 0;
    fBit64 g50_zero = 0.0;

    HNC_ChannelGetValue(HNC_CHAN_MAC_TYPE, ch, 0, &macType);
    ParaGetIntVal(PARAMAN_FILE_MAC, 0, PAR_GCODE_FANUC, &gCodeFanuc);
    ParaGetIntVal(PARAMAN_FILE_MAC, 0,PAR_MAC_NEW_FUCTION_TEST, &tmpVal); // PAR_MAC_NEW_FUCTION_TEST 0x02 或FANUC模式,叠加外部偏置与G5X
    HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_TM_MAC_CUR_TYPE, &macCurType); // VAR_TM_MAC_CUR_TYPE 1148

    bool gCodeFanucModeOpen = (gCodeFanuc == 0x02) ? true : false;
    bool crdAddOpen = ((tmpVal & 0x02) == 0x02) ? true : false;

    if (
         (macType == 1 || (macType == 2 && macCurType == 1))
         && (gCodeFanucModeOpen || crdAddOpen)
         )
    {
        // 11为MODAL_G54_59
        HNC_ChannelGetValue(HNC_CHAN_MODAL, ch, 11, &mdl);
        if (mdl >= 54 && mdl <= 59)
        {
            G5x = mdl - 54;
        }
        else
        {
            TransMdlToG54P(mdl, &p);
            G5x = p -1;
        }

        for (i = 0; i < 3; i++)
        {
            crdOffset[i] = CordData::GetInstance()->GetCrdVal(AxisCrdOffset, ch, i);

            CordType cordType;
            if (mdl >= 54 && mdl <= 59)
            {
                cordType = (CordType)(AxisCrdG5X + G5x);
            }
            else
            {
                cordType = (CordType)(AxisCrdG54Ext + G5x);
            }
            g5xAxis[i] = CordData::GetInstance()->GetCrdVal(cordType, ch, i);

            gAxis[i] = crdOffset[i] + g5xAxis[i];

            if ((macType == 1 || (macType == 2 && macCurType == 1))
                && gCodeFanucModeOpen)
            {
                HNC_ChannelGetValue(HNC_CHAN_G50_ZERO, ch, i, &g50_zero);
                gAxis[i] += g50_zero;
            }
        }
    }
}

bool TurningToolRollEnable()
{
    Bit32 val = 0;
    ParaGetIntVal(PARAMAN_FILE_MAC, 0,PAR_MAC_NEW_FUCTION_TEST, &val);
    if ((val & TURN_TOOL_ROLL_ENABLE) == TURN_TOOL_ROLL_ENABLE)
    {
        return true;
    }
    return false;
}

bool IsToolPropShow()
{
    Bit32 val = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_HMISHOW, &val);
    if ((val & TOOL_TYPE_SHOW) == TOOL_TYPE_SHOW)
    {
        return true;
    }
    return false;
}

Bit32 ToolGetToolShowPara(Bit32 t, Bit32 index, QString &str, Bit32 macType)
{
    fBit64 f_value = 0.0;
    Bit32 value = 0.0;
    Bit8 f_format[16] = "%7.3f";
    Bit32 ret = 0;
    fBit64 f_d = 0;
    Bit8 txt[STR_BUF_LEN] = "";
    Bit32 prec = 0;

    if (t < 1)
    {
        return -1;
    }

    HNC_SystemGetValue(HNC_SYS_PREC, &prec);
    snprintf(f_format, 16, "%%.%df", prec);

    switch (index)
    {
    case LTOOL_DIR:
        ret = HNC_ToolGetToolPara(t, index, &f_value);
        snprintf(txt, STR_BUF_LEN, "%d", (Bit16)f_value);
        str = QString(txt);
        break;
    case HMI_EXTOOL_LTOOL_ROLL_DIR:
        HNC_ToolGetToolPara(t, EXTOOL_LTOOL_PARM, &f_value);
        value = (Bit32)(f_value);
        if ((value & TOOL_ROLL_DIR) == TOOL_ROLL_DIR)
        {
            str = "1";
        }
        else
        {
            str = "0";
        }
        break;
    default:
        f_d = GetToolDiaByMacType(t, macType, index);
        ret = HNC_ToolGetToolPara(t, index, &f_value);
        f_value = f_d * f_value;
        f_value = TransMetric2Show(f_value);
        if (HNC_DoubleCompare(f_value, 0.0) == 0)
        {
            f_value = 0.0;
        }
        snprintf(txt, STR_BUF_LEN, f_format, f_value);
        str = QString(txt);
        break;
    }

    return ret;
}

Bit32 ToolSetToolSavePara(Bit32 t, Bit32 index, fBit64 fVal, Bit32 macType)
{
    fBit64 f_d = 0;
    Bit32 ret = -1;
    fBit64 f_value = 0.0;
    Bit32 value = 0;

    if (t < 1)
    {
        return ret;
    }
    //Fixed #5004, reason is : 参数里显示精度最多到小数点后5位,用户输入-0需要处理为0
    //最小精度限制为0.00001
    if (fabs(fVal) < 1e-5)
    {
        fVal = 0.0;
    }

    switch (index)
    {
    case LTOOL_DIR:
        ret = HNC_ToolSetToolPara(t, index, &fVal);
        break;
    case HMI_EXTOOL_LTOOL_ROLL_DIR:
        HNC_ToolGetToolPara(t, EXTOOL_LTOOL_PARM, &f_value);
        value = (Bit32)(f_value);
        if (HNC_DoubleCompare(fVal, 0.0) == 0)
        {
            value &= (~TOOL_ROLL_DIR);
        }
        else
        {
            value |= TOOL_ROLL_DIR;
        }
        f_value = value;
        ret = HNC_ToolSetToolPara(t, EXTOOL_LTOOL_PARM, &f_value);
        break;
    default:
        f_d = GetToolDiaByMacType(t, macType, index);
        fVal /= f_d;
        fVal = TransMetric2Save(fVal);
        ret = HNC_ToolSetToolPara(t, index, &fVal);
        break;
    }

    return ret;
}

QString GetAxEditName(Bit32 ch, Bit32 idx)
{
    Bit8 axEditName[32] = {'\0'};
    Bit32 axisId = 0;
    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_XINDEX + idx, &axisId);
    if(axisId >= 0)
    {
        ParaGetStrVal(PARAMAN_FILE_AXIS, axisId, PAR_AX_NAME, axEditName);
    }
    return QString(axEditName);
}

Bit32 ToolSetToolState(Bit32 t, Bit32 val)
{
    Bit32 oldVal = 0;
    HNC_ToolGetToolPara(t, INFTOOL_STATE, &oldVal);
    oldVal &= (0xffff0000); // 保留 INFTOOL_STATE 高十六的数据

    val &= (0x0000ffff);

    Bit32 tmpValue = (oldVal | val);
    return HNC_ToolSetToolPara(t, INFTOOL_STATE, &tmpValue);
}

fBit64 GetToolDiaByMacType(Bit32 t, Bit32 macType, Bit32 parmIdx)
{
    fBit64 f_d = 1.0;

    if(macType == 1) // 车
    {
        switch (parmIdx)
        {
        case GTOOL_LEN1: // X偏置
        case WTOOL_RAD1: // X磨损
        case WTOOL_TOOL_LEN_SET_X:      // X设定
        case WTOOL_TOOL_LEN_FIX_X:      // X修正
        case TOOL1_X1:
        case TOOL1_X2:
        case TOOL1_I1:
        case TOOL1_I2:
            f_d = ToolDiameterTrans(0);
            break;
        case GTOOL_LEN2: // Y偏置
        case WTOOL_LEN2: // Y磨损
        case WTOOL_TOOL_LEN_SET_Y:      // Y设定
        case WTOOL_TOOL_LEN_FIX_Y:      // Y修正
        case WTOOL_TOOL_LEN_SET_Z:      //z设定
        case WTOOL_TOOL_LEN_FIX_Z:      //z修正
        case TOOL1_Y1:
        case TOOL1_Y2:
        case TOOL1_J1:
        case TOOL1_J2:
            f_d = ToolDiameterTrans(1);
            break;
        case GTOOL_LEN3: // Z偏置
        case WTOOL_LEN1: // Z磨损
        case TOOL1_Z1:
        case TOOL1_Z2:
        case TOOL1_K1:
        case TOOL1_K2:
            f_d = ToolDiameterTrans(2);
            break;
        default:
            break;
        }
    }
    else if(macType == 2) // 车铣复合
    {
        Bit32 toolType = 0;
        HNC_ToolGetToolPara(t, INFTOOL_TYPE, &toolType);
        if(toolType == TM_TYPE_TURN)
        {
            switch (parmIdx)
            {
            case GTOOL_LEN2: // 长度X
            case WTOOL_RAD1:
                f_d = ToolDiameterTrans(0);
                break;
            case GTOOL_LEN3: // 长度Y
            case WTOOL_LEN2:
                f_d = ToolDiameterTrans(1);
                break;
            case GTOOL_LEN1: // 长度Z
            case WTOOL_LEN1:
                f_d = ToolDiameterTrans(2);
                break;
            default:
                break;
            }
        }
    }

    return f_d;
}

bool IsToolLocked(Bit32 toolNo)
{
    fBit64 fValue = 0.0;
    HNC_ToolGetToolPara(toolNo, EXTOOL_LTOOL_PARM, &fValue);
    Bit32 value = (Bit32)(fValue);
    if (IS_TOOL_LOCKED == (IS_TOOL_LOCKED & value))
    {
        return true;
    }
    return false;
}

void SetToolLocked(Bit32 toolNo, bool isLocked)
{
    fBit64 fValue = 0.0;
    HNC_ToolGetToolPara(toolNo, EXTOOL_LTOOL_PARM, &fValue);
    Bit32 value = (Bit32)(fValue);
    if (isLocked)
    {
        value |= IS_TOOL_LOCKED;
    }
    else
    {
        value &= (~IS_TOOL_LOCKED);
    }
    fValue = value;
    HNC_ToolSetToolPara(toolNo, EXTOOL_LTOOL_PARM, &fValue);
}

void SaveToolLocked()
{
    Bit32 tNum = 0;

    m_pLockToolNo.clear();
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_TOOL_NUM, &tNum);
    for (Bit32 i = 0; i < tNum; i++)
    {
        if (IsToolLocked(i))
        {
            m_pLockToolNo.append(i);
        }
    }
}

void ResetToolLocked()
{
    for (int i = 0; i < m_pLockToolNo.count(); i++)
    {
        Bit32 toolNo = m_pLockToolNo.at(i);
        SetToolLocked(toolNo, true);
    }
}

// 磨损范围与直半径/公英制无关,完全根据参数来
fBit64 GetTurningWearVal()
{
    fBit64 limit = 0;

    ParaGetFloatVal(PARAMAN_FILE_NCU, 0, PAR_NCU_TOOL_WEAR_RANGE, &limit);
    return limit;
}

Bit32 FindToolName(Bit32 ch, Bit32 t, QString name)
{
    if (name.isEmpty() == true)
    {
        return 0;
    }

    Bit32 toolNum = 0;
    Bit32 toolSt = 0;
    Bit8 toolName[MAX_TOOL_NAME_LEN] = {'\0'};

    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_TOOL_NUM, &toolNum);
    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_TOOL_START_NO, &toolSt);

    for (Bit32 i = 0; i < toolNum; i++)
    {
        if (toolSt + i == t)
        {
            continue;
        }

        HNC_ToolGetToolPara(toolSt + i, TOOL_INFO_NAME, &toolName);
        if (name == QString(toolName))
        {
            return i + 1;
        }
    }
    return 0;
}

void SetToolInputType(Bit32 type)
{
    Bit32 val = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_TABRA_ADD_EN, &val);
    if (type != val)
    {
        HmiParSet::GetInstance()->SetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_TABRA_ADD_EN, type);
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("刀具磨损累加使能设置为%1").arg(type), NULL);

        HmiParSet::GetInstance()->SaveParamChanges();
    }
}

void ChangeToolInputType()
{
    Bit32 val = 0;
    Bit32 type = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_TABRA_ADD_EN, &val);
    if (val == 0)
    {
        type = 1;
    }
    else
    {
        type = 0;
    }
    SetToolInputType(type);
}

QList<Bit32 > GetToolMagList()
{
    Bit32 magNum = 0;
    Bit32 magSt = 0;
    QList<Bit32 >magList;
    magList.clear();

    for(int ch = 0; ch < GetChannelNum(); ch++)
    {
        ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_MAG_NUM, &magNum);
        ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_MAG_START_NO, &magSt);
        if(magNum == 0)
        {
            magNum = 1; // 单刀库该参数默认为0
        }
        if(magSt < 1)
        {
            continue;
        }

        for(int mag = magSt; mag < magSt + magNum; mag++)
        {
            if(!magList.contains(mag))
            {
                magList.append(mag);
            }
        }
    }

    if(magList.count() > 1)
    {
        qSort(magList.begin(), magList.end());
    }

    return magList;
}

bool CheckToolMagData()
{
    Bit32 offTmp = 0;
    Bit32 toolNumTmp = 0;
    Bit32 offBase = 0;
    Bit32 toolNumBase = 0;
    Bit32 base = 0;
    QList<Bit32 >magList = GetToolMagList();

    if(magList.count() <= 1) // 多刀库才需要检查数据是否重叠
    {
        return true;
    }

    HNC_ToolGetMagBase(magList.at(0), MAGZTAB_HEAD, &offBase);
    HNC_ToolGetMagBase(magList.at(0), MAGZTAB_TOOL_NUM, &toolNumBase);
    base = offBase + toolNumBase*2; // 刀具、刀位两组属性，所以是2倍
    for(int i = 1; i < magList.count(); i++)
    {
        HNC_ToolGetMagBase(magList.at(i), MAGZTAB_HEAD, &offTmp);
        HNC_ToolGetMagBase(magList.at(i), MAGZTAB_TOOL_NUM, &toolNumTmp);

        if(offTmp < base)
        {
            return false;
        }

        base = offTmp + toolNumTmp*2;
    }

    return true;
}

void ResetToolMagData()
{
    Bit32 toolNumTmp = 0;
    Bit32 base = 0;
    Bit32 startChTool = 1;
    Bit32 val = 0;
    fBit64 fVal = 0.0;
    Bit32 chanToolNum = 0;
    Bit32 chanToolStart = 0;
    Bit32 magStart = 0;
    Bit32 magNum = 0;
    QList<Bit32 >magList = GetToolMagList();

    if(magList.count() < 1)
    {
        return;
    }

    HNC_ToolGetMagBase(magList.at(0), MAGZTAB_HEAD, &base);
    for(int m = 0 ; m < magList.count(); m++) // 清除表头数据，自动分配偏移地址
    {
        HNC_ToolGetMagBase(magList.at(m), MAGZTAB_TOOL_NUM, &toolNumTmp); // 获取刀库容量

        HNC_ToolSetMagBase(magList.at(m), MAGZTAB_HEAD, base); // 表头偏移地址
        HNC_ToolSetMagBase(magList.at(m), MAGZTAB_CUR_TOOL, val); // 当前刀号
        HNC_ToolSetMagBase(magList.at(m), MAGZTAB_CUR_POT, val); // 当前刀位号
        //HNC_ToolSetMagBase(magList.at(m), MAGZTAB_TYPE, val);
        HNC_ToolSetMagBase(magList.at(m), MAGZTAB_RETURN, val);
        HNC_ToolSetMagBase(magList.at(m), MAGZTAB_WAIT, val);

        base += toolNumTmp*2;
    }

    for(int ch = 0; ch < GetChannelNum(); ch++) // 刀库排刀
    {
        ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_MAG_START_NO, &magStart);
        ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_MAG_NUM, &magNum);
        ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_TOOL_START_NO, &chanToolStart);
        ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_TOOL_NUM, &chanToolNum);

        if(magNum == 0)
        {
            magNum = 1; // 单刀库该参数默认为0
        }
        for(int m = magStart; m < magStart + magNum; m++)
        {
            if(magNum > 1)
            {
                HNC_ToolGetMagBase(m, MAGZTAB_START_TOOLNO, &startChTool);
            }
            HNC_ToolGetMagBase(m, MAGZTAB_TOOL_NUM, &toolNumTmp); // 获取刀库容量
            for(int pot = 1; pot <= toolNumTmp; pot++)
            {
                Bit32 chTool = startChTool + pot -1;
                Bit32 gTool = chTool + chanToolStart -1;
                HNC_ToolSetPotAttri(m, pot, 0); // 刀位属性
                if(gTool >= chanToolStart && gTool < chanToolStart + chanToolNum)
                {
                    HNC_ToolMagSetToolNo(m, pot, chTool);
                    HNC_ToolSetToolPara(gTool, EXTOOL_LARGE_LEFT, &fVal); // 刀具属性
                    HNC_ToolSetToolPara(gTool, INFTOOL_G64MODE, &val); // 刀具加工模式
                }
                else
                {
                    HNC_ToolMagSetToolNo(m, pot, val);
                }
            }

        }
    }

    HNC_ToolMagSave();
    HNC_ToolChg();
    Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("刀库数据重置"));
}

void InitCurMag(Bit32 ch)
{
    if(ch < 0 || ch >= GetChannelNum())
    {
        return;
    }


    Bit32 magNum = 0;
    Bit32 magSt = 0;

    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_MAG_NUM, &magNum);
    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_MAG_START_NO, &magSt);

    if(m_nCurMag < magSt || m_nCurMag >= magSt + magNum)
    {
        m_nCurMag = magSt;
    }
}

/*!
 * \brief ChangeCurMag 切换刀库
 * \param ch 当前通道号
 * \return 0:成功 -1:刀库起始号错误 -2:只有一个刀库
 */
Bit32 ChangeCurMag(Bit32 ch)
{
    if(ch < 0 || ch >= GetChannelNum())
    {
        return -3;
    }

    Bit32 magNum = 0;
    Bit32 magSt = 0;

    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_MAG_NUM, &magNum);
    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_MAG_START_NO, &magSt);
    if (magSt <= 0)      //刀库起始号从1开始
    {
        return -1;
    }
    if (magNum > 1)
    {
        if (m_nCurMag >= 1 && m_nCurMag < magNum + magSt - 1)
        {
            m_nCurMag++;
        }
        else
        {
            m_nCurMag = magSt;
        }
        return 0;
    }
    else
    {
        return -2;
    }
}

Bit32 GetCurMagIdx()
{
    return m_nCurMag;
}
