﻿#ifndef OSCWAVEDRAW_H
#define OSCWAVEDRAW_H

#include <qcustomplot.h>

#include "hmioscapp.h"
#include "hmioscservo.h"

namespace Ui {
class OscWaveDraw;
}

QT_BEGIN_NAMESPACE
class QWidget;
class QCustomPlot;
class QCPCurve;
QT_END_NAMESPACE

class OscWaveDraw : public QWidget
{
    Q_OBJECT

public:
    explicit OscWaveDraw(QWidget *parent = 0, QString name = "");
    ~OscWaveDraw();

    typedef enum _OscSelMoveDir
    {
        OSC_SEL_MOVE_LEFT = 0, // 向左
        OSC_SEL_MOVE_RIGHT,	   // 向右
        OSC_SEL_MOVE_UP,	   // 向上
        OSC_SEL_MOVE_DOWN,	   // 向下
    }OscSelMoveDir;

    void SetZoomXUp(); // 横轴放大
    void SetZoomXDn(); // 横轴缩小
    void SetZoomYUp(); // 纵轴放大
    void SetZoomYDn(); // 纵轴缩小

    void ShowSelArea();
    void HideSelArea();
    Bit32 MoveSelArea(OscSelMoveDir dir);
    Bit32 SelAreaZoomUp();
    Bit32 SelAreaZoomDown();
    Bit32 SelAreaZoomFullSrceen();

    void ClearPoint();
    void LineAddPoint(Bit32 index, QVector<double>x, QVector<double>y);
    void LineZeroAddPoint(const QVector<double> x, const QVector<double> y);
    void LineOneAddPoint(const QVector<double> x, const QVector<double> y);
    void SpindleLineZeroAddPoint(const QVector<double> x, const QVector<double> y);
    void SetColor(QColor &bk, QColor &gd, QColor &ft, QColor &c1, QColor &c2, QColor &c3, QColor &c4);
    void SetLineColor(int lineNo, QColor &color);

    void SelAreaDataRestore();

    void XAxisGetTickParm(fBit64 *lower, fBit64 *upper, fBit64 *step);
    void XAxisSetTickParm(fBit64 lower, fBit64 upper, fBit64 step);
    void YAxisGetTickParm(fBit64 *lower, fBit64 *upper, fBit64 *step);
    void YAxisSetTickParm(fBit64 lower, fBit64 upper, fBit64 step);

    void WaveReplot();
    QCustomPlot *GetWavePlot();
    void CreateGroup(QCustomPlot *plot);

    void SetYName(QString str, QString str2 = ""); // 设置轴名
    void SetXBaseValue(fBit64 startCoord, Bit32 spaceNum, fBit64 spaceValue); // 设置X轴标尺
    void SetSpindleWearXBaseValue(Bit32 spaceValue, Bit32 endSpeed, Bit32 spaceNum); // 设置主轴负荷X轴标尺
    void ResetRange(); // 自动调整纵轴显示范围
    void UltrasoundResetRange(); // 超声波调整显示范围

private:
    Ui::OscWaveDraw *ui;

    QString waveName;

	// 主界面的标尺
    fBit64 baseXRangLower;
    fBit64 baseXRangUpper;
    fBit64 baseXStep;
    fBit64 baseYRangLower;
    fBit64 baseYRnagUpper;
    fBit64 baseYStep;

    fBit64 plotXStepMix;
    fBit64 plotXStepMax;
    fBit64 plotYStepMix;
    fBit64 plotYStepMax;

    // y轴数据的范围
    fBit64 yMin;
    fBit64 yMax;
    // y2轴数据的范围
    fBit64 yMin2;
    fBit64 yMax2;

    Bit32 allLength;    // 显示区域总长度
    Bit32 allHeight;    // 显示区域总宽度
    Bit32 selAreaLength;// 选择框长度
    Bit32 selAreaHeight;// 选择框宽度
    Bit32 selAreaPosX;  // 选择框左上角X坐标
    Bit32 selAreaPosY;  // 选择框左上角Y坐标

    QCPCurve *graphSel;

    void SetupPlot();
    void CalSelArea();
    void ResetRangeTick(); // 调整纵轴上下边界为间隔的整数倍

    void SpindleYAxisAutoSet(fBit64 yMaxVal);
};

#endif // OSCWAVEDRAW_H
