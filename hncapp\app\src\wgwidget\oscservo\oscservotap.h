﻿#ifndef OSCSERVOTAP_H
#define OSCSERVOTAP_H

#include "containerwidget.h"

namespace Ui {
class OscServoTap;
}

QT_BEGIN_NAMESPACE
class QWidget;
class OscWave;
class OscList;
QT_END_NAMESPACE

class OscServoTap : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoTap(QWidget *parent = 0);
    ~OscServoTap();

    void SetColorStyle();
protected:
    void resizeEvent(QResizeEvent *);
    bool eventFilter(QObject *target, QEvent *event);
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
private slots:
    void on_leftBtn_clicked();
    void on_rightBtn_clicked();
private:
    Ui::OscServoTap *ui;

    OscList *posOscList;
    OscWave *pOscWaveSpe;
    OscWave *pOscWaveSyncErr;
    Bit32 lastEndPos;
    Bit32 m_ax; // 记录posOscList列表的轴

    bool firstFlag;
    void OnBtFlagChange();
    void Reset();
    void Refresh();
    void LoadInfo();
    QStringList GetParmList();
    void LoadAxisVal();
};

#endif // OSCSERVOTAP_H
