﻿/*!
 * @file common.h
 * @brief 共通功能
 * @note
 *
 * @version V1.00
 * @date 2023/6/30
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */
#ifndef __COMMON_H__
#define __COMMON_H__

#include <QFileInfo>
#include <QTextCodec>
#include <QValidator>
#include <QTableWidget>
#include <QDomDocument>

#ifdef _WIN32
#include <stdio.h>
#define snprintf _snprintf
#endif

#include "hncdatatype.h"
#include "hncdatadef.h"
#include "uintp_decoder.h"
#include "progman_def.h"

#include "msgprompt.h"
#include "msgpromptwith3exit.h"
#include "inputmanage.h"
#include "resize.h"
#include "logdt.h"
#include "dlgreplace.h"

#define UTF8 QTextCodec::codecForName("UTF-8")
#define TR(a) tr((UTF8->fromUnicode(a).data()))

const Bit32 STR_BUF_LEN = 128; // 字符串长度
const Bit32 EDIT_LINE_MAX = MAX_CHAR_INROW; // 程序编辑单行最大字符数

#define HOR_RATIO   (Resize::GetHorRatio())
#define VER_RATIO   (Resize::GetVerRatio())
#define FONT_RATIO   (Resize::GetFontRatio())
#define HOR_SCALE_RATIO (Resize::GetHorScaleRatio())
#define TABLE_FONT (Resize::GetTableFont())

#define FONT_TYPE   "Microsoft YaHei"

#define CODE_UTF8   "UTF-8"
#define CODE_GB     "GB18030"
#define CODE_GB2312     "GB2312"

#define USERP_FILE_NAME "USERP.STR" // P参数注释

const Bit32 DLG_WIDTH = 300; // Dialog宽度
const Bit32 DLG_HEIGHT = 200; // Dialog高度

/**
 * @brief HMI_PARM_X_PLACES X:0~31
 */
const Bit32 HMI_PARM_0_PLACES = 0x1;        // 取参数第0位
const Bit32 HMI_PARM_1_PLACES = 0x2;        // 取参数第1位
const Bit32 HMI_PARM_2_PLACES = 0x4;        // 取参数第2位
const Bit32 HMI_PARM_3_PLACES = 0x8;        // 取参数第3位
const Bit32 HMI_PARM_4_PLACES = 0x10;       // 取参数第4位
const Bit32 HMI_PARM_5_PLACES = 0x20;       // 取参数第5位
const Bit32 HMI_PARM_6_PLACES = 0x40;       // 取参数第6位
const Bit32 HMI_PARM_7_PLACES = 0x80;       // 取参数第7位
const Bit32 HMI_PARM_8_PLACES = 0x100;      // 取参数第8位
const Bit32 HMI_PARM_9_PLACES = 0x200;      // 取参数第9位
const Bit32 HMI_PARM_10_PLACES = 0x400;     // 取参数第10位
const Bit32 HMI_PARM_11_PLACES = 0x800;     // 取参数第11位
const Bit32 HMI_PARM_12_PLACES = 0x1000;    // 取参数第12位
const Bit32 HMI_PARM_13_PLACES = 0x2000;    // 取参数第13位
const Bit32 HMI_PARM_14_PLACES = 0x4000;    // 取参数第14位
const Bit32 HMI_PARM_15_PLACES = 0x8000;    // 取参数第15位
const Bit32 HMI_PARM_16_PLACES = 0x10000;   // 取参数第16位
const Bit32 HMI_PARM_17_PLACES = 0x20000;   // 取参数第17位
const Bit32 HMI_PARM_18_PLACES = 0x40000;   // 取参数第18位
const Bit32 HMI_PARM_19_PLACES = 0x80000;   // 取参数第19位
const Bit32 HMI_PARM_20_PLACES = 0x100000;  // 取参数第20位
const Bit32 HMI_PARM_21_PLACES = 0x200000;  // 取参数第21位
const Bit32 HMI_PARM_22_PLACES = 0x400000;  // 取参数第22位
const Bit32 HMI_PARM_23_PLACES = 0x800000;  // 取参数第23位
const Bit32 HMI_PARM_24_PLACES = 0x1000000; // 取参数第24位
const Bit32 HMI_PARM_25_PLACES = 0x2000000; // 取参数第25位
const Bit32 HMI_PARM_26_PLACES = 0x4000000; // 取参数第26位
const Bit32 HMI_PARM_27_PLACES = 0x8000000; // 取参数第27位
const Bit32 HMI_PARM_28_PLACES = 0x10000000;// 取参数第28位
const Bit32 HMI_PARM_29_PLACES = 0x20000000;// 取参数第29位
const Bit32 HMI_PARM_30_PLACES = 0x40000000;// 取参数第30位
const Bit32 HMI_PARM_31_PLACES = 0x80000000;// 取参数第31位

const fBit64 METRIC_DISP_COEF = 25.4; // 公英制换算系数

// 快速校验控制标记字(来源datadef.h)
#define HMI_FAST_VERIFY_STOP           (0x00000000) // 快速校验停止
#define HMI_FAST_VERIFY_START          (0X00000001) // 快速校验开始
#define HMI_FAST_VERIFY_WORKING        (0X00000002) // 快速校验进行中
#define HMI_FAST_VERIFY_NO_CC_START    (0X00000004) // 无刀补快速校验开始
#define HMI_FAST_VERIFY_NO_CC_WORKING  (0X00000008) // 无刀补快速校验进行中
#define HMI_FAST_VERIFY_NO_CHECK_LIMIT (0X00000010) // 快速校验不检测软限位
#define HMI_FAST_VERIFY_TWINCODEING    (0X00000020)	// 双码启动


//#define TOOL_TMPBUF_FULL_STR (QObject::TR("加工过程中允许修改的刀具数据最大支持10个,已达上限"))

#ifndef QRENSTR_NUM_MAX
#define QRENSTR_NUM_MAX 2048//huffman压缩时使用
#endif

#ifndef QRENSTR_NUM
#define QRENSTR_NUM QRENSTR_NUM_MAX
#endif

#define PASSWORD_SIZE   (9)     // 密码文本的最大长度

#define MENU_USRDEF_HEAD    "W_USRDEF"

// 释放类内存
#define DeleteClass(pClass)  {if (pClass)\
                                {\
                                    delete pClass;\
                                    pClass = NULL;\
                                }\
                              }


// MDI切换方式定义
typedef enum _MDI_TYPE_
{
    NC_MDI_TYPE = 0,        // NC MDI
    MCP_MDI_TYPE            // MCP MDI
}ENMdiType;

// MDI状态
typedef enum _MDI_STATE_
{
    NOT_MDI_STATE = 0,      // 非MDI模式
    MDI_STATE               // MDI模式
}ENMdiState;

enum MESSAGE_COLOR_STYLE {
    BACK_STYLE = 0, OK_STYLE, ALARM_STYLE, PROMPT_STYLE, DNC_STYLE,
    MDI_STYLE, EBX_STYLE,
    SYS_MSG_STYLE, SYS_MSG_1_STYLE
};

enum INPUT_LANG // 输入语言
{
    INPUT_ENG = 0,
    INPUT_CHN,
    INPUT_COUNT
};

// 伺服类型
typedef enum _SERVO_TYPE_
{
	SERVO_INVALID = -1,                         // 无效
	SERVO_SPDL = 0,                             // 主轴驱动器
	SERVO_FEED                                  // 进给轴驱动器
}ENServoType;

/**
 * @brief CommonInit 共同模块初始化
 * @param [in] pInputMng:输入框
 * @param [in] pMsgPrompt:提示框
 */
extern void CommonInit(InputManage *pInputMng, MsgPrompt *pMsgPrompt, MsgPromptWith3Exit *pMsgPromptWith3Exit);

/**
 * @brief 获取输入对话框指针
 * @return
 */
extern InputManage * CommonGetInputDlg();

extern void CommonInitFindDlg(DlgFindAndReplace *pFindDlg);

extern DlgFindAndReplace *CommonGetFindDlg();

/*
 * @brief strlcpy 指定长度字符串拷贝(用于替代strncpy，提供更安全可靠的字符串拷贝功能)
 * @param dst:目标地址
 * @param src:源地址
 * @param dst_sz:拷贝长度
 * @return >0且<dst_sz：字符串完整复制
 */
extern size_t strlcpy(Bit8 *dst, const Bit8 *src, size_t dst_sz);

///////////////////////////////////////////////////////////////////////////////
//
//    size_t strlcat(Bit8 *dst, const Bit8 *src, size_t dst_sz);
//
//    功能：
//         指定长度字符串连接
//
//	  描述：
//		   用于替代strncat，提供更安全可靠的字符串连接功能
//
//    参数：
//         dst:目标地址
//         src:源地址
//         dst_sz:总长度
//
//    返回：
//         >0且<dst_sz：完整连接
//
///////////////////////////////////////////////////////////////////////////////
extern size_t strlcat(Bit8 *dst, const Bit8 *src, size_t dst_sz);

///////////////////////////////////////////////////////////////////////////////
//
//    extern Bit32 CommGetTimeNum(void)
//
//    功能：
//            获取当前时间转换的数字
//
//    参数：
//             ：
//
//    描述：
//
//
//    返回：
//
//
//////////////////////////////////////////////////////////////////////////
extern Bit32 CommGetTimeNum(void);

///////////////////////////////////////////////////////////////////////////////
//
//    extern Bit32 CommGetDateNum(void)
//
//    功能：
//            获取当前日期转换的数字
//
//    参数：
//             ：
//
//    描述：
//
//
//    返回：
//
//
//////////////////////////////////////////////////////////////////////////
extern Bit32 CommGetDateNum(void);

///////////////////////////////////////////////////////////////////////////////
//
//    extern fBit64 GetValueLimit(void)
//
//    功能：
//            获取坐标输入值限制
//
//    参数：
//             ：
//
//    描述：
//
//
//    返回：
//
//
//////////////////////////////////////////////////////////////////////////
extern fBit64 GetValueLimit(void);

///////////////////////////////////////////////////////////////////////////////
//
//    int IsRunning(int ch)
//
//    功能：
//            通道是否运行
//
//    参数：
//             ch ：通道号
//
//    描述：
//
//
//    返回：
//          0：未运行 1：运行
//
//////////////////////////////////////////////////////////////////////////
extern int IsRunning(int ch);

///////////////////////////////////////////////////////////////////////////////
//
//    int AnyChanIsRunning()
//
//    功能：
//            配置的通道中是否有通道在运行程序
//
//    参数：
//
//    描述：
//            程序运行指循环启动状态或进给保持状态
//
//    返回：
//          0：未运行 1：运行
//
//////////////////////////////////////////////////////////////////////////
extern int AnyChanIsRunning();

///////////////////////////////////////////////////////////////////////////////
//
//    int AnyChanIsRandom()
//
//    功能：
//            配置的通道中是否有通道在任意行
//
//    参数：
//
//    描述：
//            
//
//    返回：
//          0：未任意行 1：有通道处于任意行
//
//////////////////////////////////////////////////////////////////////////
extern int AnyChanIsRandom();

///////////////////////////////////////////////////////////////////////////////
//
//    int AnyChanIsCycing()
//
//    功能：
//            配置的通道中是否有通道在循环启动中
//
//    参数：
//
//    描述：
//      部分功能在程序进给保持时是可以使用的，仅仅在循环启动状态下不可用，此时AnyChanIsRunning()接口不能满足需求
//
//    返回：
//          0：非循环启动 1：循环启动
//
//////////////////////////////////////////////////////////////////////////
extern bool AnyChanIsCycing();

/**
 * @brief IsCycingState 通道在循环启动中
 * @param ch
 * @return
 */
bool IsCycingState(Bit32 ch);

/**
 * @brief ChanState 通道状态
 * @return 0：停止；1：进给保持；2：循环启动
 */
extern int ChanState();

extern int ChannalState(Bit32 ch);

/**
 * @brief FProgLoad 加载程序
 * @param [in] ch 通道号
 * @param [in] progName 程序名
 * @return 0：加载成功 -1：加载失败 -20：参数错误 -21：通道有程序在运行
 */
extern Bit32 FProgLoad(Bit32 ch, Bit8 *progName);

/**
 * @brief ActiveChan 获取活动通道
 * @return 活动通道
 */
extern int ActiveChan();

///////////////////////////////////////////////////////////////////////////////
//
//    void MessageOut(message)
//
//    功能：
//             在下面提示栏显示提示信息
//
//    参数：
//             message:提示内容
//    描述：
//
//
//    返回：
//
//////////////////////////////////////////////////////////////////////////
extern void MessageOut(QString message);

///////////////////////////////////////////////////////////////////////////////
//
//   void MessageTopic(QString buf)
//
//    功能:
//				刷新下面提示栏左边信息提示文字
//
//    参数:
//				buf:显示的字符串
//
//    描述:
//
//
//	  返回：
//				无返回值
//
////////////////////////////////////////////////////////////////////////////////
extern void MessageTopic(QString buf); // 设置左边信息提示文字

///////////////////////////////////////////////////////////////////////////////
//
//    Bit32 MessageInput(QString *str, Bit8 ebxType, QString info, Bit32 len, Bit32 prec, Bit32 exitType, QValidator *pVld)
//
//    功能:
//			 从下面提示栏获得输入内容
//
//    参数:
//			 str:输出参数: 输入消息栏的字符串；
//			 ebxType:消息栏字符串的类型；
//			 info:提示信息：消息栏首不可编辑的提示信息；
//			 len:指定字符串长度，-1表示默认长度；
//           prec:小数位数，-1表示默认小数位数；
//           exitType:退出方式，0表示Esc/Enter退出 1表示Esc/Enter/Up/Down退出；
//           pVld:输入限制正则表达式；
//
//    描述:
//
//
//	  返回:
//			 获得文本标志 0：由【回车键】成功获得文本；-1：未获得文本；-2：取消输入；1：由【Up】成功获得文本；2：由【Down】成功获得文本
//
//////////////////////////////////////////////////////////////////////////
extern Bit32 MessageInput(QString *str, Bit32 ebxType = DTYPE_STRING, QString info = "", Bit32 len = -1, Bit32 prec = -1, Bit32 exitType = 0, QValidator *pVld = NULL, bool selStr = true, bool emptyAble = false);
extern Bit32 MessageInput(QString *str, NcDataLimit dataLimit, QString info = "", Bit32 exitType = 0, QValidator *pVld = NULL, bool selStr = true, bool emptyAble = false);

void SetMessageInputHide();

///////////////////////////////////////////////////////////////////////////////
//
//    Bit32 PasswordInput(QString *str, Bit8 ebxType, QString &info, Bit32 len, Bit32 prec, Bit32 exitType)
//
//    功能:
//			 从下面提示栏获得输入密码
//
//    参数:
//			 str:输出参数: 输入消息栏的字符串；
//			 ebxType:消息栏字符串的类型；
//			 info:提示信息：消息栏首不可编辑的提示信息；
//			 len:指定字符串长度，-1表示默认长度；
//           prec:小数位数，-1表示默认小数位数；
//           exitType:退出方式，0表示Esc/Enter退出 1表示Esc/Enter/Up/Down退出；
//           pVld:输入限制正则表达式;最后生效，将覆盖len,prec的设置
//
//    描述:
//
//
//	  返回:
//			 获得文本标志 0：由【回车键】成功获得文本；-1：未获得文本；-2：取消输入；1：由【Up】成功获得文本；2：由【Down】成功获得文本
//
//////////////////////////////////////////////////////////////////////////
extern Bit32 PasswordInput(QString *str, NcDataLimit dataLimit, QString info = "", Bit32 exitType = 0, QValidator *pVld = NULL, bool selStr = true);

///////////////////////////////////////////////////////////////////////////////
//
//    Bit32 MessagePrompt(QString str, Bit32 style)
//
//    功能：
//            提示确认或取消的消息栏
//
//    参数：
//            buf ：提示文本
//            style ：文本显示风格
//
//    描述：
//
//
//    返回：
//            [0/1]：0：取消，1：确认；
//
//////////////////////////////////////////////////////////////////////////
extern Bit32 MessagePrompt(QString str, Bit32 style = PROMPT_STYLE);

/*!
 * \brief MessagePromptWith3Exit 提示消息栏
 * \param str 提示文本
 * \param keyList 提示消息栏接收键值
 * \param style 文本显示风格
 * \return 退出使用的按键
 */
extern int MessagePromptWith3Exit(QString str, QList<int> keyList, Bit32 style = PROMPT_STYLE);
///////////////////////////////////////////////////////////////////////////////
//
//    void MessageResize(Bit32 x, Bit32 y, Bit32 width, Bit32 height)
//
//    功能:
//			 设置信息栏输入框位置及大小
//
//    参数:
//			 x:位置x；
//			 y:位置y；
//			 width:宽度；
//			 height:长度；
//
//    描述:
//
//
//	  返回:
//			 无；
//
//////////////////////////////////////////////////////////////////////////
extern void MessageResize(Bit32 x, Bit32 y, Bit32 width, Bit32 height);

// 获取编辑方式 0:信息栏输入框 1:弹框
extern Bit32 IsDlgInput();

// 取坐标显示精度
extern Bit32 GetPosPrec();

// 取F速度显示精度
extern Bit32 GetFPrec();

// 取S速度显示精度
extern Bit32 GetSpPrec();

// 取公英制设置
extern Bit32 GetMetricDisp();

///////////////////////////////////////////////////////////////////////////////
//
//    Bit32 UpsExist()
//
//    功能：
//            判断是否有UPS
//
//    参数：
//
//    描述：
//
//
//    返回：
//            0:没有 1:有；
//
//////////////////////////////////////////////////////////////////////////
extern Bit32 UpsExist();

// 1：校验状态；0：非校验状态
extern Bit32 IsVerifyState(Bit32 ch);

// 1:图形预览开启,0:图形预览关闭
extern Bit32 IsPreviewEn();

extern Bit32 IsAnyChanIsVerify();
// 1：快速校验状态；0：非快速校验状态
extern Bit32 IsFastVerifyState(Bit32 ch);

extern void SetFastVerify(Bit32 ch, Bit32 flag);

extern void StopFastVerify(Bit32 ch);

/**
 * @brief ParaGetIntVal 通过参数类别，子类号，索引获取整形参数值
 * @param fileno 参数类别
 * @param subno 子类号
 * @param index 索引
 * @param value 输出：整形值
 * @return 0：成功 -1：失败
 */
extern Bit32 ParaGetIntVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit32* value);

/**
 * @brief ParaGetIntVal 通过参数类别，子类号，索引设置整形参数值
 * @param fileno 参数类别
 * @param subno 子类号
 * @param index 索引
 * @param value 整形值
 * @return 0：成功 -1：失败
 */
extern Bit32 ParaSetIntVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit32 value);

/**
 * @brief ParaGetFloatVal 通过参数类别，子类号，索引获取浮点型参数值
 * @param fileno 参数类别
 * @param subno 子类号
 * @param index 索引
 * @param value 输出：整形值
 * @return 0：成功 -1：失败
 */
extern Bit32 ParaGetFloatVal(Bit32 fileno, Bit32 subno, Bit32 index, fBit64* value);

/**
 * @brief ParaSetFloatVal 通过参数类别，子类号，索引设置浮点型参数值
 * @param fileno 参数类别
 * @param subno 子类号
 * @param index 索引
 * @param value 整形值
 * @return 0：成功 -1：失败
 */
extern Bit32 ParaSetFloatVal(Bit32 fileno, Bit32 subno, Bit32 index, fBit64 value);

/**
 * @brief ParaGetStrVal 通过参数类别，子类号，索引获取字符串型参数值
 * @param fileno 参数类别
 * @param subno 子类号
 * @param index 索引
 * @param value 整形值
 * @return 0：成功 -1：失败
 */
extern Bit32 ParaGetStrVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit8* value);

/**
 * @brief ParaSetStrVal 通过参数类别，子类号，索引设置字符串型参数值
 * @param fileno 参数类别
 * @param subno 子类号
 * @param index 索引
 * @param value 整形值
 * @return 0：成功 -1：失败
 */
extern Bit32 ParaSetStrVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit8* value);

extern QByteArray StrToQByte(QString str, const Bit8 *unicode);

extern QString CharToStr(const Bit8 *chars, const Bit8 *unicode);

/**
 * @brief ffputs 向指定的文件写入字符串
 * @param str:待写入的字符串
 * @param fp:文件指针
 * @return 0:写入成功 -1:写入失败
 */
extern Bit32 ffputs(const Bit8 *str, FILE *fp);

/**
 * @brief AxisCheckAbs 检查逻辑轴是否是绝对式编码器
 * @param ax：逻辑轴号；
 * @return true：是；false：否；
 */
extern bool AxisCheckAbs(Bit32 ax);

/**
 * @brief GetMdiType 获取切换MDI方式
 * @brief 在参数初始化之后
 * @return NC_MDI_TYPE(0)：NCMDI; MCP_MDI_TYPE(1)：MCPMDI
 */
extern ENMdiType GetMdiType();

/**
 * @brief GetMdiState 获取指定通道的MDI状态
 * @return 0:非MDI模式, 1:MDI模式
 */
extern ENMdiState GetMdiState();

/**
 * @brief GetMdiState 获取活动通道的MDI状态
 * @return 0:非MDI模式, 1:MDI模式
 */
extern ENMdiState GetMdiState(Bit32 ch);

/**
 * @brief GetSelfTestEn 获取健康保障功能开关
 * @return 0:关闭 1:开启
 */
extern Bit32 GetSelfTestEn();

/**
 * @brief GetCompEn 获取热误差调试功能开关
 * @return 0:关闭 1:开启
 */
extern Bit32 GetCompEn();

/**
 * @brief GetOscAppEn 获取故障记录功能开关
 * @return 0:关闭 1:开启
 */
extern Bit32 GetOscAppEn();

/**
 * @brief GetSweeperEn 获取主轴振动规避功能开关
 * @return
 */
extern Bit32 GetSweeperEn();

/**
 * @brief GetScrewwearEn 获取全生命周期负荷图功能开关
 * @return
 */
extern Bit32 GetScrewwearEn();

/**
 * @brief GetEmulateEn 获取运动仿真功能开关
 * @return
 */
extern Bit32 GetEmulateEn();

/**
 * @brief GetEstimateEn 获取工艺参数评估功能开关
 * @return
 */
extern Bit32 GetEstimateEn();

/**
 * @brief GetToolBreakEn 获取断刀检测功能开关
 * @return 0:关闭 1:开启
 */
extern Bit32 GetToolBreakEn();

/**
 * @brief GetOnekeyDataRestoreEn 获取一键还原参数/PLC，备份伺服参数功能开关
 * @return 0:关闭 1:开启
 */
extern Bit32 GetDataRestoreEn();

/**
 * @brief  GetConsistencyCheckEn获取是否打开开机一致性检测参数功能开关
 * @return 0:关闭 1:开启
 */
extern Bit32 GetConsistencyCheckEn();

/**
 * @brief  GetSelfAdjustEn获取是否打开自整定功能开关
 * @return 0:关闭 1:开启
 */
extern Bit32 GetSelfAdjustEn();
/**
 * @brief  GetSpindleWearEn获取是否打开主轴负荷功能开关
 * @return 0:关闭 1:开启
 */
extern Bit32 GetSpindleWearEn();

/**
 * @brief GetChannelNum 获取系统通道个数
 * @return 返回系统通道个数
 */
extern Bit32 GetChannelNum();

extern Bit32 ComGetInputLang();

extern void ProgCurRunInfoRefresh();

/**
 * @brief DebugOutput linux系统下的调试信息输出接口
 * @param str 输出信息
 */
extern void DebugOutput(const QString& str);

extern Bit32 GetLeadAxNo(Bit32 logicalAxis);

extern Bit32 IsDiameter(Bit32 chAxis, Bit32 ch); // 直径

/**
 * @brief TransPicName 图片支持根据皮肤的切换
 * @param path 图片文件路径
 * @return
 */
extern QString TransPicName(QString path);

extern void GetFFormat(Bit32 size, Bit8* fFormat);

extern void GetSFormat(Bit32 size, Bit8* sFormat);

extern Bit32 QueryChannelNo(const Bit32 alarmNo);

/**
 * @brief ChanHaveSyntaxAlarm 通道是否有语法报警
 * @param ch 通道号
 * @return true 有语法报警； false 没有语法报警
 */
extern bool ChanHaveSyntaxAlarm(Bit32 ch);

extern bool IsInputDlgExec();

/**
 * @brief GetCurProgName 获取当前通道程序名
 * @param ch 通道号
 * @param progName 当前通道车铣名
 */
extern void GetCurProgName(Bit32 ch, Bit8 progName[PATH_NAME_LEN]);

extern void GetCurDcdProgName(Bit32 ch, Bit8 progName[PATH_NAME_LEN]);

/**
 * @brief IsStrToNumOK 字符串转数字是否有效
 * @param str 字符串
 * @param type 待转数据类型
 * @return false:无效 true:有效
 */
extern bool IsStrToNumOK(QString str, Bit32 type);

/**
 * @brief IsPosNameFanuc
 * @return true:坐标名与fanuc一致
 */
extern bool IsPosNameFanuc();

extern bool IsProgHeiAutoChg();

extern Bit32 GetFilelistCodeType();

extern void SetHmiDispParm(Bit32 val);

extern void ShowReplaceDlg();

extern QString TeachPosStr(fBit64 fval);

extern QString RemoveExtraZeroFormStr(QString str);

extern QString RealPos2Str(fBit64 fval);

extern QString Feed2Str(fBit64 fval);

/**
 * @brief IsChValid 判断通道是否有效
 * @param [in] ch 通道值
 * @return true:通道值有效；false：通道值无效
 */
extern bool IsChValid(Bit32 ch);

///
/// \brief cycleRunForbid 置循环启动禁止标记位
/// \param f true：禁止循环启动； false：清除循环启动
///
extern void CommSetCycForbid(bool f);

extern Bit32 GetXmlDoc(Bit32 pathType, QString path, QString name, QDomDocument &doc);

extern Bit32 GetXMLElement(Bit32 pathType, QString XMLName, QDomDocument &doc, QString *errorMsg=0, int *errorLine=0, int *errorColumn=0);

extern Bit32 GetXMLElement(QString fullPath, QDomDocument &doc, QString *errorMsg=0, int *errorLine=0, int *errorColumn=0);

/**
 * @brief TransMetric2Show 将数据进行公英制处理，转换为显示数据
 * @param val 待转换数据
 */
extern fBit64 TransMetric2Show(fBit64 val);

/**
 * @brief TransMetric2Save将数据进行公英制处理，转换为保存数据
 * @param val 待保存数据
 */
extern fBit64 TransMetric2Save(fBit64 val);

extern fBit64 TransAxisUnit2Show(Bit32 axis, fBit64 val);

extern QString Pos2Str(fBit64 fval);

/**
 * @brief GetMacChCurType 获取车铣复合的当前机床类型
 * @param ch
 * @return 0：铣 1： 车 -1：其他
 */
extern Bit32 GetMacChCurType(Bit32 ch);

/**
 * @brief SwitchNoByMask
 * @param ch
 * @param axList
 * @param flag 0:数组存逻辑轴号 1:数组存通道轴号
 * @return
 */
extern Bit32 SwitchNoByMask(Bit32 ch, Bit32 axList[CHAN_AXES_NUM], Bit32 flag);

extern bool SimuTurnIsMirrored(Bit32 ch);

/**
 * @brief GetAxisType 获取轴类型（主轴，直线轴，旋转轴等）
 * @param [in] logicAxisNo 逻辑轴号
 * @return 轴类型，-1代表无效轴
 */
extern Bit32 GetAxisType(Bit32 logicAxisNo);

// 数值转Asc码
extern void Num2Asc(int num, uBit8 *buf);

/*!
 * \brief GetChanMacTypeState 获取通道的机床类型
 * \param ch 通道
 * \return 0:铣 1:车 (包含车铣复合)
 */
extern Bit32 GetChanMacTypeState(Bit32 ch);

/*!
 * \brief IsAxisChangeCord 车铣复合专机使用，判断是否产生轴交换
 * \return
 */
extern bool IsAxisChangeCord(Bit32 ch);

extern Bit32 GetMacType(Bit32 ch);

extern ENServoType GetServoType(Bit32 logicAxisNo);          // 获取驱动器类型

extern Bit32 GetAxisDeviceNo(Bit32 logicAxisNo);            // 获取轴对应的设备号

extern bool IsNCUCBusAxis(Bit32 logicAxisNo);               // 是否NCUC总线轴

extern bool IsChannelValid(Bit32 ch);

extern bool IsLogicAxisNoValid(Bit32 logicAxisNo);

extern bool IsBackGroundLoadingOff();

extern bool IsProgPrintOn();

extern bool IsProgPrintShowDlg();

extern bool IsOnRtcp(Bit32 ch);

extern bool IsGcodeNameForbid(QString filePath);

extern bool Is5AxisOn();

extern bool Is5AxisFoolProofingOn();

extern Bit32 GetLanguageFilePath(Bit8* filePath, Bit8* fileName, Bit32 dirType);
#endif
