﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservospindle.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-主轴升降速界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年5月24日
* 说    明：
*/

#include <qmath.h>

#include "hncchan.h"
#include "hncmath.h"
#include "passwd.h"

#include "hmioscproc.h"
#include "hmioscservo.h"
#include "oscwave.h"
#include "osclist.h"
#include "dlgmsgbox.h"

#include "oscservospindle.h"
#include "ui_oscservospindle.h"

OscServoSpindle::OscServoSpindle(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoSpindle)
{
    ui->setupUi(this);
    m_bStartFlag = false;
    pOscSpdl = new OscWave(this, HmiOscServo::OSC_SERVO_SPINDLE, "");
    ui->verticalLayout_2->addWidget(pOscSpdl);

    this->msgBox = new DlgMsgBox(this);
    // 列表设置
    spdlOscList = new OscList(this);
    spdlOscList->installEventFilter(this);
    ui->gridLayout_2->addWidget(spdlOscList);
    spdlOscList->SetEditAgent(true);
}

OscServoSpindle::~OscServoSpindle()
{
    delete ui;
}

/**
 * @brief OscServoSpindle::LoadInfo 加载信息
 * @param [in] flag 是否重新计算主轴相关信息
 */
void OscServoSpindle::LoadInfo(bool flag)
{
    Bit32 ch = ActiveChan();
    fBit64 upTime = 0.0;
    fBit64 downTime = 0.0;
    fBit64 delayTime = 0.0;
    Bit32 maxSpd = HmiOscServo::s_Conf[ch].stSpindleConf.speed;
    Bit32 spdlRate = HmiOscServo::s_Conf[ch].stSpindleConf.spdlRate;
    Bit32 num = oscproc_get_total();
    Bit32 period = HmiOscServo::s_Conf[ch].stSpindleConf.period;
    Bit32 client = HmiOscServo::oscservo_get_sampl_client();
    //
    Bit32 i = 0;
    Bit64 *pdata0 = NULL, *pdata1 = NULL;
    Bit32 idxM03Start = 0;
    Bit32 idxM05Start = 0;
    Bit32 idxM03End = 0;
    Bit32 idxM05End = 0;
    Bit32 idxM03Delay = 0;
    fBit64 tmp = 0;
    Bit32 actSpd = 0;
    fBit64 upCof = 0; // 升速系数
    fBit64 downCof = 0; // 降速系数

    HNC_ChannelGetValue(HNC_CHAN_SPDL_OVERRIDE, ActiveChan(), 0, &HmiOscServo::s_Conf[ch].stSpindleConf.spdlRate);
    pdata0 = oscproc_get_smpldata(0);
    pdata1 = oscproc_get_smpldata(1);

    actSpd = maxSpd * spdlRate / 100;

    if (oscproc_get_total() > 0)
    {
        if (flag == false)
        {
            return;
        }

        for (i = 0; i < num; i++)
        {
            if (pdata0[i] == 2)
            {
                idxM03Start = i;
                break;
            }
        }

        for (i = idxM03Start; i < num; i++)
        {
            tmp = fabs(pdata1[i] * HmiOscServo::smpl_calc_tapvel_coef(1, client)*60000.0);
            if (HNC_DoubleCompare(tmp, actSpd) >= 0)
            {
                idxM03End = i;
                break;
            }
        }

        for (i = idxM03Start; i < idxM03End; i++)
        {
            if (abs(pdata1[i+4]) > 0 && abs(pdata1[i+3]) > 0 && abs(pdata1[i+2]) > 0
                    && abs(pdata1[i+1]) > 0 && abs(pdata1[i]) > 0)
            {
                idxM03Delay = i;
                break;
            }
        }

        for (i = idxM03Start; i< num; i++ )
        {
            if (pdata0[i] == 4)	// M05ÆðÊ¼µã
            {
                idxM05Start = i;
                break;
            }
        }

        for (i = idxM05Start; i < num; i++)
        {
            tmp = fabs(pdata1[i] * HmiOscServo::smpl_calc_tapvel_coef(1, client)*60000.0);
            if (HNC_DoubleCompare(tmp, 0) <= 0)
            {
                idxM05End = i;
                break;
            }
        }

        // 	if (idxM05End == 0)
        // 	{
        // 		idxM05End = num - 1;
        // 	}

        upTime = idxM03End - idxM03Start;
        downTime = idxM05End - idxM05Start;
        delayTime = idxM03Delay - idxM03Start;

        if (upTime < 0)
        {
            upTime = 0;
        }
        else
        {
            upTime = upTime * period / 1000.0;
        }

        if (downTime < 0)
        {
            downTime = 0;
        }
        else
        {
            downTime = downTime * period / 1000.0;
        }

        if (delayTime < 0)
        {
            delayTime = 0;
        }
        else
        {
            delayTime = delayTime * period / 1000.0;
        }

        // 数据放入记录报告
        upCof = upTime * 1000 * 30 / (maxSpd * spdlRate / 100);
        downCof = downTime * 1000 * 30 / (maxSpd * spdlRate / 100);
        HmiOscServo::OscservoReportRecord(upTime, downTime);

        ui->labelUpTime->setText(QString("%1").arg(QString::number(upTime, 'f',3)));
        ui->labelDownTime->setText(QString("%1").arg(QString::number(downTime, 'f',3)));
        ui->labelUpCof->setText(QString("%1").arg(QString::number(upCof, 'f',3)));
        ui->labelDownCof->setText(QString("%1").arg(QString::number(downCof, 'f',3)));
        ui->labelDelayTime->setText(QString("%1").arg(QString::number(delayTime, 'f',3)));
    }
    else
    {
        ui->labelUpTime->setText(QString("%1").arg(QString::number(upTime, 'f',3)));
        ui->labelDownTime->setText(QString("%1").arg(QString::number(downTime, 'f',3)));
        ui->labelUpCof->setText(QString("%1").arg(QString::number(upCof, 'f',3)));
        ui->labelDownCof->setText(QString("%1").arg(QString::number(downCof, 'f',3)));
        ui->labelDelayTime->setText(QString("%1").arg(QString::number(delayTime, 'f',3)));
    }
}

QStringList OscServoSpindle::GetParmList()
{
    QStringList list;
    list.clear();

    Bit32 totalNo = 0;
    totalNo = HmiOscServo::ServoParmGetCount(HmiOscServo::OSC_SERVO_SPINDLE);

    for(Bit32 i = 0; i < totalNo; i++)
    {
        list.append(QString::number(HmiOscServo::ServoParRow2Id(i)));
    }
    return list;
}

void OscServoSpindle::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::SETFOCUS)
    {
        bool ret = spdlOscList->SetTableFocus();
        if(messageValue == "CLEARFOCUS" || ret == false)
        {
            spdlOscList->ClearTableFocus();
        }
    }
    else if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        QStringList strList;
        strList.clear();
        if(messageValue == "INIT") // 初始化，清除上次在该界面记住的当前行
        {
            strList = GetParmList();
            spdlOscList->RefresWidget(strList);
        }

        spdlOscList->LoadWidget();

        this->LoadInfo(false);
        this->SetColorStyle();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if (messageid == MsgData::REFRESH)
    {
        this->Refresh();
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_OSCSERVOSTART")
        {
            m_bStartFlag = true;
            this->Reset(); // 开始采样时才清除上一次的图形
        }
        else if (messageValue == "MSG_OSCSERVOSTOP")
        {
            this->LoadInfo(m_bStartFlag);       // 只有开始采样后再停止采样才能更新信息
            m_bStartFlag = false;
        }
        else if (messageValue == "MSG_OSCSERVOAPPLY")
        {
            if (spdlOscList->RightCheck(MAC_RIGHTS) < 0)
            {
                return;
            }

            this->msgBox->setText(TR("是否确认载入推荐参数值？"));
            if(this->msgBox->exec() == QMessageBox::Ok)
            {
                HmiOscServo::SpindleApply();
                spdlOscList->LoadWidget();
                HmiOscServo::SetSaveFlag(2);
            }
        }
        else if(messageValue == "MSG_OSCSERVOSAVE")
        {
            HmiOscServo::ParmSave();
            spdlOscList->LoadWidget();
        }
        else if(messageValue == "OSCSERVOCOLOR")
        {
            this->SetColorStyle();
        }
    }
}

void OscServoSpindle::Reset()
{ // 清空图形
    pOscSpdl->ClearPoint();
    this->lastEndPos = 0;
}

void OscServoSpindle::Refresh()
{
    QVector<double> x;
    QVector<double> y0;

    Bit32 i = 0;
    Bit32 stPos = 0;
    Bit32 edPos= 0;
    Bit64 *ch1_addr = NULL;

    if (oscproc_get_stat() != OSC_PROC_START)
    {
        this->lastEndPos = 0; // 停止后需要置零
        return;
    }

    x.clear();
    y0.clear();

    stPos = this->lastEndPos;
    edPos = oscproc_get_pos();
    this->lastEndPos = edPos;

    ch1_addr = oscproc_get_smpldata(1);
    if (NULL == ch1_addr)
    {
        return;
    }

    for (i = stPos+1; i < edPos; ++i)
    {
        y0.append(ch1_addr[i] * HmiOscServo::smpl_calc_tapvel_coef(1, HmiOscServo::oscservo_get_sampl_client())*60000.0);
        x.append(i * oscproc_get_smpl_period());
    }

    pOscSpdl->LineZeroAddPoint(x, y0);
    pOscSpdl->WaveReplot();
}

void OscServoSpindle::SetColorStyle()
{
    // 默认黑色风格
    QColor bk(0,0,0); // 背景
    QColor gd(0,0,0); // 网格
    QColor ft(0,0,0); // 字体颜色
    QColor c1(0,0,0); // 曲线1
    QColor c2(0,0,0); // 曲线2
    QColor c3(0,0,0); // 曲线3
    QColor c4(0,0,0); // 曲线4

    HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

    QPalette palette;
    palette.setColor(QPalette::Background, bk);
    ui->frame->setAutoFillBackground(true);
    ui->frame->setPalette(palette);

    pOscSpdl->SetColor(bk, gd, ft, c1, c2, c3, c4);
}
