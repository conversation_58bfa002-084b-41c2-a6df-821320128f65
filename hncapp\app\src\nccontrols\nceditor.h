﻿/*!
 * @file nceditor.h
 * @brief 编辑器控件
 * @note 用于程序编辑界面
 *
 * @version V1.00
 * @date 2017/11/22
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */
#ifndef NCEDITOR_H
#define NCEDITOR_H

#include <QPlainTextEdit>
#include <QSyntaxHighlighter>
#include <QTextEdit>

#include "hncdatatype.h"
#include "hmipaintercolor.h"

#include "dlginfo.h"
#include "dlgmsgbox.h"

#include "ncbusytask.h"

QT_BEGIN_NAMESPACE
class NcEditor;
QT_END_NAMESPACE

typedef struct _EditUndoData
{
    Bit32 command;
    Bit32 row;
    QString preCmdText;
    QString endCmdText;
}EditUndoData;


typedef struct _EditUndoInfo
{
    QList<EditUndoData> editUndoData;

    Bit32 preScrStartLine;
    Bit32 preCursorRow;
    Bit32 preCursorCol;

    Bit32 endScrStartLine;
    Bit32 endCursorRow;
    Bit32 endCursorCol;
}EditUndoInfo;


typedef enum _EditorType
{
    EDIT_TYPE = 0,	// 编辑
    VIEW_TYPE = 1	// 浏览
} EditorType;

// 编辑器处理状态
typedef enum _EditorInfo
{
    EDIT_PASS = -1, // 没处理
    EDIT_OK = 0,	// 成功
    EDIT_ERR,		// 失败
    EDIT_INSERT_LINE_OVFL,	// 插入行满
    EDIT_LINE_OVFL,			// 行缓冲满
    EDIT_NO_CLIP,			// 没有剪切板
    EDIT_CLIP_EMPTY,		// 剪切板中没数据
    EDIT_CLIP_OVFL,			// 剪切板缓冲满
    EDIT_BLOCK_UNDEFINED,	// 未定义块
    EDIT_DELETE_LINE_OVFL,	// 删除行满
    EDIT_MEMERY_OVFL,	// 内存不足
    EDIT_READONLY,	// 只读文件
    EDIT_LOADED,	// 已加载文件
    EDIT_PROTECT,	// 数据保护文件
    EDIT_PROGHEAD,
    EDIT_RUNNING_PROTECT,
    EDIT_GIVEN_ROW_PROTECT,  //任意行模式中
    EDIT_HOLD_ROW,
    EDIT_HOLD_CYC
} EditorInfo;

enum LINE_SHOW_TYPE // 行号显示类型（关联NC参数24 PAR_NCU_GLNO_DISP）
{
    NO_SHOW_LINE = 0,       // 不显示行号
    ONLY_EDIT_SHOW_LINE,    // 只在程序编辑界面显示行号
    ONLY_VIEW_SHOW_LINE,    // 只在程序预览界面显示行号
    ALL_SHOW_LINE,          // 显示行号
};

typedef struct _ClipBlock
{
    Bit32 row;
    Bit32 col;
}ClipBlock;

typedef struct _SEdbufMan_
{
    Bit32 editRowMax;	// 编辑窗口能显示的最大行数
    Bit32 scrStartLine;	// 屏幕起始行在文件中的行号
    Bit32 showIndex;	// 是否显示行号（是否显示参考LINE_SHOW_TYPE）
    ClipBlock srcHeadBlock, srcEndBlock; // 块首和块尾(自由定义的块，块首和块尾顺序没有前后的限制)
    ClipBlock headBlock, endBlock; // 块首和块尾(srcHeadBlock, srcEndBlock按顺序调整后的块，坐标小的为块首，坐标大的为块尾)
    Bit32 blockFlag; // 标志是否定义了块
    Bit32 definedBlock; // 是否定义块，0：没定义；0x1：定义了块首；0x2：定义了块尾
    Bit32 highLightFlag;	// 高亮选中行标记
    Bit32 blockPasteOffset; // 块粘贴时需要偏移的col
    Bit32 m_nCursorRow; // 当前光标所在行
    Bit32 m_nCursorCol; // 当前光标所在列
    Bit32 m_nEditorType; // 编辑器类型（编辑/预览）
}SEdbufMan;

class NcEditorReplaceTask : public NcBusyTask
{
public:
    NcEditorReplaceTask(NcEditor *wg);

    QStringList GetErrMsg();
    void SetReplaceStr(QString strFind, QString strReplace,
                       Bit32 stRow = 0, Bit32 stCol = 0, bool replaceSig = false);
    Bit32 GetReplacedRow();
    Bit32 GetReplacedEndCol();
    Bit32 GetReplacedCount();
    Bit32 GetReplacedEndRow();
    bool IsSigReplace();
    void SetChkWholeWord(bool chk);
    void SetRegExp(QRegExp reg);
    bool IsFindSuccessed();
private:
    QString m_sStrFind;
    QString m_sStrReplace;

    Bit32 m_nStRow;
    Bit32 m_nStCol;
    bool m_bReplaceSig;
    bool m_bFindSuccessed;
    bool m_bReplaceFlag;
    bool m_bStopTask;
    bool m_bChkWholeWord;

    Bit32 m_nRowIdx;
    Bit32 m_nEndRowIdx;
    Bit32 m_nEndColIdx;

    Bit32 m_nReplacedLineCount;
    NcEditor *m_pCurEdit;
    QStringList m_sErrorStr;

    QRegExp m_regExp;

    void TaskInit();
    void TaskDo();
    bool TaskIsFinished();
    QVariant TaskProgress();
    void FindOneLine();
    void TaskPreEnd();
};

class NcEditor : public QPlainTextEdit
{
    Q_OBJECT

public:
    NcEditor(QWidget *parent = 0);
    ~NcEditor();
    Bit32 GetCurRow();
    Bit32 EditInit(EditorType type, Bit32 show_index, QFont font, Bit32 insertSpaceClose, bool recordCursor = false); // 编辑器初始化
    void LineNoAreaPaint(QPaintEvent *event);
    int LineNoAreaWidth(); // 获取行号显示区宽度
    Bit32 GetNoEditFlg() const; // 获取禁止编辑状态
    void SetNoEditFlg(Bit32 value); // 设置禁止编辑状态 0:允许编辑 1:只读 2:可写且已加载
    void ClipSetBlock(Bit32 aFlags); // 定义块首或块尾
    Bit32 ClipCopy(); // 块复制
    Bit32 ClipDelete(); // 块删除
    Bit32 ClipPaste(); // 块粘贴
    Bit32 ClipCut(); // 块剪切
    Bit32 EditHome(); // 文件首
    Bit32 EditEnd(); // 文件尾
    QRegExp GetRegExp(QString find, bool chkWhole = false);
    Bit32 EditFind(QString find, Bit32 dir = 0, bool chkWhole = false, bool cycle = false); // 查找
    Bit32 FindStrInLine(QString lineStr, QRegExp rx, Bit32 dir = 0, Bit32 offset = 0);
    void EditDlgFindAndRepl(QString find, QString repl, bool chkWhole = false);
    void EditDlgFindAndReplAll(QString find, QString repl, bool chkWhole = false);
    Bit32 EditDelLine(); // 行删除
    Bit32 EditFindNewLine(void); // 定位行
    Bit32 EditGotoNewLine(Bit32 scrStartRow, Bit32 lineToGo); // 行跳转
    Bit32 EditCurLineReplace(QString newStr); // 用指定字符串替换当前行内容
    void EditPerror(Bit32 err); // 显示错误信息
    void EditGetLineInfo(Bit32 &startLine, Bit32 &rowMax); // 获取当前屏幕首行的行号和屏幕行数
	void EditSetStartLine(Bit32 startLine);     //设置当前屏幕首行的行号
    void EditSetHighlightFlag(Bit32 flag); // 设置选中行高亮开关
    Bit32 EditGetPageRowNum(void);  // 获取编辑页的行数
    void EditRedraw(); // 更新当前页面的内容
    void mouseMoveEvent(QMouseEvent* event);
    Bit32 DelAllRow();
    Bit32 EditGetLine(Bit32 row, QString &text, bool insSpaceEnabled = true);
    Bit32 InsertGCode(QString gCode);
    void CursorToPos(int toLine, int toCol = 0); // 设置光标到指定行指定列
    void UndoRecordStart();
    void UndoRecordEnd();
    Bit32 EditUndo();
    Bit32 EditRedo();
    void SetHeadEditFlag(bool edit);
    void SetEditState(Bit32 flag);
    void SetHoldRestartRow(Bit32 row);
    Bit32 GetHoldRestartRow();
    void SetHoldDcdRow(Bit32 row);
    Bit32 GetHoldDcdRow();

    virtual Bit32 GetRow(Bit32 row, QString &text); // 得到指定行文本内容
    virtual Bit32 SetRow(Bit32 row, QString text); // 设置指定行文本内容
    virtual Bit32 GetRowNum(void); // 得到总行数
    virtual Bit32 InsertRows(Bit32 row); // 插入行函数
    virtual Bit32 DeleteRows(Bit32 row, Bit32 rn); // 删除行函数

    Bit32 EditCurLineAddNewLine(QString newStr, bool addAfter = false, bool force = false);
    Bit32 GetCurStartLine();
    Bit32 EditDelLines(Bit32 row, Bit32 count);
    Bit32 GetEnableStat(); // 获取可编辑状态
    Bit32 IsInputEnable(); // 是否可输入字符
    bool eventFilter(QObject *target, QEvent *event); // 按键响应
    void SetSelRow(Bit32 row);

    Bit32 EditGetHighlightFlag();

    void SetMcpKeyValid(bool valid);
    void ResetCursorPos();
    void ReplaceRefresh(Bit32 ch);
    static void GetDefEditParam(Bit32 *showNo, Bit32 *insertSpaceClose, Bit32 *smallCharacter = NULL);
public slots:
    void StopTask();
private slots:
    void HighlightCurrentLine();
    void UpdateLineNoArea(const QRect &, int);
    void CursorPosChange();
    void SlotCurTextChanged();

    Bit32 ReplaceNormalFin();
    Bit32 ReplaceForceEnd();
    void BlockDynamicHighlight();
private:
    QWidget *m_pLineNoArea; // 行号显示区
    SEdbufMan m_editorMan;
    QString m_strFind; // 查找对象
    QString m_strReplace; //用来替换的字符串
    Bit32 m_nNoEditFlg; // 禁止编辑标记 0:允许编辑 1:只读文件 2:可写且已加载文件
    Bit32 m_nInsertSpaceClose; // 分词功能开关；0：开；1：关
    QFont m_font; // 编辑区显示的字体

    QVector<EditUndoInfo> m_undoStack;
    EditUndoInfo m_recordUndoInfo;
    Bit32 m_nUndoCurStep;
    bool m_bUndoRecord;
    bool m_bHedEditEnable;
    Bit32 m_nSelRow;
    Bit32 m_nEditState;
    bool m_bMcpValid;

    Bit32 m_holdRestartRow;
    Bit32 m_nHoldDcdRow;
    bool m_bIsInputMethod;
    NcEditorReplaceTask *m_pTask;
    DlgInfo *pDlgTask;
    DlgMsgBox *pDlgMsg;

    void resizeEvent(QResizeEvent *event);
    void UpdateLineNoAreaWidth(); // 更新行号显示宽度
    void EditSaveFocusLine(void); // 保存当前焦点行
    Bit32 XorSelectBlock(); // 选中块着色
    void CancelBlock(); // 块清除
    bool IsInsertSpaceEnable();
    Bit32 GetRowStringLenth(Bit32 row);
    Bit32 FindEditString(Bit32 startRow, Bit32 startCol, Bit32 endRow, Bit32 endCol, bool chkWhole = false); // 从指定行开始查找指定字符串
    Bit32 FindString(QString find, Bit32 dir = 0, bool chkWhole = false, bool cycle = false);
    void ReplaceString(bool chkWholeWord = false); // 替换字符串
    void ReplaceAll(bool chkWholeWord = false); // 全部替换
    bool EditOnMsg(QKeyEvent *keyEvent); // 按键响应
    void SetScrStartLine(Bit32 startLine);
    void ResizeFont();

    void UndoRecordInit();
    void UndoRecordMemClear();
    void UndoRecord2List();

    Bit32 EditSetLine(Bit32 row, QString text); // 保存行文本
    Bit32 EditDelLine(Bit32 row);
    Bit32 EditInsLine(Bit32 row);
    bool SelRowBackgroudColor(QTextEdit::ExtraSelection &selection);

    void EditToLine(Bit32 lineToGo, Bit32 col);
    void ReplaceFin();

    bool IsShowLineNoArea();

    friend class NcEditorReplaceTask;
};

// 显示行号区域
class LineNoArea : public QWidget
{
public:
    LineNoArea(NcEditor *editor) : QWidget(editor) {
        codeEditor = editor;
    }

    QSize sizeHint() const {
        return QSize(codeEditor->LineNoAreaWidth(), 0);
    }

protected:
    void paintEvent(QPaintEvent *event) {
        codeEditor->LineNoAreaPaint(event);
    }

private:
    NcEditor *codeEditor;
};

class NcHighlighter : public QSyntaxHighlighter
{

    Q_OBJECT
private:
    QTextCharFormat MyClassFormatExplan;    // 注释颜色
    QTextCharFormat MyClassFormatNum;       // 数字颜色
    QTextCharFormat MyClassFormatZ;         //  Z颜色
    QTextCharFormat MyClassFormatExt;       //  其他

public:
    NcHighlighter(QTextDocument *parent = 0): QSyntaxHighlighter(parent)
    {
        // 注释颜色
        MyClassFormatExplan.setForeground(HmiPainterColor::GetInstance()->GetProgEditHighLightsColor());

        // 数字颜色
        MyClassFormatNum.setForeground(HmiPainterColor::GetInstance()->GetProgEditTextNumColor());

        // Z颜色
        MyClassFormatZ.setForeground(HmiPainterColor::GetInstance()->GetProgEditTextZColor());

        // 字母颜色
        MyClassFormatExt.setForeground(HmiPainterColor::GetInstance()->GetProgEditTextAlphaColor());
    }

    void highlightBlock(const QString &text);
};
#endif // NCEDITOR_H
