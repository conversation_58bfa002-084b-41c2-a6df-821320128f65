#ifndef LADDERDATA_H
#define LADDERDATA_H

#include <QObject>
#include <QVector>
#include <QMap>
#include <QString>
#include <QColor>
#include <QMutex>

#include "lad_def_compat.h"

class LadderData : public QObject
{
    Q_OBJECT

public:
    explicit LadderData(QObject *parent = nullptr);
    ~LadderData();

    // 数据加载和保存
    bool loadFromFile(const QString &filename);
    bool saveToFile(const QString &filename);
    void clearData();
    
    // 基本数据访问
    int getTotalRows() const { return m_totalRows; }
    int getCurrentSubProgram() const { return m_currentSub; }
    void setCurrentSubProgram(int sub);
    
    // 获取梯形图行数据
    bool getLadderRow(int row, SLadRow &ladRow);
    bool setLadderRow(int row, const SLadRow &ladRow);
    
    // 获取元件数据
    bool getLadderCell(int row, int col, SLadCell &cell);
    bool setLadderCell(int row, int col, const SLadCell &cell);
    
    // 元件状态获取
    CellState getCellState(int row, int col);
    QColor getCellColor(int row, int col);
    bool isCellActive(int row, int col);
    
    // 寄存器值获取
    quint32 getRegisterValue(const SLadReg &reg);
    bool setRegisterValue(const SLadReg &reg, quint32 value);
    
    // 强制状态管理
    bool isCellForced(int row, int col);
    void setCellForceState(int row, int col, CellState state);
    void clearCellForceState(int row, int col);
    
    // 符号表管理
    QString getSymbolName(const SLadReg &reg);
    QString getSymbolComment(const SLadReg &reg);
    void addSymbol(const SLadReg &reg, const QString &name, const QString &comment);
    
    // 查找功能
    struct FindResult {
        int row;
        int col;
        QString description;
    };
    QVector<FindResult> findByRegister(const QString &regText);
    QVector<FindResult> findByInstruction(uBit16 cmdID);
    QVector<FindResult> findBySymbol(const QString &symbol);
    
    // 调试模式相关
    void setDebugMode(bool enabled) { m_debugMode = enabled; }
    bool isDebugMode() const { return m_debugMode; }
    
    // 编辑模式相关
    void setEditMode(bool enabled) { m_editMode = enabled; }
    bool isEditMode() const { return m_editMode; }
    
    // 数据变化检测
    bool hasDataChanged(int row, int col);
    void markDataChanged(int row, int col);
    void clearChangeFlags();

signals:
    void dataChanged(int row, int col);
    void rowDataChanged(int row);
    void subProgramChanged(int sub);

private:
    // 内部数据结构
    struct CellData {
        SLadCell cell;
        CellState state;
        bool forced;
        CellState forceState;
        quint32 lastValue;
        bool changed;
    };
    
    struct RowData {
        QVector<CellData> cells;
        bool hasData;
    };
    
    struct SymbolData {
        SLadReg reg;
        QString name;
        QString comment;
    };
    
    // 初始化函数
    void initializeData();
    void initializeColors();
    
    // 数据解析函数
    bool parseRegisterText(const QString &text, SLadReg &reg);
    QString formatRegisterText(const SLadReg &reg);
    
    // 状态计算函数
    CellState calculateCellState(const SLadCell &cell);
    QColor getCellStateColor(CellState state, bool selected = false);
    
    // 模拟数据生成（用于测试）
    void generateTestData();
    void simulateRegisterValues();

private:
    // 数据存储
    QVector<RowData> m_ladderRows;      // 梯形图行数据
    QMap<QString, SymbolData> m_symbols; // 符号表
    QMap<QString, quint32> m_registers;  // 寄存器值
    
    // 状态信息
    int m_totalRows;                    // 总行数
    int m_currentSub;                   // 当前子程序
    bool m_debugMode;                   // 调试模式
    bool m_editMode;                    // 编辑模式
    
    // 颜色配置
    QMap<LadderColors, QColor> m_colors;
    
    // 线程安全
    mutable QMutex m_dataMutex;
    
    // 变化检测
    QVector<QVector<bool>> m_changeFlags;
};

#endif // LADDERDATA_H
