﻿#include <QPainter>

#include "nclightwidget.h"

static QPixmap *m_green = NULL;
static QPixmap *m_red = NULL;
static QPixmap *m_gray = NULL;
static QPixmap *m_transparent = NULL;
NcLightWidget::NcLightWidget(QWidget *parent) :
    QLabel(parent),
    m_lightType(-1)
{
    if (m_gray == NULL)
    {
        CreatePixmap(Qt::gray, &m_gray);
    }
    if (m_green == NULL)
    {
        CreatePixmap(Qt::green, &m_green);
    }
    if (m_red == NULL)
    {
        CreatePixmap(Qt::red, &m_red);
    }
    if (m_transparent == NULL)
    {
        CreateRingPixmap(Qt::gray, &m_transparent);
    }

    SetColor(LIGHT_DEF);
}

void NcLightWidget::SetColor(NcLightWidget::NCLIGHT_TYPE t)
{
    m_lightType = t;
    switch (t) {
    case LIGHT_GREEN:
        setPixmap(*m_green);
        break;
    case LIGHT_RED:
        setPixmap(*m_red);
        break;
    case LIGHT_GRAY:
        setPixmap(*m_gray);
        break;
    case LIGHT_DEF:
        setPixmap(*m_transparent);
        break;
    default:
        break;
    }
}

void NcLightWidget::CreatePixmap(QColor color, QPixmap **pixmap)
{
    *pixmap = new QPixmap(20, 20);
    (*pixmap)->fill(Qt::transparent);
    QPainter painter(*pixmap);
    painter.setPen(Qt::NoPen);
    painter.setBrush(color);
    painter.drawEllipse((*pixmap)->rect().center(), 9, 9);
}

void NcLightWidget::CreateRingPixmap(QColor color, QPixmap **pixmap)
{
    *pixmap = new QPixmap(20, 20);
    (*pixmap)->fill(Qt::transparent);
    QPainter painter(*pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setPen(QPen(color, 3));
    painter.setBrush(Qt::NoBrush);
    painter.drawEllipse((*pixmap)->rect().center(), 7, 7);
}
