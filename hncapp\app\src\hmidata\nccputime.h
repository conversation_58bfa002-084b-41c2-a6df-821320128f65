﻿/*!
 * @file nccputime.h
 * @brief cpu资源监控数据类
 * @note
 *
 * @version V1.00
 * @date 2023/6/2
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */

#ifndef NCCPUTIME_H
#define NCCPUTIME_H
#include "hncdatatype.h"
#include <QDateTime>
#include <QMap>

#define RECORD_MAX_MINUTES     (3)     // 最大记录时长


class QString;

class NcCpuTime
{
public:
    typedef struct _STRecordData_
    {
        QString name;
        QString nameCN;
        int color_r;
        int color_g;
        int color_b;
        Bit32 dataCount;
        QVector<Bit32> *x;
        QVector<fBit64> *y;
        fBit64 maxValue;
        fBit64 minValue;
        fBit64 avgValue;

        _STRecordData_(QString nameStr = "", QString nameCNStr = "", int r = 0, int g = 0, int b = 0)
        {
            name = nameStr;
            nameCN = nameCNStr;
            color_r = r;
            color_g = g;
            color_b = b;
            dataCount = 0;
            maxValue = 0.0;
            minValue = 0.0;
            avgValue = 0.0;
            x = new QVector<Bit32>();
            y = new QVector<fBit64>();
        }

        void ClearData()
        {
            x->clear();
            y->clear();
            maxValue = 0.0;
            minValue = 0.0;
            avgValue = 0.0;
            dataCount = 0;
        }

        // valid：数据是否有效（添加数据无效时valid = false。 1、只记录数据 2、不进行图形绘制 3、不进行数据运算）
        void AddData(Bit32 xValue, fBit64 yValue, bool valid = true)
        {
            if (dataCount >= x->count())
            {
                return;
            }
            if (dataCount == 0)
            {
                maxValue = yValue;
                minValue = yValue;
            }
            x->replace(dataCount, xValue);
            y->replace(dataCount, yValue);
            if (yValue > maxValue && valid == true)
            {
                maxValue = yValue;
            }
            else if (yValue < minValue && valid == true)
            {
                minValue = yValue;
            }
            dataCount++;
        }
    }STRecordData;

    typedef enum _RecordType_
    {
        TYPE_NULL = -1,
        TYPE_HMI = 0,   // HMI进程
        TYPE_BUS,       // 总线读写
        TYPE_AXIS,      // 轴控制
        TYPE_PLC1,      // PLC1扫描
        TYPE_PLC2,      // PLC2扫描
        TYPE_CHAN,      // 通道控制
        TYPE_SMX,       // 从轴控制
        TYPE_PPI,       // 解释器线程
        TYPE_NCUALL,    // 插补器线程总和

        TYPE_TOTAL
    }RecordType;

public:
    NcCpuTime();

    void SetPid(Bit32 id);  // 设置监控进程id
    Bit16 SetTotalSeconds(Bit32 total);
    Bit16 GetTotalSeconds();
    Bit16 RefreshPrecent(); // 刷新cpu占用率
    fBit32 GetCpuUsage();   // 获取cpu占用率
    bool IsRecord();
    bool IsLoaded();
    bool IsFinished();
    void ClrLoaded();       // 清除载入标记
    QString GetDataName(RecordType type);
    fBit64 GetDataMax(RecordType type);
    fBit64 GetDataMin(RecordType type);
    fBit64 GetDataAvg(RecordType type);
    Bit32 GetDataColor(RecordType type, int &r, int &g, int &b);

    Bit16 StartRecord();    // 开启记录
    Bit16 StopRecord();     // 停止记录
    void ResetRecord();     // 清空记录，预申请内存
    void RefreshRecord();   // 更新记录
    Bit32 GetRecord(RecordType type, QVector<Bit32> &x, QVector<fBit64> &y, Bit32 &count);  // type:需要获取的记录类型
    Bit32 ExportToUDisk();  // 导出至U盘  path: g/ncudata.txt
    Bit32 LoadFormUDisk();  // 从U盘载入  path: g/ncudata.txt

private:
    // cpu状态数据
    Bit32 m_nPreUTime;       // 进程用户态使用时间
    Bit32 m_nPreSTime;       // 进程内核态使用时间
    fBit32 m_fCpuPrecent;    // 内存占用率
    Bit32 m_nPid;            // 监视进程号
    fBit32 m_fPreCpuTime;     // CPU总时间
    // 监控数据
    Bit32 m_nRecordTime;     // 开始监视时间点
    bool m_bRecordFlag;     // 监视开启标记
    Bit32 m_nLastSeconds;    // 上次记录CPU占用率时间 单位：s
    Bit32 m_nTotalSeconds;   // 记录最大时长 单位：s
    QDateTime m_timeCpuRecord;      // 监视开始时间
    QMap<RecordType, STRecordData> m_mapData;
    QList<Bit32> m_ncuMacroList;
    Bit16 m_nCPULevel;      // CPU负载级别   0:负载<=30%   1:30%<负载<=45%  2:45%<负载<=60%  3:60%<负载
    bool m_bLoaded;         // 加载完成
    bool m_finishedFlag;
    Bit32 m_SamplClient;        // 本机网络标志号
    Bit32 *m_tmpData;

    void InitRecord();          // 初始化记录
    void RefreshCpuRecord();    // 更新HMI进程cpu占用率记录
    void RefreshNcuRecord();    // 更新内核线程耗时记录
    void RefreshRecordAvg();    // 计算记录数据平均值

    void SmplInit();
    void SmplStart();
    void SmplStop();
};

#endif // NCCPUTIME_H
