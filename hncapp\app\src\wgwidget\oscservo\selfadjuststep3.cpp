﻿#include "hncaxis.h"
#include "hncaxisdef.h"
#include "hncchan.h"
#include "hncchandef.h"
#include "hncdatadef.h"
#include "hncparaman.h"
#include "hncparamandef.h"
#include "hncregdef.h"
#include "hncreg.h"
#include "hmiselfadjusting.h"
#include "hmimenumanage.h"
#include "msgchan.h"
#include "devdict.h"
#include "selfadjuststep3.h"
#include "ui_selfadjuststep3.h"

SelfAdjustStep3::SelfAdjustStep3(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::SelfAdjustStep3)
{
    ui->setupUi(this);
    this->firstFlag = false;
    this->curTime = QDateTime::currentDateTime();
    this->stopFlag = false;
    this->curStatus = 0;

    RefreshMotorPos();

    //this->picPath = QString("adjust/");
    //this->pMovie = new QMovie(TransPicName(picPath + "motor_rotate.gif"));

    this->installEventFilter(this);
}

SelfAdjustStep3::~SelfAdjustStep3()
{
    delete ui;
}

void SelfAdjustStep3::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if (messageid == MsgData::SETFOCUS)
    {

    }
    else if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        this->ResetAdjustStep();
        //this->pMovie->start();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if (messageid == MsgData::REFRESH)
    {
        RefreshMotorPos();
        RefreshAdjustStep();
        SetButtonState();
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_ADJUST")
        {
            this->curStatus = 0;
            this->ClearAdjustStep();
            this->InsertAjustStep(false, TR("整定未开始"));
            this->stopFlag = false;
            this->InsertAjustStep(true, TR("开始整定"));
            HmiSelfAdjusting::FillParmList(0);  // 更新原始值
        }
    }
}

bool SelfAdjustStep3::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        //QKeyEvent *keyEv = static_cast<QKeyEvent *>(event);
    }
    else if(event->type() == QEvent::Paint && !firstFlag) // Paint事件在ReSize事件之后响应，用于图片第一次重绘
    {
        //this->pMovie->setScaledSize(ui->picLb->size());
        //ui->picLb->setMovie(this->pMovie);

        this->firstFlag = true;
        return true;
    }

    return QObject::eventFilter(target, event);
}

void SelfAdjustStep3::RefreshMotorPos()
{
    fBit64 posPositive = 0;
    fBit64 posNegative = 0;
    fBit64 posCurrent = 0;
    Bit32 axisNo = HmiSelfAdjusting::GetCurAdjustAxis();
    Bit32 axisType = 0;

    HNC_AxisGetValue(HNC_AXIS_TYPE, axisNo, &axisType);
    HNC_ParamanGetFloat(PARAMAN_FILE_AXIS, axisNo, PAR_AX_PLMT, &posPositive);
    HNC_ParamanGetFloat(PARAMAN_FILE_AXIS, axisNo, PAR_AX_NLMT, &posNegative);
    HNC_AxisGetValue(HNC_AXIS_ACT_POS_EX, axisNo, &posCurrent);
    if (axisType == 1)  // 直线轴
    {
        ui->label_3->setText(TR("负限位： ") + QString::number(posNegative) + "mm");
        ui->label_4->setText(TR("正限位：") + QString::number(posPositive) + "mm");
        ui->label_5->setText(TR("当前位置： ") + QString::number(posCurrent, 'f', GetPosPrec()) + "mm");
    }
    else if (axisType == 2 || axisType == 3)    // 摆动轴或旋转轴
    {
        ui->label_3->setText(TR("负限位： ") + QString::number(posNegative) + "deg");
        ui->label_4->setText(TR("正限位：") + QString::number(posPositive) + "deg");
        ui->label_5->setText(TR("当前位置： ") + QString::number(posCurrent, 'f', GetPosPrec()) + "deg");
    }
    ui->horizontalSlider->setMinimum((Bit32)posNegative);
    ui->horizontalSlider->setMaximum((Bit32)posPositive);
    ui->horizontalSlider->setValue((Bit32)posCurrent);
}

void SelfAdjustStep3::RefreshAdjustStep()
{
    if (!hdict_is_read_finished())
    {
        return;
    }

    Bit32 axisNo = HmiSelfAdjusting::GetCurAdjustAxis();
    Bit32 axisDevType = 0;

    HNC_AxisGetValue(HNC_AXIS_DEV_TYPE, axisNo, &axisDevType);

    if (axisDevType == DEV_NCUC_AXIS)
    {
        RefreshAdjustStepNCUC();
    }
    else
    {
        RefreshAdjustStepECAT();
    }
}

void SelfAdjustStep3::RefreshAdjustStepECAT()
{
    if (this->stopFlag)
    {
        return;
    }
    Bit32 axisNo = HmiSelfAdjusting::GetCurAdjustAxis();
    Bit32 servoParmId = 147;
    Bit32 parmNo = AXIS_PARAM_ID_BASE + axisNo * AXIS_PARAM_ID_NUM + SERVO_PARM_START_IDX + servoParmId;
    SDataProperty prop;
    Bit32 servoStatus = 0;
    Bit32 chRegBase = 0;
    // 同步伺服参数-自整定功能状态字
    HNC_ParamanServoReadPara(axisNo, servoParmId);

    Bit32 isEStop = 0;
    Bit32 ch = ActiveChan();

    HNC_ChannelGetValue(HNC_CHAN_IS_ESTOP, ch, 0, &isEStop);
    Bit32 ret = HmiSelfAdjusting::SelfAdjustGetStatus();    // 获取整定状态
    if (ret == HmiSelfAdjusting::SELF_ADJUST_STAT_ING && isEStop == 1) // 整定过程中外部强制停止【急停】
    {
        // 解除自整定过程中的轴锁住状态
        HNC_RegGetFGBase(REG_FG_CHAN_BASE, &chRegBase);
        HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 19, 6);    //#define CH_STATE_SELF_ADJUST 0X0040 //伺服自整定状态

        HmiSelfAdjusting::SelfAdjustSetCtrl(HmiSelfAdjusting::SELF_ADJUST_CTRL_NULL);
        HmiSelfAdjusting::SelfAdjustSetStatus(HmiSelfAdjusting::SELF_ADJUST_STAT_NULL);
        this->stopFlag = true;
        this->InsertAjustStep(true, TR("自整定过程已被强制停止"));
        return;
    }
    // 自整定过程刷新处理
    if (ret == HmiSelfAdjusting::SELF_ADJUST_STAT_NULL)  // 整定未开始
    {
        return;
    }
    if (ret == HmiSelfAdjusting::SELF_ADJUST_STAT_DONE) // 整定完成
    {
        return;
    }

    // 同步后 获取参数值
    HNC_ParamanGetParaPropEx(parmNo, PARA_PROP_VALUE, &prop);
    servoStatus = prop.value.val_int;
    if (this->curStatus == servoStatus || (this->curStatus == 0 && servoStatus == 14))
    {
        return;
    }
    switch(servoStatus)
    {
    case 1: // 惯量辨识正在运行
        this->InsertAjustStep(true, TR("惯量辨识正在运行"));
        break;
    case ECAT_TRIGGER_CTRL: // 惯量辨识完成
        // 重新获取修改后的伺服参数
#ifndef _LINUX
    Sleep(50);
#else
    usleep(150*1000);
#endif
        HmiSelfAdjusting::FillParmList(1);
        HmiSelfAdjusting::SelfAdjustSetParmChangeFlag(1);
        HmiSelfAdjusting::SelfAdjustSaveAxisState(axisNo, 0);
        this->InsertAjustStep(true, TR("惯量辨识完成"));

        // 解除自整定过程中的轴锁住状态
        HNC_RegGetFGBase(REG_FG_CHAN_BASE, &chRegBase);
        HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 19, 6);    //#define CH_STATE_SELF_ADJUST 0X0040 //伺服自整定状态

        HmiSelfAdjusting::SelfAdjustSetCtrl(HmiSelfAdjusting::SELF_ADJUST_CTRL_NULL); // 先清除伺服整定控制字
        HmiSelfAdjusting::SelfAdjustClrServoStatus();   // 将状态清零
        HmiSelfAdjusting::SelfAdjustSetStatus(HmiSelfAdjusting::SELF_ADJUST_STAT_DONE);
        HmiSelfAdjusting::SelfAdjustSaveAxisState(axisNo, 0);
        HmiSelfAdjusting::SelfAdjustUpdateCmdPos(axisNo);   // 通知PLC更新系统指令位置
        MsgChan::Instance().TranMsg(MsgData::RESET, "");    // 发送复位消息，清除跟踪误差过大报警

        break;
    default:
        break;
    }
    this->curStatus = servoStatus;
}

void SelfAdjustStep3::RefreshAdjustStepNCUC()
{
    if (this->stopFlag)
    {
        return;
    }
    Bit32 axisNo = HmiSelfAdjusting::GetCurAdjustAxis();
    Bit32 servoParmId = 123;//136 根据雷力新的通讯协议，状态字由136改为123（2019-5-24）;   // 自整定功能状态字
    Bit32 parmNo = AXIS_PARAM_ID_BASE + axisNo * AXIS_PARAM_ID_NUM + SERVO_PARM_START_IDX + servoParmId;
    SDataProperty prop;
    Bit32 servoStatus = 0;
    Bit32 chRegBase = 0;
    // 同步伺服参数-自整定功能状态字
    HNC_ParamanServoReadPara(axisNo, servoParmId);

    Bit32 isEStop = 0;
    Bit32 ch = ActiveChan();

    HNC_ChannelGetValue(HNC_CHAN_IS_ESTOP, ch, 0, &isEStop);
    Bit32 ret = HmiSelfAdjusting::SelfAdjustGetStatus();    // 获取整定状态
    if (ret == HmiSelfAdjusting::SELF_ADJUST_STAT_ING && isEStop == 1) // 整定过程中外部强制停止【急停】
    {
        // 解除自整定过程中的轴锁住状态
        HNC_RegGetFGBase(REG_FG_CHAN_BASE, &chRegBase);
        HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 19, 6);    //#define CH_STATE_SELF_ADJUST 0X0040 //伺服自整定状态

        HmiSelfAdjusting::SelfAdjustSetCtrl(HmiSelfAdjusting::SELF_ADJUST_CTRL_NULL);
        HmiSelfAdjusting::SelfAdjustSetStatus(HmiSelfAdjusting::SELF_ADJUST_STAT_NULL);
        this->stopFlag = true;
        this->InsertAjustStep(true, TR("自整定过程已被强制停止"));
        return;
    }
    // 自整定过程刷新处理
    if (ret == HmiSelfAdjusting::SELF_ADJUST_STAT_NULL)  // 整定未开始
    {
        return;
    }
    if (ret == HmiSelfAdjusting::SELF_ADJUST_STAT_DONE) // 整定完成
    {
        return;
    }

    // 同步后 获取参数值
    HNC_ParamanGetParaPropEx(parmNo, PARA_PROP_VALUE, &prop);
    servoStatus = prop.value.val_int;
    if (this->curStatus == servoStatus || (this->curStatus == 0 && (servoStatus == 14 || servoStatus == 3)))
    {
        return;
    }
    switch(servoStatus)
    {
    case 1: // 惯量识别运行启动
        this->InsertAjustStep(true, TR("惯量辨识启动"));
        break;
    case 2: // 惯量识别正在运行
        this->InsertAjustStep(true, TR("惯量辨识正在运行"));
        break;
    case 3: // 惯量识别完成
        this->InsertAjustStep(true, TR("惯量辨识完成"));
        // 同步伺服参数
//        for (Bit32 i = 0; i < TOTAL_PARM_ADJUST; i++)
//        {
//            Bit32 parmId = HmiSelfAdjusting::axisParmList[i] - SERVO_PARM_START_IDX;
//            HNC_ParamanServoReadPara(axisNo, parmId);
//        }
        // 同步伺服参数
        // 重新获取修改后的伺服参数
//        HmiSelfAdjusting::FillParmList(1);
//        HmiSelfAdjusting::SelfAdjustSetParmChangeFlag(1);

        if (HmiSelfAdjusting::SelfAdjustGetCtrl() == HmiSelfAdjusting::SELF_ADJUST_CTRL_INERTIA)
        {
            // 重新获取修改后的伺服参数
			HmiSelfAdjusting::FillParmList(1);
			HmiSelfAdjusting::SelfAdjustSetParmChangeFlag(1);

            // 解除自整定过程中的轴锁住状态
            HNC_RegGetFGBase(REG_FG_CHAN_BASE, &chRegBase);
            HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 19, 6);    //#define CH_STATE_SELF_ADJUST 0X0040 //伺服自整定状态

            HmiSelfAdjusting::SelfAdjustSetCtrl(HmiSelfAdjusting::SELF_ADJUST_CTRL_NULL); // 先清除伺服整定控制字
            HmiSelfAdjusting::SelfAdjustClrServoStatus();   // 将状态清零
            HmiSelfAdjusting::SelfAdjustSetStatus(HmiSelfAdjusting::SELF_ADJUST_STAT_DONE);
            HmiSelfAdjusting::SelfAdjustSaveAxisState(axisNo, 0);
            HmiSelfAdjusting::SelfAdjustUpdateCmdPos(axisNo);   // 通知PLC更新系统指令位置
            MsgChan::Instance().TranMsg(MsgData::RESET, "");    // 发送复位消息，清除跟踪误差过大报警
        }
        break;
    case 4: // 惯量识别出错
        this->InsertAjustStep(true, TR("惯量辨识出错，请修改配置后重新整定"));
        HmiSelfAdjusting::SelfAdjustSetCtrl(HmiSelfAdjusting::SELF_ADJUST_CTRL_NULL); // 先清除伺服整定控制字
        HmiSelfAdjusting::SelfAdjustClrServoStatus();   // 将状态清零
        HmiSelfAdjusting::SelfAdjustSetStatus(HmiSelfAdjusting::SELF_ADJUST_STAT_NULL);
        break;
    case 5: // 惯量识别参数适配出错
        this->InsertAjustStep(true, TR("惯量辨识参数适配出错，请修改配置后重新整定"));
        HmiSelfAdjusting::SelfAdjustSetCtrl(HmiSelfAdjusting::SELF_ADJUST_CTRL_NULL); // 先清除伺服整定控制字
        HmiSelfAdjusting::SelfAdjustClrServoStatus();   // 将状态清零
        HmiSelfAdjusting::SelfAdjustSetStatus(HmiSelfAdjusting::SELF_ADJUST_STAT_NULL);
        break;
    case 6: // 速度环参数整定正在执行
        this->InsertAjustStep(true, TR("速度环参数整定正在执行"));
        break;
    case 7: // 振动抑制正在执行
        this->InsertAjustStep(true, TR("振动抑制正在执行"));
        break;
    case 8: // 陷波器参数设置完成
        this->InsertAjustStep(true, TR("陷波器参数设置完成"));
        break;
    case 9: // 速度环参数整定完成
        this->InsertAjustStep(true, TR("速度环参数整定完成"));
        // 同步伺服参数
//        for (Bit32 i = 0; i < TOTAL_PARM_ADJUST; i++)
//        {
//            Bit32 parmId = HmiSelfAdjusting::axisParmList[i] - SERVO_PARM_START_IDX;
//            HNC_ParamanServoReadPara(axisNo, parmId);
//        }
        // 同步伺服参数
        // 重新获取修改后的伺服参数
//        HmiSelfAdjusting::FillParmList(1);
//        HmiSelfAdjusting::SelfAdjustSetParmChangeFlag(1);
        break;
    case 10: // 位置环初始参数设置
        this->InsertAjustStep(true, TR("位置环初始参数正在设置"));
        break;
    case 11: // 位置环参数整定正在执行
        this->InsertAjustStep(true, TR("位置环参数整定正在执行"));
        break;
    case 12: // 位置偏差检测
        this->InsertAjustStep(true, TR("位置偏差正在检测"));
        break;
    case 13: // 位置环参数整定完成
        this->InsertAjustStep(true, TR("位置环参数整定完成"));
        break;
    case 14: // 参数整定完成
//        this->InsertAjustStep(true, TR("参数整定完成"));
//        HmiSelfAdjusting::SelfAdjustSetCtrl(HmiSelfAdjusting::SELF_ADJUST_CTRL_NULL); // 先清除伺服整定控制字
//        HmiSelfAdjusting::SelfAdjustClrServoStatus();   // 将状态清零
//        HmiSelfAdjusting::SelfAdjustSetStatus(HmiSelfAdjusting::SELF_ADJUST_STAT_DONE);
        // 同步伺服参数
//        for (Bit32 i = 0; i < TOTAL_PARM_ADJUST; i++)
//        {
//            Bit32 parmId = HmiSelfAdjusting::axisParmList[i] - SERVO_PARM_START_IDX;
//            HNC_ParamanServoReadPara(axisNo, parmId);
//        }
        // 同步伺服参数
        // 重新获取修改后的伺服参数
#ifndef _LINUX
    Sleep(50);
#else
    usleep(150*1000);
#endif
        HmiSelfAdjusting::FillParmList(1);
        HmiSelfAdjusting::SelfAdjustSetParmChangeFlag(1);
        HmiSelfAdjusting::SelfAdjustSaveAxisState(axisNo, 0);
        this->InsertAjustStep(true, TR("参数整定完成"));

        // 解除自整定过程中的轴锁住状态
        HNC_RegGetFGBase(REG_FG_CHAN_BASE, &chRegBase);
        HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 19, 6);    //#define CH_STATE_SELF_ADJUST 0X0040 //伺服自整定状态

        HmiSelfAdjusting::SelfAdjustSetCtrl(HmiSelfAdjusting::SELF_ADJUST_CTRL_NULL); // 先清除伺服整定控制字
        HmiSelfAdjusting::SelfAdjustClrServoStatus();   // 将状态清零
        HmiSelfAdjusting::SelfAdjustSetStatus(HmiSelfAdjusting::SELF_ADJUST_STAT_DONE);
        HmiSelfAdjusting::SelfAdjustUpdateCmdPos(axisNo);   // 通知PLC更新系统指令位置
        MsgChan::Instance().TranMsg(MsgData::RESET, "");    // 发送复位消息，清除跟踪误差过大报警
        break;
    default:
        break;
    }
    this->curStatus = servoStatus;
}

void SelfAdjustStep3::ClearAdjustStep()
{
    ui->plainTextEdit->clear();
}

void SelfAdjustStep3::ResetAdjustStep()
{
    if (HmiSelfAdjusting::SelfAdjustGetStatus() != HmiSelfAdjusting::SELF_ADJUST_STAT_DONE)
    {
        this->ClearAdjustStep();
        this->InsertAjustStep(false, TR("整定未开始"));
        this->curStatus = 0;
    }
}

void SelfAdjustStep3::InsertAjustStep(bool enter, QString info)
{
    curTime = QDateTime::currentDateTime();
    if (enter)
    {
        ui->plainTextEdit->insertPlainText("\n" + curTime.toString("yyyy-MM-dd hh:mm:ss  ") + info);
    }
    else
    {
        ui->plainTextEdit->insertPlainText(curTime.toString("yyyy-MM-dd hh:mm:ss  ") + info);
    }
    ui->plainTextEdit->ensureCursorVisible();
}

void SelfAdjustStep3::WidgetIn()
{
    HmiMenuManage::Instance().SetMenuValid("MSG_LAST", true);      // “上一步”有效
    if (HmiSelfAdjusting::SelfAdjustGetStatus() == HmiSelfAdjusting::SELF_ADJUST_STAT_DONE)
    {
        HmiMenuManage::Instance().SetMenuValid("MSG_NEXT", true);      // “下一步”有效
    }
    else
    {
        HmiMenuManage::Instance().SetMenuValid("MSG_NEXT", false);      // “下一步”无效
    }
    HmiMenuManage::Instance().SetMenuValid("MSG_BACK", false);      // “整定下一轴”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_MARK", false);      // “标记重力轴”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_ADJUST", true);    // “开始整定”有效
    HmiMenuManage::Instance().SetMenuValid("MSG_ACCEPT", false);     // “接受”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_IGNOR", false);      // “放弃”无效
    HmiMenuManage::Instance().MenuRedraw();
}

Bit32 SelfAdjustStep3::WidgetExitChk()
{
    Bit32 ret = 0;

    if (HmiSelfAdjusting::SelfAdjustGetStatus() == HmiSelfAdjusting::SELF_ADJUST_STAT_ING)
    {
        //MessageOut(TR("整定过程中禁止退出界面"));
        ret = 1;
    }
    return ret;
}

void SelfAdjustStep3::SetButtonState()
{
    bool update = false;
    if (HmiSelfAdjusting::SelfAdjustGetStatus() == HmiSelfAdjusting::SELF_ADJUST_STAT_DONE)
    {
        if (HmiMenuManage::Instance().IsMenuValid("MSG_NEXT") == false)
        {
            update = true;
            HmiMenuManage::Instance().SetMenuValid("MSG_NEXT", true);      // “下一步”有效
        }
    }
    else
    {
        if (HmiMenuManage::Instance().IsMenuValid("MSG_NEXT") == true)
        {
            update = true;
            HmiMenuManage::Instance().SetMenuValid("MSG_NEXT", false);      // “下一步”无效
        }
    }

    if (update)
    {
        HmiMenuManage::Instance().MenuRedraw();
    }
}

