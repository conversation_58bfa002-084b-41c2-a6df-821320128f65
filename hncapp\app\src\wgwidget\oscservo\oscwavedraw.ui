<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OscWaveDraw</class>
 <widget class="QWidget" name="OscWaveDraw">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>524</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout" stretch="1,10">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>40</number>
       </property>
       <property name="rightMargin">
        <number>40</number>
       </property>
       <item>
        <widget class="QLabel" name="yName1">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="yName2">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="yName3">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="yName4">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="yName5">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="yName6">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QLabel" name="yName11">
         <property name="text">
          <string/>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="20,1">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QCustomPlot" name="wavePlot" native="true"/>
     </item>
     <item>
      <widget class="QLabel" name="xName">
       <property name="styleSheet">
        <string notr="true"/>
       </property>
       <property name="text">
        <string>T(s)</string>
       </property>
       <property name="alignment">
        <set>Qt::AlignBottom|Qt::AlignLeading|Qt::AlignLeft</set>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QCustomPlot</class>
   <extends>QWidget</extends>
   <header>qcustomplot.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
