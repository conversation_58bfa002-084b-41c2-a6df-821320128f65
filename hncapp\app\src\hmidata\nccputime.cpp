﻿/*!
 * @file nccputime.cpp
 * @brief cpu资源监控数据类
 * @note
 *
 * @version V1.00
 * @date 2023/6/2
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */

#include <QString>
#include <QVector>

#include "datadef.h"
#include "staticdata.h"
#include "filemanage.h"

#include "hncvar.h"
#include "hncsysctrl.h"
#include "hncsmpl.h"
#include "hncmath.h"

#include "nccputime.h"

#define NCU_DATA_FILE "cpudata.txt"
#define NCU_MAX_CH			(8)     // 内核线程采样用到最大通道数
#define SMPL_MAX_DATA   (5000)      // 采样缓冲区数量
#define MAX_DATA_NUM	(RECORD_MAX_MINUTES * 60 * 1000) // 数据总数最多180000个点
#define NCU_SMPL_PERIOD (1)         // 内核采样插补周期

#define HMI_RECORD_LOG      ("HMI_CHANGE_WIDGET_LOG")   // 记录界面切换日志字符串标志

NcCpuTime::NcCpuTime()
{
    m_nPid = 0;
    m_fPreCpuTime = 0.0;
    m_nPreSTime = 0;
    m_nPreUTime = 0;
    m_fCpuPrecent = 0;
    m_nTotalSeconds = 60;
    m_nCPULevel = 0;
    m_nLastSeconds = 0;
    m_bLoaded = false;
    m_finishedFlag = false;
    m_bRecordFlag = false;
    m_nRecordTime = 0;
    m_tmpData = NULL;
    m_mapData.clear();

    InitRecord();
    // 设置需要采样的系统变量
    m_ncuMacroList.clear();
    m_ncuMacroList.append(DEBUG_INFO_BUS_CTRL_TIME);    // 总线读写实时值
    m_ncuMacroList.append(DEBUG_INFO_AXES_CTRL_TIME);   // 轴控制实时值
    m_ncuMacroList.append(DEBUG_INFO_PLC1_SCAN_TIME);   // PLC1扫描实时值
    m_ncuMacroList.append(DEBUG_INFO_PLC2_SCAN_TIME);   // PLC2扫描实时值
    m_ncuMacroList.append(DEBUG_INFO_CHAN_CTRL_TIME);   // 通道控制实时值
    m_ncuMacroList.append(DEBUG_INFO_SMX_CTRL_TIME);    // 从轴控制实时值
    m_ncuMacroList.append(DEBUG_INFO_PPI_TIME_BEGIN);   // 内核解释器线程总耗时实时值
    m_ncuMacroList.append(DEBUG_INFO_IIP_TIME_BEGIN);   // 内核插补器线程总耗时实时值

    m_SamplClient = HNC_SamplRegClient();
}

void NcCpuTime::SetPid(Bit32 id)
{
    m_nPid = id;
}

Bit16 NcCpuTime::SetTotalSeconds(Bit32 total)
{
    if (m_bRecordFlag)
    {
        return -1;      // 记录中禁止修改总时间
    }
    m_nTotalSeconds = total;
    return 0;
}

Bit16 NcCpuTime::GetTotalSeconds()
{
    return m_nTotalSeconds;
}

Bit16 NcCpuTime::RefreshPrecent()
{
    FILE *fpCPU = NULL;
    FILE *fpSTAT = NULL;
    Bit8 fileName[PATH_NAME_LEN] = {0};
    Bit8 buf[PATH_NAME_LEN] = {0};
    Bit8 *tmp = NULL;
    fBit32 cpuTime = 0;
    Bit32 uTime = 0;
    Bit32 sTime = 0;
    snprintf(fileName, sizeof(fileName), "/proc/uptime");
    fpCPU = fopen(fileName, "r");
    if (fpCPU == NULL)
    {
        return -2;
    }

    fgets(buf, PATH_NAME_LEN, fpCPU);
    sscanf(buf, "%f", &cpuTime);
    fclose(fpCPU);
//    snprintf(fileName, sizeof(fileName), "/proc/stat");
//    fpCPU = fopen(fileName, "r");
//    if (fpCPU == NULL)
//    {
//        return -2;
//    }

//    fgets(buf, PATH_NAME_LEN, fpCPU);

//    tmp = strtok(buf, " ");
//    int j = 0;
//    while(tmp != NULL)
//    {
//        if (j >= 1 && j <= 7)
//        {
//            cpuTime += atol(tmp);
//        }
//        else if (j >7)
//        {
//            break;
//        }
//        tmp = strtok(NULL, " ");
//        j++;
//    }

//    fclose(fpCPU);

    // 避免两次计算结果波动过大，计算操作需要适当间隔1s
    if ((cpuTime - m_fPreCpuTime) < 1.0)
    {
        return -1;
    }

    memset(fileName, 0, sizeof(fileName));
    memset(buf, 0, sizeof(buf));
    snprintf(fileName, sizeof(fileName), "/proc/%ld/stat", m_nPid);
    fpSTAT = fopen(fileName, "r");
    if (fpSTAT == NULL)
    {
        return -1;
    }

    fgets(buf, PATH_NAME_LEN, fpSTAT);

    tmp = strtok(buf, " ");
    int i = 1;
    while(tmp != NULL)
    {
        if (i == 14)
        {
            uTime = atol(tmp);
        }
        else if (i == 15)
        {
            sTime = atol(tmp);
        }
        else if (i >15)
        {
            break;
        }
        tmp = strtok(NULL, " ");
        i++;
    }

    fclose(fpSTAT);

//    m_fCpuPrecent = ((uTime - m_nPreUTime) + (sTime - m_nPreSTime)) / (cpuTime - m_fPreCpuTime);
    m_fCpuPrecent = (uTime - m_nPreUTime) / (cpuTime - m_fPreCpuTime);

    m_nPreUTime = uTime;
    m_nPreSTime = sTime;
    m_fPreCpuTime = cpuTime;

    Bit16 level = 0;
    if (m_fCpuPrecent <= 30)
    {
        level = 0;
    }
    else if (m_fCpuPrecent <= 45)
    {
        level = 1;
    }
    else if (m_fCpuPrecent <= 60)
    {
        level = 2;
    }
    else
    {
        level = 3;
    }

    m_nCPULevel = level;
    return 0;
}

fBit32 NcCpuTime::GetCpuUsage()
{
    return m_fCpuPrecent;
}

bool NcCpuTime::IsRecord()
{
    return m_bRecordFlag;
}

bool NcCpuTime::IsLoaded()
{
    return m_bLoaded;
}

bool NcCpuTime::IsFinished()
{
    return m_finishedFlag;
}

void NcCpuTime::ClrLoaded()
{
    m_bLoaded = false;
}

QString NcCpuTime::GetDataName(NcCpuTime::RecordType type)
{
    if (type < TYPE_HMI || type > TYPE_NCUALL)
    {
        return "";
    }
    return m_mapData[type].nameCN;
}

fBit64 NcCpuTime::GetDataMax(NcCpuTime::RecordType type)
{
    if (type < TYPE_HMI || type > TYPE_NCUALL)
    {
        return -1;
    }
    return m_mapData[type].maxValue;
}

fBit64 NcCpuTime::GetDataMin(NcCpuTime::RecordType type)
{
    if (type < TYPE_HMI || type > TYPE_NCUALL)
    {
        return -1;
    }
    return m_mapData[type].minValue;
}

fBit64 NcCpuTime::GetDataAvg(NcCpuTime::RecordType type)
{
    if (type < TYPE_HMI || type > TYPE_NCUALL)
    {
        return -1;
    }
    return m_mapData[type].avgValue;
}

Bit32 NcCpuTime::GetDataColor(NcCpuTime::RecordType type, int &r, int &g, int &b)
{
    if (type < TYPE_HMI || type > TYPE_NCUALL)
    {
        return -1;
    }
    r = m_mapData[type].color_r;
    g = m_mapData[type].color_g;
    b = m_mapData[type].color_b;
    return 0;
}

Bit16 NcCpuTime::StopRecord()
{
    if (m_bRecordFlag)
    {
        m_bRecordFlag = false;
        m_finishedFlag = true;
    }
    SmplStop();

    return 0;
}

void NcCpuTime::ResetRecord()
{
    QList<RecordType> keyList = m_mapData.keys();
    for (int i = 0; i < keyList.count(); i++)
    {
        m_mapData[keyList.at(i)].ClearData();
        m_mapData[keyList.at(i)].x->resize(m_nTotalSeconds * 1000);
        m_mapData[keyList.at(i)].y->resize(m_nTotalSeconds * 1000);
    }
}

void NcCpuTime::RefreshRecord()
{
    if (!m_bRecordFlag)
    {
        return;
    }
    RefreshPrecent();    // 刷新CPU占用率

    RefreshCpuRecord();
    RefreshNcuRecord();

    QDateTime CurTime = QDateTime::currentDateTime();
    Bit32 tt = m_timeCpuRecord.secsTo(CurTime);
    if (tt >= m_nTotalSeconds)     // 记录结束，
    {
        RefreshRecordAvg();     // 计算平均值
        StopRecord();           // 停止记录
    }
}

void NcCpuTime::RefreshCpuRecord()
{
    QDateTime CurTime = QDateTime::currentDateTime();
    Bit32 tt = m_timeCpuRecord.secsTo(CurTime);
    if (tt <= m_nLastSeconds)
    {
        return;
    }

    m_nLastSeconds = tt;

    m_mapData[TYPE_HMI].AddData(tt * 1000, m_fCpuPrecent);
}

void NcCpuTime::SmplInit()
{
    for (int i = 0; i < m_ncuMacroList.count(); i++)
    {
        HNC_SamplRemoveChannel(m_SamplClient, i);
    }
    HNC_SamplSetPeriod(NCU_SMPL_PERIOD);
    for (int i = 0; i < m_ncuMacroList.count(); i++)
    {
        int tt = HNC_SamplSetConfig(m_SamplClient, i, SAMPL_SYS_VAL, 0, VAR_SYS_DEBUG_INF + m_ncuMacroList.at(i), 4);
        tt += 1;
    }

    if (m_tmpData == NULL)
    {
        m_tmpData = (Bit32 *)malloc(sizeof(Bit32) * NCU_MAX_CH * SMPL_MAX_DATA);
    }
}

void NcCpuTime::SmplStart()
{
    HNC_SamplClientStop(m_SamplClient);
    HNC_SamplClientStart(m_SamplClient);
    HNC_SamplTriggerOn();
}

void NcCpuTime::SmplStop()
{
    HNC_SamplTriggerOff();
    HNC_SamplClientStop(m_SamplClient);
    for (int i = 0; i < m_ncuMacroList.count(); i++)
    {
        HNC_SamplRemoveChannel(m_SamplClient, i);
    }
    if (m_tmpData != NULL)
    {
        free(m_tmpData);
        m_tmpData = NULL;
    }
}

void NcCpuTime::RefreshNcuRecord()
{
    if (m_tmpData == NULL)
    {
        return;
    }
    Bit32 tick = 0;
    HNC_SamplGetPeriod(tick);
    Bit32 actSmplChNum = 0;
    Bit32 numPerCh = SMPL_MAX_DATA;
    Bit32 leftNum = 0;
    RecordType type = TYPE_NULL;
    Bit32 lastPos = m_mapData[TYPE_BUS].dataCount;

    HNC_SamplGetData(m_SamplClient, m_tmpData, actSmplChNum, numPerCh);
    if (lastPos + numPerCh > MAX_DATA_NUM - 1)
    {
        leftNum = MAX_DATA_NUM - lastPos - 1;
    }
    else
    {
        leftNum = numPerCh;
    }

    bool isDataValid = false;
    for (Bit32 i = TYPE_BUS; i <= TYPE_NCUALL; i++)
    {
        Bit32 idx = i - TYPE_BUS;
        type = (RecordType)i;
        for (Bit32 j = 0; j < leftNum; j++)
        {
            isDataValid = true; // 默认数据有效
            fBit64 value = m_tmpData[idx * numPerCh + j] / 1000.0;   // 转换为μs
            m_mapData[type].AddData((lastPos + j) * tick, value);

            if (type == TYPE_PPI && HNC_DoubleCompare(value, 0.0) == 0)
            {
                isDataValid = false; // 解释器数据为0时为无效数据
            }
            m_mapData[type].AddData((lastPos + j) * tick, value, isDataValid);
        }
    }
}

void NcCpuTime::RefreshRecordAvg()
{
    int type = TYPE_NULL;
    Bit32 count = 0;
    for (type = TYPE_HMI; type <= TYPE_NCUALL; type++)
    {
        fBit64 sum = 0.0;
        count = m_mapData[(RecordType)type].dataCount;
        for (Bit32 i = 0; i < m_mapData[(RecordType)type].dataCount; i++)
        {
            if (type == TYPE_PPI && HNC_DoubleCompare(m_mapData[(RecordType)type].y->at(i), 0.0) == 0)
            {
                count--;
                continue; // 解释器数据为0时为无效数据
            }
            sum += m_mapData[(RecordType)type].y->at(i);
        }

        if (count <= 0)
        {
            m_mapData[(RecordType)type].avgValue = 0.0;
        }
        else
        {
            m_mapData[(RecordType)type].avgValue = sum / count;
        }
    }
}

Bit32 NcCpuTime::GetRecord(RecordType type, QVector<Bit32> &x, QVector<fBit64> &y, Bit32 &count)
{
    if (type < TYPE_HMI || type >= TYPE_TOTAL)
    {
        return -1;
    }
    x = *m_mapData[type].x;
    y = *m_mapData[type].y;
    count = m_mapData[type].dataCount;
    return 0;
}

Bit32 NcCpuTime::ExportToUDisk()
{
    FILE *fp = NULL;
    Bit8 pathName[PATH_NAME_LEN] = {'\0'};

#ifdef _LINUX
    Bit32 state = FileManage::GetDiskState(FileManage::DRIVE_U); // U盘状态
    if (FileManage::LOADDISK != state)
    {
        return -1;      // U盘未加载
    }

    snprintf(pathName, PATH_NAME_LEN, "%s%c%s", "/g", DIR_SEPARATOR, NCU_DATA_FILE);
#else
    Bit8 dataPath[PATH_NAME_LEN] = {0};
    HNC_SysCtrlGetConfig(HNC_SYS_CFG_DATA_PATH, dataPath);

    snprintf(pathName, PATH_NAME_LEN, "%s%c%s", dataPath, DIR_SEPARATOR, NCU_DATA_FILE);
#endif
    fp = fopen(pathName, "wt");
    if (NULL == fp)
    {
        return -2;      // 创建文件失败
    }

    QString strTime = QString("Time: %1\n").arg(m_timeCpuRecord.toString("yyyy-MM-dd hh:mm:ss"));
    QString strSeconds = QString("Total Seconds: %1\n").arg(m_nTotalSeconds);
    fputs("****************************\n", fp);
    fputs(strTime.toLatin1().data(), fp);
    fputs(strSeconds.toLatin1().data(), fp);
    fputs("****************************\n\n", fp);

    for (int i = TYPE_HMI; i < TYPE_TOTAL; i++)
    {
        Bit8 buf[STR_BUF_LEN] = {0};
        QString strType = m_mapData.value((RecordType)i).name;
        fputs(strType.toLatin1().data(), fp);
        fputs("\n", fp);

        QString info = QString("max:%1 min:%2 avg:%3").arg(m_mapData.value((RecordType)i).maxValue).arg(m_mapData.value((RecordType)i).minValue).arg(m_mapData.value((RecordType)i).avgValue);
        fputs(info.toLatin1().data(), fp);
        fputs("\n", fp);

        QVector<Bit32> *pX = m_mapData[(RecordType)i].x;
        QVector<fBit64> *pY = m_mapData[(RecordType)i].y;
        Bit32 len = m_mapData[(RecordType)i].dataCount;
        for (int j = 0; j < len; j++)
        {
            snprintf(buf, STR_BUF_LEN, "x:%d y:%lf\n", pX->at(j), pY->at(j));
            fputs(buf, fp);
        }
        fputs("\n\n", fp);
    }

    fputs("****************************\n\n", fp);

    // 记录界面切换日志
    QString strLog = QString("%1\n").arg(HMI_RECORD_LOG);
    fputs(strLog.toLatin1().data(), fp);

    QList<QString> list = StaticData::Instance().m_CpuLog.GetLogList();
    for (Bit32 ii = 0; ii < list.count(); ii++)
    {
        strLog = QString("No%1----%2\n").arg(ii + 1).arg(list.at(ii));
        fputs(StrToQByte(strLog, CODE_UTF8).data(), fp);
    }

    NcFSync(fp);

    fclose(fp);
    fp = NULL;
#ifdef _LINUX
    Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("cpu负载记录已导出至U盘根目录%1").arg(NCU_DATA_FILE));
#else
    Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("cpu负载记录已导出至data目录%1").arg(NCU_DATA_FILE));
#endif
    return 0;
}

Bit32 NcCpuTime::LoadFormUDisk()
{
    StopRecord();

    FILE *fp = NULL;
    Bit8 pathName[PATH_NAME_LEN] = {'\0'};
#ifdef _LINUX
    Bit32 state = FileManage::GetDiskState(FileManage::DRIVE_U); // U盘状态
    if (FileManage::LOADDISK != state)
    {
        return -1;      // U盘未加载
    }

    snprintf(pathName, PATH_NAME_LEN, "%s%c%s", "/g", DIR_SEPARATOR, NCU_DATA_FILE);
#else
    Bit8 dataPath[PATH_NAME_LEN] = {0};
    HNC_SysCtrlGetConfig(HNC_SYS_CFG_DATA_PATH, dataPath);

    snprintf(pathName, PATH_NAME_LEN, "%s%c%s", dataPath, DIR_SEPARATOR, NCU_DATA_FILE);
#endif

    fp = fopen(pathName, "rt");
    if (NULL == fp)
    {
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("cpu负载监控记录载入失败"));
        return -2;      // 打开文件失败
    }

    Bit32 index = 0;
    bool isHasLog = false;
    bool isDataValid = false;
    Bit8 buf[STR_BUF_LEN] = {0};
    RecordType type = TYPE_NULL;
    while(!feof(fp))
    {
        if (fgets(buf, STR_BUF_LEN, fp) == NULL)
        {
            break;
        }

        QString strTmp = QString(buf);
        if (strTmp.startsWith("Total Seconds")) // 记录总时长
        {
            sscanf(buf, "Total Seconds: %d\n", &m_nTotalSeconds);
            this->ResetRecord();    // 重置记录需要根据总记录时长
        }
        else if (strTmp.startsWith("TYPE")) // 扫描记录类型
        {
            strTmp.remove('\n');
            for (int i = TYPE_HMI; i < TYPE_TOTAL; i++)
            {
                if (m_mapData.value((RecordType)i).name == strTmp)
                {
                    fBit64 maxvalue = 0;
                    fBit64 minvalue = 0;
                    fBit64 avgvalue = 0;
                    type = (RecordType)i;
                    fgets(buf, STR_BUF_LEN, fp);
                    sscanf(buf, "max:%lf min:%lf avg:%lf", &maxvalue, &minvalue, &avgvalue);
                    m_mapData[type].maxValue = maxvalue;
                    m_mapData[type].minValue = minvalue;
                    m_mapData[type].avgValue = avgvalue;
                    break;
                }
            }
        }
        else if (strTmp.startsWith("x") && type >= TYPE_HMI && type < TYPE_TOTAL) // 扫描记录行
        {
            Bit32 tmpX = 0;
            fBit64 tmpY = 0.0;
            sscanf(buf, "x:%d y:%lf", &tmpX, &tmpY);

            isDataValid = true; // 默认数据有效
            if (type == TYPE_PPI && HNC_DoubleCompare(tmpY, 0.0) == 0)
            {
                isDataValid = false; // 解释器数据为0时为无效数据
            }
            m_mapData[type].AddData(tmpX, tmpY, isDataValid);
        }
        else if (strTmp.startsWith(HMI_RECORD_LOG)) // 记录界面切换日志标志
        {
            isHasLog = true;
            type = TYPE_TOTAL;
            StaticData::Instance().m_CpuLog.ClearLog();
        }
        else if (strTmp.startsWith("No") && isHasLog == true) // 记录界面切换日志
        {
            strTmp = CharToStr(buf, CODE_UTF8);
            index = strTmp.lastIndexOf("-");
            if (index >= 0)
            {
                strTmp = strTmp.right(strTmp.length() - index - 1);
                StaticData::Instance().m_CpuLog.AddLogEx(strTmp);
            }
        }
    }

    fclose(fp);
    fp = NULL;
    m_bLoaded = true;
    Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("cpu负载监控记录载入成功"));
    return 0;
}

void NcCpuTime::InitRecord()
{
    m_mapData.insert(TYPE_HMI, STRecordData("TYPE_HMI", QObject::TR("HMI进程(%)"), 0, 255, 0));
    m_mapData.insert(TYPE_BUS, STRecordData("TYPE_BUS", QObject::TR("总线读写(μs)"), 0, 255, 0));
    m_mapData.insert(TYPE_AXIS, STRecordData("TYPE_AXIS", QObject::TR("轴控制(μs)"), 255, 0, 0));
    m_mapData.insert(TYPE_PLC1, STRecordData("TYPE_PLC1", QObject::TR("PLC1扫描(μs)"), 0, 128, 192));
    m_mapData.insert(TYPE_PLC2, STRecordData("TYPE_PLC2", QObject::TR("PLC2扫描(μs)"), 128, 0, 255));
    m_mapData.insert(TYPE_CHAN, STRecordData("TYPE_CHAN", QObject::TR("通道控制(μs)"), 237, 101, 252));
    m_mapData.insert(TYPE_SMX, STRecordData("TYPE_SMX", QObject::TR("从轴控制(μs)"), 255, 255, 0));
    m_mapData.insert(TYPE_PPI, STRecordData("TYPE_PPI", QObject::TR("解释器(μs)"), 0, 255, 255));
    m_mapData.insert(TYPE_NCUALL, STRecordData("TYPE_NCUALL", QObject::TR("插补总和(μs)"), 255, 128, 0));
}

Bit16 NcCpuTime::StartRecord()
{
    if (m_bRecordFlag)
    {
        return -1;  // 已开启记录，无法重新启动记录
    }
    ResetRecord();  // 清空记录，提前申请内存
    m_bRecordFlag = true;
    m_finishedFlag = false;
    m_timeCpuRecord = QDateTime::currentDateTime();
    m_nLastSeconds = 0;

    SmplInit();
    SmplStart();

    return 0;
}
