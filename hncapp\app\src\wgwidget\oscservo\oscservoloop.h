﻿#ifndef OSCSERVOLOOP_H
#define OSCSERVOLOOP_H

#include <QModelIndex>
#include <QStyledItemDelegate>
#include <QWidget>

#include "containerwidget.h"
#include "hmioscservo.h"

namespace Ui {
class OscServoLoop;
}

QT_BEGIN_NAMESPACE
class OscWave;
QT_END_NAMESPACE

class OscServoLoop : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoLoop(QWidget *parent = 0);
    ~OscServoLoop();

protected:
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    bool eventFilter(QObject *target, QEvent *event);
    void resizeEvent(QResizeEvent *);

private slots:

    void on_leftBtn_clicked();

    void on_rightBtn_clicked();
private:
    Ui::OscServoLoop *ui;

    bool firstFlag;
    Bit32 curRow;
    OscWave *pOscWaveLoop;
    LoopConf oldLoopConf; // 前次全闭环诊断参数

    void OnBtFlagChange();
    void LoadAxisVal();
    void LoadView();
    void SetTableFoucs();
    void ClearTableFoucs();
    void SetColorStyle();
    void Reset();
    void LoadInfo();
    void LoadWave();
    bool ConfChangeChk();
};

class LoopListDelegate : public QStyledItemDelegate
{
    Q_OBJECT

public:
        LoopListDelegate(QWidget *parent = 0) : QStyledItemDelegate(parent) {}
};

class LoopListModel:public QAbstractTableModel
{
    Q_OBJECT
public:
    LoopListModel(QObject *parent = 0):QAbstractTableModel(parent){
    }

    void refresh();
    int rowCount(const QModelIndex &parent) const;
    int columnCount(const QModelIndex &parent) const;
    Qt::ItemFlags flags(const QModelIndex &index) const;
    QVariant data(const QModelIndex &index, int role) const;
};

#endif // OSCSERVOLOOP_H
