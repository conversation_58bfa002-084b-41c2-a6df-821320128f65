﻿#ifndef HMIRATIODATA_H
#define HMIRATIODATA_H

#include <QString>
#include <QStringList>

#include "hncdatatype.h"

const QStringList hmiRationWidList = (QStringList() << QString("800") << QString("1024") << QString("1280") << QString("800"));
const QStringList hmiRationHeightList = (QStringList() << QString("600") << QString("768") << QString("1024") << QString("480"));
const QStringList hmiRatioFlagList = (QStringList() << QString("314") << QString("317") << QString("318") << QString("314"));
 // 此处使用×符号界面显示乱码
const QStringList hmiRationStr = (QStringList() << QString("800*600") << QString("1024*768") << QString("1280*1024") <<QString("800*480"));
const Bit32 hmiRationCount = 4;

extern Bit32 HmiGetLinuxRationIndex();

extern void HmiSetLinuxRation(Bit32 index);

extern void HmiSetHmicfgRation(Bit32 index);

extern Bit32 HmiGetHmicfgRation();

extern QString HmiGetRationStr(Bit32 index);

extern QString HmiGetSysRationFlagStr(Bit32 index);

// 获取长宽比 用于选择开机图片
extern QString HmiGetWidHeiRation(Bit32 index);

#endif // HMIRATIODATA_H
