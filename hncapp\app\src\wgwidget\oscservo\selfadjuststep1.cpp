﻿#include "hncaxis.h"
#include "hncaxisdef.h"
#include "hncchan.h"
#include "hncchandef.h"
#include "hncdatadef.h"
#include "hncparaman.h"
#include "hncparamandef.h"
#include "hmiselfadjusting.h"
#include "hmimenumanage.h"

#include "selfadjuststep1.h"
#include "ui_selfadjuststep1.h"

SelfAdjustStep1::SelfAdjustStep1(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::SelfAdjustStep1)
{
    ui->setupUi(this); 
    this->firstFlag = false;
    this->axisLastStatus = -2;   // 初始值，不能为-1，0，1，否则gif第一次刷不出来
    this->m_listCount = 0;

    m_nSaveChannel = ActiveChan();
    this->GetTableCount();

    this->picPath = QString("adjust/");
    //this->pMovie = new QMovie(TransPicName(picPath + "move_axis.gif"));
    this->pMovie = new QMovie(this);

    this->LoadWidget();
    this->LoadAxisInfo();

    connect(ui->tableWidget, SIGNAL(currentCellChanged(int,int,int,int)), this, SLOT(SlotTableWidgetCellChanged(int, int, int, int)));
    this->installEventFilter(this);
}

SelfAdjustStep1::~SelfAdjustStep1()
{
    delete ui;
}

void SelfAdjustStep1::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    UNREFERENCED_PARAM(messageValue);
    if (messageid == MsgData::SETFOCUS)
    {
        ui->tableWidget->setFocus();
    }
    else if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {        
        Bit32 ch = ActiveChan();
        if (m_nSaveChannel != ch)
        {
            ui->tableWidget->clear();
            HmiSelfAdjusting::SetCurSelection(0);
            GetTableCount();
            m_nSaveChannel = ch;
        }
        this->LoadWidget();
        this->pMovie->start();
        this->LoadAxisInfo();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if (messageid == MsgData::REFRESH)
    {
        this->LoadWidget();
    }
}

bool SelfAdjustStep1::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        //QKeyEvent *keyEv = static_cast<QKeyEvent *>(event);
    }
    else if(event->type() == QEvent::Paint && !firstFlag) // Paint事件在ReSize事件之后响应，用于图片第一次重绘
    {
        //this->pMovie->setScaledSize(ui->picLb->size());
        ui->picLb->setMovie(this->pMovie);

        this->firstFlag = true;
        return true;
    }

    return QObject::eventFilter(target, event);
}

void SelfAdjustStep1::LoadWidget()
{
    fBit64 posPositive = 0;
    fBit64 posNegative = 0;
    fBit64 posCurrent = 0;
    fBit64 posGap = 0;
    Bit32 curAxisStatus = this->axisLastStatus;
    Bit32 axisNo = HmiSelfAdjusting::GetCurAdjustAxis();
    Bit32 axisType = 0;

    HNC_AxisGetValue(HNC_AXIS_TYPE, axisNo, &axisType);
    HNC_ParamanGetFloat(PARAMAN_FILE_AXIS, axisNo, PAR_AX_PLMT, &posPositive);
    HNC_ParamanGetFloat(PARAMAN_FILE_AXIS, axisNo, PAR_AX_NLMT, &posNegative);
    HNC_AxisGetValue(HNC_AXIS_ACT_POS_EX, axisNo, &posCurrent);
    if (axisType == 1)  // 直线轴
    {
        ui->label_2->setText(TR("负限位： ") + QString::number(posNegative) + "mm");
        ui->label_3->setText(TR("正限位：") + QString::number(posPositive) + "mm");
        ui->label_5->setText(TR("当前位置： ") + QString::number(posCurrent, 'f', GetPosPrec()) + "mm");
    }
    else if (axisType == 2 || axisType == 3)    // 摆动轴或旋转轴
    {
        ui->label_2->setText(TR("负限位： ") + QString::number(posNegative) + "deg");
        ui->label_3->setText(TR("正限位：") + QString::number(posPositive) + "deg");
        ui->label_5->setText(TR("当前位置： ") + QString::number(posCurrent, 'f', GetPosPrec()) + "deg");
    }
    ui->horizontalSlider->setMinimum((Bit32)posNegative);
    ui->horizontalSlider->setMaximum((Bit32)posPositive);
    ui->horizontalSlider->setValue((Bit32)posCurrent);

    Bit32 currentLang = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_LANGUAGE, &currentLang);

    posGap = (posPositive - posNegative) * 0.3; // 正负限位预留30%的距离,取中间40%作为安全区域(需与wgselfadjusting.cpp同步系数)
    if (posCurrent < posNegative + posGap)  // 安全区域以左
    {
        if (curAxisStatus != -1)
        {
            this->axisLastStatus = -1;
            this->pMovie->stop();
            if (currentLang == 0)
            {
                this->pMovie->setFileName(TransPicName(picPath + "move_right.gif"));
            }
            else
            {
                this->pMovie->setFileName(TransPicName(picPath + "move_right_en.gif"));
            }

            this->pMovie->start();
        }
    }

    if (posCurrent >= posNegative + posGap && posCurrent <= posPositive - posGap)   // 安全区域
    {
        if (curAxisStatus != 0)
        {
            this->axisLastStatus = 0;
            this->pMovie->stop();
            if (currentLang == 0)
            {
                this->pMovie->setFileName(TransPicName(picPath + "safe.gif"));
            }
            else
            {
                this->pMovie->setFileName(TransPicName(picPath + "safe_en.gif"));
            }

            this->pMovie->start();
        }
    }

    if (posCurrent > posPositive - posGap)  // 安全区域以右
    {
        if (curAxisStatus != 1)
        {
            this->axisLastStatus = 1;
            this->pMovie->stop();
            if (currentLang == 0)
            {
                this->pMovie->setFileName(TransPicName(picPath + "move_left.gif"));
            }
            else
            {
                this->pMovie->setFileName(TransPicName(picPath + "move_left_en.gif"));
            }

            this->pMovie->start();
        }
    }
}

void SelfAdjustStep1::GetTableCount()
{
    Bit32 sysChn = ActiveChan();
    Bit32 axisNo = -1;
    Bit32 axisType = 0;
    m_listCount = 0;
    for (Bit32 i = 0; i < CHAN_AXES_NUM; i++)
    {
        ParaGetIntVal(PARAMAN_FILE_CHAN, sysChn, PAR_CH_XINDEX + i, &axisNo);
        if (axisNo >= 0)
        {
            ParaGetIntVal(PARAMAN_FILE_AXIS, axisNo, PAR_AX_TYPE, &axisType);
            if (axisType == 1 || axisType == 3 || axisType == 2)  // 自整定目前只支持进给轴，放开旋转轴限制20201222；放开摆动轴限制20210128
            {
                m_listCount++;
            }
        }
    }

    Qt::ItemFlags noEditable = Qt::ItemIsSelectable | Qt::ItemIsDragEnabled |
                            Qt::ItemIsDropEnabled | Qt::ItemIsUserCheckable |
                            Qt::ItemIsEnabled ;

    ui->tableWidget->setRowCount(m_listCount);
    ui->tableWidget->setColumnCount(COL_TOTAL);

    QStringList header;
    header << TR("轴号") << TR("轴名") << TR("重力轴") << TR("旋转轴") << TR("摆动轴") << TR("状态") << TR("整定完成时间");
    ui->tableWidget->setHorizontalHeaderLabels(header);
    // 设置表格样式
    ui->tableWidget->horizontalHeader()->setHighlightSections(false); // 点击表时不对表头行光亮（获取焦点）
    ui->tableWidget->setAlternatingRowColors(true); // 设置交替行颜色
    ui->tableWidget->verticalHeader()->setVisible(false); //隐藏列表头
    ui->tableWidget->setShowGrid(true); // 显示表格分隔线
    ui->tableWidget->horizontalHeader()->setStretchLastSection(true); //设置充满表宽度
    ui->tableWidget->horizontalHeader()->setResizeMode(QHeaderView::Fixed);
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows); // 选中一行
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableWidget->setColumnWidth(COL_AXISNO, 100);
    ui->tableWidget->setColumnWidth(COL_AXISNAME, 100);
    ui->tableWidget->setColumnWidth(COL_AXISTYPE, 100);
    ui->tableWidget->setColumnWidth(COL_AXISSWING, 100);
    ui->tableWidget->setColumnWidth(COL_AXISSPIN, 100);
    ui->tableWidget->setColumnWidth(COL_AXISFLAG, 100);
    ui->tableWidget->setColumnWidth(COL_AXISTIME, 150);
    ui->tableWidget->setFont(QFont(FONT_TYPE, 12));
    ui->tableWidget->horizontalHeader()->setFont(QFont(FONT_TYPE, 12));

    for (Bit32 ii = 0; ii < m_listCount; ii++)
    {
        for(Bit32 jj = 0; jj < COL_TOTAL; jj++)
        {
            ui->tableWidget->setItem(ii, jj, new QTableWidgetItem());
            ui->tableWidget->item(ii, jj)->setTextAlignment(Qt::AlignCenter);
            ui->tableWidget->item(ii, jj)->setFlags(noEditable);
        }
    }
}

void SelfAdjustStep1::LoadAxisInfo()
{
    Bit32 sysChn = ActiveChan();
    Bit32 rowIdx = 0;
    Bit32 axisNo = -1;
    Bit32 axisType = 0;
    Bit8 axisName[64] = {'\0'};
    Bit32 axisFlag = -1;
    Bit32 axisDir = 0; // 是否重力轴标记
    qint64 axisCheckTime = 0;

    for (Bit32 i = 0; i < CHAN_AXES_NUM; i++)
    {
        ParaGetIntVal(PARAMAN_FILE_CHAN, sysChn, PAR_CH_XINDEX + i, &axisNo);
        if (axisNo >= 0)
        {
            ParaGetIntVal(PARAMAN_FILE_AXIS, axisNo, PAR_AX_TYPE, &axisType);
            if ((axisType == 1 || axisType == 3 || axisType == 2) && rowIdx < m_listCount)  // 自整定目前只支持进给轴，放开旋转轴限制20201222；放开摆动轴限制20210128
            {
                ui->tableWidget->item(rowIdx, COL_AXISNO)->setText(QString::number(axisNo));
                HNC_AxisGetValue(HNC_AXIS_NAME, axisNo, axisName);
                ui->tableWidget->item(rowIdx, COL_AXISNAME)->setText(QString(axisName));

                Bit32 idx = 0;
                for (idx = 0; idx < TOTAL_AXES_NUM; idx++)
                {
                    if (axisNo == HmiSelfAdjusting::s_ChnAxisInfo[idx].axisNo)
                    {
                        break;
                    }
                }
                axisDir = HmiSelfAdjusting::s_ChnAxisInfo[idx].axisType;
                if (axisDir == 1)
                {
                    ui->tableWidget->item(rowIdx, COL_AXISTYPE)->setText(TR("是"));
                }
                else
                {
                    ui->tableWidget->item(rowIdx, COL_AXISTYPE)->setText(TR(""));
                }

                if (axisType == 2)
                {
                    ui->tableWidget->item(rowIdx, COL_AXISSWING)->setText(TR("是"));
                    ui->tableWidget->item(rowIdx, COL_AXISSPIN)->setText(TR(""));
                }
                else if (axisType == 3)
                {
                    ui->tableWidget->item(rowIdx, COL_AXISSWING)->setText(TR(""));
                    ui->tableWidget->item(rowIdx, COL_AXISSPIN)->setText(TR("是"));
                }
                else
                {
                    ui->tableWidget->item(rowIdx, COL_AXISSWING)->setText(TR(""));
                    ui->tableWidget->item(rowIdx, COL_AXISSPIN)->setText(TR(""));
                }
                axisFlag = HmiSelfAdjusting::s_ChnAxisInfo[idx].axisFlag;
                if (axisFlag == 1)  // 已优化
                {
                    ui->tableWidget->item(rowIdx, COL_AXISFLAG)->setText(TR("已整定"));
                }
                else
                {
                    ui->tableWidget->item(rowIdx, COL_AXISFLAG)->setText(TR("未整定"));
                }
                axisCheckTime = HmiSelfAdjusting::s_ChnAxisInfo[idx].checkTime;
                if (axisCheckTime == 0) // 未优化，则显示空
                {
                    ui->tableWidget->item(rowIdx, COL_AXISTIME)->setText("");
                }
                else
                {
                    QDateTime time = QDateTime::fromMSecsSinceEpoch(axisCheckTime);
                    ui->tableWidget->item(rowIdx, COL_AXISTIME)->setText(QString(time.toString("yyyy-MM-dd hh:mm:ss")));
                }

                rowIdx++;
            }
        }
    }

    if (ui->tableWidget->rowCount() > 0)
    {
        Bit32 currentRow = HmiSelfAdjusting::GetCurSelection();
        if (currentRow < 0 || currentRow >= ui->tableWidget->rowCount())
        {
            currentRow = 0;
        }

        ui->tableWidget->selectRow(currentRow);
        ui->tableWidget->setFocus();

		Bit32 axisNo = ui->tableWidget->item(currentRow, 0)->text().toInt();
		HmiSelfAdjusting::SetCurAdjustAxis(axisNo);
    }
}

void SelfAdjustStep1::WidgetIn()
{
    HmiMenuManage::Instance().SetMenuValid("MSG_LAST", false);      // “上一步”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_NEXT", true);     // “下一步”有效
    HmiMenuManage::Instance().SetMenuValid("MSG_BACK", false);      // “整定下一轴”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_MARK", true);      // “标记重力轴”有效
    HmiMenuManage::Instance().SetMenuValid("MSG_ADJUST", false);    // “开始整定”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_ACCEPT", false);    // “接受”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_IGNOR", false);     // “放弃”无效
    HmiMenuManage::Instance().MenuRedraw();
}

// 退出检测
void SelfAdjustStep1::WidgetExit()
{
    this->pMovie->stop(); // 停止动态图片
}

void SelfAdjustStep1::SlotTableWidgetCellChanged(int currentRow, int currentColumn, int previousRow, int previousColumn)
{
    UNREFERENCED_PARAM(currentColumn);
    UNREFERENCED_PARAM(previousColumn);

    if (currentRow != previousRow && currentRow >= 0)
    {
        MessageOut("");
        // 如果上一个轴整定完成，参数未保存，则先提示是否保存
//        if (HmiSelfAdjusting::SelfAdjustGetParmChangeFlag() == 1)
//        {
//            QString tmpStr = QObject::TR("是否接受轴%1的整定参数?").arg(HmiSelfAdjusting::GetCurAdjustAxis());
//            Bit32 ret = 0;
//            if (MessagePrompt(tmpStr) == 1)    // 接受
//            {
//                MessageOut(TR("正在保存参数"));
//                ret = HmiSelfAdjusting::SelfAdjustParmSave(true);
//                if (ret == 0)
//                {
//                    MessageOut(TR("参数已保存"));
//                }
//            }
//            else
//            {
//                MessageOut(TR("正在恢复参数"));
//                ret = HmiSelfAdjusting::SelfAdjustParmSave(false);
//                if (ret == 1)
//                {
//                    MessageOut(TR("参数已放弃"));
//                }
//            }
//        }

        Bit32 axisNo = ui->tableWidget->item(currentRow, 0)->text().toInt();
        HmiSelfAdjusting::SetCurAdjustAxis(axisNo);
        //HmiSelfAdjusting::SelfAdjustInitConf();
        this->LoadWidget();
        // 切换轴时 切换自整定状态为未开始
        HmiSelfAdjusting::SelfAdjustSetStatus(HmiSelfAdjusting::SELF_ADJUST_STAT_NULL);
        HmiSelfAdjusting::SetCurSelection(currentRow);
    }
}
