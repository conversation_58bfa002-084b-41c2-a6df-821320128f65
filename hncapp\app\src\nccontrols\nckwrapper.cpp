﻿/*!
 * @file API二次开发接口封装
 * @brief 供qml用
 * @note 详细说明
 *
 * @version V1.00
 * @date 2019/7/7
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */
#include <QCoreApplication>
#include <QDebug>
#include <QDomDocument>
#include <QProcess>
#include <QSettings>

#include "eventdef.h"
#include "hncfileverify.h"
#include "hncsys.h"
#include "hncsysctrl.h"
#include "hncdatadef.h"
#include "hncparaman.h"

#include "common.h"
#include "filemanage.h"

#include "nckwrapper.h"

int NckWrapper::GetParmValue(void)
{
    Bit32 languageType = 0;

    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_LANGUAGE, &languageType);

    return languageType;
}

void NckWrapper::SetParmValue(int val)
{
    Bit32 languageType = val;

    ParaSetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_LANGUAGE, languageType);
    HNC_ParamanSave();
}

void NckWrapper:: OpenExternProcess(QString progName)
{
    QString progPath = progName + ".sh";
    QProcess *process = new QProcess;
    process->setEnvironment(process->environment());
    process->start("./../bin/" + progPath);
    process->waitForStarted();
}
