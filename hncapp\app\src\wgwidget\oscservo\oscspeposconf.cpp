﻿
#include <QKeyEvent>

#include "hncdatadef.h"
#include "hncaxis.h"
#include "msgchan.h"

#include "oscspeposconf.h"
#include "ui_oscspeposconf.h"

OscSpePosConf::OscSpePosConf(QWidget *parent, Bit32 type) :
    ContainerWidget(parent),
    ui(new Ui::OscSpePosConf)
{
    ui->setupUi(this);

    m_type = type;
    m_pstServoConf = NULL;

    if(m_type == HmiOscServo::OSC_SERVO_SPE)
    {
        ui->label->setText(TR("速度环"));
    }
    else if(m_type == HmiOscServo::OSC_SERVO_POS)
    {
        ui->label->setText(TR("位置环"));
    }

//    ui->label_2->setText(TR("mm"));
//    ui->label_3->setText(TR("mm/min"));
    ui->label_26->setText(TR("ms"));

    intValidator = new QIntValidator(this);
    curFocusIndex = 0;
    wList.clear();
    wList.append(ui->labelDist);
    wList.append(ui->labelSpe);
    wList.append(ui->labelPeriodMulti);
    for(int i = 0; i < wList.count(); i++)
    {
        wList.at(i)->setFocusPolicy(Qt::StrongFocus);
        wList.at(i)->installEventFilter(this);
    }
    this->dirLayout = new DirMoveLayout(this);
    dirLayout->SetDirMoveLayout(3, 1, this->wList);
}

OscSpePosConf::~OscSpePosConf()
{
    delete ui;
}

void OscSpePosConf::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    UNREFERENCED_PARAM(messageValue);
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        Bit32 axisType = 0;

        HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetCurAxesConf(), &axisType);
        if (axisType == 1 || axisType == 7) //  7(主轴做进给轴使用)和1一样按直线轴处理
        {
            ui->label_2->setText(TR("mm"));
            ui->label_3->setText(TR("mm/min"));
        }
        else
        {
            ui->label_2->setText(TR("deg"));
            ui->label_3->setText(TR("deg/min"));
        }

        this->LoadWidget();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::SETFOCUS)
    {
        this->wList.at(this->curFocusIndex)->setFocus(); // 防止焦点丢失
    }
}

bool OscSpePosConf::eventFilter(QObject *wg, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        QWidget *tmp = dynamic_cast<QWidget *>(wg);
        if(this->wList.contains(tmp) && tmp != NULL)
        {
            this->curFocusIndex = this->wList.indexOf(tmp);
            if(keyEvent->key() == Qt::Key_Right || keyEvent->key() == Qt::Key_Left
              || keyEvent->key() == Qt::Key_Down || keyEvent->key() == Qt::Key_Up)
            {
                dirLayout->DirMoveOnKey(tmp, keyEvent->key());
                this->curFocusIndex = dirLayout->GetCurFocusIdx();
                return true;
            }
            else if(keyEvent->key() == Qt::Key_Enter || keyEvent->key() == Qt::Key_Return)
            {
                this->CurValSet(this->curFocusIndex);
                return true;
            }

        }
    }
    else if(event->type() == QEvent::FocusIn)
    {
        QWidget *tmp = dynamic_cast<QWidget *>(wg);
        if(this->wList.contains(tmp) && tmp != NULL)
        {
            this->curFocusIndex = this->wList.indexOf(tmp);
        }
    }
    return QObject::eventFilter(wg, event);
}

void OscSpePosConf::LoadWidget()
{
    Bit32 ch = ActiveChan();
    Bit32 logicAxisNo = HmiOscServo::GetCurAxesConf();
    if (logicAxisNo < 0 || logicAxisNo >= TOTAL_AXES_NUM)
    {
        return;
    }

    if(m_type == HmiOscServo::OSC_SERVO_SPE)
    {
        m_pstServoConf = &HmiOscServo::s_Conf[ch].stSpeConf[logicAxisNo];
//        ui->labelDist->setText(QString::number(HmiOscServo::s_Conf[ch].stSpeConf[logicAxisNo].axis_dist));
//        ui->labelSpe->setText(QString::number(HmiOscServo::s_Conf[ch].stSpeConf[logicAxisNo].axis_f));
//        ui->labelPeriod->setText(QString::number(HmiOscServo::s_Conf[ch].stSpeConf[logicAxisNo].smpl_period));
    }
    else if(m_type == HmiOscServo::OSC_SERVO_POS)
    {
        m_pstServoConf = &HmiOscServo::s_Conf[ch].stPosConf[logicAxisNo];
//        ui->labelDist->setText(QString::number(HmiOscServo::s_Conf[ch].stPosConf[logicAxisNo].axis_dist));
//        ui->labelSpe->setText(QString::number(HmiOscServo::s_Conf[ch].stPosConf[logicAxisNo].axis_f));
//        ui->labelPeriod->setText(QString::number(HmiOscServo::s_Conf[ch].stPosConf[logicAxisNo].smpl_period));
    }

    if (m_pstServoConf == NULL)
    {
        return;
    }

    ui->labelDist->setText(QString::number(m_pstServoConf->axis_dist));
    ui->labelSpe->setText(QString::number(m_pstServoConf->axis_f));
//    ui->labelPeriod->setText(QString::number(m_pstServoConf->smpl_period));
    Bit32 ncuCycle = 0;
    Bit32 multi = 0;
    Bit32 ret = HmiOscServo::GetNcuCycleMulti(m_pstServoConf->smpl_period, ncuCycle, multi);
    ncuCycle = ncuCycle / 1000;

    if(ret == -1)
    {
        ui->labelPeriodMulti->setText("");
        ui->labelNcuCycle->setText(QString::number(ncuCycle));
        ui->labelPeriodResult->setText(QString::number(m_pstServoConf->smpl_period));

        MessageOut(QObject::TR("采样周期不是插补周期整数倍，请手动设置采样周期"));
    }
    else
    {
        ui->labelPeriodMulti->setText(QString::number(multi));
        ui->labelNcuCycle->setText(QString::number(ncuCycle));
        ui->labelPeriodResult->setText(QString::number(ncuCycle * multi));
    }
}

void OscSpePosConf::CurValSet(Bit32 row)
{
    QString editStr = "";
    Bit32 max = 0;
    Bit32 min = 0;
    Bit32 tmp = 0;
    Bit32 ret = -2;
    bool ok = false;
    Bit32 cycle = HmiOscServo::GetNcuCycle() / 1000;
    if(cycle == 0)
    {
        cycle = 1;
    }

    Bit32 logicAxisNo = HmiOscServo::GetCurAxesConf();
    if (logicAxisNo < 0 || logicAxisNo >= TOTAL_AXES_NUM)
    {
        return;
    }

    if(m_pstServoConf == NULL || row < 0 || row >= this->wList.count())
    {
        return;
    }

    if (row == 0)
    {
        max = 10000;
        min = 10;

        intValidator->setBottom(min);
        intValidator->setTop(max);

        editStr = ui->labelDist->text();
        if (MessageInput(&editStr, DTYPE_UINT, TR("请输入[行程]:"), -1, -1, 0, intValidator) == 0)
        {
            tmp = editStr.toInt(&ok);
            if (ok == false || tmp < min || tmp > max)
            {
                ret = -1;
            }
            else
            {
                ret = 0;

                if(m_pstServoConf->axis_dist != tmp)
                {
                    HmiOscServo::SetConfChgFlag(true);
                }
                m_pstServoConf->axis_dist = tmp;
            }
        }
    }
    else if (row == 1)
    {
        max = 100000;
        min = 10;

        intValidator->setBottom(min);
        intValidator->setTop(max);

        editStr = ui->labelSpe->text();
        if (MessageInput(&editStr, DTYPE_UINT, TR("请输入[速度]:"), -1, -1, 0, intValidator) == 0)
        {
            tmp = editStr.toInt(&ok);
            if (ok == false || tmp < min || tmp > max)
            {
                ret = -1;
            }
            else
            {
                ret = 0;

                if(m_pstServoConf->axis_f != tmp)
                {
                    HmiOscServo::SetConfChgFlag(true);
                }
                m_pstServoConf->axis_f = tmp;
            }
        }
    }
    else
    {
        max = 1000 / cycle;
        min = 1;

        intValidator->setBottom(min);
        intValidator->setTop(max);

        editStr = ui->labelPeriodMulti->text();
        if (MessageInput(&editStr, DTYPE_UINT, TR("请输入[采样周期/插补周期 倍率]:"), -1, -1, 0, intValidator) == 0)
        {
            tmp = editStr.toInt(&ok);
            if (ok == false || tmp < min || tmp > max)
            {
                ret = -1;
            }
            else
            {
                ret = 0;
                if(m_pstServoConf->smpl_period != tmp * cycle)
                {
                    HmiOscServo::SetConfChgFlag(true);
                }
                m_pstServoConf->smpl_period = tmp * cycle;
            }
        }
    }


    if (ret == -1)
    {
        MessageOut(TR("输入数据无效，范围:%1~%2").arg(min).arg(max));
        this->wList.at(row)->setFocus();
        this->LoadWidget();
        return;
    }
    else
    {
        if (row == 0 || row == 1) // 重算采样周期
        {
            Bit32 wishPeriod = 1;
            fBit64 runTime = 0.0;

            if (m_pstServoConf->axis_f <= 0) // 防止除0
            {
                m_pstServoConf->axis_f = 10;
            }
            runTime = 4.0*m_pstServoConf->axis_dist/m_pstServoConf->axis_f*60000+1000; // +1000ms考虑到额外时间
            wishPeriod = (Bit32)(runTime / SMPL_DATA_NUM + 0.99); // +0.99相当于向上圆整

            if (wishPeriod < 1)
            {
                wishPeriod = 1;
            }
            if (wishPeriod < 1)
            {
                wishPeriod = 1;
            }
            else if (wishPeriod > 1000)
            {
                wishPeriod = 1000;
            }

            if (wishPeriod != m_pstServoConf->smpl_period)
            {
                m_pstServoConf->smpl_period = wishPeriod;
            }
        }
        HmiOscServo::OscServoDataSave();
    }
    MessageOut("");
    this->LoadWidget();
    //this->wList.at(row)->setFocus(); // 防止焦点丢失
    MsgChan::Instance().TranMsg(MsgData::SETFOCUS, ""); // 设置焦点
}
