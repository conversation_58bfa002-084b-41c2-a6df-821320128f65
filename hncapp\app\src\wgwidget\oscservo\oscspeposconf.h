﻿#ifndef OSCSPEPOSCONF_H
#define OSCSPEPOSCONF_H

#include "containerwidget.h"
#include "hmioscservo.h"
#include "dirmovelayout.h"

namespace Ui {
class OscSpePosConf;
}

QT_BEGIN_NAMESPACE
class QWidget;
class QValidator;
QT_END_NAMESPACE

class OscSpePosConf : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscSpePosConf(QWidget *parent = 0, Bit32 type = HmiOscServo::OSC_SERVO_SPE);
    ~OscSpePosConf();

protected:
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    bool eventFilter(QObject *wg, QEvent *event);
    //void keyPressEvent(QKeyEvent *event);
private:
    Ui::OscSpePosConf *ui;
    QIntValidator *intValidator;
    Bit32 curFocusIndex;
    QList<QWidget *>wList;
    DirMoveLayout *dirLayout;
    Bit32 m_type;
    ServoConf* m_pstServoConf;

    void LoadWidget();
    void CurValSet(Bit32 row);
    void FocusRedraw();
};

#endif // OSCSPEPOSCONF_H
