﻿#ifndef HMITOOLDATA_H
#define HMITOOLDATA_H

#include "common.h"

enum TmToolType // 车铣复合刀具类型
{
    TM_TYPE_NULL = 0,  // 无刀具
    TM_TYPE_MILL,      // 铣刀
    TM_TYPE_TURN,      // 车刀
    TM_TYPE_TM,        // 通用刀具

    TM_TYPE_NUM
};

enum ToolTypeEx
{
    HMI_EXTOOL_LTOOL_ROLL_DIR = TOOL_PARA_TOTAL,
};

extern void InitEdgeNum();

extern Bit32 GetEdgeNum(Bit32 ch);

/**
 * @brief GetToolValueLimit [车床]获取刀偏值设置范围
 * @return 范围值
 */
extern fBit64 GetTurnToolOffValLimit(void);

/**
 * @brief ToolfrmGetPulseUnit 获取公英制换算倍率
 * @return 倍率值
 */
extern fBit64 ToolfrmGetPulseUnit(void);

/**
 * @brief IsWearCount 磨损是否累加
 * @return 1:是,0:否
 */
extern Bit8 IsTurnToolCount(void);

/**
 * @brief ToolDiameterTrans [车床]刀具直半径倍率
 * @param ax 逻辑轴号
 * @return 倍率值
 */
extern fBit64 ToolDiameterTrans(Bit32 ax);

/**
 * @brief TurningHaveYComp [车床]通道是否有Y轴
 * @param ch 通道号
 * @return 1:是,0:否
 */
extern Bit32 TurningHaveYComp(Bit32 ch);

/**
 * @brief ToolFauncOff [车床]刀补值叠加的坐标值
 * @param ch 通道号
 * @param gAxis 坐标值(传出参数)
 */
extern void ToolFauncOff(Bit32 ch, fBit64 *gAxis);

/**
 * @brief TurningToolRollEnable [车床]是否开启刀具旋转方向属性
 * @return
 */
extern bool TurningToolRollEnable();

/**
 * @brief IsToolPropShow [铣床]是否开启刀具属性显示
 * @return
 */
extern bool IsToolPropShow();

/**
 * @brief GetAxEditName 获取轴编程名
 * @param ch
 * @param idx
 * @return
 */
extern QString GetAxEditName(Bit32 ch, Bit32 idx);

/**
 * @brief ToolGetToolShowPara 获取表格中显示的刀具值(进行直半径/公英制转换)
 * @param t 刀具号
 * @param index 索引号
 * @param str 刀具值
 * @param macType 机床类型 -1 不分车铣
 * @return <0:失败, 0:成功
 */
extern Bit32 ToolGetToolShowPara(Bit32 t, Bit32 index, QString &str, Bit32 macType = -1);

extern Bit32 ToolSetToolSavePara(Bit32 t, Bit32 index, fBit64 fVal, Bit32 macType = -1);
/**
 * @brief ToolSetToolState 设置刀具属性值
 * @param t 刀号
 * @param val 属性(暂时开放16个bit位)
 * @return
 * -  0:成功;
 * - -1:失败;
 * - -2:临时缓冲区满;
 */
extern Bit32 ToolSetToolState(Bit32 t, Bit32 val);

extern fBit64 GetToolDiaByMacType(Bit32 t, Bit32 macType, Bit32 parmIdx);

/*!
 * \brief IsToolLocked 刀具数据是否锁定
 * \param toolNo刀号
 * \return
 */
extern bool IsToolLocked(Bit32 toolNo);

/*!
 * \brief SetToolLocked 刀具数据设置锁定
 * \param toolNo 刀号
 * \param isLocked 是否锁定
 */
extern void SetToolLocked(Bit32 toolNo, bool isLocked);

/*!
 * \brief SaveToolLocked 记录锁定的车床刀具(数据管理刀具载入用)
 */
extern void SaveToolLocked();

/*!
 * \brief ResetToolLocked 恢复锁定的车床刀具(数据管理刀具载入用)
 */
extern void ResetToolLocked();

/*!
 * \brief GetTurningWearVal 获取车床刀具磨损限制值
 * \param ax 通道轴号
 * \return 磨损限制值
 */
extern fBit64 GetTurningWearVal();

/*!
 * \brief FindToolName 查找通道内刀具名称为name的刀具
 * \param ch 通道号
 * \param name 刀具名
 * \return 0:查找失败 >0:找到的刀具号
 */
extern Bit32 FindToolName(Bit32 ch, Bit32 t, QString name);

/*!
 * \brief SetToolInputType 设置车床刀具磨损使能
 * \param type 0:关,1:开
 */
extern void SetToolInputType(Bit32 type);

/*!
 * \brief ChangeToolInputType 改变车床刀具磨损使能
 */
extern void ChangeToolInputType();
extern bool CheckToolMagData();

extern void ResetToolMagData();
extern void InitCurMag(Bit32 ch);

extern QList<Bit32 > GetToolMagList();

extern Bit32 ChangeCurMag(Bit32 ch);

extern Bit32 GetCurMagIdx();
#endif // HMITOOLDATA_H
