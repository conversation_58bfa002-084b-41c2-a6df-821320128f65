﻿#ifndef OSCSERVOREPORT_H
#define OSCSERVOREPORT_H

#include "containerwidget.h"

const Bit32 REPORT_TABLE_COL = 5;
const Bit32 REPORT_TABLE_ROW_MAX = 10;

namespace Ui {
class OscServoReport;
}

QT_BEGIN_NAMESPACE
class QLabel;
class QWidget;
QT_END_NAMESPACE

class OscServoReport : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoReport(QWidget *parent = 0);
    ~OscServoReport();

protected:
    void resizeEvent(QResizeEvent *);
    bool eventFilter(QObject *target, QEvent *event);
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);

private:
    Ui::OscServoReport *ui;

    bool firstFlag;
    Bit32 curRow;
    Bit32 axisNum;

    QList<QLabel *> listAxisName1;
    QList<QLabel *> listAxisName2;
    QList<QWidget *> listWidget;
    QDoubleValidator *doubleValidator;

    void SetTableFoucs();
    void ClearTableFoucs();
    void LoadTable();
    void CurItemSet(Bit32 row, Bit32 col, QString strVal);
    Bit32 PowerCheck();
};

#endif // OSCSERVOREPORT_H
