﻿#ifndef RANDOMRECORDDATA_H
#define RANDOMRECORDDATA_H

#include <QString>

#include "hncdatatype.h"
#include "hncdatadef.h"

class RandomRecordData
{
public:
    typedef enum _RECORD_TYPE
    {
        NULL_RECORD = 0,
        ALARM_RECORD,
        ESTOP_RECORD,
        RESET_RECORD,
        EXIT_RECORD,

        RECORD_TYPE_NUM,

    }RECORD_TYPE;
    RandomRecordData();

    void SetRandomRecordRow(Bit32 ch, RECORD_TYPE type, Bit32 row);
    void SetRandomRecordRow(Bit32 ch, RandomRecordData::RECORD_TYPE type);
    void ClrRandomRecordRow(Bit32 ch);
    Bit32 GetRandomRecordRow(Bit32 ch);
    void RecordAlarmRowInfoByAlarmIdx(Bit32 idx);

    void Save();
    void Load();
private:
    RECORD_TYPE m_type[SYS_CHAN_NUM];
    Bit32 m_nRunRow[SYS_CHAN_NUM];// 程序运行异常中断时，任意行跳到运行行的行号
};


#endif // RANDOMRECORDDATA_H
