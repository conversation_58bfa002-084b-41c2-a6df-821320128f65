﻿#include <QKeyEvent>

#include "hncaxis.h"
#include "hncaxisdef.h"
#include "hncparaman.h"
#include "hncparamandef.h"
#include "hmimenumanage.h"

#include "selfadjuststep2.h"
#include "ui_selfadjuststep2.h"

SelfAdjustStep2::SelfAdjustStep2(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::SelfAdjustStep2)
{
    ui->setupUi(this);

    curFocusIndex = 0;//2;
    wList.clear();
//    wList.append(ui->checkBoxInertia);
    wList.append(ui->checkBoxPara);

    wList.append(ui->labelAmp);
    wList.append(ui->labelFreq);

    wList.append(ui->labelSpeed);
    wList.append(ui->labelDistance);
    wList.append(ui->labelAcc);

//    if (HmiSelfAdjusting::s_SelfAdjustConf.ctrl1 == 1)
//    {
//        ui->checkBoxInertia->setChecked(true);
//    }
//    else
//    {
//        ui->checkBoxInertia->setChecked(false);
//    }
    ui->checkBoxInertia->setChecked(true);
    connect(ui->checkBoxInertia, SIGNAL(stateChanged(int)), this, SLOT(CheckBoxStateReset(int)));

    if (HmiSelfAdjusting::s_SelfAdjustConf.ctrl2 == 1)
    {
        ui->checkBoxPara->setChecked(true);
    }
    else
    {
        ui->checkBoxPara->setChecked(false);
    }

    for(int i = 0; i < wList.count(); i++)
    {
        wList.at(i)->setFocusPolicy(Qt::StrongFocus);
        wList.at(i)->installEventFilter(this);
    }
    this->dirLayout = new DirMoveLayout(this);
    this->dirLayoutList << ui->checkBoxPara
                            << ui->labelAmp
                            << ui->labelFreq
                            << ui->labelSpeed
                            << ui->labelDistance
                            << ui->labelAcc;
    dirLayout->SetDirMoveLayout(6, 1, this->dirLayoutList);

    //HmiSelfAdjusting::SelfAdjustInitConf();
}

SelfAdjustStep2::~SelfAdjustStep2()
{
    delete ui;
}

void SelfAdjustStep2::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    UNREFERENCED_PARAM(messageValue);
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        this->LoadWidget();
        this->dirLayoutList.at(this->curFocusIndex)->setFocus();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::SETFOCUS)
    {
        this->dirLayoutList.at(this->curFocusIndex)->setFocus();
    }
}

bool SelfAdjustStep2::eventFilter(QObject *wg, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        QWidget *tmp = dynamic_cast<QWidget *>(wg);
        if(this->dirLayoutList.contains(tmp) && tmp != NULL)
        {
            if(keyEvent->key() == Qt::Key_Right || keyEvent->key() == Qt::Key_Left
              || keyEvent->key() == Qt::Key_Down || keyEvent->key() == Qt::Key_Up)
            {
                dirLayout->DirMoveOnKey(tmp, keyEvent->key());
                this->curFocusIndex = dirLayout->GetCurFocusIdx();
                MessageOut("");
                return true;
            }
            else if(keyEvent->key() == Qt::Key_Enter || keyEvent->key() == Qt::Key_Return)
            {
                this->CurValSet(this->curFocusIndex);
                return true;
            }
        }
    }

    return QObject::eventFilter(wg, event);
}

void SelfAdjustStep2::LoadWidget()
{
    disconnect(ui->checkBoxPara, SIGNAL(clicked(bool)), this,SLOT(CheckBoxParaChg(bool)));
    // 2019-5-24 取出测试电机的额定转速
    Bit32 axisNo = HmiSelfAdjusting::GetCurAdjustAxis();
    Bit32 axisType = 0;
    Bit32 axisDevType = 0;
    Bit32 dist = 1;
    Bit32 pulse = 1;
    Bit32 mtppr = 1;
    Bit32 parmNo = 0;
    SDataProperty prop;

    HNC_AxisGetValue(HNC_AXIS_TYPE, axisNo, &axisType);
    HNC_AxisGetValue(HNC_AXIS_DEV_TYPE, axisNo, &axisDevType);

    if (axisDevType == DEV_NCUC_AXIS)
    {
        ui->label_2->setText(TR("辨识幅值"));
        ui->label_17->setText(TR("辨识频率"));
        ui->label_18->setText(TR(""));
        ui->label_3->setText(TR("(Hz)"));
        ui->labelAmp->setText(QString::number(HmiSelfAdjusting::s_SelfAdjustConf.amp));
        ui->labelFreq->setText(QString::number(HmiSelfAdjusting::s_SelfAdjustConf.freq));
        ui->labelAmp->setEnabled(false);
        ui->labelFreq->setEnabled(false);

        ui->label_15->setVisible(true);
        ui->label_5->setVisible(true);
        ui->label_8->setVisible(true);
        ui->label_24->setVisible(true);
        ui->label_7->setText(TR("(rpm)"));
        ui->label_23->setText(TR("(0.1转)"));
        ui->label_25->setText(TR("(rpm/ms)"));
        ui->labelSpeed->setVisible(true);
        ui->labelDistance->setVisible(true);
        ui->labelAcc->setVisible(true);
        if (axisType == 1)
        {
            ui->labelSpeed->setText(QString::number(HmiSelfAdjusting::s_SelfAdjustConf.speed));
            ui->labelDistance->setText(QString::number(HmiSelfAdjusting::s_SelfAdjustConf.dist)); // 单位0.1圈
            ui->labelAcc->setText(QString::number(HmiSelfAdjusting::s_SelfAdjustConf.acc));
        }
        else
        {
            ui->labelSpeed->setText(QString::number(HmiSelfAdjusting::s_SelfAdjustConf_AC.speed));
            ui->labelDistance->setText(QString::number(HmiSelfAdjusting::s_SelfAdjustConf_AC.dist)); // 单位0.1圈
            ui->labelAcc->setText(QString::number(HmiSelfAdjusting::s_SelfAdjustConf_AC.acc));
        }

        parmNo = AXIS_PARAM_ID_BASE + axisNo * AXIS_PARAM_ID_NUM + PAR_AX_PM_MUNIT;  // 电子齿轮比分子
        HNC_ParamanGetParaPropEx(parmNo, PARA_PROP_VALUE, &prop);
        dist = prop.value.val_int;
        parmNo = AXIS_PARAM_ID_BASE + axisNo * AXIS_PARAM_ID_NUM + PAR_AX_PM_PULSE;  // 电子齿轮比分子
        HNC_ParamanGetParaPropEx(parmNo, PARA_PROP_VALUE, &prop);
        pulse = prop.value.val_int;
        parmNo = AXIS_PARAM_ID_BASE + axisNo * AXIS_PARAM_ID_NUM + PAR_AX_MT_PPR;  // 电机每转脉冲数
        HNC_ParamanGetParaPropEx(parmNo, PARA_PROP_VALUE, &prop);
        mtppr = prop.value.val_int;

        fBit64 posToMove = HmiSelfAdjusting::s_SelfAdjustConf.dist * 0.1 * dist / pulse * mtppr / 1000.0;  // mm
        if (axisType == 1)
        {
            ui->labelDistanceInfo->setText(QString::number(posToMove) + "mm");
        }
        else
        {
            ui->labelDistanceInfo->setText(" ");
        }
        ui->labelDistanceInfo->setVisible(false);

        ui->checkBoxPara->setVisible(true);
        if (HmiSelfAdjusting::s_SelfAdjustConf.ctrl2 == 1)
        {
            ui->checkBoxPara->setChecked(true);
        }
        else
        {
            ui->checkBoxPara->setChecked(false);
        }
    }
    else
    {
        Bit32 curFocus = dirLayout->GetCurFocusIdx();
        if (curFocus > this->curFocusIndex)
        {
            this->curFocusIndex = 1;
        }
        ui->label_2->setText(TR("辨识最大速度"));
        ui->label_17->setText(TR("辨识加速时间"));
        ui->label_18->setText(TR("(rpm)"));
        ui->label_3->setText(TR("(ms)"));
        ui->labelAmp->setText(QString::number(HmiSelfAdjusting::s_SelfAdjustConfEcat.identifySpeed));
        ui->labelFreq->setText(QString::number(HmiSelfAdjusting::s_SelfAdjustConfEcat.identifyAcc));
        ui->labelAmp->setEnabled(true);
        ui->labelFreq->setEnabled(true);

        ui->label_15->setVisible(false);
        ui->label_5->setVisible(false);
        ui->label_8->setVisible(false);
        ui->label_24->setVisible(false);
        ui->label_7->setText(TR(""));
        ui->label_23->setText(TR(""));
        ui->label_25->setText(TR(""));
        ui->labelSpeed->setVisible(false);
        ui->labelDistance->setVisible(false);
        ui->labelAcc->setVisible(false);
        ui->labelDistanceInfo->setVisible(false);

        ui->checkBoxPara->setChecked(false);
        HmiSelfAdjusting::s_SelfAdjustConf.ctrl2 = 0;
        ui->checkBoxPara->setVisible(false);
    }

    connect(ui->checkBoxPara, SIGNAL(clicked(bool)), this,SLOT(CheckBoxParaChg(bool)));
}

void SelfAdjustStep2::CurValSet(Bit32 idx)
{
    QString editStr = "";
    Bit32 max = 0;
    Bit32 min = 0;
    Bit32 ret = -2;
    Bit32 axisNo = HmiSelfAdjusting::GetCurAdjustAxis();
    Bit32 axisType = 0;
    Bit32 parmNo = 0;
    SDataProperty prop;
    Bit32 parmVal = 0;
    bool ok = false;

    if(idx < 0 || idx >= this->dirLayoutList.count())
    {
        return;
    }

    HNC_AxisGetValue(HNC_AXIS_TYPE, axisNo, &axisType);

    parmNo = AXIS_PARAM_ID_BASE + axisNo * AXIS_PARAM_ID_NUM + SERVO_PARM_START_IDX + 87;//587：电机额定速度，单位：1rpm
    HNC_ParamanGetParaPropEx(parmNo, PARA_PROP_VALUE, &prop);
    parmVal = prop.value.val_int;

    MessageOut("");
    switch (idx)
    {
//    case 0: // 惯量识别开关
//        ui->checkBoxInertia->click();
//        break;
    case 0: // 参数搜索开关
        ui->checkBoxPara->click();
        break;
    case 1: // 辨识幅值NCUC/辨识最大速度ECAT，（NCUC无需设置）
        min = 100;
        max = 1000;
        editStr = QString::number(HmiSelfAdjusting::s_SelfAdjustConfEcat.identifySpeed);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[辨识最大速度]:"));
        if(ret == 0)
        {
            Bit32 intTmp = editStr.toInt(&ok);
            if (ok == false || intTmp < min || intTmp > max)
            {
                MessageOut(TR("输入数据无效！有效范围：%1~%2").arg(min).arg(max));
                this->dirLayoutList.at(idx)->setFocus();
                return;
            }
            else
            {
                HmiSelfAdjusting::s_SelfAdjustConfEcat.identifySpeed = intTmp;
                this->LoadWidget();
            }
        }
        break;
    case 2: // 辨识频率NCUC/辨识加速时间ECAT，（NCUC无需设置）
        min = 20;
        max = 800;
        editStr = QString::number(HmiSelfAdjusting::s_SelfAdjustConfEcat.identifyAcc);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[辨识加速时间]:"));
        if(ret == 0)
        {
            Bit32 intTmp = editStr.toInt(&ok);
            if (ok == false || intTmp < min || intTmp > max)
            {
                MessageOut(TR("输入数据无效！有效范围：%1~%2").arg(min).arg(max));
                this->dirLayoutList.at(idx)->setFocus();
                return;
            }
            else
            {
                HmiSelfAdjusting::s_SelfAdjustConfEcat.identifyAcc = intTmp;
                this->LoadWidget();
            }
        }
        break;
    case 3: // 电机运行速度
        min = 1;
        //max = 1000;
        max = parmVal * 50 /100;    // 运行速度不能超过电机额定转速50%
        if (axisType == 1)
        {
            editStr = QString::number(HmiSelfAdjusting::s_SelfAdjustConf.speed);
        }
        else
        {
            editStr = QString::number(HmiSelfAdjusting::s_SelfAdjustConf_AC.speed);
        }
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[电机运行速度]:"));
        if(ret == 0)
        {
            Bit32 intTmp = editStr.toInt(&ok);
            if (ok == false || intTmp < min || intTmp > max)
            {
                MessageOut(TR("输入数据无效！有效范围：%1~%2").arg(min).arg(max));
                this->dirLayoutList.at(idx)->setFocus();
                return;
            }
            else
            {
                if (axisType == 1)
                {
                    HmiSelfAdjusting::s_SelfAdjustConf.speed = intTmp;
                }
                else
                {
                    HmiSelfAdjusting::s_SelfAdjustConf_AC.speed = intTmp;
                }
                this->LoadWidget();
            }
        }
        break;
    case 4: // 电机运行行程
        min = 1;
        max = 1000;
        if (axisType == 1)
        {
            editStr = QString::number(HmiSelfAdjusting::s_SelfAdjustConf.dist);
        }
        else
        {
            editStr = QString::number(HmiSelfAdjusting::s_SelfAdjustConf_AC.dist);
        }
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[电机运行行程]:"));
        if(ret == 0)
        {
            Bit32 intTmp = editStr.toInt(&ok);
            if (ok == false || intTmp < min || intTmp > max)
            {
                MessageOut(TR("输入数据无效！有效范围：%1~%2").arg(min).arg(max));
                this->dirLayoutList.at(idx)->setFocus();
                return;
            }
            else
            {
                if (axisType == 1)
                {
                    HmiSelfAdjusting::s_SelfAdjustConf.dist = intTmp;
                }
                else
                {
                    HmiSelfAdjusting::s_SelfAdjustConf_AC.dist = intTmp;
                }
                this->LoadWidget();
            }
        }
        break;
    case 5: // 电机运行加速度
        min = 1;
        max = 1000;
        if (axisType == 1)
        {
            editStr = QString::number(HmiSelfAdjusting::s_SelfAdjustConf.acc);
        }
        else
        {
            editStr = QString::number(HmiSelfAdjusting::s_SelfAdjustConf_AC.acc);
        }
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[电机运行加速度]:"));
        if(ret == 0)
        {
            Bit32 intTmp = editStr.toInt(&ok);
            if (ok == false || intTmp < min || intTmp > max)
            {
                MessageOut(TR("输入数据无效！有效范围：%1~%2").arg(min).arg(max));
                this->dirLayoutList.at(idx)->setFocus();
                return;
            }
            else
            {
                if (axisType == 1)
                {
                    HmiSelfAdjusting::s_SelfAdjustConf.acc = intTmp;
                }
                else
                {
                    HmiSelfAdjusting::s_SelfAdjustConf_AC.acc = intTmp;
                }
                this->LoadWidget();
            }
        }
        break;
    }
    this->dirLayoutList.at(idx)->setFocus(); // 防止焦点丢失
}

void SelfAdjustStep2::CheckBoxStateReset(int state)
{
    if(state != Qt::Checked)
    ui->checkBoxInertia->setCheckState(Qt::Checked);
}

void SelfAdjustStep2::CheckBoxParaChg(bool checked)
{
    if (true == checked)
    {
        HmiSelfAdjusting::s_SelfAdjustConf.ctrl2 = 1;
    }
    else
    {
        HmiSelfAdjusting::s_SelfAdjustConf.ctrl2 = 0;
    }
    this->LoadWidget();
}

void SelfAdjustStep2::WidgetIn()
{
    HmiMenuManage::Instance().SetMenuValid("MSG_LAST", true);      // “上一步”有效
    HmiMenuManage::Instance().SetMenuValid("MSG_NEXT", true);      // “下一步”有效
    HmiMenuManage::Instance().SetMenuValid("MSG_BACK", false);       // “整定下一轴”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_MARK", false);      // “标记重力轴”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_ADJUST", false);     // “开始整定”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_ACCEPT", false);     // “接受”无效
    HmiMenuManage::Instance().SetMenuValid("MSG_IGNOR", false);      // “放弃”无效
    HmiMenuManage::Instance().MenuRedraw();
}

