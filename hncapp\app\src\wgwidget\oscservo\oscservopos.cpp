﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservopos.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-位置环界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年5月24日
* 说    明：
*/

#include <qmath.h>

#include "hncaxis.h"
#include "hncchan.h"
#include "hncchandef.h"
#include "hncmath.h"
#include "hncparaman.h"
#include "hncsmpl.h"
#include "hncsys.h"
#include "hmiparaman.h"
#include "loadsave.h"
#include "ncassert.h"
#include "passwd.h"
#include "smplcalc.h"

#include "hmicommon.h"
#include "hotkeycfg.h"
#include "hmioscproc.h"
#include "hmioscservo.h"
#include "oscwave.h"
#include "osclist.h"

#include "oscservopos.h"
#include "ui_oscservopos.h"

OscServoPos::OscServoPos(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoPos)
{
    ui->setupUi(this);
    m_bSampleStart = false;
    pOscWavePos = new OscWave(this, HmiOscServo::OSC_SERVO_POS, "POS");
    pOscWaveTrackErr = new OscWave(this, HmiOscServo::OSC_SERVO_POS, "TrackErr");

    ui->verticalLayout_2->addWidget(pOscWavePos);
    ui->verticalLayout_2->addWidget(pOscWaveTrackErr);
    ui->verticalLayout_2->setStretch(0, 1);
    ui->verticalLayout_2->setStretch(1, 1);
    ui->verticalLayout_2->setContentsMargins(0, 0, 0, 0);
    ui->verticalLayout_2->setSpacing(5);

    pOscWavePos->CreateGroup(pOscWaveTrackErr->GetWavePlot());

    // 参数初始化
    this->firstFlag = false;
    m_nWcsZero = 0;
    m_bWcsFlag = false;

    // 信息区设置
    ui->trackErrMaxLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    ui->trackErrMinLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);

    ui->labelAxisName->setAlignment(Qt::AlignCenter);
    ui->labelImg->setAlignment(Qt::AlignCenter);
    ui->labelImg->setText(HotKeyCfg::GetDirChgMsg());
    ui->labelImg->setFont(QFont(FONT_TYPE, 10));

    // 列表设置
    posOscList = new OscList(this);
    posOscList->installEventFilter(this);
    ui->gridLayout->addWidget(posOscList);
    posOscList->SetEditAgent(true);

//    ui->leftBtn->setShortcut(QKeySequence(Qt::AltModifier + Qt::Key_Left));
//    ui->rightBtn->setShortcut(QKeySequence(Qt::AltModifier + Qt::Key_Right));
    ui->leftBtn->setFocusPolicy(Qt::NoFocus);
    ui->rightBtn->setFocusPolicy(Qt::NoFocus);
}

OscServoPos::~OscServoPos()
{
    delete ui;
}

void OscServoPos::LoadInfo()
{
    Bit32 max =0, min = 0;
    Bit32 chn = 0;
    Bit32 axis_no = 0;
    Bit32 ncu_cycle = 1000, moving_unit = 0;
    Bit32 num = 0;
    Bit32 ch = ActiveChan();
    Bit32 type = 0;
    Bit32 offset = 0;
    Bit32 len = 0;
    fBit64 trackErrMaxVal = 0.000;
    fBit64 trackErrMinVal = 0.000;
    Bit32 logicAxisNo = HmiOscServo::GetCurAxesConf();
    if (logicAxisNo < 0 || logicAxisNo >= TOTAL_AXES_NUM)
    {
        return;
    }

    if(oscproc_get_total() > 0)
    {
        for (Bit32 i = 0; i < CHAN_AXES_NUM; ++i)
        {
            if (HmiOscServo::GetIndexAxesConf(i) > -1)
            {
                num++;
            }
        }
        if (num > MAX_REPORT_AXIS)
        {
            num = MAX_REPORT_AXIS;
        }

        HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &moving_unit);
        ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_CYCLE, &ncu_cycle);
//        HNC_SystemGetValue(HNC_SYS_ACTIVE_CHAN, &ch);

        chn = 2;
        Bit32 client = HmiOscServo::oscservo_get_sampl_client();
        HmiOscServo::SmplCalcMaxmin(chn, &max, &min, 0, oscproc_get_total());
        HNC_SamplGetConfig(client, chn, type, axis_no, offset, len);
        trackErrMaxVal = fabs(smpl_calc_follow_err_coef(axis_no))*max; // mm
        trackErrMinVal = fabs(smpl_calc_follow_err_coef(axis_no))*min; // mm

        if (HNC_DoubleCompare(fabs(trackErrMaxVal), fabs(trackErrMinVal)) >= 0)
        {
            HmiOscServo::OscservoReportRecord(fabs(trackErrMaxVal / (HmiOscServo::s_Conf[ch].stPosConf[logicAxisNo].axis_f / 1000.0)), 0);
        }
        else
        {
            HmiOscServo::OscservoReportRecord(fabs(trackErrMinVal / (HmiOscServo::s_Conf[ch].stPosConf[logicAxisNo].axis_f / 1000.0)), 0);
        }
    }
    ui->trackErrMaxLabel->setText(QString::number(trackErrMaxVal, 'f',3));
    ui->trackErrMinLabel->setText(QString::number(trackErrMinVal, 'f',3));
}

QStringList OscServoPos::GetParmList()
{
    Bit32 totalNo = 0;
	Bit32 paramId = 0;
    QStringList list;
    list.clear();

    totalNo = HmiOscServo::ServoParmGetCount(HmiOscServo::OSC_SERVO_POS);

    for(Bit32 i = 0; i < totalNo; i++)
    {
		paramId = HmiOscServo::ServoParRow2Id(i);
        if (IsParamanVisible(paramId))
        {
            list.append(QString::number(paramId));
        }
    }
    return list;
}

void OscServoPos::LoadAxisVal(Bit32 type)
{
    QString axisName = "";
    QString unit1 = "";
    QString unit2 = "";
    QStringList strList;
    Bit32 axisType = 0;

    HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetCurAxesConf(), &axisType);

    strList.clear();

    axisName = HmiOscServo::OscAxisToName(HmiOscServo::GetCurAxesConf());
    ui->labelAxisName->setText(TR("%1轴").arg(axisName));

    if(axisType == 1 || axisType == 7) // 7(主轴做进给轴使用)和1一样按直线轴处理
    {
        unit1 = TR("mm");
        unit2 = TR("um");
    }
    else
    {
        unit1 = TR("deg");
        unit2 = TR("deg/1000");
    }

    ui->label_14->setText(unit1);
    ui->label_16->setText(unit1);

    pOscWavePos->SetAxisName(QStringList() << axisName << unit1);
    pOscWaveTrackErr->SetAxisName(QStringList() << unit2);

    if(type == 1)
    {
        strList = GetParmList();
        posOscList->RefresWidget(strList);
        posOscList->LoadWidget();
    }
}

void OscServoPos::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::SETFOCUS)
    {
        bool ret = posOscList->SetTableFocus();
        if(messageValue == "CLEARFOCUS" || ret == false)
        {
            posOscList->ClearTableFocus();
        }
    }
    else if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        QStringList strList;
        strList.clear();
        if(messageValue == "INIT") // 初始化，清除上次在该界面记住的当前行
        {
            strList = GetParmList();
            posOscList->RefresWidget(strList);
        }
        this->LoadInfo();
        this->OnBtFlagChange();
        posOscList->LoadWidget();
        this->LoadAxisVal(0);
        this->SetColorStyle();

        if(HmiOscServo::GetConfChgFlag())
        {
            this->Reset();
            HmiOscServo::SetConfChgFlag(false);
        }
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_OSCSERVOSTART")
        {
            m_bSampleStart = true;
            m_bWcsFlag = false;
            this->Reset(); // 开始采样时才清除上一次的图形
        }
        else if(messageValue == "MSG_OSCSERVOSAVE")
        {
            HmiOscServo::ParmSave();
            posOscList->LoadWidget();
        }
        else if (messageValue == "MSG_OSCSERVOSTOP")
        {
            if (m_bSampleStart == true)
            {
                m_bSampleStart = false;
                m_bWcsFlag = false;
                this->LoadInfo();
            }
        }
        else if(messageValue == "OSCSERVOCOLOR")
        {
            this->SetColorStyle();
        }
    }
    else if (messageid == MsgData::REFRESH)
    {
        this->Refresh();

		Bit32 logicAxisNo = HmiOscServo::GetCurAxesNo();
		Bit32 devAxisType = HNC_ParamanGetAxisDevType(logicAxisNo);
		if (devAxisType == DEV_ETHERCAT_AXIS || devAxisType == DEV_NCUC_AXIS)
		{
            posOscList->Refresh();
		}
    }
    else if(messageid == MsgData::KEYBOARD && messageValue == "TURNTABSLEFT")
    {
        this->on_leftBtn_clicked();
    }
    else if(messageid == MsgData::KEYBOARD && messageValue == "TURNTABSRIGHT")
    {
        this->on_rightBtn_clicked();
    }
}

void OscServoPos::resizeEvent(QResizeEvent *)
{
    this->firstFlag = false;
}

bool OscServoPos::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::Paint && !firstFlag) // Paint事件在ReSize事件之后响应，用于图片第一次重绘
    {
        ui->leftBtn->setIconSize(ui->leftBtn->size());
        ui->rightBtn->setIconSize(ui->rightBtn->size());
        //ui->leftBtn->setIcon(PixMapToSize(ui->leftBtn->size(), "../pic/left-2.png"));
        //ui->rightBtn->setIcon(PixMapToSize(ui->rightBtn->size(), "../pic/right-1.png"));

        this->firstFlag = true;
        this->OnBtFlagChange(); // 解决初次进入界面时，redraw消息在paint事件前响应，导致界面刷新错误
    }
    return QObject::eventFilter(target, event);
}

void OscServoPos::OnBtFlagChange()
{
	HmiOscServo::OscservoSamplReset();
	HmiOscServo::OscservoInit();
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();
    if(curAxesIndex <= 0)
    {
        HmiOscServo::SetCurAxesIndex(0);
        ui->leftBtn->setProperty("valid",false);
        ui->rightBtn->setProperty("valid",true);
        ui->leftBtn->style()->polish(ui->leftBtn);
        ui->rightBtn->style()->polish(ui->rightBtn);
        return;
    }
    if(HmiOscServo::GetIndexAxesConf(curAxesIndex + 1) < 0)
    {
        ui->leftBtn->setProperty("valid",true);
        ui->rightBtn->setProperty("valid",false);
        ui->leftBtn->style()->polish(ui->leftBtn);
        ui->rightBtn->style()->polish(ui->rightBtn);
        return;
    }
    ui->leftBtn->setProperty("valid",true);
    ui->rightBtn->setProperty("valid",true);
    ui->leftBtn->style()->polish(ui->leftBtn);
    ui->rightBtn->style()->polish(ui->rightBtn);
}

void OscServoPos::on_leftBtn_clicked()
{
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();

    if (oscproc_get_stat() == OSC_PROC_START)
    {
        MessageOut(TR("采样中禁止轴切换!"));
        return;
    }

    if(curAxesIndex <= 0)
    {
        HmiOscServo::SetCurAxesIndex(0);
    }
    else
    {
        curAxesIndex--;
        HmiOscServo::SetCurAxesIndex(curAxesIndex);
    }
    this->LoadAxisVal(1);
    this->OnBtFlagChange();
    oscproc_smpldata_reset();
    //HmiOscServo::OscservoLoadGcode(1); // 重新生成并加载G代码
    this->Reset(); // 清除图形
    ResetInfo();        // 清除调机报表的显示数据
}

void OscServoPos::on_rightBtn_clicked()
{
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();

    if (oscproc_get_stat() == OSC_PROC_START)
    {
        MessageOut(TR("采样中禁止轴切换!"));
        return;
    }

    if(curAxesIndex >= (TOTAL_AXES_PER_CHN - 1))
    {
        curAxesIndex = TOTAL_AXES_PER_CHN - 1;
        HmiOscServo::SetCurAxesIndex(curAxesIndex);
    }
    else if(HmiOscServo::GetIndexAxesConf(curAxesIndex + 1) < 0)
    {
        return;
    }
    else
    {
        curAxesIndex++;
        HmiOscServo::SetCurAxesIndex(curAxesIndex);
    }
    this->LoadAxisVal(1);
    this->OnBtFlagChange();
    oscproc_smpldata_reset();
    //HmiOscServo::OscservoLoadGcode(1); // 重新生成并加载G代码
    this->Reset(); // 清除图形
    ResetInfo();        // 清除调机报表的显示数据
}

void OscServoPos::Reset()
{ // 清空图形
    pOscWavePos->ClearPoint();
    pOscWaveTrackErr->ClearPoint();
    this->lastEndPos = 0;
}

void OscServoPos::ResetInfo()
{
    fBit64 trackErrMaxVal = 0.000;
    fBit64 trackErrMinVal = 0.000;

    ui->trackErrMaxLabel->setText(QString::number(trackErrMaxVal, 'f',3));
    ui->trackErrMinLabel->setText(QString::number(trackErrMinVal, 'f',3));
}

void OscServoPos::Refresh()
{
    QVector<double> x;
    QVector<double> y0;
    QVector<double> y1;
    QVector<double> y2;

    Bit32 i = 0;
    Bit32 stPos = 0;
    Bit32 edPos= 0;
    Bit64 *ch0_addr = NULL;
    Bit64 *ch1_addr = NULL;
    Bit64 *ch2_addr = NULL;

    Bit32 client = HmiOscServo::oscservo_get_sampl_client();
    Bit32 type = 0;
    Bit32 offset = 0;
    Bit32 len = 0;
    Bit32 axisNo = 0;
//    Bit32 tmp = 0;
    fBit64 yConf = 0;
    Bit32 off = 0;

    if (oscproc_get_stat() != OSC_PROC_START)
    {
        this->lastEndPos = 0; // 停止后需要置零
        return;
    }

    x.clear();
    y0.clear();
    y1.clear();
    y2.clear();

    stPos = this->lastEndPos;
    edPos = oscproc_get_pos();
    this->lastEndPos = edPos;

    ch0_addr = oscproc_get_smpldata(0);
    ch1_addr = oscproc_get_smpldata(1);
    ch2_addr = oscproc_get_smpldata(2);
    if (NULL == ch0_addr || NULL == ch1_addr || NULL == ch2_addr)
    {
        return;
    }

    HNC_SamplGetConfig(client, 0, type, axisNo, offset, len);

    if (!m_bWcsFlag)
    {
        HNC_AxisGetValue(HNC_AXIS_WCS_ZERO, axisNo, &m_nWcsZero);
        HmiOscServo::SetWcsZero(m_nWcsZero);
        m_bWcsFlag = true;
    }
//    HNC_AxisGetValue(HNC_AXIS_WCS_ZERO, axisNo, &tmp);
    for (i = stPos+1; i < edPos; ++i)
    {
        yConf = smpl_calc_dist_coef(axisNo)*1000.0;
        off = -yConf* m_nWcsZero;
        y0.append(ch0_addr[i] * yConf + off); // 指令位置：mm
        y1.append(ch1_addr[i] * yConf + off); // 实际位置：mm
        y2.append(ch2_addr[i] * fabs(smpl_calc_follow_err_coef(axisNo)*1000.0)); // 跟踪误差um

        x.append(i * oscproc_get_smpl_period());
    }

    pOscWavePos->LineZeroAddPoint(x, y0);
    pOscWavePos->LineOneAddPoint(x, y1);
    pOscWavePos->WaveReplot();
    pOscWaveTrackErr->LineZeroAddPoint(x, y2);
    pOscWaveTrackErr->WaveReplot();
}

void OscServoPos::SetColorStyle()
{
    // 默认黑色风格
    QColor bk(0, 0, 0); // 背景
    QColor gd(0, 0, 0); // 网格
    QColor ft(0, 0, 0); // 字体颜色
    QColor c1(0, 0, 0); // 曲线1
    QColor c2(0, 0, 0); // 曲线2
    QColor c3(0, 0, 0); // 曲线3
    QColor c4(0, 0, 0); // 曲线4

    HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

    QPalette palette;
    palette.setColor(QPalette::Background, bk);
    ui->frame->setAutoFillBackground(true);
    ui->frame->setPalette(palette);

    pOscWavePos->SetColor(bk, gd, ft, c1, c2, c3, c4);
    pOscWaveTrackErr->SetColor(bk, gd, ft, c3, c4, c3, c4);
}
