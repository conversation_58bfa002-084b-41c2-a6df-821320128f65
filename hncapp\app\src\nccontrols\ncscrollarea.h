﻿#ifndef NCSCROLLAREA_H
#define NCSCROLLAREA_H

#include <QSpacerItem>
#include "containerwidget.h"

#include "dirmovelayout.h"

namespace Ui {
class NcScrollArea;
}

class NcScrollArea: public ContainerWidget
{
    Q_OBJECT
public:
    NcS<PERSON>roll<PERSON>rea(QWidget *parent);
    ~NcScrollArea();
    void InstallInsideObjEventFilter(QWidget *wg);

    typedef enum _NC_SCROLL_AREA_TYPE
    {
        BLK_TIPS,
        BLK_LABEL,
        BLK_LINE,
        BLK_EDIT,
        BLK_COMBOBOX,

        BLK_TYPE_COUNT,
    }NC_SCROLL_AREA_TYPE;

    bool eventFilter(QObject *target, QEvent *event);
    void Redraw();
    bool Refresh();
    bool FocusOn(int row, int col);
    void ClearFoucs();
    int GetCurFocusIdx();
    void SetLastFocus(); // 重设储存的焦点

    void ResetFocus();
signals:
    void FoucuChanged(int idx);

protected:
    DirMoveLayout *m_pDirMoveLayout;
    QSpacerItem* m_pHorizontalSpacer;

    QList<QWidget *> m_widgets;
    QList<Bit32> m_nWidgetIdx;

    QList<QWidget *> m_scrollAreaItems;
    QList<Bit32> m_nItemIdx;

    QFont m_titleFont;
    QFont m_tipsFont;
    QFont m_nameFont;
    QFont m_editFont;

    void PageUp();
    void PageDown();

    virtual Bit32 GetItemsCount() = 0;
    virtual NC_SCROLL_AREA_TYPE GetItemsType(Bit32 idx) = 0;
    virtual QString GetItemsName(Bit32 idx) = 0;
    virtual QString GetItemsVal(Bit32 idx) = 0;
    virtual QStringList GetItemsComboxList(Bit32 idx) = 0;
    virtual void SetItemsProperty(Bit32 idx, QWidget *w);

    virtual ENMoveDirection DirMoveContentChanged(Bit32 idx, QString value);
    virtual QValidator* GetValidator(Bit32 idx);
    virtual QVariant RoleStyle(Bit32 idx, int role);
    virtual QString GetDataInfo(Bit32 idx);
    virtual NcDataLimit InputDataLimit(Bit32 idx);
    virtual bool GetItemsVisible(Bit32 idx);
protected slots:
    void FocusChangedSlot(int idxInput);

private:
    Ui::NcScrollArea *ui;

    Bit32 GetScrollAreaIdx(Bit32 row);

    ENMoveDirection DirMoveContentChanged(Bit32 row, Bit32 col, QString value);
    QValidator* GetValidator(Bit32 row, Bit32 col);
    QVariant RoleStyle(Bit32 row, Bit32 col, int role);
    QString GetDataInfo(Bit32 row, Bit32 col);
    NcDataLimit InputDataLimit(Bit32 row, Bit32 col);
};

#endif // NCSCROLLAREA_H
