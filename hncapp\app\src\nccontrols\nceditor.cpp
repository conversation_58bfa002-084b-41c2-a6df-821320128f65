﻿/*!
 * @file nceditor.cpp
 * @brief 编辑器控件
 * @note 用于程序编辑界面
 *
 * @version V1.00
 * @date 2017/11/22
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */
#include <QKeyEvent>
#include <QPainter>
#include <QScrollBar>
#include <QProcess>
#include <QCoreApplication>

#include "hncdatadef.h"
#include "hncchan.h"

#include "common.h"
#include "hmicommon.h"
#include "msgchan.h"
#include "msgdata.h"
#include "hmipaintercolor.h"

#include "nceditor.h"

const Bit32 LINE_PAGE_NUM = 19; // 每页默认行数

// 编辑区缓冲容量（预留'\r'、'\n'、'\0'）
#define EDIT_LINE_SIZE (EDIT_LINE_MAX-3)

// 块操作相关
const Bit32 MAX_LINE_NUM = 5000;     // 块操作最大行数

// 查找/替换相关
const Bit32 FIND_STR_LEN = 32; // 想要查找字符串的最大长度

const Bit32 UNDO_MAX_STEP = 20; // 还原操作最多20步

const Bit32 REPLACE_TASK_ROW = 1000; // 替换TaskDo 一次查找 REPLACE_TASK_ROW 行

static QStringList m_Clip;
static Bit32 s_blockPasteOffset = 0; // 块粘贴时需要偏移的col

enum Command {
    UNDO_INSTER = 0,
    UNDO_DEL,
    UNDO_CHANGED,
};


/**
 * @brief NcEditor::NcEditor 编辑框控件
 * @param parent
 */
NcEditor::NcEditor(QWidget *parent) :
    QPlainTextEdit(parent)
{
    this->m_pLineNoArea = new LineNoArea(this);
    this->m_pLineNoArea->setFont(QFont(FONT_TYPE, 12));
    this->verticalScrollBar()->setMaximumWidth(0);
    this->verticalScrollBar()->setMaximumHeight(0);
    this->verticalScrollBar()->setVisible(false); // 隐藏滚动条
    this->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    this->setWordWrapMode(QTextOption::NoWrap); // 关闭自动换行
    this->horizontalScrollBar()->setMaximumHeight(0);
    this->horizontalScrollBar()->setVisible(false); // 隐藏水平滚动条
    this->horizontalScrollBar()->setHidden(true);
    this->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTask = new NcEditorReplaceTask(this);
    pDlgTask = new DlgInfo(this);
    pDlgTask->SetTips(TR("按[Cancel/Esc/Alt+X]键终止替换操作"));
    pDlgTask->setWindowTitle(TR("替换进行中..."));

    pDlgMsg = new DlgMsgBox(this);
    pDlgMsg->setWindowTitle(TR("提示"));
    pDlgMsg->ButtonHide(true);
    pDlgMsg->setModal(false);

	m_font = QFont(FONT_TYPE, NORMAL_FONT_SIZE);// 编辑区显示的字体

    memset(&m_editorMan, 0 , sizeof(m_editorMan));
    this->m_strFind = "";
    this->m_strReplace = "";
    this->m_nNoEditFlg = 0;
    this->m_bHedEditEnable = true;
    this->m_bMcpValid = true;
    this->SetScrStartLine(0);
    m_nSelRow = -1;
    m_nEditState = EDIT_OK;
    m_holdRestartRow = 0;
    m_bIsInputMethod = false;

    new NcHighlighter(this->document()); // 高亮注释内容

    this->UpdateLineNoAreaWidth();
    this->HighlightCurrentLine();

    this->connect(this, SIGNAL(updateRequest(QRect,int)), this, SLOT(UpdateLineNoArea(QRect,int)));
    this->connect(this, SIGNAL(cursorPositionChanged()), this, SLOT(HighlightCurrentLine()));
    this->connect(this, SIGNAL(cursorPositionChanged()), this, SLOT(CursorPosChange()));
    this->connect(this, SIGNAL(textChanged()), this, SLOT(SlotCurTextChanged()));
    this->connect(m_pTask, SIGNAL(SignalTaskFinish()), this, SLOT(ReplaceNormalFin()));
    this->connect(m_pTask, SIGNAL(SignalTaskForceEnd()), this, SLOT(ReplaceForceEnd()));
    this->connect(pDlgTask, SIGNAL(SignalTaskExit()), this, SLOT(StopTask()));

    this->connect(this, SIGNAL(cursorPositionChanged()), this, SLOT(BlockDynamicHighlight()));

    this->installEventFilter(this);
    this->setCursorWidth(-2);
    this->setTabStopWidth(20);
    this->ResizeFont();
}

NcEditor::~NcEditor()
{

}

void NcEditor::StopTask()
{
    m_pTask->StopTask();
}

/**
 * @brief NcEditor::eventFilter 按键响应
 * @param target
 * @param event
 * @return true:响应 false：不响应
 */
bool NcEditor::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent*>(event);

        if (keyEvent == QKeySequence::SelectAll
         || keyEvent == QKeySequence::Copy)
        {
            return true;
        }
        if (keyEvent->key() == Qt::Key_Escape
        || (((keyEvent->modifiers() & Qt::AltModifier) == Qt::AltModifier) && keyEvent->key() == Qt::Key_X))
        {
            m_pTask->StopTask();
        }
    }

    if(target == this && event->type() == QEvent::KeyPress) // 按键可能触发多个事件，KeyPress，paint等
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent*>(event);

        if (keyEvent->key() == Qt::Key_Escape ||
           (keyEvent->key() >= Qt::Key_F1 && keyEvent->key() <= Qt::Key_F10))         //屏蔽Esc及F1~F10按键
        {
            return true;
        }
        else
        {
            bool retVal = this->EditOnMsg(keyEvent); // 按键响应

            return retVal;
        }
    }
    else if(target == this && event->type() == QEvent::InputMethod) // 输入法事件
    {
#ifdef _WIN32
        if(IsInputEnable() != EDIT_OK)
        {
            return true;
        }

        QInputMethodEvent *inputEvent = static_cast<QInputMethodEvent *>(event);
        QString inputStr = inputEvent->commitString(); // 最终输入的文字
        if(inputStr.isEmpty() == false)
        {
            m_bIsInputMethod = true;
        }
#endif
    }
    /*else if (target == this && event->type() == QEvent::Wheel)
    {
        // 不处理鼠标滚轮事件
        return true;
    }
    else if (target == this && event->type() == QEvent::Resize)
    {
        //ResizeFont();
        return false;
    }*/

    return QObject::eventFilter(target, event);
}

void NcEditor::mouseMoveEvent(QMouseEvent* event)
{
    //忽略鼠标选择事件
    event->accept();
}

void NcEditor::ResizeFont()
{
	QFont font(FONT_TYPE);
    QRect cr = this->contentsRect();
    fBit64 fontSize = m_font.pointSizeF();
    //Bit32 reserve = 4; // 第一行距离顶部4个像素

    fontSize *= FONT_RATIO;
    font.setPointSizeF(fontSize);
    // 此处为临时方案，同样字体linux下非常大，ascent值不同
#ifdef _NCUPPER_
    QFontMetrics tmp(font);
    int fonth = tmp.lineSpacing();
    this->setFont(font);
    this->m_pLineNoArea->setFont(font); // 行号显示区
#else
#ifdef _WIN32
    QFontMetrics tmp(font);
#else
    QFontMetrics tmp(m_font);
#endif
    int fonth = tmp.lineSpacing();
#ifdef _WIN32
    this->setFont(font);
    this->m_pLineNoArea->setFont(font); // 行号显示区
#else
    this->setFont(m_font);
    this->m_pLineNoArea->setFont(m_font); // 行号显示区
#endif
#endif

    // 计算每页显示行数
    if (fonth > 0)
    {
        m_editorMan.editRowMax = cr.height() / fonth; // 编辑区最大显示行数
    }
    else
    {
        m_editorMan.editRowMax = LINE_PAGE_NUM;
    }

    if (m_editorMan.editRowMax == 0)                    // 除零保护
    {
        m_editorMan.editRowMax = LINE_PAGE_NUM;
    }


    while(this->cursorRect().height() * m_editorMan.editRowMax > cr.height())
    {
        m_editorMan.editRowMax --;
    }

    this->setMaximumBlockCount(m_editorMan.editRowMax);
}

/**
 * @brief NcEditor::CursorToPos 设置光标到指定行指定列
 * @param toLine 页内行号
 * @param toCol 行内列号
 */
void NcEditor::CursorToPos(int toLine, int toCol)
{
    QTextCursor tc = this->textCursor();
    QTextBlock blk = this->document()->findBlockByNumber(toLine);
    int toPos = blk.position();
    int length = blk.length();

    if(toCol >= length)
    {
        toPos += (length - 1);
    }
    else
    {
        toPos += toCol;
    }

    if (toPos < 0)
    {
        toPos = 0;
    }
    tc.setPosition(toPos);
    this->setTextCursor(tc);
}

/**
 * @brief NcEditor::LineNoAreaWidth 获取行号显示宽度
 * @return 宽度
 */
int NcEditor::LineNoAreaWidth()
{
    int digits = 1; // 行号位数
    if (IsShowLineNoArea())
    {
        int lineNum = m_editorMan.scrStartLine + this->blockCount() - 1; // 行数
        int max = qMax(1, lineNum);

        while (max >= 10)
        {
            max /= 10;
            ++digits;
        }
    }
    else
    {
        digits = 0;
    }
    // 行号显示宽度加间距，处理行号显示不全的问题
    int space = this->fontMetrics().width(QLatin1Char('9')) * digits + (digits + 1) * this->font().letterSpacing();
    return space;
}

/**
 * @brief NcEditor::UpdateLineNoAreaWidth 更新行号显示宽度
 */
void NcEditor::UpdateLineNoAreaWidth()
{
    int width = this->LineNoAreaWidth();
    this->setViewportMargins(width, 0, 0, 0);
}

/**
 * @brief NcEditor::UpdateLineNoArea 更新行号显示区域
 * @param rect
 * @param dy
 */
void NcEditor::UpdateLineNoArea(const QRect &rect, int dy)
{
    if (dy)
    {
        this->m_pLineNoArea->scroll(0, dy);
    }
    else
    {
        this->m_pLineNoArea->update(0, rect.y(), this->m_pLineNoArea->width(), rect.height());
    }
}

void NcEditor::SlotCurTextChanged()
{
#ifdef _WIN32
    // 输入法输入导致文本改变后，更新当前行文本
    if(m_bIsInputMethod == false)
    {
        return;
    }

    m_bIsInputMethod = false;

    Bit32 cursorRow = this->textCursor().blockNumber(); // 焦点行
    Bit32 cursorCol = this->textCursor().positionInBlock(); // 焦点列
    QString strLine = this->document()->findBlockByLineNumber(cursorRow).text();
    this->EditSetLine(this->m_editorMan.scrStartLine + cursorRow, strLine);
    EditRedraw();
    this->CursorToPos(cursorRow, cursorCol);
    MessageOut("");
#endif
}

void NcEditor::SetSelRow(Bit32 row)
{
    if (m_nSelRow != row)
    {
        m_nSelRow = row;
        HighlightCurrentLine();
    }
}

/**
 * @brief NcEditor::GetNoEditFlg 获取禁止编辑状态
 * @return 状态
 */
Bit32 NcEditor::GetNoEditFlg() const
{
    return this->m_nNoEditFlg;
}

/**
 * @brief NcEditor::SetNoEditFlg 设置禁止编辑状态
 * @param value 状态
 */
void NcEditor::SetNoEditFlg(Bit32 value)
{
    this->m_nNoEditFlg = value;
}

/**
 * @brief NcEditor::resizeEvent 编辑框大小改变响应
 * @param e
 */
void NcEditor::resizeEvent(QResizeEvent *e)
{
    QPlainTextEdit::resizeEvent(e);

    QRect cr = this->contentsRect();
    Bit32 cursorRow = this->textCursor().blockNumber(); // 焦点行
    Bit32 cursorCol = this->textCursor().positionInBlock(); // 焦点列

    ResizeFont();
    this->EditRedraw();
    this->CursorToPos(cursorRow, cursorCol);

    // 设置行号显示区的宽度,代码顺序调整解决九型#bug13455
    this->m_pLineNoArea->setGeometry(cr.left(), cr.top(), this->LineNoAreaWidth(), cr.height());
}

/**
 * @brief NcEditor::HighlightCurrentLine 高亮当前焦点行
 */
void NcEditor::HighlightCurrentLine()
{
    QList<QTextEdit::ExtraSelection> extraSelections;

    QTextEdit::ExtraSelection selection;

    QColor lineColorBack = HmiPainterColor::GetInstance()->GetProgEditCurBackgrdColor();

    QColor lineColor = HmiPainterColor::GetInstance()->GetProgEditTextColor();

    selection.format.setForeground(lineColor);
    selection.format.setBackground(lineColorBack);

    // bug #4789 解决特定字符(如:W,K,...)处于一行最后一个,后面有竖线问题
    QPen p(lineColorBack);
    QVariant prop(QVariant::Pen, &p);
    selection.format.setProperty(QTextFormat::OutlinePen, prop);

    if (m_editorMan.highLightFlag == 1) // 高亮显示行时，显示区域为行的全区域，否则不高亮显示(应用：当前行为-1，不显示高亮)
    {
        selection.format.setProperty(QTextFormat::FullWidthSelection, true);
    }
    selection.cursor = textCursor();
    selection.cursor.clearSelection();

    QTextEdit::ExtraSelection selection2;
    bool flag = SelRowBackgroudColor(selection2);

    if (flag == false || selection.cursor.position() != selection2.cursor.position())
    {
        extraSelections.append(selection);
    }
    if (flag == true)
    {
        extraSelections.append(selection2);
    }

    this->setExtraSelections(extraSelections);
}

bool NcEditor::SelRowBackgroudColor(QTextEdit::ExtraSelection &selection)
{
    Bit32 row = m_nSelRow - m_editorMan.scrStartLine;
    if (m_nSelRow < 0 || row < 0 || row >= m_editorMan.editRowMax)
    {
        return false;
    }
    QColor c = HmiPainterColor::GetInstance()->GetRandomRunRowColor();

    selection.format.setBackground(c);

    // bug #4789 解决特定字符(如:W,K,...)处于一行最后一个,后面有竖线问题
    QPen p(c);
    QVariant prop(QVariant::Pen, &p);
    selection.format.setProperty(QTextFormat::OutlinePen, prop);

    selection.format.setProperty(QTextFormat::FullWidthSelection, true);

    QTextCursor cursor(this->document());
    cursor.movePosition(QTextCursor::Start);
    cursor.movePosition(QTextCursor::Down, QTextCursor::MoveAnchor, row);
    cursor.movePosition(QTextCursor::StartOfLine);
    selection.cursor = cursor;
    return true;
}
/**
 * @brief NcEditor::lineNoAreaPaint 设置显示的行号
 * @param event
 */
void NcEditor::LineNoAreaPaint(QPaintEvent *event)
{
    QPainter painter(this->m_pLineNoArea);

    painter.fillRect(event->rect(), HmiPainterColor::GetInstance()->GetProgEditRowNumBackgrdColor());
    painter.setPen(HmiPainterColor::GetInstance()->GetProgEditRowNumTextColor());

    QTextBlock block = this->firstVisibleBlock(); // 当前显示的最上面一行内容
    int showNo = block.blockNumber() + m_editorMan.scrStartLine; // 行号
    QPointF pointf= this->contentOffset();
    int top = (int) this->blockBoundingGeometry(block).translated(pointf).top();
    int bottom = top + (int) this->blockBoundingRect(block).height();
    int width = this->m_pLineNoArea->width();
    int height = this->fontMetrics().height();

    while (block.isValid() && top <= event->rect().bottom())
    {
        if (block.isVisible() && bottom >= event->rect().top())
        {
            QString number = QString::number(showNo);
            painter.drawText(0, top, width, height,
                             Qt::AlignRight | Qt::AlignBottom, number);
        }

        block = block.next();
        top = bottom;
        bottom = top + (int) this->blockBoundingRect(block).height();

        showNo++;
    }
}

void NcEditor::BlockDynamicHighlight()
{
    long aStartRow = 0; // 块着色区域
    long aStartCol = 0;
    QTextCursor tc = this->textCursor();
    int endPos = tc.position();
    int toPos = 0;

    if ((m_editorMan.definedBlock & 0x1) != 0x1)
    {
        return;
    }

    if(this->m_editorMan.scrStartLine > this->m_editorMan.srcHeadBlock.row) // 块首在上一页
    {
        aStartRow = 0;
        aStartCol = 0;
    }
    else if(this->m_editorMan.srcHeadBlock.row >= (this->m_editorMan.scrStartLine + this->m_editorMan.editRowMax))
    {
        aStartRow = this->m_editorMan.editRowMax - 1;
        aStartCol = this->document()->findBlockByNumber(aStartRow).length() - 1;
    }
    else // 块首在当前页
    {
        aStartRow = this->m_editorMan.srcHeadBlock.row - this->m_editorMan.scrStartLine;
        aStartCol = this->m_editorMan.srcHeadBlock.col;
    }

    Bit32 cursorRow = this->textCursor().blockNumber(); // 焦点行
    Bit32 cursorCol = this->textCursor().positionInBlock(); // 焦点列
    Bit32 row = m_editorMan.scrStartLine + cursorRow; // 当前行的行号
    m_editorMan.srcEndBlock.row = row;
    m_editorMan.srcEndBlock.col = cursorCol;
    if (abs(m_editorMan.srcHeadBlock.row - m_editorMan.srcEndBlock.row) > MAX_LINE_NUM)
    {
        MessageOut(QObject::TR("块操作超过%1行，请重新定义块尾").arg(MAX_LINE_NUM));
        return;
    }

    // 块的起始边界调整，将小的坐标设置为块头，大的坐标设置为块尾
    if (m_editorMan.srcEndBlock.row < m_editorMan.srcHeadBlock.row ||
        ((m_editorMan.srcEndBlock.row == m_editorMan.srcHeadBlock.row)
         && (m_editorMan.srcEndBlock.col < m_editorMan.srcHeadBlock.col)))
    {
        m_editorMan.headBlock.row = m_editorMan.srcEndBlock.row;
        m_editorMan.headBlock.col = m_editorMan.srcEndBlock.col;
        m_editorMan.endBlock.row = m_editorMan.srcHeadBlock.row;
        m_editorMan.endBlock.col = m_editorMan.srcHeadBlock.col;
    }
    else
    {
        m_editorMan.headBlock.row = m_editorMan.srcHeadBlock.row;
        m_editorMan.headBlock.col = m_editorMan.srcHeadBlock.col;
        m_editorMan.endBlock.row = m_editorMan.srcEndBlock.row;
        m_editorMan.endBlock.col = m_editorMan.srcEndBlock.col;
    }
    if (m_editorMan.headBlock.row == m_editorMan.endBlock.row)
    {
        m_editorMan.blockPasteOffset = m_editorMan.endBlock.col - m_editorMan.headBlock.col;
    }
    else
    {
        m_editorMan.blockPasteOffset = m_editorMan.endBlock.col;
    }

    this->disconnect(this, SIGNAL(cursorPositionChanged()), this, SLOT(BlockDynamicHighlight()));

    toPos = this->document()->findBlockByNumber(aStartRow).position() + aStartCol;
    tc.setPosition(toPos);
    this->setTextCursor(tc);
    tc.setPosition(endPos, QTextCursor::KeepAnchor);
    this->setTextCursor(tc);

    this->connect(this, SIGNAL(cursorPositionChanged()), this, SLOT(BlockDynamicHighlight()));
}

/*!
 * \brief NcEditor::CursorPosChange 光标位置更改时记录
 */
void NcEditor::CursorPosChange()
{
    // 记录光标位置
    m_editorMan.m_nCursorRow = this->textCursor().blockNumber();
    m_editorMan.m_nCursorCol = this->textCursor().positionInBlock();
}

/**
 * @brief NcEditor::XorSelectBlock 块选择
 * @return 0 操作失败
 *         1 操作成功
 */
Bit32 NcEditor::XorSelectBlock(void)
{
    long aStartRow = 0; // 块着色区域
    long aEndRow = 0;
    long aStartCol = 0;
    long aEndCol = 0;
	QString strLine = "";
    QTextCursor tc = this->textCursor();
    int toPos = 0;

    if(this->m_editorMan.blockFlag == 0) // 标志是否定义了块
    {
        return 0;
    }

    if((this->m_editorMan.scrStartLine > this->m_editorMan.headBlock.row) &&
        this->m_editorMan.scrStartLine > this->m_editorMan.endBlock.row)
    {
        return 0;
    }

    if((this->m_editorMan.scrStartLine + this->m_editorMan.editRowMax) < this->m_editorMan.headBlock.row &&
       (this->m_editorMan.scrStartLine + this->m_editorMan.editRowMax) < m_editorMan.endBlock.row)
    {
        return 0;
    }

    if(this->m_editorMan.scrStartLine > this->m_editorMan.headBlock.row) // 块首在上一页
    {
        aStartRow = this->m_editorMan.scrStartLine;
        aStartCol = 0;
    }
    else // 块首在当前页
    {
        aStartRow = this->m_editorMan.headBlock.row;
        aStartCol = this->m_editorMan.headBlock.col;
    }

    if((this->m_editorMan.scrStartLine + this->m_editorMan.editRowMax) < this->m_editorMan.endBlock.row) // 块尾在下一页
    {
        aEndRow = this->m_editorMan.scrStartLine + this->m_editorMan.editRowMax - 1;
        Bit32 endRTmp = aEndRow - this->m_editorMan.scrStartLine;

        if(endRTmp >= 0 && endRTmp < this->document()->blockCount())
        {
            aEndCol = this->document()->findBlockByNumber(endRTmp).length() - 1;
        }
        else
        {
            this->EditGetLine(endRTmp, strLine);
            aEndCol = strLine.length() - 1;
        }
    }
    else // 块尾在当前页
    {
        aEndRow = this->m_editorMan.endBlock.row;
        aEndCol = this->m_editorMan.endBlock.col;
    }

    aStartRow -= this->m_editorMan.scrStartLine;
    aEndRow -= this->m_editorMan.scrStartLine;

    toPos = this->document()->findBlockByNumber(aStartRow).position() + aStartCol;

    tc.setPosition(toPos);
    this->setTextCursor(tc);

    toPos = this->document()->findBlockByNumber(aEndRow).position() + aEndCol;
    tc.setPosition(toPos, QTextCursor::KeepAnchor);
    this->setTextCursor(tc);

    return 1;
}

/**
 * @brief NcEditor::CancelBlock 取消块定义
 */
void NcEditor::CancelBlock(void)
{
    this->m_editorMan.srcHeadBlock.row = 0; // 自由定义的块的首和尾
    this->m_editorMan.srcHeadBlock.col = 0;
    this->m_editorMan.srcEndBlock.row = 0;
    this->m_editorMan.srcEndBlock.col = 0;

    this->m_editorMan.headBlock.row = 0; // 定义的块的首和尾
    this->m_editorMan.headBlock.col = 0;
    this->m_editorMan.endBlock.row = 0;
    this->m_editorMan.endBlock.col = 0;
    this->m_editorMan.blockFlag = 0; // 标志是否定义了块
    this->m_editorMan.definedBlock = 0;

    QTextCursor tc = this->textCursor();

    tc.clearSelection();
    this->setTextCursor(tc);
}

/**
 * @brief NcEditor::ClipSetBlock 设置块的大小
 * @param aFlags 1：定义块首 2：定义块尾
 */
void NcEditor::ClipSetBlock(Bit32 aFlags)
{
    Bit32 cursorRow = this->textCursor().blockNumber(); // 焦点行
    Bit32 cursorCol = this->textCursor().positionInBlock(); // 焦点列
    Bit32 row = m_editorMan.scrStartLine + cursorRow; // 当前行的行号

    switch(aFlags)
    {
    case 1: // 定义块首
        m_editorMan.srcHeadBlock.row = row;
        m_editorMan.srcHeadBlock.col = cursorCol;
        if ((m_editorMan.definedBlock & 0x1) != 0x1)
        {
            if ((m_editorMan.definedBlock & 0x2) == 0x2) // 已经定义了块尾，需要检查块头定义是否超过限定
            {
                if (abs(m_editorMan.srcHeadBlock.row - m_editorMan.srcEndBlock.row) > MAX_LINE_NUM)
                {
                    MessageOut(QObject::TR("块操作超过%1行，请重新定义块头").arg(MAX_LINE_NUM));
                    return;
                }
            }
            m_editorMan.definedBlock |= 0x1;
            MessageOut(QObject::TR("块头已选定"));
        }
        break;
    case 2: // 定义块尾
        m_editorMan.srcEndBlock.row = row;
        m_editorMan.srcEndBlock.col = cursorCol;
        if ((m_editorMan.definedBlock & 0x2) != 0x2)
        {
            if ((m_editorMan.definedBlock & 0x1) == 0x1) // 已经定义了块头，需要检查块尾定义是否超过限定
            {
                if (abs(m_editorMan.srcHeadBlock.row - m_editorMan.srcEndBlock.row) > MAX_LINE_NUM)
                {
                    MessageOut(QObject::TR("块操作超过%1行，请重新定义块尾").arg(MAX_LINE_NUM));
                    return;
                }
            }
            m_editorMan.definedBlock |= 0x2;
            MessageOut(QObject::TR("块尾已选定"));
        }
        break;
    default:
        break;
    }

    if ((m_editorMan.definedBlock & 0x3) == 0x3)
    {
        // 块的起始边界调整，将小的坐标设置为块头，大的坐标设置为块尾
        if (m_editorMan.srcEndBlock.row < m_editorMan.srcHeadBlock.row ||
            ((m_editorMan.srcEndBlock.row == m_editorMan.srcHeadBlock.row)
             && (m_editorMan.srcEndBlock.col < m_editorMan.srcHeadBlock.col)))
        {
            m_editorMan.headBlock.row = m_editorMan.srcEndBlock.row;
            m_editorMan.headBlock.col = m_editorMan.srcEndBlock.col;
            m_editorMan.endBlock.row = m_editorMan.srcHeadBlock.row;
            m_editorMan.endBlock.col = m_editorMan.srcHeadBlock.col;
        }
        else
        {
            m_editorMan.headBlock.row = m_editorMan.srcHeadBlock.row;
            m_editorMan.headBlock.col = m_editorMan.srcHeadBlock.col;
            m_editorMan.endBlock.row = m_editorMan.srcEndBlock.row;
            m_editorMan.endBlock.col = m_editorMan.srcEndBlock.col;
        }

        m_editorMan.definedBlock = 0;
        m_editorMan.blockFlag = 1; // 标志是否定义了块
        if (m_editorMan.headBlock.row == m_editorMan.endBlock.row)
        {
            m_editorMan.blockPasteOffset = m_editorMan.endBlock.col - m_editorMan.headBlock.col;
        }
        else
        {
            m_editorMan.blockPasteOffset = m_editorMan.endBlock.col;
        }

        this->XorSelectBlock(); // 选中块设置反色
    }
    else
    {
        m_editorMan.blockFlag = 0; // 标志是否定义了块
    }
}

/**
 * @brief NcEditor::ClipCopy 剪贴板：复制
 * @return EditorInfo
 */
Bit32 NcEditor::ClipCopy(void)
{
    Bit32 row = 0;
    QString strLine = "";

    if (m_editorMan.blockFlag == 0) // 标志是否定义了块
    {
        return EDIT_BLOCK_UNDEFINED;
    }

    m_Clip.clear();

    if (m_editorMan.headBlock.row == m_editorMan.endBlock.row)
    {
        this->EditGetLine(m_editorMan.headBlock.row, strLine);
        strLine = strLine.left(m_editorMan.endBlock.col);
        strLine = strLine.mid(m_editorMan.headBlock.col);
        m_Clip<<strLine;
    }
    else
    {
        for (row = m_editorMan.headBlock.row; row <= m_editorMan.endBlock.row; row++)
        {
            this->EditGetLine(row, strLine);
            if (row == m_editorMan.headBlock.row)
            {
                m_Clip<<strLine.mid(m_editorMan.headBlock.col);
            }
            else if (row == m_editorMan.endBlock.row)
            {
                m_Clip<<strLine.left(m_editorMan.endBlock.col);
            }
            else
            {
                m_Clip<<strLine;
            }
        }
    }
    s_blockPasteOffset = m_editorMan.blockPasteOffset;

    return EDIT_OK;
}

/**
 * @brief NcEditor::ClipDelete 剪贴版：删除
 * @return EditorInfo
 */
Bit32 NcEditor::ClipDelete(void)
{
    Bit32 ret = EDIT_OK;
    Bit32 row = 0;
    Bit32 col = 0;
    QString strLine = "";

    ret = this->GetEnableStat(); // 获取可编辑状态
    if(ret != EDIT_OK)
    {
        return ret;
    }

    if (m_editorMan.blockFlag == 0) // 标志是否定义了块
    {
        return EDIT_BLOCK_UNDEFINED;
    }

    if (m_editorMan.headBlock.row == m_editorMan.endBlock.row
        && m_editorMan.headBlock.col == m_editorMan.endBlock.col) // 定义的块中无字符
    {
        this->CancelBlock();
        return ret;
    }

    UndoRecordStart();

    if (m_editorMan.headBlock.row == m_editorMan.endBlock.row)
    {
        this->EditGetLine(m_editorMan.headBlock.row, strLine);
        strLine.remove(m_editorMan.headBlock.col, m_editorMan.endBlock.col - m_editorMan.headBlock.col);
        this->EditSetLine(m_editorMan.headBlock.row, strLine);
    }
    else
    {
        // 最后一行删除
        row = m_editorMan.endBlock.row;
        col = m_editorMan.endBlock.col;
        this->EditGetLine(row, strLine);
        strLine = strLine.mid(col);
        if (strLine == "\n")    // 如果仅剩下换行符
        {
            this->EditDelLine(row);
        }
        else
        {
            this->EditSetLine(row, strLine);
        }

        // 删除中间的行(必须从下向上删除)
        if ((m_editorMan.headBlock.row + 1) <= (m_editorMan.endBlock.row - 1))
        {
            Bit32 row1 = m_editorMan.headBlock.row + 1;
            Bit32 row2 = m_editorMan.endBlock.row - 1;
            for (row = row2; row >= row1; row--)
            {
                this->EditDelLine(row);
            }
        }

        // 第一行删除
        row = m_editorMan.headBlock.row;
        col = m_editorMan.headBlock.col;
        if (col == 0)
        {
            this->EditDelLine(row);
        }
        else
        {
            this->EditGetLine(row, strLine);
            strLine = strLine.left(col);
            this->EditSetLine(row, strLine);
        }
    }

    Bit32 cursorRow = this->textCursor().blockNumber(); // 焦点行
    Bit32 cursorCol = m_editorMan.headBlock.col;
    Bit32 totalNum = this->GetRowNum();

    // 设置当前行、起始行；当前列、起始列
    if (totalNum < m_editorMan.editRowMax)
    {
        SetScrStartLine(0);
        cursorRow = m_editorMan.headBlock.row;
    }
    else if (m_editorMan.headBlock.row > m_editorMan.scrStartLine + m_editorMan.editRowMax) // 块首在前页
    {
        SetScrStartLine(m_editorMan.headBlock.row - m_editorMan.editRowMax);
        cursorRow = m_editorMan.editRowMax - 1;
    }
    else if (m_editorMan.scrStartLine > m_editorMan.headBlock.row) // 块首在后页
    {
        SetScrStartLine(m_editorMan.headBlock.row);
        cursorRow = 0;
    }
    else if (totalNum < m_editorMan.scrStartLine + m_editorMan.editRowMax)
    {
        SetScrStartLine(totalNum - m_editorMan.editRowMax);
        cursorRow = m_editorMan.headBlock.row - m_editorMan.scrStartLine;
    }
    else // 块首在当前页
    {
        cursorRow = m_editorMan.headBlock.row - m_editorMan.scrStartLine;
    }

    this->CancelBlock();
    this->EditRedraw();
    this->CursorToPos(cursorRow, cursorCol);

    UndoRecordEnd();

    return ret;
}

/**
 * @brief NcEditor::ClipPaste 剪贴板：粘贴
 * @return EditorInfo
 */
Bit32 NcEditor::ClipPaste(void)
{
    Bit32 row = m_editorMan.scrStartLine + this->textCursor().blockNumber();
    Bit32 pasteRow = row;
    Bit32 col = this->textCursor().positionInBlock();
    Bit32 i = 0;
    Bit32 ret = EDIT_OK;
    QString strLine1 = "";
    QString strLine2 = "";
    QString strLine = "";

    ret = this->GetEnableStat(); // 获取可编辑状态
    if(ret != EDIT_OK)
    {
        return ret;
    }

    if (m_Clip.count() == 0)
    {
        return EDIT_CLIP_EMPTY;
    }

    UndoRecordStart();

    this->EditGetLine(row, strLine);
    strLine1 = strLine.left(col);
    strLine2 = strLine.mid(col);

    QStringList strList = m_Clip;
    strList.replace(0, strLine1 + strList.at(0));
    if (strList.last().endsWith('\n'))
    {
        strList.insert(strList.count(), "");
        strList.replace(strList.count() - 1, strLine2);
    }
    else
    {
        strList.replace(strList.count() - 1, strList.last() + strLine2);
    }

    this->EditDelLine(row);
    for (i = 0; i < strList.count(); i++)
    {
        this->EditInsLine(row + i);
        this->EditSetLine(row + i, strList.at(i));
    }

    // 当前屏幕页能够显示下，则将光标移动到粘贴内容的末尾；
    // 显示不下则需要滚动行，使复制的最后一行位于屏幕最后一行
    row += (strList.count() - 1);
    Bit32 tmpRow = row - m_editorMan.scrStartLine;
    if (tmpRow >= m_editorMan.editRowMax)
    {
        SetScrStartLine(row - m_editorMan.editRowMax + 1);
        tmpRow = m_editorMan.editRowMax - 1;
    }

    if (pasteRow == row)
    {
        if (1 != m_nInsertSpaceClose)   // 分词功能开启
        {
            Bit32 tmp = col;
            if (tmp > 0)
            {
                tmp = col - 1;
            }

            QChar c1;
            QChar c2;
            if (strLine1.count() > tmp)
            {
                c1 = strLine1.at(tmp);
            }
            if (m_Clip.isEmpty() == false && m_Clip.at(0).isEmpty() == false)
            {
                c2 = m_Clip.at(0).at(0);
            }
            if ((c1.isDigit() || (c1 == QChar('.'))) && (c2.isLetter() || c2 == QChar('#'))) // 前后拼接复合分词条件的，增加一个列位置
            {
                col += 1;
            }
        }

        col += s_blockPasteOffset;
    }
    else
    {
        col = s_blockPasteOffset;
    }

    this->CancelBlock();

    this->EditRedraw();
    this->CursorToPos(tmpRow, col);

    UndoRecordEnd();

    return ret;
}

/**
 * @brief NcEditor::ClipPaste 剪贴板：剪切
 * @return EditorInfo
 */
Bit32 NcEditor::ClipCut(void)
{
    Bit32 ret = EDIT_OK;

    ret = this->GetEnableStat(); // 获取可编辑状态
    if(ret != EDIT_OK)
    {
        return ret;
    }

    ret = this->ClipCopy(); // 复制块
    if (EDIT_OK == ret)
    {
        ret = this->ClipDelete(); // 删除块
    }
    return ret;
}

/*!
 * \brief NcEditor::GetRegExp 根据待查找字符生成校验器
 * \param find 待查找字符
 * \param chkWhole 是否全词匹配
 * \return 校验器
 * 全词匹配规则：
 * 规则1：find处于边界位置(e.g. "find xxx" "xxx find" "xxx find xxx")，只要符合该条件即可认为全词匹配
 * 规则2：如果不满足规则1，则判断find前后紧跟的字符与find首尾字符是否一致，如果首尾都不一致，则认为全词匹配
 * 首尾字母规则相同，以首字母为例进行一致性说明：
 * 1.find以数字开头(0~9 . -)，如果find前一个字符也为数字，则为一致，否则就是不一致。
 *   例如：find为"300" 则"X300" "#300" "+300"不一致 ".300" "-300" "1300"一致
 * 2.find以字母开头(A~Z),如果find前一个字符也为字母，则为一致，否则就是不一致
 *   例如：find为"IF",则"3IF" ";IF"不一致 "ENDIF"一致
 * 3.find以其他字符开头(如"#" "["),不需要判断一致性，直接认为符合匹配规则
 */
QRegExp NcEditor::GetRegExp(QString find, bool chkWhole)
{
    if(find.isEmpty())
    {
        QRegExp rx("");
        return rx;
    }
    QString rxStr = find;

    if(chkWhole)
    {
//        QString notNumEnd = "(?!\\d)"; // (?![\\d.])find表示匹配后面不为数字或.的find
//        QString notLetterEnd = "(?!A-Z)"; // (?!A-Z)find表示匹配后面不为字母的find

        QString notNum = "([^0-9]|\\b)";
        QString notLetter = "([^A-Z]|\\b)";

        QChar first = find.at(0);
        QChar last = find.at(find.length() - 1);

         // 判断首字符是否为数字
        if(first.isDigit() || first == '.' || first == '-')
        {
            rxStr = notNum + rxStr;
        }
         // 判断首字符是否为字母
        else if(first.isLetter())
        {
            rxStr = notLetter + rxStr;
        }

        // 判断末尾字符是否为数字
        if(last.isDigit() || last == '.')
        {
           rxStr = rxStr + notNum;
        }
        // 判断末尾字符是否为字母
        else if(last.isLetter())
        {
           rxStr = rxStr + notLetter;
        }
    }

    QRegExp rx(rxStr);
    return rx;
}

/*!
 * \brief FindStrInLine 查找字符串
 * \param lineStr 行文本
 * \param find 待查找字符
 * \param dir 查找方向
 * \param chkWhole 是否全词匹配
 * \return 字符位置 -1没找到
 */
Bit32 NcEditor::FindStrInLine(QString lineStr, QRegExp rx, Bit32 dir, Bit32 offset)
{
    if(lineStr.isEmpty())
    {
        return -1;
    }

    Bit32 pos = 0;
    if(dir == 0)
    {
        pos = rx.indexIn(lineStr, offset);
    }
    else
    {
        if(offset > 0)
        {
            pos = rx.lastIndexIn(lineStr.left(offset));
        }
        else
        {
            pos = rx.lastIndexIn(lineStr);
        }
    }

    return pos;
}

bool NcEditor::IsInsertSpaceEnable()
{
    bool enable = true;
    Bit32 maxLine = this->GetRowNum(); // 文件最大行数

    // 1、分词功能对大程序查找效率有较大影响
    // 2、分词功能开启，并且程序大于50000行，则默认强制关掉分词功能
    // 3、强制关掉分词功能进行查找，如果查找的内容包含空格，可能会有查找不准确的问题
    if (m_nInsertSpaceClose == 0 && maxLine > 50000)
    {
        enable = false;
    }

    return enable;
}

/*!
 * \brief NcEditor::FindEditString 查找字符
 * \param startCol 起始查找列
 * \param startRow 起始查找行
 * \param endCol 截止查找列
 * \param endRow 截止查找行
 * \param chkWhole 是否全词匹配
 * \return 0:未找到 1：找到了
 */
Bit32 NcEditor::FindEditString(Bit32 startRow, Bit32 startCol, Bit32 endRow, Bit32 endCol, bool chkWhole)
{
    Bit32 i = 0;
    Bit32 dir = 0; // 查找方向0:下 1:上
    Bit32 maxLine = 0; // 文件最大行数
    Bit32 result = 0;
    Bit32 posit = -1; // 查找位置
    Bit32 selen = 0; // 待查找字符串长度
    QString strLine = "";
    Bit32 row = 0;
    Bit32 col = 0;
    QRegExp rx = GetRegExp(this->m_strFind, chkWhole);

    selen = this->m_strFind.length(); // 待查找字符串长度
    maxLine = this->GetRowNum(); // 文件最大行数

    // 1、分词功能对大程序查找效率有较大影响
    // 2、分词功能开启，并且程序大于50000行，则默认强制关掉分词功能
    // 3、强制关掉分词功能进行查找，如果查找的内容包含空格，可能会有查找不准确的问题
    bool insSpaceEnabled = this->IsInsertSpaceEnable();
    if(startRow < 0 || endRow < 0 || startRow >= maxLine ||
            endRow >= maxLine || startCol < 0 || endCol < 0)
    {
        return 0;
    }
     // 根据查找起始位置判断查找方向
    if(startRow > endRow || ((startRow == endRow) && endCol < startCol))
    {
        dir = 1;
    }

    if(dir == 0) // 向下查找
    {
        for(i = startRow; i <= endRow; i++)
        {
            // 获取该行内容
            result = this->EditGetLine(i, strLine, insSpaceEnabled);
            if(result != 0)
            {
                continue;
            }

            // 找到相符字符串
            if(i == startRow)    // 起始位置所在行
            {
                posit = FindStrInLine(strLine, rx,dir, startCol);
            }
            else
            {
                if(i == endRow)
                {
                    strLine = strLine.left(endCol);
                }
                posit = FindStrInLine(strLine, rx,dir);
            }

            if(posit >= 0)
            {
                break;
            }
        }
    }
    else // 向上查找
    {
        for(i = startRow; i >= endRow; i--)
        {
            result = this->EditGetLine(i, strLine, insSpaceEnabled);
            if(result != EDIT_OK)
            {
                continue;
            }

            // 找到相符字符串
            if(i == startRow)    // 起始光标所在行
            {
                 // 如果此时光标正好在带查找字符的位置，直接调用lastIndexOf光标不会变
                if(strLine.mid(startCol - selen, selen) == m_strFind)
                {
                    QTextCursor tc = this->textCursor();
                    if(tc.anchor() != tc.position() && startCol - selen >= 1)
                    {
                        posit = FindStrInLine(strLine, rx, dir, startCol - selen);
                    }
                    else if(tc.anchor() == tc.position() && startCol >= 1)
                    {
                        posit = FindStrInLine(strLine, rx, dir, startCol);
                    }
                    else
                    {
                        posit = -1;
                    }
                }
                else
                {
                    posit = FindStrInLine(strLine, rx, dir, startCol);
                }
            }
            else
            {
                if(i == endRow)
                {
                    strLine = strLine.mid(endCol);
                }
                posit = FindStrInLine(strLine, rx, dir);
            }
            if(posit >= 0)
            {
                break;
            }
        }
    }

    // 程序大于50000行强制关掉分词功能时，导致关键词光标位置计算错误
    // 解决方法：再次获取关键词的该行（分词功能打开时），重新计算关键词位置
    if(posit >= 0 && insSpaceEnabled == false)
    {
        result = this->EditGetLine(i, strLine);
        if(dir == 0)   // 向下查找
        {
            if(i == startRow)    // 当前光标所在行
            {
                posit = FindStrInLine(strLine.mid(startCol), rx, dir);
                if(posit >= 0)
                {
                    posit += startCol;
                }
            }
            else
            {
                if(i == endRow)
                {
                    strLine = strLine.left(endCol);
                }
                posit = FindStrInLine(strLine, rx, dir);
            }
        }
        else    // 向上查找
        {
            // 找到相符字符串
            if(i == startRow)    // 当前光标所在行
            {
                 // lastIndexOf 如果待查找字符串首字符刚好与from位置的首字符一致，会先往后匹配，如果匹配一致就返回当前位置
                 // 如果此时光标正好在带查找字符的位置，直接调用lastIndexOf光标不会变
                if(strLine.mid(startCol - selen, selen) == m_strFind)
                {
                    if(startCol - selen - 1 > 0)
                    {
                        posit = FindStrInLine(strLine.left(startCol - selen - 1), rx, dir);
                    }
                }
                else
                {
                    posit = FindStrInLine(strLine.left(startCol), rx, dir);
                }
            }
            else
            {
                if(i == endRow)
                {
                    strLine = strLine.mid(endCol);
                }
                posit = FindStrInLine(strLine, rx, dir);
            }
        }
    }

    if(posit < 0) // 没找到
    {
        return 0;
    }

    if(chkWhole) // 全词匹配时取得首字符位置可能有偏差
    {
        posit = strLine.indexOf(this->m_strFind, posit, Qt::CaseInsensitive);
    }

    if(m_editorMan.blockFlag == 1)
    {
        this->CancelBlock(); // 取消块定义
    }

    // 找到的行不在当前页
    if(i < m_editorMan.scrStartLine || i >= (m_editorMan.scrStartLine + m_editorMan.editRowMax))
    {
        Bit32 maxRowNum = this->GetRowNum(); // 最大行数

        if(i > maxRowNum - m_editorMan.editRowMax && maxRowNum - m_editorMan.editRowMax >= 0)
        {
            SetScrStartLine(maxRowNum - m_editorMan.editRowMax);
        }
        else
        {
            SetScrStartLine(i);
        }
        this->EditRedraw();
    }
    col = posit;

    row = i - m_editorMan.scrStartLine;

    QTextCursor tc = this->textCursor();
    int toPos = 0;

    toPos = this->document()->findBlockByNumber(row).position() + col;
    tc.setPosition(toPos);
    this->setTextCursor(tc);

    toPos += selen;
    tc.setPosition(toPos, QTextCursor::KeepAnchor);
    this->setTextCursor(tc);

    return 1;
}

/*!
 * \brief NcEditor::GetRowStringLenth 获取行文本长度
 * \param row 行号
 * \return 长度
 * 注意：返回长度值可能是关闭分词功能后的长度，只能查找功能调用
 */
Bit32 NcEditor::GetRowStringLenth(Bit32 row)
{
    Bit32 rowCount = this->GetRowNum();

    if(row < 0 || row >= rowCount)
    {
        return EDIT_LINE_MAX;
    }

    QString strLine = "";
    bool insSpaceEnabled = this->IsInsertSpaceEnable();
    Bit32 ret = this->EditGetLine(row, strLine, insSpaceEnabled);
    if(ret != EDIT_OK)
    {
        return EDIT_LINE_MAX;
    }
    else
    {
        return strLine.length();
    }
}

/**
 * @brief NcEditor::FindString // 从当前行开始查找指定字符串
 * @param type 查找方向 0：向下查找 1：向上查找
 * @return 查找成功：EDIT_OK；查找失败：EDIT_PASS
 */
Bit32 NcEditor::FindString(QString find, Bit32 dir, bool chkWhole, bool cycle)
{
    if(find.isEmpty())
    {
        return EDIT_OK;
    }

    Bit32 ret = 0;
    Bit32 stRow = m_editorMan.scrStartLine + this->textCursor().blockNumber();
    Bit32 stCol = this->textCursor().positionInBlock();
    Bit32 endRow = 0;
    Bit32 endCol = 0;

    if(dir == 0)
    {
        endRow = this->GetRowNum() - 1;
        endCol = this->GetRowStringLenth(endRow);
    }

    m_strFind = find;
    // 查找指定字符串
    ret = this->FindEditString(stRow, stCol,endRow, endCol, chkWhole); // 从当前行开始查找
    if(ret == 0) // 没找到
    {
        if(cycle) // 循环找
        {
            if(dir == 0)
            {
                stCol = this->GetRowStringLenth(stRow); // 防止光标在待查找字符中间时查找不到的情况
                ret = this->FindEditString(0,0,stRow, stCol, chkWhole);//从第0行0列开始查找
            }
            else
            {
                endRow = this->GetRowNum() - 1;
                endCol = this->GetRowStringLenth(endRow);
                ret = this->FindEditString(endRow, endCol, stRow,0, chkWhole);//从最后一行开始查找
            }
        }
        if(ret == 0)
        {
            return EDIT_PASS; //直接返回
        }
    }
    return EDIT_OK;
}

Bit32 NcEditor::EditFind(QString find, Bit32 dir, bool chkWhole, bool cycle)
{
    if(find.isEmpty())
    {
        return 0;
    }
    this->m_strFind = find;
    this->m_strReplace = ""; // 清空上次替换内容
    Bit32 ret = EDIT_OK;

    // 查找指定字符串
    ret = this->FindString(find, dir, chkWhole, cycle);

    return ret;
}

void NcEditor::EditDlgFindAndRepl(QString find, QString repl, bool chkWhole)
{
    if(find.isEmpty())
    {
        return;
    }
    this->m_strFind = find;
    this->m_strReplace = repl;

    ReplaceString(chkWhole);
}

void NcEditor::EditDlgFindAndReplAll(QString find, QString repl, bool chkWhole)
{
    if(find.isEmpty())
    {
        return;
    }
    this->m_strFind = find;
    this->m_strReplace = repl;
    ReplaceAll(chkWhole);
}

/**
 * @brief NcEditor::EditRedraw 编辑窗口重绘
 */
void NcEditor::EditRedraw()
{
    Bit32 totalNum = this->GetRowNum();
    QString str = "";
    QString strLine = "";

    for(Bit32 ii = 0; ii < m_editorMan.editRowMax && (ii + m_editorMan.scrStartLine < totalNum); ii++)
    {
        this->EditGetLine(ii + m_editorMan.scrStartLine, strLine);
        str += strLine;
    }
    if(str.endsWith('\n') && totalNum > 1)
    {
        str.chop(1);
    }

    // 设置文本内容会触发cursorPositionChanged()信号，导致光标位置为[0,0]
    this->disconnect(this, SIGNAL(cursorPositionChanged()), this, SLOT(CursorPosChange()));
    this->setPlainText(str);
    this->connect(this, SIGNAL(cursorPositionChanged()), this, SLOT(CursorPosChange()));
    this->UpdateLineNoAreaWidth();
}

void NcEditor::SetHoldRestartRow(Bit32 row)
{
    m_holdRestartRow = row;
}

Bit32 NcEditor::GetHoldRestartRow()
{
    return m_holdRestartRow;
}

/*!
 * \brief NcEditor::SetHoldDcdRow 设置进给保持时的超前解释行号
 * \param row 行号
 */
void NcEditor::SetHoldDcdRow(Bit32 row)
{
    m_nHoldDcdRow = row;
}

Bit32 NcEditor::GetHoldDcdRow()
{
    return m_nHoldDcdRow;
}

void NcEditor::GetDefEditParam(Bit32 *showNo, Bit32 *insertSpaceClose, Bit32 *smallCharacter)
{
    if (showNo != NULL)
    {
        ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_GLNO_DISP, showNo); // 是否显示行号
    }
    if (smallCharacter != NULL)
    {
        ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_PROG_SMALL_CHARACTER, smallCharacter);
    }
    if (insertSpaceClose != NULL)
    {
        ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_INSERT_SPACE_CLOSE, insertSpaceClose);
    }
}

/**
 * @brief EditInit 编辑器初始化
 * @param type 编辑窗口类型
 * @param showIndex 是否显示行索引；1：显示；0：不显示
 * @param font 编辑器的字体
 * @param recordCursor 记录光标位置  true：进入编辑界面时回到上次光标所在位置
 * @return 0：成功；-1：失败
 */
Bit32 NcEditor::EditInit(EditorType type, Bit32 showIndex, QFont font, Bit32 insertSpaceClose, bool recordCursor)
{
    this->m_nNoEditFlg = 0;

    UndoRecordInit();

    m_editorMan.highLightFlag = 1;

    if (m_font != font)
    {
        m_font = font;
        ResizeFont();
    }

    m_nInsertSpaceClose = insertSpaceClose;

    m_editorMan.m_nEditorType = type;
    m_editorMan.showIndex = showIndex;
    this->m_pLineNoArea->setVisible(IsShowLineNoArea());

    if (type == VIEW_TYPE)
    {
        this->setReadOnly(true);
    }

    if (recordCursor == true)
    {
        SetScrStartLine(m_editorMan.scrStartLine); // 回到光标所在位置时，设置起始行
    }
    else
    {
        SetScrStartLine(0);
    }

    this->CancelBlock(); // 剪贴板复位

    this->EditRedraw();

    if (recordCursor == true) // 回到光标所在位置时，设置光标为上次记录位置
    {
        if (this->blockCount() > 1 && m_editorMan.m_nCursorRow <= 0 && m_editorMan.scrStartLine == 0)
        {
            m_editorMan.m_nCursorRow = 1; // 光标在文件头时且编辑界面有内容时，光标行默认设置为第一行
        }
        this->CursorToPos(m_editorMan.m_nCursorRow, m_editorMan.m_nCursorCol);
    }
    else
    {
        if(this->blockCount() > 1)
        {
            this->CursorToPos(1);
        }
    }

    return 0;
}

/**
 * @brief NcEditor::EditGetLine 获取当前行文本字符串（去除行末回车换行符）
 * @param row 行号
 * @param text 存储行文本
 * @param insSpaceEnabled 分词功能使能
 * @note 获取的是指定程序行原始文本，而不是正在编辑而未保存的内容
 * @return EDIT_OK:成功；其他：获取失败；
 */
Bit32 NcEditor::EditGetLine(Bit32 row, QString &text, bool insSpaceEnabled)
{
    //获取当前行
    Bit32 ret = this->GetRow(row, text);
    if (ret == 0)
    {
        if (1 != m_nInsertSpaceClose && insSpaceEnabled)
        {
            InsertSpace(text);
        }
        text.replace('\r', ""); // 去掉'\r'，避免在qt编辑器中'\r'处理成换行
        return EDIT_OK;
    }

    return EDIT_ERR;
}

/**
 * @brief NcEditor::EditSetLine 设置程序行为指定文本字符串（行末加上换行符）
 * @param row 行号
 * @param text 存储行文本
 * @return EDIT_OK：设置成功
 *         EDIT_ERR：设置失败
 */
Bit32 NcEditor::EditSetLine(Bit32 row, QString text)
{
    Bit8 buf[EDIT_LINE_MAX] = {0};
    Bit32 ret = 0;
    QString strLine = "";
    QString oldStrLine = "";

    // 限制程序行有效字符数
    strlcpy(buf, text.toAscii().data(), EDIT_LINE_SIZE);
    strLine = buf;

    strLine.replace('\r', "");
    if (!strLine.endsWith('\n'))
    {
        strLine += "\n";
    }

    // 设置当前行
    this->EditGetLine(row, oldStrLine);
    ret = this->SetRow(row, strLine);
    if (ret != 0)
    {
        return EDIT_ERR;
    }
    else
    {
        if (m_bUndoRecord)
        {
            EditUndoData undoData;
            undoData.command = UNDO_CHANGED;
            undoData.row = row;
            undoData.preCmdText = oldStrLine;
            undoData.endCmdText = strLine;
            m_recordUndoInfo.editUndoData.append(undoData);
        }

        return EDIT_OK;
    }
}

Bit32 NcEditor::EditDelLines(Bit32 row, Bit32 count)
{
    for (Bit32 i = row + count - 1; i >= row; i--)
    {
        Bit32 ret = this->EditDelLine(i);
        if (ret != 0)
        {
            return EDIT_ERR;
        }
    }
    return EDIT_OK;
}

Bit32 NcEditor::EditDelLine(Bit32 row)
{
    Bit32 ret = 0;
    QString oldStrLine = "";

    this->EditGetLine(row, oldStrLine);

    ret = this->DeleteRows(row, 1);
    if (ret != 0)
    {
        return EDIT_ERR;
    }
    else
    {
        if (this->GetRowNum() == 0)
        {
            this->InsertRows(0);
        }
        if (m_bUndoRecord)
        {
            EditUndoData undoData;
            undoData.command = UNDO_DEL;
            undoData.row = row;
            undoData.preCmdText = oldStrLine;
            undoData.endCmdText = "";
            m_recordUndoInfo.editUndoData.append(undoData);
        }

        return EDIT_OK;
    }
}

Bit32 NcEditor::EditInsLine(Bit32 row)
{
    Bit32 ret = 0;
    ret = this->InsertRows(row);
    if (ret != 0)
    {
        return EDIT_ERR;
    }
    else
    {
        if (m_bUndoRecord)
        {
            EditUndoData undoData;
            undoData.command = UNDO_INSTER;
            undoData.row = row;
            undoData.preCmdText = "";
            undoData.endCmdText = "";
            m_recordUndoInfo.editUndoData.append(undoData);
        }

        return EDIT_OK;
    }
}

/**
 * @brief NcEditor::EditSaveFocusLine 保存焦点行
 */
void NcEditor::EditSaveFocusLine(void)
{
    Bit32 cursorRow = this->textCursor().blockNumber();
    QString tmpVal = this->document()->findBlockByNumber(cursorRow).text();
    this->EditSetLine(this->m_editorMan.scrStartLine + cursorRow, tmpVal);
}

/**
 * @brief NcEditor::EditHome 文件首
 * @return EditorInfo
 */
Bit32 NcEditor::EditHome(void)
{
    Bit32 ret = EDIT_PASS;
    int pos = this->textCursor().position();

    if (m_editorMan.scrStartLine > 0 || pos > 0) // 非首页首行
    {
        SetScrStartLine(0);
        this->CursorToPos(0,0);
        this->EditRedraw();
        ret = EDIT_OK;
    }

    if(m_editorMan.blockFlag == 1)
    {
        this->CancelBlock(); // 取消块定义
    }

    return ret;
}

/**
 * @brief NcEditor::EditEnd 文件尾
 * @return EditorInfo
 */
Bit32 NcEditor::EditEnd(void)
{
    Bit32 ret = EDIT_PASS;
    Bit32 maxRowNum = this->GetRowNum(); // 最大行数
    Bit32 cursorRow = this->textCursor().blockNumber();
    Bit32 cursorCol = this->textCursor().positionInBlock();
    Bit32 len = this->document()->lastBlock().length() - 1; // 最后一行长度
    Bit32 curLine = m_editorMan.scrStartLine + cursorRow;

    if (curLine < maxRowNum - 1 || (curLine == maxRowNum - 1 && cursorCol < len))
    {
        if (m_editorMan.scrStartLine + m_editorMan.editRowMax < maxRowNum) // 非最后一页
        {
            SetScrStartLine(maxRowNum - m_editorMan.editRowMax);
            this->EditRedraw();
        }

        this->moveCursor(QTextCursor::End);
        ret = EDIT_OK;
    }

    if(m_editorMan.blockFlag == 1)
    {
        this->CancelBlock(); // 取消块定义
    }

    return ret;
}

/**
 * @brief NcEditor::EditDelLine 行删除
 * @return EditorInfo
 */
Bit32 NcEditor::EditDelLine(void)
{
    Bit32 ret = EDIT_PASS;
    Bit32 maxRowNum = this->GetRowNum(); // 最大行数
    int pos = this->textCursor().position();
    int len = this->document()->findBlock(pos).length(); //当前行长度
    Bit32 row = m_editorMan.scrStartLine + this->textCursor().blockNumber();

    ret = this->GetEnableStat(); // 获取可编辑状态
    if(ret != EDIT_OK)
    {
        return ret;
    }

    if (maxRowNum < 1 || (maxRowNum <= 1 && len <= 0))
    {
        return ret;
    }

    if (maxRowNum <= 1) // 总行数为1时将当前行清空
    {
        ret = this->EditSetLine(0, "");
        if(ret != EDIT_OK)
        {
            return ret;
        }
    }
    else // 删除当前行
    {
        ret = this->EditDelLine(row);
        if (-1 == ret)
        {
            ret = EDIT_DELETE_LINE_OVFL;
            return ret;
        }
    }

    this->CancelBlock(); // 取消块定义

    if (row >= maxRowNum - 1) // 如果删除了末行，则上移一行
    {
        row--;
        SetScrStartLine(m_editorMan.scrStartLine - 1); // 上翻页，首行上滚一行
    }

    this->EditRedraw();
    this->CursorToPos(row - m_editorMan.scrStartLine);
    ret = EDIT_OK;

    return ret;
}

/**
 * @brief NcEditor::EditFindNewLine 定位行
 * @return EditorInfo
 */
Bit32 NcEditor::EditFindNewLine(void)
{
    Bit32 ret = EDIT_PASS;
    Bit32 lineToGo = 0;
    QString valStr = "";

    ret = MessageInput(&valStr, DTYPE_UINT, TR("请输入行号:"), 10); // 不允许输入负值
    if(ret < 0)
    {
        return ret;
    }

    lineToGo = valStr.toInt();
    Bit32 cursorCol = this->textCursor().positionInBlock();
    EditToLine(lineToGo, cursorCol);
    return ret;
}

void NcEditor::EditToLine(Bit32 lineToGo, Bit32 col)
{
    Bit32 maxRowNum = this->GetRowNum();
    Bit32 cursorRow = 0;
    Bit32 cursorCol = col;
    if(lineToGo > (maxRowNum - 1)) // 超过最大行数则到最后一行
    {
        lineToGo = maxRowNum - 1;
    }
    if (lineToGo < 0)
    {
        lineToGo = 0;
    }

    if (lineToGo >= m_editorMan.scrStartLine
     && lineToGo - m_editorMan.scrStartLine < m_editorMan.editRowMax) // 当前显示页内跳转
    {
        cursorRow = lineToGo - m_editorMan.scrStartLine;
    }
    else if (lineToGo + m_editorMan.editRowMax >= maxRowNum) // 跳转结尾部分
    {
        SetScrStartLine(maxRowNum - m_editorMan.editRowMax);
        cursorRow = lineToGo - m_editorMan.scrStartLine;
    }
    else
    {
        SetScrStartLine(lineToGo);
        cursorRow = 0;
    }

    if(m_editorMan.blockFlag == 1)
    {
        this->CancelBlock(); // 取消块定义
    }

    this->EditRedraw();

    this->CursorToPos(cursorRow, cursorCol);

    return;
}

Bit32 NcEditor::EditGotoNewLine(Bit32 scrStartRow, Bit32 lineToGo)
{
    Bit32 maxRowNum = this->GetRowNum();
    Bit32 ret = EDIT_PASS;
    Bit32 cursorRow = 0;
    Bit32 cursorCol = this->textCursor().positionInBlock();

    if (lineToGo < 0)
    {
        SetScrStartLine(0);
        cursorRow = -1;
    }
    else
    {
        if(lineToGo > (maxRowNum - 1)) // 超过最大行数则到最后一行
        {
            lineToGo = maxRowNum - 1;
        }

        if (scrStartRow >= lineToGo)
        {
            scrStartRow = lineToGo;
        }

        if ((lineToGo - scrStartRow) >= m_editorMan.editRowMax)
        {
            scrStartRow = lineToGo - m_editorMan.editRowMax + 1;
        }

        SetScrStartLine(scrStartRow);
        cursorRow = lineToGo - m_editorMan.scrStartLine;
    }

    if(m_editorMan.blockFlag == 1)
    {
        this->CancelBlock(); // 取消块定义
    }

    this->EditRedraw();

    if (cursorRow >= 0)
    {
        this->CursorToPos(cursorRow, cursorCol);
    }

    return ret;
}

/**
 * @brief NcEditor::EditCurLineReplace 替换当前行内容
 * @param newStr 替换后的字符串
 * @return EditorInfo
 */
Bit32 NcEditor::EditCurLineReplace(QString newStr)
{
    Bit32 ret = EDIT_PASS;
    Bit32 row = m_editorMan.scrStartLine + this->textCursor().blockNumber();

    ret = this->GetEnableStat(); // 获取可编辑状态
    if(ret != EDIT_OK)
    {
        return ret;
    }

    ret = this->EditSetLine(row, newStr);
    if (ret != EDIT_OK)
    {
        return ret;
    }

    this->CancelBlock(); // 取消块定义

    this->EditRedraw();
    this->CursorToPos(row - m_editorMan.scrStartLine);
    ret = EDIT_OK;

    return ret;
}

Bit32 NcEditor::EditCurLineAddNewLine(QString newStr, bool addAfter, bool force)
{
    Bit32 ret = this->GetEnableStat(); // 获取可编辑状态
    Bit32 cursorRow = this->textCursor().blockNumber(); // 焦点行
    Bit32 row = m_editorMan.scrStartLine + cursorRow; // 当前行的行号
    QString strLine = "";

    if(ret != EDIT_OK)
    {
        return ret;
    }

    ret = EDIT_OK;
    this->EditGetLine(row, strLine); //获取当前行

    if (strLine.simplified().isEmpty() == false || GetRowNum() == 0 || force) // 当前行不为空则另起一行
    {
        if (addAfter == true)
        {
            row++;
        }
        Bit32 tmpRet = this->EditInsLine(row);
        if(-1 == tmpRet) // 插入新行成功
        {
            return EDIT_INSERT_LINE_OVFL;
        }
    }

    // 设置新行
    ret = this->EditSetLine(row, newStr);
    if(ret != EDIT_OK)
    {
        return ret;
    }

    if (cursorRow >= m_editorMan.editRowMax - 1)// 本页末行，整屏向下滚动一行，当前光标位置不变
    {
        m_editorMan.scrStartLine++;
    }

    this->CancelBlock(); // 取消块定义

    this->EditRedraw();
    this->CursorToPos(row - m_editorMan.scrStartLine);
    ret = EDIT_OK;

    return ret;
}

Bit32 NcEditor::GetCurStartLine()
{
    return m_editorMan.scrStartLine;
}
/**
 * @brief NcEditor::EditOnMsg 按键响应
 * @param keyEvent 键值
 * @return true:响应 false：不响应
 */
bool NcEditor::EditOnMsg(QKeyEvent *keyEvent)
{
    Bit32 maxRowNum = this->GetRowNum(); // 最大行数
    Bit32 len = 0; // 文本长度
    Bit32 ret = EDIT_PASS;
    Bit32 cursorRow = this->textCursor().blockNumber(); // 焦点行
    Bit32 cursorCol = this->textCursor().positionInBlock(); // 焦点列
    Bit32 row = m_editorMan.scrStartLine + cursorRow; // 当前行的行号
    Bit32 tmpRet = 0;
    QString strLine = "";
    QString strLine0 = "";
    QString strLine1 = "";

    if ((keyEvent->modifiers() & Qt::AltModifier) != 0) // alt组合键
    {
        return true;
    }
    else if (keyEvent->modifiers() & Qt::ControlModifier)
    {
        if (keyEvent->key() == Qt::Key_Home) // 文件首ctrl+home
        {
            ret = this->EditHome();
        }
        else if (keyEvent->key() == Qt::Key_End) // 文件尾ctrl+end
        {
            ret = this->EditEnd();
        }
        else
        {
            return true; // 其他ctrl组合键屏蔽
        }
        MessageOut("");
        this->EditPerror(ret);
        return true;
    }
    else // 非组合键
    {
        switch (keyEvent->key())
        {
        case Qt::Key_Up: // 上移一行
            if (cursorRow == 0)
            {
                if (m_editorMan.scrStartLine > 0)
                {
                    SetScrStartLine(m_editorMan.scrStartLine - 1); // 上翻页，首行上滚一行
                    this->EditRedraw();
                    ret = EDIT_OK;
                }
                else
                {
                    ret = EDIT_OK;
                    break;
                }
            }
            else
            {
                this->CursorToPos(cursorRow - 1, cursorCol);    // bug6418，不交给控件处理
                ret = EDIT_OK;
            }
            if(m_editorMan.blockFlag == 1)
            {
                this->CancelBlock(); // 取消块定义
            }
            break;
        case Qt::Key_Down: // 下移一行
            if (row == maxRowNum - 1)   // 最后一行，下移无效
            {
                ret = EDIT_OK;
            }
            else if (cursorRow >= (m_editorMan.editRowMax - 1) && row < maxRowNum - 1)
            {
                SetScrStartLine(m_editorMan.scrStartLine + 1);
                this->EditRedraw();
                this->CursorToPos(cursorRow, cursorCol);
                ret = EDIT_OK;
            }
            else
            {
                this->CursorToPos(cursorRow + 1, cursorCol);        // bug6418，不交给控件处理
                ret = EDIT_OK;
            }
            if(m_editorMan.blockFlag == 1)
            {
                this->CancelBlock(); // 取消块定义
            }
            break;
        case Qt::Key_PageUp: // 上翻一页
            if(m_editorMan.scrStartLine > 0) // 前面还有内容
            {
                if(m_editorMan.scrStartLine - m_editorMan.editRowMax < 0) // 前面不足一页
                {
                    SetScrStartLine(0);
                }
                else
                {
                        SetScrStartLine(m_editorMan.scrStartLine - m_editorMan.editRowMax);
                    }

                this->EditRedraw();
                    this->CursorToPos(cursorRow, cursorCol); // 光标移到相同行
                ret = EDIT_OK;
            }
            else
            {
                this->CursorToPos(0, cursorCol); // 光标移到相同行,// bug6418，不交给控件处理
                ret = EDIT_OK;
            }
            if(m_editorMan.blockFlag == 1)
            {
                this->CancelBlock(); // 取消块定义
            }
            break;
        case Qt::Key_PageDown: // 下翻一页
            if (row == maxRowNum - 1)   // 最后一行，操作无效
            {
                ret = EDIT_OK;
            }
            else if(m_editorMan.scrStartLine + m_editorMan.editRowMax < maxRowNum) // 后面还有内容
            {
                if(maxRowNum - m_editorMan.scrStartLine < m_editorMan.editRowMax * 2) // 后面不足一页
                {
                    SetScrStartLine(maxRowNum - m_editorMan.editRowMax);
                }
                else
                {
                    SetScrStartLine(m_editorMan.scrStartLine + m_editorMan.editRowMax);
                }

                this->EditRedraw();
                this->CursorToPos(cursorRow, cursorCol); // 光标移到相同行
                ret = EDIT_OK;
            }
            else
            {
                this->CursorToPos(maxRowNum - 1, cursorCol); // 光标移到相同行,// bug6418，不交给控件处理
                ret = EDIT_OK;
            }
            if(m_editorMan.blockFlag == 1)
            {
                this->CancelBlock(); // 取消块定义
            }
            break;
        case Qt::Key_Left: // 左移一个字符，如果走到头则移动到上一行末尾
            if (this->textCursor().atStart() && m_editorMan.scrStartLine > 0)
            {
                SetScrStartLine(m_editorMan.scrStartLine - 1); // 上翻页，首行上滚一行
                this->EditRedraw();
                this->moveCursor(QTextCursor::EndOfBlock);
                ret = EDIT_OK;
            }
            if(m_editorMan.blockFlag == 1)
            {
                this->CancelBlock(); // 取消块定义
            }
            break;
        case Qt::Key_Right: // 右移一个字符，如果走到尾则移动到下一行行首
            if (this->textCursor().atEnd() && row < maxRowNum - 1)
            {
                SetScrStartLine(m_editorMan.scrStartLine + 1);
                this->EditRedraw();
                this->CursorToPos(m_editorMan.editRowMax - 1);
                ret = EDIT_OK;
            }
            if(m_editorMan.blockFlag == 1)
            {
                this->CancelBlock(); // 取消块定义
            }
            break;
        case Qt::Key_Delete: // 删除光标后一字符，光标位置不变
            ret = this->GetEnableStat(); // 获取可编辑状态
            if(ret != EDIT_OK)
            {
                break;
            }

            if(m_holdRestartRow > 0 && IsGmodeG71(ActiveChan()))
            {
                ret = EDIT_HOLD_CYC;
                break;
            }

//            if(m_holdRestartRow > 0 && (m_holdRestartRow == row || m_holdRestartRow - 1== row))
//            {
//                ret = EDIT_HOLD_ROW;
//                break;
//            }

            UndoRecordStart(); // 开始记录操作

            if (m_editorMan.blockFlag) // 标志是否定义了块
            {
                ret = this->ClipDelete();
            }
            else
            {
                ret = EDIT_OK;
                if(!m_bHedEditEnable && row == 0)
                {
                    ret = EDIT_PROGHEAD;
                    break;
                }
                if (maxRowNum <= 0
                   || (this->textCursor().atEnd() && row >= maxRowNum - 1) // 末行末页末字符，不响应
                   || cursorCol >= EDIT_LINE_SIZE) // 当前行编辑区满，无法拼接
                {
                    break;
                }

                if (!this->textCursor().atBlockEnd()) // 行内删除
                {
                    this->textCursor().deleteChar();
                    this->EditSaveFocusLine();
                }
                else // 行末删除，提升下一行到本行末，保存本行并删除被提升的行
                {
                    if(m_holdRestartRow > 0 && row <= m_holdRestartRow)
                    {
                        ret = EDIT_HOLD_ROW;
                        break;
                    }
                    this->EditGetLine(row, strLine0); // 获取当前行文本
                    this->EditGetLine(row + 1, strLine1); // 获取下一行文本

                    strLine0.replace('\n', "");
                    cursorCol = strLine0.length();
                    strLine = strLine0 + strLine1;

                    strLine.replace('\r', "");

                    if (strlen(strLine.toAscii().data()) > EDIT_LINE_SIZE) // 判断上下两行拼接长度是否超出限制
                    {
                        ret = EDIT_LINE_OVFL;  //行编辑器满
                        break;
                    }

                    ret = this->EditSetLine(row, strLine0 + strLine1);
                    if(ret != EDIT_OK)
                    {
                        break;
                    }

                    ret = this->EditDelLine(row + 1);
                    if (-1 == ret)
                    {
                        ret = EDIT_DELETE_LINE_OVFL;
                        break;
                    }

                     // 文件尾删除行光标下移一行
                    if ((cursorRow < m_editorMan.editRowMax - 1) && (m_editorMan.scrStartLine > 0)
                         && (m_editorMan.scrStartLine == maxRowNum - m_editorMan.editRowMax))
                    {
                        cursorRow++;
                        SetScrStartLine(m_editorMan.scrStartLine - 1);
                    }
                    this->EditRedraw();
                    this->CursorToPos(cursorRow, cursorCol);
                }
                this->CancelBlock(); // 取消块定义
            }
            break;
        case Qt::Key_Backspace: // 删除光标前一字符，光标前移一个字符
            ret = this->GetEnableStat(); // 获取可编辑状态
            if(ret != EDIT_OK)
            {
                break;
            }

            if(m_holdRestartRow > 0 && IsGmodeG71(ActiveChan()))
            {
                ret = EDIT_HOLD_CYC;
                break;
            }

//            if(m_holdRestartRow > 0 && (m_holdRestartRow == row || m_holdRestartRow - 1== row))
//            {
//                ret = EDIT_HOLD_ROW;
//                break;
//            }

            UndoRecordStart(); // 开始记录操作

            if (m_editorMan.blockFlag) // 标志是否定义了块
            {
                ret = this->ClipDelete();
            }
            else
            {
                ret = EDIT_OK;
                // 首页首行首字符，不响应
                if (this->textCursor().atStart() && m_editorMan.scrStartLine <= 0)
                {
                    break;
                }
                if(!m_bHedEditEnable && ((row == 0) || (row == 1 && this->textCursor().atBlockStart())))
                {
                    ret = EDIT_PROGHEAD;
                    break;
                }
                if (cursorCol > 0) // 行内删除
                {
                    this->textCursor().deletePreviousChar();
                    this->EditSaveFocusLine();
                }
                else // 行首删除，提升本行到上一行行末
                {
                    if(m_holdRestartRow > 0 && row <= m_holdRestartRow + 1)
                    {
                        ret = EDIT_HOLD_ROW;
                        break;
                    }
                    this->EditGetLine(row - 1, strLine0);
                    this->EditGetLine(row, strLine1);

                    strLine0.replace('\n', "");
                    cursorCol = strLine0.length();
                    strLine = strLine0 + strLine1;

                    strLine.replace('\r', "");

                    if (strlen(strLine.toAscii().data()) > EDIT_LINE_SIZE) // 判断上下两行拼接长度是否超出限制
                    {
                        ret = EDIT_LINE_OVFL;  //行编辑器满
                        break;
                    }

                    this->EditSetLine(row - 1, strLine);
                    this->EditDelLine(row);

                    if (cursorRow == 0 || (m_editorMan.scrStartLine > 0
                        && m_editorMan.scrStartLine >= maxRowNum - m_editorMan.editRowMax)) // 编辑框首行，向上滚动一行
                    {
                        SetScrStartLine(m_editorMan.scrStartLine - 1);
                    }
                    else
                    {
                        cursorRow--;
                    }

                    this->EditRedraw();
                    this->CursorToPos(cursorRow, cursorCol);
                }
                this->CancelBlock(); // 取消块定义
            }
            break;
        case Qt::Key_Enter: // 小键盘回车
        case Qt::Key_Return: // 标准回车
            if(!m_bHedEditEnable && row == 0 && !this->textCursor().atBlockEnd())
            {
                ret = EDIT_PROGHEAD;
                break;
            }
            ret = this->GetEnableStat(); // 获取可编辑状态
            if(ret != EDIT_OK)
            {
                break;
            }
            if(m_holdRestartRow > 0 && IsGmodeG71(ActiveChan()))
            {
                ret = EDIT_HOLD_CYC;
                break;
            }
            if(row <= m_holdRestartRow && m_holdRestartRow > 0)
            {
                ret = EDIT_HOLD_ROW;
                break;
            }

            UndoRecordStart(); // 开始记录操作

            ret = EDIT_OK;
            this->EditGetLine(row, strLine);

            // 得到当前行拆分的前半部分和后半部分
            strLine0 = strLine.left(cursorCol);
            strLine1 = strLine.mid(cursorCol);
            strLine0 += "\n";

            tmpRet = this->EditInsLine(row + 1);
            if(tmpRet != EDIT_OK) // 插入新行成功
            {
                ret = EDIT_INSERT_LINE_OVFL;
                break;
            }

            // 设置新行为拆分的前半部分
            ret = this->EditSetLine(row, strLine0);
            if(ret != EDIT_OK)
            {
                ret = EDIT_ERR;
                break;
            }

            // 设置新行为拆分的后半部分
            ret = this->EditSetLine(row + 1, strLine1);
            if(ret != EDIT_OK)
            {
                ret = EDIT_ERR;
                break;
            }

            if (cursorRow < m_editorMan.editRowMax - 1) // 页内，下移一行
            {
                cursorRow++;
            }
            else // 本页末行，整屏向下滚动一行，当前光标位置不变
            {
                SetScrStartLine(m_editorMan.scrStartLine + 1);
            }
            this->EditRedraw();
            this->CursorToPos(cursorRow);
            this->CancelBlock(); // 取消块定义
            break;
        case Qt::Key_Tab: // 4字符
            ret = this->GetEnableStat(); // 获取可编辑状态
            if(ret != EDIT_OK)
            {
                break;
            }

            // TODO
            ret = EDIT_OK;
            this->CancelBlock(); // 取消块定义
            break;
        case Qt::Key_Home:  // 当前行首
        case Qt::Key_End:   // 当前行尾
            ret = EDIT_PASS; // return false; QPlainTextEdit接收按键处理
            break;
        default: // 行编辑，键入字符
            ret = this->GetEnableStat(); // 获取可编辑状态
            if(ret != EDIT_OK)
            {
                break;
            }
            if(m_holdRestartRow > 0 && IsGmodeG71(ActiveChan()))
            {
                ret = EDIT_HOLD_CYC;
                break;
            }
//            if((row == m_holdRestartRow - 1 || row == m_holdRestartRow) && m_holdRestartRow > 0)
//            {
//                ret = EDIT_HOLD_ROW;
//                break;
//            }

            UndoRecordStart(); // 开始记录操作

            ret = EDIT_OK;
            Bit32 charNum = this->textCursor().block().length();
            if (charNum >= EDIT_LINE_SIZE)
            {
                ret = EDIT_LINE_OVFL;
                break;
            }

            this->CancelBlock(); // 取消块定义
            if(!m_bHedEditEnable && row == 0)
            {
                ret = EDIT_PROGHEAD;
                break;
            }

            QString inputStr = keyEvent->text();
            bool isLetter = (keyEvent->key() >= Qt::Key_A && keyEvent->key() <= Qt::Key_Z);
            Bit32 input = ComGetInputLang();

            if(input == INPUT_CHN && isLetter)
            {
                Bit32 inputRet = 0;
                inputRet = MessageInput(&inputStr, DTYPE_STRING, "",-1,-1,0,NULL,false);
                if(inputRet == 0)
                {
                    this->textCursor().insertText(inputStr);
                    this->EditSaveFocusLine();
                }
            }
            else if(!inputStr.isEmpty())
            {
                //this->textCursor().insertText(keyEvent->text().toUpper());
                //this->EditSaveFocusLine();

                strLine = this->document()->findBlockByLineNumber(cursorRow).text();
                len = strLine.count() - cursorCol;
                strLine.insert(cursorCol, keyEvent->text().toUpper());
                QString oldStrLine = strLine;
                if (1 != m_nInsertSpaceClose)
                {
                    InsertSpace(strLine);
                }
                this->EditSetLine(this->m_editorMan.scrStartLine + cursorRow, strLine);

                bool ok = false;
                keyEvent->text().toInt(&ok);
                if (ok && (oldStrLine.left(cursorCol + 1) == strLine.left(cursorCol + 1))) // 输入数字，光标前进一列（判断插入数字位置前面的内容是否相同（是否插入空格））
                {
                    cursorCol++;
                }
                else
                {
                    cursorCol = strLine.count() - len; // 计算光标位置：1、输入其他字符  2、输入数字时，插入数字位置前面内容不同（例如：遇到前面有/时插入空格）
                }

                this->EditRedraw();
                this->CursorToPos(cursorRow, cursorCol);
            }
            break;
        }

        UndoRecordEnd(); // 结束记录操作

        MessageOut("");
        this->EditPerror(ret);
        if(ret == EDIT_PASS) // 没处理
        {
            return false;
        }
        else // 响应处理
        {
            return true;
        }
    }
}

/**
 * @brief NcEditor::EditPerror 显示错误信息
 * @param err 错误号
 */
void NcEditor::EditPerror(Bit32 err)
{
    switch (err)
    {
    case EDIT_LINE_OVFL: // 行编辑区满(125字符)
        MessageOut(TR("行编辑区满"));
        break;
    case EDIT_INSERT_LINE_OVFL: // 插入行满
        MessageOut(TR("插入的行数超过最大限制"));
        break;
    case EDIT_DELETE_LINE_OVFL: // 删除行满
        MessageOut(TR("删除的行数超过最大限制"));
        break;
    case EDIT_CLIP_OVFL: // 剪切板缓冲满
        MessageOut(TR("字符数超过剪贴板的大小,有些数据可能丢失"));
        break;
    case EDIT_NO_CLIP: // 剪切板不能用
        MessageOut(TR("内存不足,剪贴板功能无法使用"));
        break;
    case EDIT_CLIP_EMPTY: // 剪切板中没数据
        MessageOut(TR("剪贴板中没有字符"));
        break;
    case EDIT_BLOCK_UNDEFINED: // 没有块定义
        MessageOut(TR("没有定义块"));
        break;
    case EDIT_MEMERY_OVFL: // 内存不足
        MessageOut(TR("内存不足,请先保存退出后再重新编辑"));
        break;
    case EDIT_READONLY:
        MessageOut(TR("只读文件,不能编辑"));
        break;
    case EDIT_LOADED:
        MessageOut(TR("已加载文件运行中,不能编辑"));
        break;
    case EDIT_PROTECT:
        MessageOut(TR("数据保护中"));
        break;
    case EDIT_PROGHEAD:
        MessageOut(TR("不允许编辑程序头"));
        break;
    case EDIT_RUNNING_PROTECT:
        MessageOut(TR("程序运行中,不能编辑"));
        break;
    case EDIT_GIVEN_ROW_PROTECT:
        MessageOut(TR("任意行模式中,不能编辑"));
        break;
    case EDIT_HOLD_ROW:
//        MessageOut(TR("进给保持下不允许编辑当前行和上一行，不允许更改当前行前的行数"));
        MessageOut(TR("进给保持下不允许更改当前行前的行数"));
        break;
    case EDIT_HOLD_CYC:
        MessageOut(TR("复合循环加工中禁止编辑"));
        break;
    default:
        break;
    }
}

/**
 * @brief NcEditor::EditGetLineInfo 获取当前屏幕首行的行号和屏幕行数
 * @param startLine 首行的行号
 * @param rowMax 屏幕行数
 */
void NcEditor::EditGetLineInfo(Bit32 &startLine, Bit32 &rowMax)
{
    startLine = m_editorMan.scrStartLine;
    rowMax = m_editorMan.editRowMax;
}

Bit32 NcEditor::EditGetHighlightFlag()
{
    return m_editorMan.highLightFlag;
}

void NcEditor::EditSetHighlightFlag(Bit32 flag)
{
    m_editorMan.highLightFlag = flag;
}

Bit32 NcEditor::EditGetPageRowNum(void)
{
    return m_editorMan.editRowMax;
}

void NcEditor::EditSetStartLine(Bit32 startLine)     //设置当前屏幕首行的行号
{
    Bit32 maxRowNum = this->GetRowNum(); // 最大行数
    Bit32 cursorRow = this->textCursor().blockNumber(); // 焦点行
    Bit32 cursorCol = this->textCursor().positionInBlock(); // 焦点列
    Bit32 oldStartLine = m_editorMan.scrStartLine;

    if (startLine + m_editorMan.editRowMax <= maxRowNum)
    {
        QTextCursor tc = this->textCursor();
        int r = QString::compare(tc.selectedText(), this->m_strFind, Qt::CaseInsensitive);
        bool keepSel = false;
        if(r == 0 && tc.hasSelection() && m_editorMan.scrStartLine == startLine)
        {
            keepSel = true;
        }
        m_editorMan.scrStartLine = startLine;
        this->EditRedraw();

        // 起始行变化的情况下,保持焦点行号不变
        // 块首在上页,块尾在最后一页,删除块后
        // WgProgEdit中verticalScrollBar在Refresh中setMaximum()使得pEdMan.scrStartLine变化
        // 此时若仍用this->textCursor().blockNumber()做焦点行,会导致焦点行移动
        // 因为在有多页的情况下,最后一页尾行固定为G代码尾行
        if (oldStartLine != m_editorMan.scrStartLine)
        {
            Bit32 curRow = cursorRow + (oldStartLine - m_editorMan.scrStartLine);
            if (curRow < m_editorMan.editRowMax && curRow >= 0)
            {
                cursorRow = curRow;
            }
        }

        this->CursorToPos(cursorRow, cursorCol);
        // 解决CursorToPos取消查找功能的字符选中的问题
        tc = this->textCursor();
        if(keepSel)
        {
            int toPos = tc.position() - this->m_strFind.length();
            if(toPos >= 0)
            {
                tc.setPosition(toPos);
                this->setTextCursor(tc);

                toPos += this->m_strFind.length();
                tc.setPosition(toPos, QTextCursor::KeepAnchor);
                this->setTextCursor(tc);
            }
        }
    }
    if(m_editorMan.blockFlag == 1)
    {
        this->CancelBlock(); // 取消块定义
    }
}

void NcEditor::SetMcpKeyValid(bool valid)
{
    m_bMcpValid = valid;
}

/**
 * @brief NcEditor::GetEnableStat 获取可编辑状态
 * @return EDIT_OK:可以编辑 其他:不能编辑
 */
Bit32 NcEditor::GetEnableStat()
{
    Bit32 ret = EDIT_OK; // 可以编辑

    if(m_nEditState != EDIT_OK)
    {
        ret = m_nEditState;
    }
    else if(this->m_nNoEditFlg == 1)
    {
        ret = EDIT_READONLY; // 只读文件
    }
    else if(this->m_nNoEditFlg == 2)
    {
        ret = EDIT_LOADED; // 已加载文件
    }
    else if(GetMcpKey() == 0)
    {
        ret = EDIT_PROTECT; // 数据保护文件
    }
    else if(GetMcpKey() == 0 && m_bMcpValid)
    {
        ret = EDIT_PROTECT; // 数据保护文件
    }

    return ret;
}

Bit32 NcEditor::IsInputEnable()
{
    Bit32 cursorRow = this->textCursor().blockNumber(); // 焦点行
//    Bit32 row = m_editorMan.scrStartLine + cursorRow; // 当前行的行号
    Bit32 ret = this->GetEnableStat(); // 获取可编辑状态
    if(ret != EDIT_OK)
    {
        return ret;
    }
    if(m_holdRestartRow > 0 && IsGmodeG71(ActiveChan()))
    {
        ret = EDIT_HOLD_CYC;
    }
//    if((row == m_holdRestartRow - 1 || row == m_holdRestartRow) && m_holdRestartRow > 0)
//    {
//        ret = EDIT_HOLD_ROW;
//    }

    return ret;
}

/**
 * @brief NcEditor::GetRow 获取指定行文本
 * @note 回调函数
 * @return 0：成功；-1：失败；
 */
Bit32 NcEditor::GetRow(Bit32, QString &)
{
    return 0;
}

/**
 * @brief NcEditor::SetRow 设置指定行文本
 * @note 回调函数
 * @return 0：成功；-1：失败；
 */
Bit32 NcEditor::SetRow(Bit32, QString)
{
    return 0;
}

/**
 * @brief NcEditor::SetRow 获取总行数
 * @note 回调函数
 * @return 0：成功；-1：失败；
 */
Bit32 NcEditor::GetRowNum()
{
    return 0;
}

/**
 * @brief NcEditor::InsertRows 插入行
 * @note 回调函数
 * @return 0：成功；-1：失败；
 */
Bit32 NcEditor::InsertRows(Bit32)
{
    return 0;
}

/**
 * @brief NcEditor::DeleteRows 删除行
 * @note 回调函数
 * @return 0：成功；-1：失败；
 */
Bit32 NcEditor::DeleteRows(Bit32, Bit32)
{
    return 0;
}

Bit32 NcEditor::InsertGCode(QString gCode)
{
    Bit32 ret = 0;
    Bit32 cursorRow = this->textCursor().blockNumber();
    Bit32 cursorCol = 0; // 焦点列
    Bit32 row = m_editorMan.scrStartLine + cursorRow;
    QString leftStr = "";
    QString rightStr = "";
    QString strLine = "";

    UndoRecordStart();
    this->EditGetLine(row, strLine); //获取当前行

    if(gCode.isEmpty())
    {
        Bit32 tmpRet = this->EditInsLine(row);
        if(-1 == tmpRet) // 插入新行成功
        {
            return EDIT_INSERT_LINE_OVFL;
        }

        ret = this->EditSetLine(row, gCode);
        if(ret != EDIT_OK)
        {
            return ret;
        }
    }
    else
    {
        Bit32 tmpRet = this->EditInsLine(row + 1);
        if(-1 == tmpRet) // 插入新行成功
        {
            return EDIT_INSERT_LINE_OVFL;
        }

        // 得到当前行拆分的前半部分和后半部分
        leftStr = strLine.left(cursorCol);
        rightStr = strLine.mid(cursorCol);

        // 设置新行为拆分的前半部分
        ret = this->EditSetLine(row, gCode + leftStr);
        if(ret != EDIT_OK)
        {
            return ret;
        }

        // 设置新行为拆分的后半部分
        ret = this->EditSetLine(row + 1, rightStr);
        if(ret != EDIT_OK)
        {
            return ret;
        }
        if (cursorRow < m_editorMan.editRowMax - 1) // 页内，下移一行
        {
            cursorRow++;
        }
        else // 本页末行，整屏向下滚动一行，当前光标位置不变
        {
            m_editorMan.scrStartLine++;
        }
    }

    this->CancelBlock(); // 取消块定义

    this->EditRedraw();
    this->CursorToPos(cursorRow);
    ret = EDIT_OK;
    UndoRecordEnd();

    return ret;
}

/**
 * @brief MyHighlighter::highlightBlock 高亮指定的文字
 * @param text 要高亮的文字
 */
void NcHighlighter::highlightBlock(const QString &text)
{
     Bit32 len = text.length();
     Bit8 buf[EDIT_LINE_MAX] = "";
     snprintf(buf, EDIT_LINE_MAX, "%s", StrToQByte(text, CODE_GB).data());
     Bit32 flag = 0; // 1：‘;’注释；2：‘(’注释；3：数字；4：Z轴；0：普通字母显示

     for (Bit32 ii = 0; ii < len; )
     {
        if (buf[ii] == ';')
        {
            flag = 1;
        }
        else if (buf[ii] == '(')
        {
            flag = 2;
        }
        else if (flag == 2 && (ii > 0 && buf[ii - 1] == ')'))
        {
            flag = 0;
        }

        if (flag != 1 && flag != 2)
        {
            if (IsNum(buf[ii]) == 1)
            {
                flag = 3;
            }
            else if (buf[ii] == 'z' || buf[ii] == 'Z')
            {
                flag = 4;
            }
            else
            {
                flag = 0;
            }
        }

        if (flag == 1)
        {
            setFormat(ii, len, this->MyClassFormatExplan);
            ii += len;
        }
        else if (flag == 2)
        {
            setFormat(ii, 1, this->MyClassFormatExplan);
            ii++;
        }
        else if (flag == 3)
        {
            setFormat(ii, 1, MyClassFormatNum);
            ii++;
        }
        else if (flag == 4)
        {
            setFormat(ii, 1, MyClassFormatZ);
            ii++;
        }
        else
        {
            setFormat(ii, 1, MyClassFormatExt);
            ii++;
        }
     }
}

void NcEditor::SetScrStartLine(Bit32 startLine)
{
    Bit32 maxRowNum = this->GetRowNum(); // 最大行数
    if (startLine < 0 || maxRowNum <= 0)
    {
        this->m_editorMan.scrStartLine = 0;
    }
    else if (startLine >= maxRowNum)
    {
        this->m_editorMan.scrStartLine = maxRowNum - 1;
    }
    else
    {
        this->m_editorMan.scrStartLine = startLine;
    }
}

Bit32 NcEditor::GetCurRow()
{
    return this->m_editorMan.scrStartLine + this->textCursor().blockNumber();
}

Bit32 NcEditor::DelAllRow()
{
    Bit32 ret = EDIT_OK;

    UndoRecordStart();
    for (Bit32 row = this->GetRowNum() - 1; row >= 0; row--)
    {
        ret = this->EditDelLine(row);
        if (ret != EDIT_OK)
        {
            UndoRecordEnd();
            return EDIT_DELETE_LINE_OVFL;
        }
    }

    this->m_editorMan.scrStartLine = 0;
    this->EditRedraw();
    this->EditCurLineAddNewLine("%1234");
    this->EditCurLineAddNewLine("", true);
    this->EditCurLineAddNewLine("M30", true, true);
    this->CursorToPos(1);
    UndoRecordEnd();
    return 0;
}

void NcEditor::UndoRecordInit()
{
    m_undoStack.clear();
    m_recordUndoInfo.editUndoData.clear();
    m_nUndoCurStep = m_undoStack.count() - 1;
    m_bUndoRecord = false;
}

void NcEditor::UndoRecordStart()
{
    m_bUndoRecord = true;

    m_recordUndoInfo.editUndoData.clear();

    m_recordUndoInfo.preScrStartLine = this->m_editorMan.scrStartLine;
    m_recordUndoInfo.preCursorRow = this->textCursor().blockNumber();
    m_recordUndoInfo.preCursorCol = this->textCursor().columnNumber();

    m_recordUndoInfo.endScrStartLine = 0;
    m_recordUndoInfo.endCursorRow = 0;
    m_recordUndoInfo.endCursorCol = 0;
}

void NcEditor::UndoRecordEnd()
{
    if (m_bUndoRecord)
    {
         if (m_recordUndoInfo.editUndoData.count() > 0)
         {
             if (m_nUndoCurStep < (m_undoStack.count() - 1))
             {
                  // 先清除后续的步骤
                 while ((m_nUndoCurStep < (m_undoStack.count() - 1)) && (m_undoStack.count() > 0))
                 {
                     m_undoStack.pop_back();
                 }
              }

             // 记录光标信息
             m_recordUndoInfo.endScrStartLine = this->m_editorMan.scrStartLine;
             m_recordUndoInfo.endCursorRow = this->textCursor().blockNumber();
             m_recordUndoInfo.endCursorCol = this->textCursor().columnNumber();

             m_undoStack.push_back(m_recordUndoInfo);
             if (m_undoStack.count() > UNDO_MAX_STEP)
             {
                 m_undoStack.pop_front(); // 移除第一个元素
             }
             m_nUndoCurStep = m_undoStack.count() - 1;
         }

         m_bUndoRecord = false;
    }
}


Bit32 NcEditor::EditUndo()
{
    Bit32 ret = EDIT_OK;

    if (m_nUndoCurStep >= 0 && m_nUndoCurStep < m_undoStack.count())
    {
        EditUndoInfo textUndoInfo = m_undoStack.at(m_nUndoCurStep);

        for (Bit32 ii = textUndoInfo.editUndoData.count() - 1; ii >= 0; ii--)
        {
            EditUndoData data = textUndoInfo.editUndoData.at(ii);
            switch (data.command)
            {
            case UNDO_INSTER:
                this->EditDelLine(data.row);
                break;
            case UNDO_DEL:
                this->EditInsLine(data.row);
                this->EditSetLine(data.row, data.preCmdText);
                break;
            case UNDO_CHANGED:
                this->EditSetLine(data.row, data.preCmdText);
                break;
            default:
                break;
            }
        }
        m_nUndoCurStep--;

        m_editorMan.scrStartLine = textUndoInfo.preScrStartLine;
        this->EditRedraw();
        this->CursorToPos(textUndoInfo.preCursorRow, textUndoInfo.preCursorCol);
    }

    return ret;
}

Bit32 NcEditor::EditRedo()
{
    Bit32 ret = EDIT_OK;
    Bit32 step = m_nUndoCurStep + 1;
    if (step >= 0 && step < m_undoStack.count())
    {
        EditUndoInfo textUndoInfo = m_undoStack.at(step);

		for (Bit32 ii = 0; ii < textUndoInfo.editUndoData.count(); ii++)
        {
            EditUndoData data = textUndoInfo.editUndoData.at(ii);
            switch (data.command)
            {
            case UNDO_INSTER:
                this->EditInsLine(data.row);
                break;
            case UNDO_DEL:
                this->EditDelLine(data.row);
                break;
            case UNDO_CHANGED:
                this->EditSetLine(data.row, data.endCmdText);
                break;
            default:
                break;
            }
        }
        m_nUndoCurStep = step;

        m_editorMan.scrStartLine = textUndoInfo.endScrStartLine;
        this->EditRedraw();
        this->CursorToPos(textUndoInfo.endCursorRow, textUndoInfo.endCursorCol);
    }

    return ret;
}

void NcEditor::SetHeadEditFlag(bool edit)
{
    m_bHedEditEnable = edit;
}

void NcEditor::SetEditState(Bit32 flag)
{
    m_nEditState = flag;
    if (m_nEditState != EDIT_OK)
    {
        this->setReadOnly(true);
    }
    else
    {
        this->setReadOnly(false);
        this->setFocus();
    }
}

/*!
 * \brief NcEditor::ResetCursorPos 清除光标位置
 */
void NcEditor::ResetCursorPos()
{
    m_editorMan.scrStartLine = 0;
    m_editorMan.m_nCursorRow = 0;
    m_editorMan.m_nCursorCol = 0;
}

void NcEditor::ReplaceString(bool chkWholeWord)
{
    this->CancelBlock(); // 取消块选中
    UndoRecordStart(); // 开始记录操作
    // 获取光标位置
    Bit32 row = m_editorMan.scrStartLine + this->textCursor().blockNumber();
    Bit32 col = this->textCursor().positionInBlock();

    QRegExp rx = this->GetRegExp(m_strFind, chkWholeWord);

    m_pTask->SetReplaceStr(this->m_strFind, this->m_strReplace, row, col, 1);
    m_pTask->SetChkWholeWord(chkWholeWord);
    m_pTask->SetRegExp(rx);
    m_pTask->RunTask();
    pDlgTask->exec();
}

/**
 * @brief NcEditor::ReplaceAll 替换全部字符串
 * @return 成功：EDIT_OK；行编辑区满导致失败：EDIT_LINE_OVFL
 */
void NcEditor::ReplaceAll(bool chkWholeWord)
{
    this->CancelBlock(); // 取消块选中
    UndoRecordStart(); // 开始记录操作

    QRegExp rx = this->GetRegExp(m_strFind, chkWholeWord);

    m_pTask->SetReplaceStr(this->m_strFind, this->m_strReplace);
    m_pTask->SetChkWholeWord(chkWholeWord);
    m_pTask->SetRegExp(rx);
    m_pTask->RunTask();
    pDlgTask->exec();
}

void NcEditor::ReplaceRefresh(Bit32 ch)
{
    Bit32 isEStop = 0;
    HNC_ChannelGetValue(HNC_CHAN_IS_ESTOP, ch, 0, &isEStop);
    if (isEStop)
    {
        m_pTask->StopTask();
    }
    if (m_pTask->isRunning() == false)
    {
        return;
    }
    QString msg = m_pTask->GetTaskProgress().toString();
    pDlgTask->SetText(msg);
}

void NcEditor::ReplaceFin()
{
    pDlgTask->accept();
    Bit32 count = m_pTask->GetReplacedCount();
    bool findSuccess = m_pTask->IsFindSuccessed();
    if (!findSuccess && count == 0)
    {
        return;
    }
    Bit32 row = m_pTask->GetReplacedEndRow();
    Bit32 col = m_pTask->GetReplacedEndCol();
    EditToLine(row, col);
    UndoRecordEnd(); // 结束记录操作

    QTextCursor tc = this->textCursor();
    int pos = tc.position();
    if (findSuccess)
    {
        int toPos = pos + this->m_strFind.length();
        tc.setPosition(toPos, QTextCursor::KeepAnchor);
    }
    this->setTextCursor(tc);
}

bool NcEditor::IsShowLineNoArea()
{
    bool isShow = false;

    switch (m_editorMan.showIndex)
    {
    case NO_SHOW_LINE:  // 不显示行号
        isShow = false;
        break;
    case ONLY_EDIT_SHOW_LINE:   // 只在程序编辑界面显示行号
        if (m_editorMan.m_nEditorType == EDIT_TYPE)
        {
            isShow = true;
        }
        break;
    case ONLY_VIEW_SHOW_LINE:   // 只在程序预览界面显示行号
        if (m_editorMan.m_nEditorType == VIEW_TYPE)
        {
            isShow = true;
        }
        break;
    case ALL_SHOW_LINE: // 显示行号
        isShow = true;
        break;
    default:
        break;
    }

    return isShow;
}

Bit32 NcEditor::ReplaceNormalFin()
{
    ReplaceFin();
    Bit32 count = m_pTask->GetReplacedCount();
    bool findSuccess = m_pTask->IsFindSuccessed();
    if (!findSuccess && count == 0)
    {
        if (m_pTask->IsSigReplace())
        {
            this->pDlgMsg->setText(TR("未找到被替换字符"));
            this->pDlgMsg->exec();
        }
        else
        {
            MessageOut(TR("未找到被替换字符"));
        }
        return EDIT_PASS;
    }

    QStringList err = m_pTask->GetErrMsg();
    if (m_pTask->IsSigReplace())
    {
        if (err.count() != 0)
        {
            this->pDlgMsg->setText(TR("替换失败") + err.at(0));
            this->pDlgMsg->exec();
        }
        return EDIT_OK;
    }

    if (err.count() == 0)
    {
        MessageOut(TR("替换成功,完成替换%1处").arg(count));
    }
    else
    {
        MessageOut(TR("完成替换%1处,替换失败%2处,详见日志").arg(count).arg(err.count()));
        for (int i = 0; i < err.count(); i++)
        {
            Logdt::LogdtInput(LOG_FILECHANGE, TR("文件编辑替换-%1").arg(err.at(i)));
        }
    }
    return EDIT_OK;
}

Bit32 NcEditor::ReplaceForceEnd()
{
    ReplaceFin();
    QStringList err = m_pTask->GetErrMsg();
    Bit32 count = m_pTask->GetReplacedCount();
    if (count == 0)
    {
        return EDIT_PASS;
    }

    if (err.count() == 0)
    {
        MessageOut(TR("查询至%1行,完成替换共%2处").arg(m_pTask->GetReplacedRow()).arg(count));
    }
    else
    {
        MessageOut(TR("查询至%1行,完成替换%2处,替换失败%3处,详见日志").arg(m_pTask->GetReplacedRow()).arg(count).arg(err.count()));
        for (int i = 0; i < err.count(); i++)
        {
            Logdt::LogdtInput(LOG_FILECHANGE, TR("文件编辑替换-%1").arg(err.at(i)));
        }
    }
    return EDIT_OK;
}

NcEditorReplaceTask::NcEditorReplaceTask(NcEditor *wg)
{
    m_sStrFind = "";
    m_sStrReplace = "";
    m_pCurEdit = wg;

    m_nStCol = 0;
    m_nStRow = 0;
    m_bReplaceSig = false;
    m_bReplaceFlag = false;
    m_bStopTask = false;
    m_bChkWholeWord = false;

    m_nRowIdx = m_nStRow;
    m_nEndRowIdx = m_nStRow;
    m_nEndColIdx = m_nStCol;
    TaskInit();
}

void NcEditorReplaceTask::TaskInit()
{
    m_nRowIdx = m_nStRow;
    m_nEndRowIdx = m_nStRow;
    m_nEndColIdx = m_nStCol;
    m_nReplacedLineCount = 0;
    m_bStopTask = false;
    m_bFindSuccessed = false;
    m_sErrorStr.clear();
}

void NcEditorReplaceTask::SetChkWholeWord(bool chk)
{
    m_bChkWholeWord = chk;
}

void NcEditorReplaceTask::SetRegExp(QRegExp reg)
{
    m_regExp = reg;
}

bool NcEditorReplaceTask::IsFindSuccessed()
{
    return m_bFindSuccessed;
}

void NcEditorReplaceTask::TaskDo()
{
    for (int i = 0; i < REPLACE_TASK_ROW; i++) // 一次查找REPLACE_TASK_ROW行
    {
        FindOneLine();
        if (m_bReplaceSig
        && (m_bFindSuccessed || (m_bReplaceFlag && m_nRowIdx > m_nStRow)))
        {
            return;
        }
        else if (m_nRowIdx >= m_pCurEdit->GetRowNum())
        {
            return;
        }
        else if (m_bStopTask)
        {
            return;
        }
    }
}

void NcEditorReplaceTask::TaskPreEnd()
{
    m_bStopTask = true;
}

void NcEditorReplaceTask::FindOneLine()
{
    QString strLine = "";

    Bit32 ret = m_pCurEdit->EditGetLine(m_nRowIdx, strLine);
    if(ret != EDIT_OK)
    {
        m_sErrorStr << TR("获取%1行失败").arg(m_nRowIdx);
        return;
    }

    // 单个替换流程：如果当前光标在查找字符结尾，则先替换，再进行查找
    if (m_bReplaceSig)
    {
        Bit32 st = 0;
        if (m_nRowIdx == m_nStRow && m_nEndColIdx == m_nStCol)
        {
            st = m_nEndColIdx;
            Bit32 startP = m_nStCol - m_sStrFind.length();
            Bit32 pos = m_pCurEdit->FindStrInLine(strLine, m_regExp,0,startP);
            if(m_bChkWholeWord && pos >= 0) // 全词匹配时取得首字符位置可能有偏差
            {
                pos = strLine.indexOf(m_sStrFind, pos, Qt::CaseInsensitive);
            }
            if(pos >= 0 && pos == startP && m_nReplacedLineCount == 0) // 判断光标是否在待替换字符结尾（单个替换只替换一次）
            {
                strLine.replace(startP, m_sStrFind.length(), m_sStrReplace);
                ret = m_pCurEdit->EditSetLine(m_nRowIdx, strLine);
                if(ret != EDIT_OK)
                {
                    m_sErrorStr << TR("替换%1行失败").arg(m_nRowIdx);
                    return;
                }
                st = startP + m_sStrReplace.length();
                m_nReplacedLineCount++;
            }
        }
        Bit32 idx = m_pCurEdit->FindStrInLine(strLine, m_regExp,0, st);

        if (idx < 0)
        {
            m_nRowIdx++;
            if (m_nRowIdx >= m_pCurEdit->GetRowNum())
            {
                m_nRowIdx = 0;
                m_bReplaceFlag = true;
            }
            return;
        }
        if(m_bChkWholeWord) // 全词匹配时取得首字符位置可能有偏差
        {
            idx = strLine.indexOf(m_sStrFind, idx, Qt::CaseInsensitive);
        }
        m_nEndColIdx = idx;
        m_nEndRowIdx = m_nRowIdx;
        m_bFindSuccessed = true;
    }
    else
    {
        Bit32 pos = 0;

        while(1)
        {
            pos = m_pCurEdit->FindStrInLine(strLine, m_regExp, 0, pos);
            if(pos < 0)
            {
                m_nRowIdx++;
                return;
            }
            m_bFindSuccessed = true;
            if(m_bChkWholeWord) // 全词匹配时取得首字符位置可能有偏差
            {
                pos = strLine.indexOf(m_sStrFind, pos, Qt::CaseInsensitive);
            }
			m_nReplacedLineCount++;
            if (m_nReplacedLineCount == 5000)
            {
                m_pCurEdit->UndoRecordInit();
            }
            strLine.replace(pos, m_sStrFind.length(), m_sStrReplace);
            ret = m_pCurEdit->EditSetLine(m_nRowIdx, strLine);
            if(ret != EDIT_OK)
            {
                m_sErrorStr << TR("替换%1行失败").arg(m_nRowIdx);
                m_nRowIdx++;
                return;
            }
            
            m_nEndColIdx = pos;
            m_nEndRowIdx = m_nRowIdx;
            pos += m_sStrReplace.length();
        }

    }

}

bool NcEditorReplaceTask::TaskIsFinished()
{
    if (m_bReplaceSig
    && (m_bFindSuccessed || (m_bReplaceFlag && m_nRowIdx > m_nStRow)))
    {
        return true;
    }
    else if (m_nRowIdx >= m_pCurEdit->GetRowNum())
    {
        return true;
    }
    else
    {
        return false;
    }
}

QVariant NcEditorReplaceTask::TaskProgress()
{
    Bit32 lineCount = m_pCurEdit->GetRowNum();
    return TR("正在查找(%1/%2),已替换完成%3项").arg(m_nRowIdx).arg(lineCount).arg(m_nReplacedLineCount);
}

QStringList NcEditorReplaceTask::GetErrMsg()
{
    return m_sErrorStr;
}

Bit32 NcEditorReplaceTask::GetReplacedRow()
{
    return m_nRowIdx;
}

Bit32 NcEditorReplaceTask::GetReplacedEndRow()
{
    return m_nEndRowIdx;
}

bool NcEditorReplaceTask::IsSigReplace()
{
    return m_bReplaceSig;
}

Bit32 NcEditorReplaceTask::GetReplacedEndCol()
{
    return m_nEndColIdx;
}

Bit32 NcEditorReplaceTask::GetReplacedCount()
{
    return m_nReplacedLineCount;
}

void NcEditorReplaceTask::SetReplaceStr(QString strFind, QString strReplace,
                Bit32 stRow, Bit32 stCol, bool replaceSig)
{
    m_sStrFind = strFind;
    m_sStrReplace = strReplace;

    m_nStRow = stRow;
    m_nStCol = stCol;
    m_bReplaceSig = replaceSig;
    m_bReplaceFlag = false;
}
