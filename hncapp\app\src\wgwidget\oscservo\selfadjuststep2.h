﻿#ifndef SELFADJUSTSTEP2_H
#define SELFADJUSTSTEP2_H

#include "containerwidget.h"
#include "hmiselfadjusting.h"
#include "dirmovelayout.h"

namespace Ui {
class SelfAdjustStep2;
}

QT_BEGIN_NAMESPACE
class QWidget;
QT_END_NAMESPACE

class SelfAdjustStep2 : public ContainerWidget
{
    Q_OBJECT

public:
    explicit SelfAdjustStep2(QWidget *parent = 0);
    ~SelfAdjustStep2();

    void WidgetIn();

protected:
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    bool eventFilter(QObject *wg, QEvent *event);

private slots:
    void CheckBoxParaChg(bool checked);

    void CheckBoxStateReset(int state);

private:
    Ui::SelfAdjustStep2 *ui;
    Bit32 curFocusIndex;
    QList<QWidget *>wList;
    DirMoveLayout *dirLayout;
    QList<QWidget* >dirLayoutList;

    void LoadWidget();
    void CurValSet(Bit32 row);
    void FocusRedraw();
};

#endif // SELFADJUSTSTEP2_H
