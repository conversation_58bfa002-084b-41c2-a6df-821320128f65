﻿#ifndef NCBUSYTASK_H
#define NCBUSYTASK_H

#include <QThread>
#include <QVariant>

class NcBusyTask : public QThread
{
    Q_OBJECT
public:
    NcBusyTask(QObject *parent=0);
    ~NcBusyTask();

    void RunTask();
    void StopTask();
    void SetTaskSleep(int ms);
    QVariant GetTaskProgress();

    bool IsRunning();
signals:
    void SignalTaskFinish();
    void SignalTaskForceEnd();

private:
    int m_nTaskSleep;
    bool m_bStop;

    void run();

    virtual void TaskInit();
    virtual void TaskDo() = 0;
    virtual void TaskEnd();
    virtual void TaskPreEnd();
    virtual bool TaskIsFinished() = 0;
    virtual QVariant TaskProgress();
};

#endif // NCBUSYTASK_H
