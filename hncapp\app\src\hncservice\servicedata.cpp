﻿/*!
 * @file servicedata.cpp
 * @brief 后台服务数据处理模块
 * @note 校验，报警等后台数据处理
 *
 * @version V1.00
 * @date 2023/6/5
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */
#include "spposhold.h"
//#include "toolgroup.h"

#ifdef _XIANGYANG
#include "hmitemperature.h"
#endif

#include "servicedata.h"

ServiceData::ServiceData()
{
    this->toolLife.ToollifeInit(); // 刀具寿命数据初始化
    SpposholdInit();

#ifdef _XIANGYANG
	Temper_TrsCom_PowerInit();  //温度采集上电初始化
#endif

    m_pSimuMill = new SimuMill();
    m_pSimuTurn = new SimuTurn();
}

ServiceData& ServiceData::Instance()
{
    static ServiceData sData;

    return sData;
}

ServiceData::~ServiceData()
{
    SpposholdExit();
}

void ServiceData::ServiceDataRefresh()
{
    this->alarmData.Refresh();
    toolLife.DataRefresh();
    SpposholdRefresh();
    this->sysTime.SysTimeRefresh();

    m_pSimuMill->RefreshMacPos2AllView();
    m_pSimuTurn->RefreshMacPos2AllView();
}

void ServiceData::ServiceDataRefresh2() // 20ms
{
    m_pSimuMill->ModeRefresh();
    m_pSimuTurn->ModeRefresh();
}
