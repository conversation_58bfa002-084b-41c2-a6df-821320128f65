﻿/*!
 * @file servicedata.h
 * @brief 后台服务数据处理模块
 * @note 校验，报警等后台数据处理
 *
 * @version V1.00
 * @date 2023/6/5
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */
#ifndef SERVICEDATA_H
#define SERVICEDATA_H

#include "alarmdata.h"
#include "toollife.h"
#include "usermacrodata.h"
#include "systime.h"
#include "hmimcode.h"
#include "simumacposdata.h"
#include "simumill.h"
#include "simuturn.h"

class ServiceData
{
public:
    ServiceData();
    ~ServiceData();
    static ServiceData& Instance();
    void ServiceDataRefresh();
    void ServiceDataRefresh2(); // 20ms

    Toollife toolLife;
    UserMacroData userMacro;
    ChanMacroData chanMacroData;
    NcMacroData ncMacro;
    AlarmData alarmData;
    SysTime sysTime;
    HmiMcode hmiMcode;
    SimuMill *m_pSimuMill;
    SimuTurn *m_pSimuTurn;
};

#endif // SERVICEDATA_H
