<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OverLimitLogList</class>
 <widget class="QWidget" name="OverLimitLogList">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>577</width>
    <height>435</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QTableWidget" name="tableWidget">
     <property name="font">
      <font>
       <pointsize>12</pointsize>
      </font>
     </property>
     <property name="contextMenuPolicy">
      <enum>Qt::DefaultContextMenu</enum>
     </property>
     <property name="horizontalScrollBarPolicy">
      <enum>Qt::ScrollBarAlwaysOff</enum>
     </property>
     <property name="selectionMode">
      <enum>QAbstractItemView::ExtendedSelection</enum>
     </property>
     <property name="columnCount">
      <number>0</number>
     </property>
     <attribute name="horizontalHeaderMinimumSectionSize">
      <number>0</number>
     </attribute>
    </widget>
   </item>
   <item>
    <widget class="QScrollBar" name="verticalScrollBar">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
