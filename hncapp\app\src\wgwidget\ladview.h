#ifndef LADVIEW_H
#define LADVIEW_H

#include <QWidget>

#include "hncdatatype.h"
#include "lad_def.h"

#include "containerwidget.h"
#include <QLabel>

namespace Ui {
class LadView;
}

class LadView : public ContainerWidget
{
    Q_OBJECT

public:
    explicit LadView(QWidget *parent = 0);
    ~LadView();

    void SetVerticalScrollBarVal(int val);

    Bit8 ladRefreshFlag; // 梯形图刷新标记[0/1]，0：比较刷新，1：强制刷新；

    // 设置调试标记
    void lad_set_diag_flag(void);
    // 清除调试标记
    void lad_clr_diag_flag(void);

    // 设置编辑标记
    void lad_set_edit_flag();
    // 清除编辑标记
    void lad_clr_edit_flag();

    //  查找界面
    void lad_find_redraw(Bit8 flag);

    void ladChgVarMode();  // 切换诊断的取值模式

    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
private:
    Ui::LadView *ui;
    QPainter *painter;
    Bit8 ladDiagView;//梯形图诊断界面标记
    Bit8 ladEditFlag; // 梯形图界面可编辑标记
    Bit32 wheelFlag;
    QLabel* lable_6;
    QLabel* lable_6_1;
    QLabel* lable_6_2;
    QLabel* lable_6_3;
    QPixmap *m_midPixmap;

    // 获取元件参数的正则
    void GetCellParamRegExp(QRegExp &rx);

    void LadResfreshScrollBarMax();
    void SetBeginRow(int row);

    void wheelEvent(QWheelEvent *wheelEvt);

    void LadMainRedraw();

    // 画出梯图的一页
    // flag ：[0/1]，0：比较刷新，1：强制刷新；
    void ladmon_refresh(Bit8 flag);

    // 画梯图标题栏
    void lad_view_draw_title(void);

    // 显示行号  Y ：行位置 n ：行号
    void lad_draw_row_No(Bit32 y, Bit32 n);

    // 显示一行梯形图元件 y ：梯形图元件开始位置  pln ：梯形图元件行 flag ：[0/1]，0：比较刷新，1：强制刷新；
    void lad_draw_row(Bit32 y, SLadRow *pln, Bit8 flag);

    // 显示元件监控状态 x ：元件单元X坐标 y ：元件单元Y坐标 p ：元件单元
    QColor lad_draw_cell_diagnose_view(/*Bit32 x, Bit32 y, */ SLadCell *p);

    //   判断能强制允许、禁止的元件 p ：元件单元
    Bit8 lad_view_able(SLadCell* p);


    //  显示元件单元参数  x ：元件单元X坐标 y ：元件单元Y坐标  p ：元件单元 flag ：
    void lad_draw_cell_buf(Bit32 x, Bit32 y,  SLadCell *p, Bit8 flag);

    Bit8 lad_get_reg_debug(Bit8 type);

    //          获得元件参数区域
    void lad_get_parm_pos( SLadCell* p, QRect* qrect);

    Bit32 lad_get_reg_rt_val(SLadCell *p);

    void lad_draw_cell_comments(Bit32 x, Bit32 y, SLadCell *p);

    // 显示元件边框  x ：元件单元X坐标 y ：元件单元Y坐标 p ：元件单元
    void lad_draw_cell_border(Bit32 x, Bit32 y,  SLadCell *p);

    //  画直线 x1 ：起点X坐标  y1 ：起点Y坐标 x2 ：终点X坐标 y2 ：终点Y坐标
    void lad_draw_line(Bit32 x1, Bit32 y1, Bit32 x2, Bit32 y2);

    // 显示元件名 x ：元件单元X坐标 y ：元件单元Y坐标 p ：元件单元
    void lad_draw_cell_name( Bit32 x, Bit32 y, SLadCell *p);

    // 画矩形 x1 ：矩形左边坐标 y1 ：矩形上边坐标 x2 ：矩形右边坐标 y2 ：矩形下边坐标
    void lad_draw_rectangle( Bit32 x1, Bit32 y1, Bit32 x2, Bit32 y2);

    // 画并联线 x ：元件起点X坐标  y ：元件起点Y坐标
    void lad_draw_ver(Bit32 x, Bit32 y);

    //  画附加显示栏
    void lad_view_draw_cell_info(void);

    //  画输入窗口
    void lad_view_draw_input_dlg(void);

    bool eventFilter(QObject *wg, QEvent *event);

    void LadRedraw();
    bool CmdInputMinus(uBit16 cmdId, Bit8 n);
private slots:
    void LadViewSelCellRow(int row);

};

#endif // LADVIEW_H
