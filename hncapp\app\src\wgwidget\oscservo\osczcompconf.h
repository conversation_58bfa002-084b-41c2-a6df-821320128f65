﻿#ifndef OSCZCOMPCONF_H
#define OSCZCOMPCONF_H

#include "dirmovelayout.h"
#include "containerwidget.h"

namespace Ui {
class OscZCompConf;
}

QT_BEGIN_NAMESPACE
class QIntValidator;
class QRegExpValidator;
QT_END_NAMESPACE

class OscZCompConf : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscZCompConf(QWidget *parent = 0);
    ~OscZCompConf();

    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    bool eventFilter(QObject *target, QEvent *event);
    //void keyPressEvent(QKeyEvent *ev);

private slots:
    void SlotCheckBoxAutoMeasureClicked(bool checked);

    void SlotCheckBoxCoordOnOffClicked(bool checked);

    void SlotCheckBoxBlowOnOffClicked(bool checked);

private:
    Ui::OscZCompConf *ui;
    Bit32 curFocusIndex;
    QIntValidator *intValidator;
    QRegExpValidator *regValidator;
    QList<QWidget *>wList;
    DirMoveLayout *dirLayout;
    QList<QWidget* >dirLayoutList;
    void FocusRedraw();
    void LoadData();
    void DataSet(Bit32 idx);
};

#endif // OSCZCOMPCONF_H
