#-------------------------------------------------
#
# Project created by QtCreator 2016-05-24T16:02:41
#
#-------------------------------------------------

QT       += core
QT       += gui
QT       += declarative
QT       += xml
QT       += webkit network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets printsupport


unix:!macx:KVER=$$system(uname -r)

win32: DESTDIR = $$PWD/../../winlib/
win32: TARGET = qthlxcnc
unix:!macx:contains(KVER, 3.14.*):TARGET = $$PWD/../../bin/qthlxcnc
unix:!macx:contains(KVER, 3.4.*):TARGET = $$PWD/../../lib_3.4/qthlxcnc
OBJECTS_DIR = ../obj

TEMPLATE = app

unix:!macx: QMAKE_CXXFLAGS += -std=c++0x

unix:!macx: DEFINES += _LINUX_ _LINUX LINUX3_4
win32: DEFINES += _WIN32 _MBCS

win32: QMAKE_LFLAGS_RELEASE += /NODEFAULTLIB:LIBC.lib /NODEFAULTLIB:LIBCMT.lib
win32: QMAKE_LFLAGS_DEBUG += /NODEFAULTLIB:LIBCD.lib /NODEFAULTLIB:LIBCMTD.lib

QMAKE_CFLAGS_RELEASE = -O2 -MT -g
QMAKE_CFLAGS_DEBUG = -Zi -MTd -g

INCLUDEPATH = $$PWD/../../../include/api $$PWD/../src/wgwidget/hmimain

unix:!macx:contains(KVER, 3.14.*): INCLUDEPATH += /usr/xenomai/include
unix:!macx:contains(KVER, 3.4.*): INCLUDEPATH += /usr/xenomai/include /linux-3.4.6/include

unix:!macx: LIBS += -lm -lpthread -lrt -lts -L/usr/xenomai/lib -lxenomai -lnative
unix:!macx:contains(KVER, 3.14.*): LIBS += -L$$PWD/../../lib/linux
unix:!macx:contains(KVER, 3.4.*): LIBS += -L$$PWD/../../lib_3.4
unix:!macx: LIBS += -L$$PWD/../../shrlib/tslib/lib -L$$PWD/../../shrlib

win32: LIBS += -lgdi32 -luser32 -lws2_32 -lsetupapi

unix:!macx: LIBS += -L$$PWD/../../lib/linux -lqthmi -lhncapi3 -lsqlite3 -lnchid -larith -lcomm -lpubapi -ltinyxml -lgettext -lqrencode -lhuffman -lmempool -lcurl -ljson -lcloudapi -lmosq -liconv -lnclink -lusrscada -llua -lCollisionDetection -lOpcode
win32: LIBS += -L$$PWD/../../lib/win32 -lqthmi -lhxpapi3 -lsqlite3 -lhid -lnchid -lpubapi -lhxplad3 -ldecoder -lchanctrl -ldatamath -lintpltor -lkinemtcs -laxesctrl -lsmooth -lrobot -lvplan -lmotionplan -lmov_space -ltinyxml -lgettext -lqrencode -lhuffman -lmempool -lusrscada -llua -larithapi -lCollisionDetection -lOpcode

unix:!macx: CONFIG += release

SOURCES += $$PWD/../src/main.cpp

HEADERS += \
    $$PWD/../src/wgwidget/hmimain/hmimain.h

RESOURCES += \
    $$PWD/hmirc.qrc

win32 {
RESOURCES += \
    $$PWD/../src/tools/res.qrc
}

#win32:PRE_TARGETDEPS += $$PWD/../lib/win32/usrscada.lib
#unix:!macx:contains(KVER, 3.14.*): PRE_TARGETDEPS += $$PWD/../lib/linux/libusrscada.a
#unix:!macx:contains(KVER, 3.4.*): PRE_TARGETDEPS += $$PWD/../lib_3.4/libusrscada.a

win32:PRE_TARGETDEPS += $$PWD/../../lib/win32/qthmi.lib
unix:!macx:contains(KVER, 3.14.*): PRE_TARGETDEPS += $$PWD/../../lib/linux/libqthmi.a
unix:!macx:contains(KVER, 3.4.*): PRE_TARGETDEPS += $$PWD/../../lib_3.4/libqthmi.a
