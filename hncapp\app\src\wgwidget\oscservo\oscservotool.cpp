﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservotool.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-换刀时间界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年5月24日
* 说    明：
*/

#include "hmioscservo.h"
#include "hmioscproc.h"
#include "oscwave.h"

#include "oscservotool.h"
#include "ui_oscservotool.h"

OscServoTool::OscServoTool(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoTool)
{
    ui->setupUi(this);

    pOscToolTime = new OscWave(this, HmiOscServo::OSC_SERVO_TOOL, "TOOL");
    ui->verticalLayout_3->addWidget(pOscToolTime);
}

OscServoTool::~OscServoTool()
{
    delete ui;
}

void OscServoTool::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        this->LoadInfo();
//        this->LoadWidget();
        this->SetColorStyle();
        pOscToolTime->SetAxisName(QStringList() << "");
//        if(messageValue == "INIT") // 初始化，清除上次在该界面记住的当前行
//        {
//            curRow = 0;
//        }
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if (messageid == MsgData::REFRESH)
    {
        this->Refresh();
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_OSCSERVOSTART")
        {
            this->Reset(); // 开始采样时才清除上一次的图形
        }
        else if (messageValue == "MSG_OSCSERVOSTOP")
        {
            this->LoadInfo();
        }
        else if(messageValue == "OSCSERVOCOLOR")
        {
            this->SetColorStyle();
        }
    }
}

void OscServoTool::Reset()
{ // 清空图形
    pOscToolTime->ClearPoint();
    this->lastEndPos = 0;
}

void OscServoTool::Refresh()
{
    QVector<double> x;
    QVector<double> y0;

    Bit32 i = 0;
    Bit32 stPos = 0;
    Bit32 edPos= 0;
    Bit64 *ch0_addr = NULL;

    if (oscproc_get_stat() != OSC_PROC_START)
    {
        this->lastEndPos = 0; // 停止后需要置零
        return;
    }

    x.clear();
    y0.clear();

    stPos = this->lastEndPos;
    edPos = oscproc_get_pos();
    this->lastEndPos = edPos;

    ch0_addr = oscproc_get_smpldata(0);
    if (NULL == ch0_addr)
    {
        return;
    }

    for (i = stPos+1; i < edPos; ++i)
    {
        y0.append(HmiOscServo::CalcToolTimeYVal(ch0_addr[i]));
        x.append(i * oscproc_get_smpl_period());
    }

    pOscToolTime->LineZeroAddPoint(x, y0);
    pOscToolTime->WaveReplot();
}

void OscServoTool::LoadInfo()
{
    fBit64 time = 0;

    if (oscproc_get_total() > 0)
    {
        HmiOscServo::CalcToolChangeTime(time);
        HmiOscServo::OscservoReportRecord(time, 0);
    }

    ui->label_2->setText(QString::number(time, 'f', 3));
}

void OscServoTool::SetColorStyle()
{
    // 默认黑色风格
    QColor bk(0,0,0); // 背景
    QColor gd(0,0,0); // 网格
    QColor ft(0,0,0); // 字体颜色
    QColor c1(0,0,0); // 曲线1
    QColor c2(0,0,0); // 曲线2
    QColor c3(0,0,0); // 曲线3
    QColor c4(0,0,0); // 曲线4

    HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

    QPalette palette;
    palette.setColor(QPalette::Background, bk);
    ui->frame->setAutoFillBackground(true);
    ui->frame->setPalette(palette);

    pOscToolTime->SetColor(bk, gd, ft, c1, c2, c3, c4);
}
