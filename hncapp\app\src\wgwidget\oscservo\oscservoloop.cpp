﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservoloop.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-全闭环诊断界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年6月21日
* 说    明：
*/

#include <qmath.h>

#include "hncaxis.h"
#include "hnccrds.h"
#include "hncsys.h"
#include "hncsmpl.h"
#include "hncparamandef.h"

#include "common.h"
#include "hotkeycfg.h"
#include "hmioscproc.h"
#include "hmioscservo.h"
#include "msgchan.h"
#include "oscwave.h"

#include "oscservoloop.h"
#include "ui_oscservoloop.h"

OscServoLoop::OscServoLoop(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoLoop)
{
    ui->setupUi(this);

    pOscWaveLoop = new OscWave(this, HmiOscServo::OSC_SERVO_LOOP, "");
    ui->verticalLayout_2->addWidget(pOscWaveLoop);

    // 参数初始化
    this->curRow = 0;
    this->firstFlag = false;
    memset(&oldLoopConf, 0, sizeof(oldLoopConf));

    this->ConfChangeChk();

    ui->labelAxisName->setAlignment(Qt::AlignCenter);
    ui->labelImg->setAlignment(Qt::AlignCenter);
    ui->labelImg->setText(HotKeyCfg::GetDirChgMsg());
    ui->labelImg->setFont(QFont(FONT_TYPE, 10));

    // 列表设置
    ui->tableView->setItemDelegate(new LoopListDelegate);
    ui->tableView->setModel(new LoopListModel);
    ui->tableView->setFont(QFont(FONT_TYPE, 12));
    ui->tableView->horizontalHeader()->setVisible(false);
    ui->tableView->verticalHeader()->setVisible(false);
    ui->tableView->horizontalHeader()->setStretchLastSection(true);
    ui->tableView->horizontalHeader()->setResizeMode(QHeaderView::Stretch);
    ui->tableView->setAlternatingRowColors(true);
    ui->tableView->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableView->installEventFilter(this);

//    ui->leftBtn->setShortcut(QKeySequence(Qt::AltModifier + Qt::Key_Left));
//    ui->rightBtn->setShortcut(QKeySequence(Qt::AltModifier + Qt::Key_Right));
//    ui->leftBtn->setStyleSheet("padding: 0px;");
//    ui->rightBtn->setStyleSheet("padding: 0px;");
    ui->leftBtn->setFocusPolicy(Qt::NoFocus);
    ui->rightBtn->setFocusPolicy(Qt::NoFocus);
}

OscServoLoop::~OscServoLoop()
{
    delete ui;
}

void OscServoLoop::LoadAxisVal()
{
    QString axisName = "";
    QStringList strList;
    Bit32 type = 0;
    QString str = "";
    Bit32 ch = ActiveChan();

    strList.clear();

    HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetCurAxesConf(), &type);
    if (type == 1 || type == 7) // 直线轴  7(主轴做进给轴使用)和1一样按直线轴处理
    {
        str = TR("um");
    }
    else
    {
        str = TR("deg");
    }

    HNC_CrdsGetValue(HNC_CRDS_CH_G5X_ZERO, HmiOscServo::GetCurAxesConf(), &HmiOscServo::s_Conf[ch].stLoopConf.zeroPos, ch, 54 + HmiOscServo::s_Conf[ch].stLoopConf.crd);
    axisName = HmiOscServo::OscAxisToName(HmiOscServo::GetCurAxesConf());
    ui->labelAxisName->setText(TR("%1轴").arg(axisName));
    pOscWaveLoop->SetAxisName(QStringList() << str << axisName);

    this->LoadView();
}

void OscServoLoop::LoadWave()
{
    Bit32 type = 0;
    Bit32 dir = 1;
    Bit32 unit = 100000;
    fBit64 rangLower = 0.0;
    fBit64 rangUpper = 0.0;
    fBit64 rangStep = 0.0;
    Bit32 num = 0;
    Bit32 ch = ActiveChan();
	if (0 != HmiOscServo::s_Conf[ch].stLoopConf.dist)	// 除0保护
	{
		num = (Bit32)(abs(HmiOscServo::s_Conf[ch].stLoopConf.edPos - HmiOscServo::s_Conf[ch].stLoopConf.stPos) / HmiOscServo::s_Conf[ch].stLoopConf.dist + 1);
	}
    

    if (HmiOscServo::s_Conf[ch].stLoopConf.stPos > HmiOscServo::s_Conf[ch].stLoopConf.edPos)
    {
        dir = -1;
    }

    HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetCurAxesConf(), &type);
    if (type == 1 || type == 7)//  7(主轴做进给轴使用)和1一样按直线轴处理
    {
        HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &unit);
    }
    else
    {
        HNC_SystemGetValue(HNC_SYS_TURN_UNIT, &unit);
    }

    rangLower = HmiOscServo::s_Conf[ch].stLoopConf.stPos;
    rangUpper = HmiOscServo::s_Conf[ch].stLoopConf.stPos + (num - 1) * HmiOscServo::s_Conf[ch].stLoopConf.dist * dir;
    rangStep = Bit32((rangUpper - rangLower) / 10);
    pOscWaveLoop->SetXRange(rangLower, rangUpper, rangStep);
}

void OscServoLoop::LoadView()
{
    LoopListModel* model = static_cast<LoopListModel*>(ui->tableView->model());
    model->refresh();
    if(ui->tableView->hasFocus()) // 解决refresh后选中状态丢失
    {
        if (curRow < 0 || curRow >= ui->tableView->verticalHeader()->count())
        {
            ui->tableView->selectRow(0);
        }
        else
        {
            ui->tableView->selectRow(curRow);
        }
    }
}

bool OscServoLoop::ConfChangeChk()
{
    bool flag = false;
    Bit32 ch = ActiveChan();
    if(oldLoopConf.crd != HmiOscServo::s_Conf[ch].stLoopConf.crd)
    {
        oldLoopConf.crd = HmiOscServo::s_Conf[ch].stLoopConf.crd;
        flag = true;
    }
    if(oldLoopConf.dist != HmiOscServo::s_Conf[ch].stLoopConf.dist)
    {
        oldLoopConf.dist = HmiOscServo::s_Conf[ch].stLoopConf.dist;
        flag = true;
    }
    if(oldLoopConf.edPos != HmiOscServo::s_Conf[ch].stLoopConf.edPos)
    {
        oldLoopConf.edPos = HmiOscServo::s_Conf[ch].stLoopConf.edPos;
        flag = true;
    }
    if(oldLoopConf.spd != HmiOscServo::s_Conf[ch].stLoopConf.spd)
    {
        oldLoopConf.spd = HmiOscServo::s_Conf[ch].stLoopConf.spd;
        flag = true;
    }
    if(oldLoopConf.stPos != HmiOscServo::s_Conf[ch].stLoopConf.stPos)
    {
        oldLoopConf.stPos = HmiOscServo::s_Conf[ch].stLoopConf.stPos;
        flag = true;
    }
    if(oldLoopConf.zeroPos != HmiOscServo::s_Conf[ch].stLoopConf.zeroPos)
    {
        oldLoopConf.zeroPos = HmiOscServo::s_Conf[ch].stLoopConf.zeroPos;
        flag = true;
    }
    return flag;
}

void OscServoLoop::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        Bit32 row = ui->tableView->currentIndex().row();
        if (row >= 0)
        {
            curRow = row;
        }
        this->LoadAxisVal();
        this->LoadWave();
        this->SetColorStyle();
        if(this->ConfChangeChk())
        {
            oscproc_smpldata_reset();
            oscproc_init_data();
            this->Reset();
        }
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::SETFOCUS)
    {
        this->SetTableFoucs();
        if(messageValue == "CLEARFOCUS")
        {
            this->ClearTableFoucs();
        }
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_OSCSERVOSTART")
        {
            this->Reset(); // 开始采样时才清除上一次的图形
        }
        else if(messageValue == "MSG_OSCSERVOSAVE")
        {
            MessageOut(TR("当前界面没有保存功能"));
        }
        else if (messageValue == "MSG_OSCSERVOSTOP")
        {
            MessageOut("");
            this->LoadInfo();
            this->LoadView();
        }
        else if(messageValue == "OSCSERVOCOLOR")
        {
            this->SetColorStyle();
        }
    }
    else if(messageid == MsgData::REFRESH)
    {
        if (oscproc_get_stat() == OSC_PROC_START)
        {
            MessageOut(TR("采样中…"));
            return;
        }
    }
    else if(messageid == MsgData::KEYBOARD && messageValue == "TURNTABSLEFT")
    {
        this->on_leftBtn_clicked();
    }
    else if(messageid == MsgData::KEYBOARD && messageValue == "TURNTABSRIGHT")
    {
        this->on_rightBtn_clicked();
    }
}

void OscServoLoop::resizeEvent(QResizeEvent *)
{
    this->firstFlag = false;
}

bool OscServoLoop::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::Paint && !firstFlag)
    {
        // 此处可以获得tableWidget实际显示高度，然后设置表格每行高度可以自适应多种分辨率布局,
        Bit32 nRowHeight = ui->tableView->height() / 11;
        ui->tableView->verticalHeader()->setDefaultSectionSize(nRowHeight);
        ui->tableView->verticalHeader()->setResizeMode(QHeaderView::Stretch);

        ui->tableView->setColumnWidth(0,ui->tableView->width() / 2);
        ui->tableView->setColumnWidth(1,ui->tableView->width() / 2);

        ui->leftBtn->setIconSize(ui->leftBtn->size());
        ui->rightBtn->setIconSize(ui->rightBtn->size());
        //ui->leftBtn->setIcon(PixMapToSize(ui->leftBtn->size(), "../pic/left-2.png"));
        //ui->rightBtn->setIcon(PixMapToSize(ui->rightBtn->size(), "../pic/right-1.png"));

        this->firstFlag = true;
        this->OnBtFlagChange();
    }
    return QObject::eventFilter(target, event);
}

void OscServoLoop::OnBtFlagChange()
{
	HmiOscServo::OscservoSamplReset();
	HmiOscServo::OscservoInit();
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();
    if(curAxesIndex <= 0)
    {
        HmiOscServo::SetCurAxesIndex(0);
        ui->leftBtn->setProperty("valid",false);
        ui->rightBtn->setProperty("valid",true);
        ui->leftBtn->style()->polish(ui->leftBtn);
        ui->rightBtn->style()->polish(ui->rightBtn);
        return;
    }
    if(HmiOscServo::GetIndexAxesConf(curAxesIndex + 1) < 0)
    {
        ui->leftBtn->setProperty("valid",true);
        ui->rightBtn->setProperty("valid",false);
        ui->leftBtn->style()->polish(ui->leftBtn);
        ui->rightBtn->style()->polish(ui->rightBtn);
        return;
    }
    ui->leftBtn->setProperty("valid",true);
    ui->rightBtn->setProperty("valid",true);
    ui->leftBtn->style()->polish(ui->leftBtn);
    ui->rightBtn->style()->polish(ui->rightBtn);
}

void OscServoLoop::on_leftBtn_clicked()
{
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();

    if (oscproc_get_stat() == OSC_PROC_START)
    {
        MessageOut(TR("采样中禁止轴切换!"));
        return;
    }

    if(curAxesIndex <= 0)
    {
        HmiOscServo::SetCurAxesIndex(0);
    }
    else
    {
        curAxesIndex--;
        HmiOscServo::SetCurAxesIndex(curAxesIndex);
    }
    this->LoadAxisVal();
    MsgChan::Instance().TranMsg(MsgData::SETFOCUS, "");
    this->OnBtFlagChange();
    oscproc_smpldata_reset();
    oscproc_init_data();
    this->Reset();
    //HmiOscServo::OscservoLoadGcode(1); // 重新生成并加载G代码
}

void OscServoLoop::on_rightBtn_clicked()
{
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();
//    Bit32 type = 0;

    if (oscproc_get_stat() == OSC_PROC_START)
    {
        MessageOut(TR("采样中禁止轴切换!"));
        return;
    }

    if(curAxesIndex >= (TOTAL_AXES_PER_CHN - 1))
    {
        curAxesIndex = TOTAL_AXES_PER_CHN - 1;
        HmiOscServo::SetCurAxesIndex(curAxesIndex);
    }
    else if(HmiOscServo::GetIndexAxesConf(curAxesIndex + 1) < 0)
    {
        return;
    }
    else
    {
//        HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetIndexAxesConf(curAxesIndex + 1), &type);
//        if(type != 1) // 只考虑直线轴
//        {
//            return;
//        }
        curAxesIndex++;
        HmiOscServo::SetCurAxesIndex(curAxesIndex);
    }
    this->LoadAxisVal();
    MsgChan::Instance().TranMsg(MsgData::SETFOCUS, "");
    this->OnBtFlagChange();
    oscproc_smpldata_reset();
    oscproc_init_data();
    this->Reset();
    //HmiOscServo::OscservoLoadGcode(1); // 重新生成并加载G代码
}

void OscServoLoop::SetTableFoucs()
{
    if (ui->tableView->verticalHeader()->count() <= 0)
    {
        // 行数为0时，不设置焦点
        return;
    }

    ui->tableView->setFocus();
    if (curRow < 0 || curRow >= ui->tableView->verticalHeader()->count())
    {
        ui->tableView->selectRow(0);
    }
    else
    {
        ui->tableView->selectRow(curRow);
    }
}

void OscServoLoop::ClearTableFoucs()
{
    ui->tableView->clearSelection();
    ui->tableView->clearFocus();
}

void OscServoLoop::LoadInfo()
{
    QVector<double> x;
    QVector<double> y0;

    Bit32 type = 0;
    Bit32 unit = 100000;
    fBit64 cof = fabs(smpl_calc_follow_err_coef(HmiOscServo::GetCurAxesConf())) * 1000;
    Bit32 last = oscproc_get_total();
    Bit64 startPos = 0;
    Bit64 endPos = 0;
    Bit32 step = 0;
    Bit32 i = 0;
    Bit32 dir = 1;
    Bit64 *ch0_addr = NULL;
    Bit64 *ch1_addr = NULL;
    Bit32 ch = ActiveChan();

    fBit64 outputData[LOOP_MAX_SAMP_POINT] = {0};
    Bit32 dataNum = 0;

    HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetCurAxesConf(), &type);
    if (type == 1 || type == 7) //  7(主轴做进给轴使用)和1一样按直线轴处理
    {
        HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &unit);
    }
    else
    {
        HNC_SystemGetValue(HNC_SYS_TURN_UNIT, &unit);
    }

    startPos = HmiOscServo::s_Conf[ch].stLoopConf.stPos * unit + HmiOscServo::s_Conf[ch].stLoopConf.zeroPos;
    endPos = HmiOscServo::s_Conf[ch].stLoopConf.edPos * unit + HmiOscServo::s_Conf[ch].stLoopConf.zeroPos;
    step = HmiOscServo::s_Conf[ch].stLoopConf.dist * unit;

	if (step == 0)		// 除0保护
	{
		return;
	}
    dataNum = abs(endPos - startPos) / step + 1;

    if(startPos > endPos)
    {
        dir = -1;
    }

    ch0_addr = oscproc_get_smpldata(0);
    ch1_addr = oscproc_get_smpldata(1);
    if (NULL == ch0_addr || NULL == ch1_addr)
    {
        return;
    }

    if(last <= 0)
    {
        return;
    }

    smpl_calc_pos_relative_data(ch0_addr, ch1_addr, last, startPos, endPos, step, outputData);

    for(i = 0; i < dataNum; i++)
    {
		if (unit == 0)
		{
			return;
		}
        x.append((startPos - HmiOscServo::s_Conf[ch].stLoopConf.zeroPos + dir * step * i)/unit);
        y0.append(outputData[i] * cof);
    }
    pOscWaveLoop->LineZeroAddPoint(x, y0);
    pOscWaveLoop->WaveReplot();
}

void OscServoLoop::Reset()
{ // 清空图形
    pOscWaveLoop->ClearPoint();
}

void OscServoLoop::SetColorStyle()
{
    // 默认黑色风格
    QColor bk(0, 0, 0); // 背景
    QColor gd(0, 0, 0); // 网格
    QColor ft(0, 0, 0); // 字体颜色
    QColor c1(0, 0, 0); // 曲线1
    QColor c2(0, 0, 0); // 曲线2
    QColor c3(0, 0, 0); // 曲线3
    QColor c4(0, 0, 0); // 曲线4

    HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

    QPalette palette;
    palette.setColor(QPalette::Background, bk);
    ui->frame->setAutoFillBackground(true);
    ui->frame->setPalette(palette);

    pOscWaveLoop->SetColor(bk, gd, ft, c1, c2, c3, c4);
}


void LoopListModel::refresh()
{
    this->reset();
}

int LoopListModel::rowCount(const QModelIndex &) const
{
    Bit32 num = 0;
    Bit32 ch = ActiveChan();
    if (HmiOscServo::s_Conf[ch].stLoopConf.dist != 0)       // 除0保护
	{
		num = (Bit32)(abs(HmiOscServo::s_Conf[ch].stLoopConf.edPos - HmiOscServo::s_Conf[ch].stLoopConf.stPos) / HmiOscServo::s_Conf[ch].stLoopConf.dist + 1);
	}
    
    return num;
}

int LoopListModel::columnCount(const QModelIndex &) const
{
    return 2;
}

Qt::ItemFlags LoopListModel::flags(const QModelIndex &index) const
{
    Qt::ItemFlags flags = QAbstractItemModel::flags(index);

    flags |= Qt::ItemIsSelectable;

    return flags;
}

QVariant LoopListModel::data(const QModelIndex &index, int role) const
{
    Bit32 dir = 1;
    Bit32 dataNum = 0;
    Bit32 posVal = 0;
    Bit32 ch = ActiveChan();

    fBit64 outputData[LOOP_MAX_SAMP_POINT] = {0};
    Bit32 unit = 100000;
    Bit32 type = 0;
    Bit64 *chn_addr1 = NULL, *chn_addr2 = NULL;
    fBit64 errVal = 0.0;
    if (!index.isValid())
    {
        return QVariant();
    }

    if (role == Qt::DisplayRole && index.column() == 0)
    {
        if (HmiOscServo::s_Conf[ch].stLoopConf.stPos > HmiOscServo::s_Conf[ch].stLoopConf.edPos)
        {
            dir = -1;
        }
        if (0 != HmiOscServo::s_Conf[ch].stLoopConf.dist)       // 除0保护
        {
            dataNum = (Bit32)(abs(HmiOscServo::s_Conf[ch].stLoopConf.edPos - HmiOscServo::s_Conf[ch].stLoopConf.stPos) / HmiOscServo::s_Conf[ch].stLoopConf.dist + 1);
        }
        posVal = HmiOscServo::s_Conf[ch].stLoopConf.stPos + index.row() * HmiOscServo::s_Conf[ch].stLoopConf.dist * dir;
        return QString::number(posVal);
    }
    if (role == Qt::DisplayRole && index.column() == 1)
    {
        HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetCurAxesConf(), &type);
        if (type == 1 || type == 7) //  7(主轴做进给轴使用)和1一样按直线轴处理
        {
            HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &unit);
        }
        else
        {
            HNC_SystemGetValue(HNC_SYS_TURN_UNIT, &unit);
        }
        chn_addr1 = oscproc_get_smpldata(0);
        chn_addr2 = oscproc_get_smpldata(1);
        if(chn_addr1 == NULL || chn_addr2 == NULL)
        {
            return QVariant();
        }
        smpl_calc_pos_relative_data(chn_addr1, chn_addr2, oscproc_get_total(), HmiOscServo::s_Conf[ch].stLoopConf.stPos*unit+HmiOscServo::s_Conf[ch].stLoopConf.zeroPos,\
                                    HmiOscServo::s_Conf[ch].stLoopConf.edPos*unit+HmiOscServo::s_Conf[ch].stLoopConf.zeroPos, HmiOscServo::s_Conf[ch].stLoopConf.dist*unit, outputData);
        errVal = outputData[index.row()] * fabs(smpl_calc_follow_err_coef(HmiOscServo::GetCurAxesConf())) * 1000;//um
        return QString::number(errVal, 'f', 3);
    }
    else if(role == Qt::TextAlignmentRole && index.column() == 1)
    {
        return int(Qt::AlignRight | Qt::AlignVCenter);
    }

    return QVariant();
}
