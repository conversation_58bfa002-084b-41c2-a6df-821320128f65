#include "gwg_pos_main.h"
#include "ui_gwg_pos_main.h"
#include <QDebug>
#include "msgdata.h"
#include "hncaxis.h"

GwgPosMain::GwgPosMain(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::GwgPosMain)
{
    ui->setupUi(this);

    setStyleSheet(QString("QLabel{color : white;}"));
}

GwgPosMain::~GwgPosMain()
{
    delete ui;
}

void GwgPosMain::refreshPos()
{
    Bit64 value;
    HNC_AxisGetValue(HNC_AXIS_ACT_POS, 0, &value);
    ui->actX->setText(QString::number(double(value) / 100000, 'f', 4));
    HNC_AxisGetValue(HNC_AXIS_ACT_POS, 1, &value);
    ui->actY->setText(QString::number(double(value) / 100000, 'f', 4));
    HNC_AxisGetValue(HNC_AXIS_ACT_POS, 2, &value);
    ui->actZ->setText(QString::number(double(value) / 100000, 'f', 4));
}

void GwgPosMain::showAllPos()
{
    
}

void GwgPosMain::showAbsolutePos()
{
    
}

void GwgPosMain::showRelativePos()
{
    
}

void GwgPosMain::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if (messageid == MsgData::REFRESH)
    {
        refreshPos();
    }

    else if (messageid == MsgData::GENERAL)
    {

    }
}
