﻿/*
* Copyright (c) 2016, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：hmicommon.cpp
* 文件标识：根据配置管理计划书
* 摘    要：共同功能
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2016年2月14日
* 说    明：
*/
#ifdef _LINUX
#include <errno.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/vfs.h>
#include <linux/if.h>
#include <sys/types.h>
#endif
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <time.h>
#include <math.h>

#include <QApplication>
#include <QDialog>
#include <QTextStream>
#include <QDateTime>
#include <QIODevice>
#include <QDir>
#include <QKeyEvent>
#include <QScrollBar>
#include <QHeaderView>
#include <QWidget>
#include <QTableView>
#include <QTreeView>
#include <QWindowsStyle>

#include "datadef.h"
#include "hncaxis.h"
#include "hncchan.h"
#include "hncdatadef.h"
#include "hncdatatype.h"
#include "hncfprogman.h"
#include "hncmath.h"
#include "hncparaman.h"
#include "hncparamandef.h"
#include "hncsys.h"
#include "hncvar.h"
#include "hncsysctrl.h"
#include "hncalarm.h"
#include "hncreg.h"
#include "hncmst.h"
#include "hnccrds.h"
#include "hncverify.h"
#include "hncmodal.h"

#include "inputmanage.h"
#include "msgdata.h"
#include "msgchan.h"
#include "hmimenupage.h"
#include "hmiconfig.h"
#include "ncdatalimit.h"
#include "apposdepend.h"
#include "staticdata.h"
#include "servicedata.h"

#include "hmicommon.h"

static bool s_bSysLangChging = false;   // 系统语言是否切换中

// hmi显示参数
static Bit32 s_TimeChangeFlag = 0;                   // 修改时间标志
static Bit32 s_HmiRefreshTime = -1;

// 9型上位机用
static QString s_serverIp = ""; // 启动参数，上位机IP
#define MIN_REFRESH_TIME (50) // 界面最小刷新周期

fBit64 GetValueValidLimit(Bit32 prec)
{
    fBit64 limit = GetValueLimit();
    fBit64 tmp = 1.0;

    for (int i = 0; i < prec; i++)
    {
        tmp *= 0.1;
    }

    return limit - tmp;
}

bool FilePathIsSame(QString file1, QString file2)
{
    QFileInfo fileInfo1(file1);
    QFileInfo fileInfo2(file2);

    if (fileInfo1.absoluteFilePath() == fileInfo2.absoluteFilePath())
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool ProgIsLoadedCh(int ch, QString absPathName)
{
    Bit8 curFile[PATH_NAME_LEN] = {0};
    Bit32 chanNum = 0;

    HNC_SystemGetValue(HNC_SYS_CHAN_NUM, &chanNum); //获取通道数
    if (ch < 0 || ch >= chanNum)
    {
        return false;
    }

    HNC_FprogGetFullName(ch, curFile);

    if (FilePathIsSame(curFile, absPathName))
    {
        return true;
    }

    return false;
}

bool ProgIsLoaded(QString absPathName)
{
    Bit8 curFile[PATH_NAME_LEN] = {0};
    Bit32 chanNum = 0;
    Bit32 ch = 0;
    Bit32 i = 0;
    Bit32 progId = 0;

    HNC_SystemGetValue(HNC_SYS_CHAN_NUM, &chanNum); //获取通道数
    for (ch = 0; ch < chanNum; ch++)
    {
        HNC_FprogGetFullName(ch, curFile);
        if (FilePathIsSame(curFile, absPathName))
        {
            return true;
        }
    }

    // 是否作为外部子程序载入
    for (i = 1; i < MAX_PROG_IN_CHAN; i++) // MAX_PROG_IN_CHAN 9
    {
        progId = USER_PROG_BASE + i; // USER_PROG_BASE 59
        memset(curFile, 0, sizeof(curFile));
        HNC_FprogGetProgPathByIdx(progId, curFile);
        if (FilePathIsSame(curFile, absPathName))
        {
            return true;
        }
    }

    return false;
}

#if 0
// 保留此接口
Bit32 SetShowChannel(Bit32 channel)
{
    Bit32 ch = 0;

    if (channel < 0 || channel >= SYS_CHAN_NUM)
    {
        return -1;
    }

    //预留
    //...(保存设置的显示通道)
//    HmiMenuPage::SetChannelChange(channel);

    HNC_SystemGetValue(HNC_SYS_ACTIVE_CHAN, &ch);
    if (ch < 0 || ch >= SYS_CHAN_NUM)
    {
        return -1;
    }

    //保存当前活动通道
    HmiMenuPage::SetChannel(ch);

    return 0;
}
#endif

QPixmap PixMapToSize(QSize size, QString path)
{
    QPixmap pixmap;
    pixmap.load(path);
    if (pixmap.isNull() == false)
    {
        pixmap = pixmap.scaled(size, Qt::KeepAspectRatio, Qt::SmoothTransformation); // 图片适应label大小
    }
    return pixmap;
}

QPixmap PixMapToSize(QSize size, QPixmap pixmap)
{
    return pixmap.scaled(size, Qt::IgnoreAspectRatio, Qt::SmoothTransformation); // 图片适应label大小
}

Bit8 *QString2Bit8(QString str)
{
    return str.toUtf8().data();
}

// 判断数字（包括负号和小数点）
Bit32 IsNum(Bit8 key)
{
    Bit8 ret = 0;

    if ((key >= '0' && key <= '9') || '-' == key || '.' == key)
    {
        ret = 1;
    }

    return ret;
}

// 判断坐标数据是否合法
Bit32 isCoordValueLegal(Bit8 *buf)
{
    Bit32 i = 0;
    Bit8 findDecimalPoint = 0;
    Bit32 ret = 1;
    Bit32 len = 0;

    if (buf == NULL || strlen(buf) > 11)
    {
        return 0;
    }

    len = (Bit32)strlen(buf);
    for (i = 0; i < len; ++i)
    {
        if (buf[i] == '-')
        {
            if (i != 0 || len == 1)
            {
                ret = 0;
                break;
            }
        }
        else if (buf[i] == '.')
        {
            if (i == 0 || findDecimalPoint)
            {
                ret = 0;
                break;
            }
        }
        else if (!isdigit(buf[i]))
        {
            ret = 0;
            break;
        }
    }

    return ret;
}

fBit64 BritishToMetric(fBit64 val)
{
    return (val * METRIC_DISP_COEF);
}

fBit64 MetricToBritish(fBit64 val)
{
    return (val / METRIC_DISP_COEF);
}

void InsertSpace(QString& str)
{
    int no = 0;
    bool inQuotation = false; // 是否在引号中间
    QString::iterator itor = str.begin();

    while (itor != str.end())
    {
        if (*itor == '"')
        {
            if (inQuotation == false)
            {
                inQuotation = true; // 双引号开始
            }
            else
            {
                inQuotation = false; // 双引号结束
            }
        }

        if (inQuotation == true)
        {
            ++itor;
            ++no;
            continue; // 在双引号中间说明是字符串，不自动插入空格
        }

        //AND
        if (*itor == 'A' && *(itor+1) == 'N' && *(itor+2) == 'D')
        {
            if (itor == str.begin())
            {
                ++itor;
                ++no;
            }
            else
            {
                //判断AND前面是否有空格
                --itor;
                if (*itor != ' ')
                {
                    //没有空格，插入空格
                    str = str.insert(no," ");
                    ++no;
                    itor = str.begin() + no;
                }
                else
                {
                    //有空格
                    ++itor;
                }

                //判断AND后面是滞有空格
                itor = itor + 3;
                no = no + 3;
                if (*itor != ' ')
                {
                    //没有空格，插入空格
                    str = str.insert(no," ");
                    itor = str.begin() + no;
                }
                ++itor;
                ++no;
            }
        }
        else if ((*itor == 'O' && *(itor+1) == 'R') ||          //OR
                 (*itor == 'E' && *(itor+1) == 'Q') ||          //EQ
                 (*itor == 'N' && *(itor+1) == 'E') ||          //NE
                 (*itor == 'G' && *(itor+1) == 'T') ||          //GT
                 (*itor == 'G' && *(itor+1) == 'E' && *(itor+2) != 'T') ||          //GE，排除GETD指令
                 (*itor == 'L' && *(itor+1) == 'T') ||          //LT
                 (*itor == 'L' && *(itor+1) == 'E')             //LE
                 )
        {
            if (itor == str.begin())
            {
                ++itor;
                ++no;
            }
            else
            {
                //判断AND前面是否有空格
                --itor;
                if (*itor != ' ')
                {
                    //没有空格，插入空格
                    str = str.insert(no," ");
                    ++no;
                    itor = str.begin() + no;
                }
                else
                {
                    //有空格
                    ++itor;
                }

                //判断AND后面是滞有空格
                itor = itor + 2;
                no = no + 2;
                if (*itor != ' ')
                {
                    //没有空格，插入空格
                    str = str.insert(no," ");
                    itor = str.begin() + no;
                }
                ++itor;
                ++no;
            }
        }
        else if (*itor == 'I' && *(itor+1) == 'F')          //IF
        {
            itor = itor + 2;
            no = no + 2;
            if (*itor != ' ')
            {
                //没有空格，插入空格
                str = str.insert(no," ");
                itor = str.begin() + no;
            }
            ++itor;
            ++no;
        }
        else if (*itor == 'W' && *(itor+1) == 'H' && *(itor+2) == 'I' &&
                 *(itor+3) == 'L' && *(itor+4) == 'E')          //WHILE
        {
            itor = itor + 5;
            no = no + 5;
            if (*itor != ' ')
            {
                //没有空格，插入空格
                str = str.insert(no," ");
                itor = str.begin() + no;
            }
            ++itor;
            ++no;
        }
        else if ((*itor >= 'a' && *itor <= 'z') || (*itor >= 'A' && *itor <= 'Z') || *itor == '#')
        {
            if (itor == str.begin())
            {
                ++itor;
                ++no;
            }
            else
            {
                --itor;
                if ((*itor >= '0' && *itor <= '9') || *itor == '.')
                {
                    str = str.insert(no," ");
                    ++no;
                    itor = str.begin() + no;
                }
                else
                {
                    ++itor;
                }
                ++itor;
                ++no;
            }
        }
        else if (*itor == '+' || *itor == '-' || *itor == '*' || *itor == '/' || *itor == '=' ||
                 *itor == '&' || *itor == '|')
        {
            if (itor == str.begin())
            {
                ++itor;
                ++no;
            }
            else
            {
                if ((*(itor-1) >= '0' && *(itor-1) <= '9') || *(itor-1) == '.')
                {
                    //+-*/=&|前面加空格
                    str = str.insert(no," ");
                    //+-*/=&|后面加空格
                    no = no + 2;
                    itor = str.begin() + no;
                    if (*itor != ' ')
                    {
                        //没有空格，插入空格
                        str = str.insert(no," ");
                        ++no;
                        itor = str.begin() + no;
                    }
                }
//                else if ((*(itor+1) >= '0' && *(itor+1) <= '9') || *(itor+1) == '.')
//                {
//                    //+-*/=&|后面是数字则插入空格
//                    no = no + 1;
//                    str = str.insert(no," ");
//                    ++no;
//                    itor = str.begin() + no;
//                }
                else
                {
                    ++no;
                    ++itor;
                }
            }
        }
        else
        {
            ++itor;
            ++no;
        }
    }
}

// 获取数据保护状态
Bit32 GetMcpKey()
{
    Bit32 mcpKey = 0;

    HNC_SystemGetValue(HNC_SYS_MCP_KEY, &mcpKey);

    return mcpKey;
}

/**
 * @brief SetTableWidgetRowHeight 根据tableWidget大小设置每一行的高度
 * @param tableWidget tableWidget指针
 */
void SetTableWidgetRowHeight(QTableWidget *tableWidget)
{
    if (tableWidget == NULL)
    {
        return;
    }

    Bit32 fontHeight = tableWidget->fontMetrics().height();
    Bit32 tableHeight = tableWidget->size().height();
    Bit32 horizontalScrollBarHeight = tableWidget->horizontalScrollBar()->height();
    Bit32 headerHeight = tableWidget->horizontalHeader()->height();
    double countPerPage = 0.0;
    double tmp = 0.0;
    Bit32 rowHeight = 0;
    if (tableWidget->horizontalScrollBar()->isVisible())
    {
        countPerPage = (double)(tableHeight - headerHeight - horizontalScrollBarHeight - 1) / (fontHeight + 10);
        tmp = (double)(tableHeight - headerHeight - horizontalScrollBarHeight - 1) / countPerPage;
    }
    else
    {
        countPerPage = (double)(tableHeight - headerHeight - 1) / (fontHeight + 10);
        tmp = (double)(tableHeight - headerHeight - 1) / countPerPage;
    }

    rowHeight = (Bit32)tmp;

    for (int i = 0; i < tableWidget->rowCount(); i++)
    {
        tableWidget->setRowHeight(i, rowHeight);
    }
}

/**
 * @brief 快捷键转换为字符串
 * @param keyEvent 快捷键事件
 */
QString KeyEventToStr(QKeyEvent keyEvent)
{
    QString str = "";
    Qt::KeyboardModifiers modifiers = keyEvent.modifiers();
    int key = keyEvent.key();

    if ((modifiers & Qt::UNICODE_ACCEL) == Qt::UNICODE_ACCEL)
    {
        str += "";
    }
    if ((modifiers & Qt::META) == Qt::META)
    {
        str += "Meta+";
    }
    if ((modifiers & Qt::SHIFT) == Qt::SHIFT)
    {
        //str += "Shift+";      // 注:部分装置按下字母键时,会发送Shift+字母键键值，因此Shift暂时不匹配.
    }
    if ((modifiers & Qt::ALT) == Qt::ALT)
    {
        str += "Alt+";
    }
    if ((modifiers & Qt::CTRL) == Qt::CTRL)
    {
        str += "Ctrl+";
    }

    if(key == Qt::Key_Escape)
    {
        str += "Esc";
    }
    else if(key == Qt::Key_Tab)
    {
        str += "Tab";
    }
    else if(key == Qt::Key_Backspace)
    {
        str += "Backspace";
    }
    else if(key == Qt::Key_Return)
    {
        str += "Return";
    }
    else if(key == Qt::Key_Enter)
    {
        str += "Enter";
    }
    else if(key == Qt::Key_Delete)
    {
        str += "Del";
    }
    else if(key == Qt::Key_Home)
    {
        str += "Home";
    }
    else if(key == Qt::Key_End)
    {
        str += "End";
    }
    else if(key == Qt::Key_Left)
    {
        str += "Left";
    }
    else if(key == Qt::Key_Right)
    {
        str += "Right";
    }
    else if(key == Qt::Key_Up)
    {
        str += "Up";
    }
    else if(key == Qt::Key_Down)
    {
        str += "Down";
    }
    else if(key == Qt::Key_PageUp)
    {
        str += "PageUp";
    }
    else if(key == Qt::Key_PageDown)
    {
        str += "PageDown";
    }
    else if(key == Qt::Key_Space)
    {
        str += "Space";
    }
    else if (key >= Qt::Key_A && key <= Qt::Key_Z)
    {
        str += QString('A' + key - Qt::Key_A);
    }
    else if (key >= Qt::Key_F1 && key <= Qt::Key_F12)
    {
        str += ("F" + QString('1' + key - Qt::Key_F1));
    }
    else if (key >= Qt::Key_0 && key <= Qt::Key_9)
    {
        str += QString('0' + key - Qt::Key_0);
    }

    return str;
}

bool IsSysLangChging()
{
    return s_bSysLangChging;
}

void SetStatus_SysLangChging(bool bChging)
{
    s_bSysLangChging = bChging;
}

bool IsSwapProg(Bit32 totalRow)
{
    if (totalRow >= 0x7FFFFFFF)
    {
        return true;
    }

    return false;
}

Bit32 GetChannelAxisId(Bit32 ch, Bit32 idx, QString &axisNameStr)
{
    Bit32 ax = 0;
    Bit32 logicalAx = 0;
    Bit8 axisName[PARAM_STR_LEN] = {'\0'};

    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_XINDEX + idx, &ax);
    if (ax >= 0 || ax == -2 || ax == -3) // 轴参数填-2表示该轴动态的从属于通道，详情参考参数说明书；
    {
        logicalAx = idx;
    }
    else
    {
        logicalAx = -1;
    }
    if (logicalAx >= 0 && axisNameStr.isNull() == false)
    {
        HNC_ChannelGetValue(HNC_CHAN_AXIS_NAME, ch, logicalAx, axisName);

        axisNameStr = QString(axisName);
    }
    return logicalAx;
}

bool PlcIsRunning()
{
    Bit32 sysRegBase = 0;
    Bit32 tmp = 0;

    HNC_RegGetFGBase(REG_FG_SYS_BASE, &sysRegBase);

    HNC_RegGetValue(REG_TYPE_F, sysRegBase * 2 + SYS_NC_STATE, &tmp);
//#define SYS_NC_STATE	0 //系统状态字
//	#define SYS_PLC_ONOFF   	0x0002 //1运行 0停止
    if (tmp & SYS_PLC_ONOFF)
    {
        return true;
    }
    else
    {
        return false;
    }
}

/**
 * @brief RecordToFile 记录信息到文件中
 * @param file 文件路径及名称
 * @param str 信息
 */
void RecordToFile(QString file, QString str)
{
    if (file.isNull())
    {
        return;
    }

    QFileInfo fileInfo(file);
    if (fileInfo.isDir() == true)
    {
        return;
    }

    QFile qFile(file);
    if (!qFile.open(QIODevice::WriteOnly|QIODevice::Append))
    {
        return;
    }

    QDateTime curTime = QDateTime::currentDateTime();

    QTextStream textStream(&qFile);
    textStream << "[time:" << curTime.toString("yyyy-MM-dd hh:mm:ss") << "] " << str << endl;

    qFile.flush();
    qFile.close();
    FileStrFSync(file);
}

/**
 * @brief RunRowDebugInfo 程序行调试接口
 * @param ch
 * @param strIn
 */
void RunRowDebugInfo(Bit32 ch, QString strIn)
{
    Bit32 tmp = 0;
    Bit32 curRunRow = 0;
    Bit32 progLvl = 0;
    Bit32 progIdx[CHAN_AXES_NUM] = {0};	// 每层程序号
    Bit32 runRow[CHAN_AXES_NUM] = {0};	// 每层运行行号
    QString str;

    HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_PROG_IDX, &tmp);  //VAR_PROG_IDX

    progIdx[0] = (tmp >> 16) & (0x0000ffff);
    progLvl = tmp & (0x0000ffff);
    str += QString("%1;").arg(progLvl);

    for (int i = 0; i < (CHAN_AXES_NUM - 1) / 2; i++)
    {
        HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_PROG_IDX+1 + i, &tmp);//972
        progIdx[i * 2 + 2] = (tmp >> 16) & (0x0000ffff);
        progIdx[i * 2 + 1] = tmp & (0x0000ffff);
    }

    for (int i = 0; i < CHAN_AXES_NUM; i++)
    {
        HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_PROG_ROW + i, &tmp); // 976
        runRow[i] = tmp ;
    }

    for (int i = 0; i < CHAN_AXES_NUM; i++)
    {
        str += QString("%1 %2;").arg(progIdx[i]).arg(runRow[i]);
    }

    HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_CHAN_RUN_ROW, &curRunRow); // 4
    str += QString("C:%1;").arg(curRunRow) + (strIn);
    MessageOut(str);
}

// 获取螺纹修复,再切削有效标记
bool GetScrewThreadState(Bit32 ch)
{
    Bit32 val = 0;
    Bit32 regFBase = 0;

    HNC_RegGetFGBase(REG_FG_CHAN_BASE, &regFBase);
    HNC_RegGetValue(REG_TYPE_G, regFBase * 2 + ch * CHAN_REG_NUM + SCREW_THRED_REG_ID, &val);

    bool state = ((val & (1 << SCREW_THRED_REG_BIT)) == (1 << SCREW_THRED_REG_BIT));

    return state;
}

bool IsLoginConnect()
{
    Bit32 val = 0;
    Bit32 val2 = 0;

    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_NET_START, &val);
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_NET_CONNECT_TYPE, &val2);
    if (val == 1 && (val2 & CONNECT_LOG_IN) == CONNECT_LOG_IN)
    {
        return true;
    }
    return false;
}

bool IsAutoConnect()
{
    Bit32 val = 0;
    Bit32 val2 = 0;

    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_NET_START, &val);
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_NET_CONNECT_TYPE, &val2);
    if (val == 1 && (val2 & CONNECT_AUTO_CONNECT) == CONNECT_AUTO_CONNECT)
    {
        return true;
    }
    return false;
}

bool CommGetCycForbidState()
{
    Bit32 base = 0;
    Bit16 val = 0;

    HNC_RegGetFGBase(REG_FG_SYS_BASE, &base);
    HNC_RegGetValue(REG_TYPE_F, base * 2 + SYS_NC_STATE, &val);
    if ((val & 0x2000) != 0)
    {
        return true;
    }
    else
    {
        return false;
    }
}

Bit32 SaveXMLElement(QString fullPath, QDomDocument &doc)
{
    QString filepath = "";
    filepath = QDir::toNativeSeparators(fullPath); // 转化为本地路径

    QFile file(filepath);

    if (!file.open(QFile::WriteOnly | QFile::Truncate))
    {
        return -1;
    }

    QTextStream textStream(&file);
    textStream.reset();
    textStream.setCodec("utf-8"); // 设置流的编码
    doc.save(textStream, 4, QDomNode::EncodingFromTextStream);
    file.close();

    FileStrFSync(filepath);

    return 0;
}

fBit64 HmiChanGetTransVal(Bit32 type, Bit32 ch, Bit32 index)
{
    fBit64 value = 0.0;

    HNC_ChannelGetValue(type, ch, index, &value);
    value = TransMetric2Show(value);

    return value;
}

fBit64 TransAxisUnit2Save(Bit32 axis, fBit64 val)
{
    Bit32 transUnit = 1;
    Bit32 axType = 0;
    HNC_AxisGetValue(HNC_AXIS_TYPE, axis, &axType);
    if(axis >= 0 && axType < 0)
    {
        return val;
    }
    else if(axis < 0 || 1 == axType || 7 == axType)
    {
        HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &transUnit);
    }
    else
    {
        HNC_SystemGetValue(HNC_SYS_TURN_UNIT, &transUnit);
    }

    if(transUnit == 0) // 除零保护
    {
        transUnit = 1;
    }

    val *= transUnit;
    return val;
}

bool TableNoDefaultMove()
{
    Bit32 tmpVal = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_HMISHOW, &tmpVal);
    if ((tmpVal & TABLE_NO_DEFAULT_MOVE) != TABLE_NO_DEFAULT_MOVE)
    {
        return false;
    }
    return true;
}

/*!
 * \brief GetStringPixedWidth 根据字体计算字符串占用像素宽度
 * \param [in] font 字体
 * \param [in] str 字符串
 * \return 像素宽度
 */
Bit32 GetStringPixedWidth(const QFont& font, const QString& str)
{
    QFontMetrics fm(font);
    QRect rec = fm.boundingRect(str);
    return rec.width();
}

/**
 * @brief HasTemperatureSensor 判断是否温度传感器
 * @return true:有;false:无
 */
bool HasTemperatureSensor()
{
    Bit32 hasTemperatureSensor = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_TEMPERATURE_SENSOR, &hasTemperatureSensor);
    if (hasTemperatureSensor == 1)
    {
        return true;
    }
    else
    {
        return false;
    }
}

/*!
 * \brief SetTimeChangeFlag 设置时间修改标志
 */
void SetTimeChangeFlag()
{
    s_TimeChangeFlag = 0xFFFFFFFF;
}

/*!
 * \brief GetTimeChangeFlag 获取时间修改标志
 * \param [in] index 需要判断时间是否修改的模块索引号
 * \return true:时间修改;false:没修改
 */
bool GetTimeChangeFlag(TimeChange index)
{
    return (s_TimeChangeFlag & (1<<index)) == 0 ? false : true;
}

/*!
 * \brief ResetTimeChangeFlag 清除模块对应的时间修改标志
 * \param [in] index 需要判断时间是否修改的模块索引号
 */
void ResetTimeChangeFlag(TimeChange index)
{
    Bit32 tmp = 1 << index;
    tmp = ~tmp;
    s_TimeChangeFlag = s_TimeChangeFlag & tmp;
}

fBit64 CalRound(fBit64 fVal, Bit32 prec)
{
    if (prec < 0)
    {
        return fVal;
    }

    fBit64 mul = pow(10.0, prec);
    fBit64 fTmp = mul * fVal;

    if (fTmp > 0)
    {
        fTmp += 0.5;
    }
    else if (fTmp < 0)
    {
        fTmp -= 0.5;
    }
    else
    {
        return fVal;
    }

    return (Bit32)(fTmp) / mul;
}

QString CalRoundStr(fBit64 fVal, Bit32 prec)
{
    fBit64 fTmp = CalRound(fVal, prec);

    return QString::number(fTmp, 'f', prec);
}

bool ShowCmdFeedToAct()
{
    Bit32 val = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_HMISHOW, &val);
    if ((val & ACT_FEED_SHOW_CMD) == ACT_FEED_SHOW_CMD)
    {
        return true;
    }
    return false;
}

// 设置上位机启动参数
void SetServerIp(const char *serverIp)
{
    s_serverIp = QString(serverIp);
}

// 获取上位机启动参数
QString GetServerIp(void)
{
    return s_serverIp;
}

bool IsChanFreNCOpen()
{
    Bit32 val = 0;

    ParaGetIntVal(PARAMAN_FILE_MAC, 0,PAR_MAC_NEW_FUCTION_TEST, &val);
    //if ((val & CHAMFER_NC) == CHAMFER_NC)// 内核未支持
    {
    //    return true;
    }
    //else
    {
        return false;
    }
}

/*!
 * \brief FileIsCycle 判断文件是否为固定循环文件
 * \param fullPathName 文件全路径名
 * \return -1：不是固定循环文件；其他固定循环序号
 */
Bit32 FileIsCycle(Bit8* fullPathName)
{
    if (fullPathName == NULL)
    {
        return -1;
    }

    QDomDocument doc;
    Bit32 ret = GetXMLElement(HNC_SYS_CFG_BIN_PATH, QString("CYC.XML"), doc);
    if (ret < 0)
    {
        return -1;
    }

    QFileInfo fileInfo1(fullPathName);

    QDomElement root = doc.documentElement();
    QDomElement item = root.firstChildElement();
    while (!item.isNull())
    {
        Bit32 idx = 0;
        bool ok = false;
        QString strIdx = item.attribute("idx");
        QString strPath = item.attribute("path");

        idx = strIdx.toInt(&ok);
        if (ok && (idx >= 0))
        {
            QFileInfo fileInfo2(strPath);
            if (fileInfo1.absoluteFilePath() == fileInfo2.absoluteFilePath())
            {
                return idx;
            }
        }

        item = item.nextSiblingElement();
    }

    return -1;
}


//根据filePath路径存储的文件名，判断当前文件是否是如O9901这样可以
//用于作为子程序被调用的G代码
static Bit32 IsGprogPureDigitWithO(QString filePath, Bit32 *prog_no)
{
    //返回1:表示当前文件名是类似于O9901这样的，此时*prog_no = 9901,
    //返回0:表示当前文件名无规则，可能有字符串，或者非纯粹数字的
    //返回<0:判断失败，和0的意义一样
    Bit8 filePathName[PATH_NAME_LEN] = {0};
    Bit8 fileName[PATH_NAME_LEN] = {0};
    Bit32 filePathNameLen = 0;
    Bit32 i = 0, dir_idx = 0, len = 0;

    filePathNameLen = sizeof(filePath.toStdString().data());
    if (filePathNameLen >= PATH_NAME_LEN)
    {
        return -1;
    }

    strcpy(filePathName, filePath.toStdString().data());
    for (i=0; i<PATH_NAME_LEN; i++)
    {
        if (filePathName[i] == DIR_SEPARATOR)
        {
            dir_idx = i;
        }
    }

    //得到类似于"O9901"这样的字符串串
    if (dir_idx > 0)//PATH
    {
        strncpy(fileName, filePathName+dir_idx+1, PATH_NAME_LEN);
    }
    else//NAME ALREADY
    {
        strncpy(fileName, filePathName, PATH_NAME_LEN);
    }

    //如果不是'O'， 'o'开头，不能作为子程序调用，返回
    if (fileName[0]!='O' && fileName[0]!='o')
    {
        return 0;
    }

    //判断字符串是否是纯数字
    len = strlen(fileName);
    for (i=1; i<len; i++)
    {
        if (fileName[i]<'0' ||fileName[i]>'9')
        {
            return 0;
        }
    }

    //转换为数字
    *prog_no = atol(fileName+1);
    return 1;
}

/*!
 * \brief IsGcodeNameConflictWithSys 判断当前路径G代码是否和系统已占程序号冲突 
 * \param filePath 文件全路径名
 * \param prog_no 解析出的文件程序号
 * \return 0：没有冲突，1：与系统定义的有冲突 
 */
Bit32 IsGcodeNameConflictWithSys(QString filePath, Bit32 *prog_no)
{
    Bit32 i = 0, ret = 0, prog_number  = 0, iVal = 0;
    //判断新建命名是否和参数里系统占用程序号冲突
    ret = IsGprogPureDigitWithO(filePath, &prog_number);
    if (ret != 1)
    {
        return 0;
    }

    //此处40对照progman.h里SYS_OCCUPY_GCODE_NO_NUM定义
    for (i=0; i<40; i++)
    {
        ParaGetIntVal(PARAMAN_FILE_MAC, 0, PAR_MAC_USER_PROG_NO_BASE+i, &iVal);
        if (iVal > 0 && iVal == prog_number)
        {
            *prog_no = prog_number;
            return 1;
        }
    }

    return 0;
}

/*!
* \brief VSystem 使用vfork实现system功能
* @param [in] cmd 指令
* \return 0:成功 1:指令为空 -1:失败
*/
Bit32 VSystem(const Bit8* cmd)
{
#ifdef _LINUX_
	pid_t pid;
	int status = 0;

	if (cmd == NULL)
	{
		return 1;				//如果cmdstring为空，返回非零值，一般为1
	}

	if ((pid = vfork()) < 0)
	{
		status = -1;			//fork失败，返回-1
	}
	else if (pid == 0)
	{
		execl("/bin/sh", "sh", "-c", cmd, (char *)0);
		_exit(127); // exec执行失败返回127，注意exec只在失败时才返回现在的进程，成功的话现在的 子进程正常执行则不会执行此语句
	}
	else
	{
		while (waitpid(pid, &status, 0) < 0)
		{
			//				if (errno != 0)//EINTER) 
			if (errno != EINTR)
			{
				status = -1;		//如果waitpid被信号中断，则返回-1
				break;
			}
		}
	}

	return status;		//如果waitpid成功，则返回子进程的返回状态
#else
    UNREFERENCED_PARAM(cmd);
	return 0;
#endif
}

bool IsGmodeG71(Bit32 ch)
{
    Bit32 modeG = 6; // 第6组模态

    Bit32 mid = 0;

    HNC_ChannelGetValue(HNC_CHAN_MODAL, ch, modeG, &mid);
    if(mid >= 70 && mid <= 73)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool IsGmodeG36(Bit32 ch)
{
    Bit32 mid = 0;
    HNC_ChannelGetValue(HNC_CHAN_MODAL, ch, MODAL_G36_37, &mid);
    if(mid == 36)
    {
        return true;
    }
    return false;
}

bool IsGmodeG41_4G42_4(Bit32 ch)
{
    Bit32 modeG = 9; // 第9组模态 MODAL_G41_42
    Bit32 mid = 0;
    HNC_ChannelGetValue(HNC_CHAN_MODAL, ch, modeG, &mid);
    if(mid >= 17449 && mid <= 17450)
    {
        Bit8 buf[16] = {0};
        HNC_ModalGetModeStr(mid, modeG, buf);
        if(QString(buf) == "G41.4" || QString(buf) == "G42.4")
        {
            return true;
        }
    }

    return false;
}

/*!
 * \brief IsModalPop 判断当前是否存在模态窗口或下拉选项框
 * \return true:存在;
 *         false:不存在
 */
bool IsModalPop()
{
    QWidget *pModal = QApplication::activeModalWidget();
    QWidget *pWidget = QApplication::activeWindow();
    if (pWidget == NULL)
    {
        return false;
    }
    bool bFlag = pWidget->inherits("InputManage");

    // 关闭弹框
    if(pModal != NULL)
    {
        return true;
    }
    else if (bFlag == true)
    {
        return true;
    }
    // 关闭右键
    QWidget *pPopup = QApplication::activePopupWidget();
    if(pPopup != NULL)
    {
        return true;
    }

    return false;
}

bool IsInitGifShow()
{
   /* Bit32 tmpVal = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_HMISHOW, &tmpVal);
    if((tmpVal & HMI_SHOW_PNG_FIRST) == HMI_SHOW_PNG_FIRST)
    {
        return false;
    }
    else
    {*/
        return true;
    /*}*/
}

Bit32 HmiRefreshTime()
{
    if (s_HmiRefreshTime <= 0)
    {
        SDataProperty prop_min,prop_max,prop_store;
        ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_HMI_REFRESH_TIME, &s_HmiRefreshTime);
        HNC_ParamanGetParaProp(PARAMAN_FILE_NCU, 0, PAR_NCU_HMI_REFRESH_TIME, PARA_PROP_STORE, &prop_store);
        HNC_ParamanGetParaProp(PARAMAN_FILE_NCU, 0, PAR_NCU_HMI_REFRESH_TIME, PARA_PROP_MAXVALUE, &prop_max);
        HNC_ParamanGetParaProp(PARAMAN_FILE_NCU, 0, PAR_NCU_HMI_REFRESH_TIME, PARA_PROP_MINVALUE, &prop_min);
        Bit32 min = prop_min.value.val_int;
        if(prop_min.value.val_int < MIN_REFRESH_TIME)
        {
            min = MIN_REFRESH_TIME;
        }

        if (prop_store.value.val_int != DTYPE_INT || s_HmiRefreshTime > prop_max.value.val_int || s_HmiRefreshTime < min)
        {
            s_HmiRefreshTime = 200;
            ParaSetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_HMI_REFRESH_TIME, s_HmiRefreshTime);
        }
    }
    return s_HmiRefreshTime;
}

void DoProcessEvents(bool forceProcessFlag) // 防止加载界面时调整界面卡顿
{
    QEventLoop::ProcessEventsFlags flag = QEventLoop::AllEvents;
    if (IsBackGroundLoadingOff())
    {
        flag = QEventLoop::ExcludeUserInputEvents;
    }
    QTime oldT = StaticData::Instance().m_BackGroundLoadProcessTime;
    if (oldT.isNull())
    {
        return;
    }
    QTime curT = QTime::currentTime();
    Bit32 msec = HmiRefreshTime();
    Bit32 off = oldT.msecsTo(curT);
    if (forceProcessFlag || (msec > 0 && off >= msec * 2 / 3))
    {
        QCoreApplication::processEvents(flag);
        StaticData::Instance().m_BackGroundLoadProcessTime = curT;
    }
}

void NormalizeChildren(QWidget *pConWidget)
{
    // 界面的字体全部转换为pixel大小，并进行缩放
    int pixelSize = 0;
    QList<QWidget*> widgetList = pConWidget->findChildren<QWidget*>();
    for (Bit32 i = 0; i < widgetList.count(); i++)
    {
        if (widgetList.at(i) == NULL)
        {
            continue;
        }
        QFont font = widgetList[i]->font();
        if (font.pixelSize() == -1)
        {
            fBit64 size = (fBit64)font.pointSize();
            size = 96.0 * size / 72.0; // pointSize转换到pixelSize;
            pixelSize = int(size * FONT_RATIO);
        }
        else
        {
            pixelSize = int(font.pixelSize() * FONT_RATIO);
        }
        font.setFamily(FONT_TYPE);
        font.setPixelSize(pixelSize);
        widgetList.at(i)->setFont(font);
        DoProcessEvents();
    }
    DoProcessEvents();

    // 所有界面的表格的宽度进行缩放
    QList<QTableView*>tableViewList = pConWidget->findChildren<QTableView*>();
    for (Bit32 i = 0; i < tableViewList.count(); i++)
    {
        for(Bit32 col = 0; col < tableViewList[i]->horizontalHeader()->count(); col++)
        {
            Bit32 width = Bit32(tableViewList[i]->columnWidth(col) * HOR_RATIO);
            tableViewList[i]->setColumnWidth(col, width);
        }
        // 解决linux下滚动条被遮挡的问题
        tableViewList[i]->verticalScrollBar()->setStyle(new QWindowsStyle);
        DoProcessEvents();
        tableViewList[i]->horizontalScrollBar()->setStyle(new QWindowsStyle);
        DoProcessEvents();
    }
    DoProcessEvents();

    QList<QTreeView*>treeViewList = pConWidget->findChildren<QTreeView*>();
    for (Bit32 i = 0; i < treeViewList.count(); i++)
    {
        // 解决linux下滚动条被遮挡的问题
        treeViewList[i]->verticalScrollBar()->setStyle(new QWindowsStyle);
        DoProcessEvents();
        treeViewList[i]->horizontalScrollBar()->setStyle(new QWindowsStyle);
        DoProcessEvents();
    }
    DoProcessEvents();

    QList<QDialog*>dialogList = pConWidget->findChildren<QDialog*>();
    for(Bit32 i = 0; i < dialogList.count(); i++)
    {
        // 对话框尺寸
        Bit32 width = Bit32(dialogList[i]->width() * HOR_RATIO);
        Bit32 height = Bit32(dialogList[i]->height() * VER_RATIO);
        dialogList[i]->setFixedSize(width, height);
    }
    DoProcessEvents();
}

///////////////////////////////////////////////////////////////////////////////
//
//    Bit32 GetMemUsage()
//
//    功能：
//            取得当前内存使用的情况
//
//    参数：
//
//
//    描述：
//
//
//    返回：
//            -1  读取文件失败
//              0  成功
//////////////////////////////////////////////////////////////////////////
Bit32 GetMemUsage(Bit32 &totalMem, Bit32 &freeMem)
{
    FILE   *fp = NULL;
    Bit8   tmpbuf[2048] = {0};
    size_t   byte_read = 0;
    Bit8   *pos = NULL;

    fp = fopen( "/proc/meminfo", "r");

    if(NULL == fp)
    {
        return -1;
    }

    byte_read = fread(tmpbuf, 1, sizeof(tmpbuf), fp);
    fclose(fp);
    fp = NULL;

    if (0 == byte_read || byte_read == sizeof(tmpbuf))
    {
        return -1;
    }

    pos = strstr(tmpbuf, "MemTotal: ");

    if (NULL == pos)
    {
        return -1;
    }

    sscanf(pos, "MemTotal: %dkB", &totalMem);
    pos = strstr(pos, "MemFree: ");

    if (NULL == pos)
    {
        return -1;
    }

    sscanf(pos, "MemFree: %dkB", &freeMem);

    return 0;
}

bool IsProgPrintDlgCountDown()
{
    Bit32 val = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_G_PRINT_TYPE, &val);
    if (val == 2)
    {
        return true;
    }
    return false;
}

Bit32 ProgPrintCountDownTotalTime()
{
    Bit32 val = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_G_PRINT_TIME, &val);

    return val;
}

/**
 * @brief IsEStop 是否为急停
 * @param ch 通道号
 * @return 急停:true 非急停:false
 */
bool IsEStop(Bit32 ch)
{
    Bit32 estopState = 0;
    HNC_ChannelGetValue(HNC_CHAN_IS_ESTOP, ch, 0, &estopState);
    if(estopState)
    {
        return true;
    }

    return false;
}

Bit32 GetPIDByName(const Bit8 *name)
{
    Bit8 cmd[STR_BUF_LEN];
    Bit32 pid = -1;
    snprintf(cmd, sizeof(cmd), "pidof %s", name);
#ifdef _LINUX
    FILE *fp = NULL;
    Bit8 buf[STR_BUF_LEN];
    fp = popen(cmd, "r");
    if (fp == NULL)
    {
        return pid;
    }
    if (fgets(buf, STR_BUF_LEN, fp) != NULL)
    {
        pid = atol(buf);
    }
    fclose(fp);
#endif
    return pid;
}

Bit32 ParaGetID(Bit32 fileno, Bit32 subno, Bit32 index)
{
    SDataProperty addr;
    memset(&addr, 0, sizeof(addr));

    HNC_ParamanGetParaProp(fileno, subno, index, PARA_PROP_ID, &addr);

    return addr.value.val_int;
}

bool IsParamanVisible(Bit32 paramId)
{
	Bit32 ret = HNC_ParamanIdIsVisible(paramId);
	if (ret == 0)
	{
		return true;
	}

	return false;
}

Bit32 GetFeedUnitType(Bit32 ch)
{
    Bit32 g95_mode = 0;
    Bit32 g01_mode = 0;
    Bit32 g95_f_disp = -1;
    Bit32 macType = -1;
    Bit32 preModalG94 = -1;
    Bit32 unitType = F_TYPE_MINTER;
    Bit32 rigid = 0;
    Bit32 mode = 0;
    Bit32 isSbl = 0;
    Bit32 isMdi = 0;
    Bit32 threadState = 0;

    HNC_ChannelGetValue(HNC_CHAN_MODE, ch, 0, &mode);
    HNC_ChannelGetValue(HNC_CHAN_IS_SBL, ch, 0, &isSbl);
    HNC_ChannelGetValue(HNC_CHAN_IS_MDI, ch, 0, &isMdi);

    if (CHAN_MODE_AUTO != mode && isSbl != 1 && isMdi != 1) // 不满足自动/单段显示mm/min
    {
        return F_TYPE_MINTER;
    }

    HNC_ChannelGetValue(HNC_CHAN_MODAL, ch, 1, &g01_mode);// G00显示mm/min
    if (g01_mode == 0)
    {
        return F_TYPE_MINTER;
    }

    HNC_ChannelGetValue(HNC_CHAN_MAC_TYPE, ch, 0, &macType);// 铣床显示mm/min
    if (macType == 0)
    {
        return F_TYPE_MINTER;
    }

    ParaGetIntVal(PARAMAN_FILE_MAC, 0, PAR_G95_F_DISP, &g95_f_disp);// 参数未开显示mm/min
    if (g95_f_disp == 0)
    {
        return F_TYPE_MINTER;
    }

    HNC_ChannelGetValue(HNC_CHAN_MODAL, ch, 14, &g95_mode);
    HNC_ChannelGetValue(HNC_CHAN_IS_RIGID, ch, 0, &rigid);
    HNC_ChannelGetValue(HNC_CHAN_IS_THREADING, ch, 0, &threadState);
    if (rigid == 1) // 刚攻
    {
        // 刚攻时MODAL_G94_95获取的是G94.2需要获取G94.2的前一个G94状态,在VAR_THREED_G9495_MODE中
        HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, 1136, &preModalG94); // VAR_THREED_G9495_MODE

        if (preModalG94 == 94)
        {
            unitType = F_TYPE_MINTER;
        }
        else
        {
            // 刚攻G94.2 + G95 显示转进给
            unitType = F_TYPE_ROUND;
        }
    }
    else if (threadState == 1) // 螺纹
    {
        unitType = F_TYPE_ROUND;
    }
    else // 非刚攻, 非螺纹
    {
        if (g95_mode == 94) // G94分进给
        {
            unitType = F_TYPE_MINTER;
        }
        else // G95转进给
        {
            unitType = F_TYPE_ROUND;
        }
    }

    return unitType;
}

fBit64 GetCmdFeed(Bit32 ch)
{
   // Bit32 showType = 0;
    Bit32 macType = 0;
    ParaGetIntVal(PARAMAN_FILE_MAC, 0, PAR_MAC_CHAN_TYPE + ch, &macType);
    //ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_FS_SHOWTYPE, &showType);

    fBit64 fCmd = 0.0;
   // if ((macType == 1 || macType == 0) && ((showType & PAR_NCU_FEED_SHOW_ACT) == PAR_NCU_FEED_SHOW_ACT))
    {
   //     HNC_ChannelGetValue(HNC_CHAN_ACT_FEEDRATE, ch, 0, &fCmd); // 实际速度
    }
  //  else
    {
        HNC_ChannelGetValue(HNC_CHAN_CMD_FEEDRATE, ch, 0, &fCmd); // 指令速度
    }
    return fCmd;
}

fBit64 NorAngle(fBit64 fval)
{
    while (HNC_DoubleCompare(fval, -360) <= 0)
    {
        fval += 360;
    }
    while (HNC_DoubleCompare(fval, 360) >= 0)
    {
        fval -= 360;
    }
    return fval;
}

bool IsInRotTolerande(fBit64 pos, fBit64 tolerande)
{
    pos = NorAngle(pos);
    if (pos < -tolerande && pos > (-360 + tolerande))
    {
        return false;
    }
    if (pos < (360 - tolerande) && pos > tolerande)
    {
        return false;
    }
    return true;
}
