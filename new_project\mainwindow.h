#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QScrollBar>
#include <QLabel>
#include <QToolBar>
#include <QAction>
#include <QStatusBar>
#include <QSplitter>
#include <QTimer>

#include "ladderview.h"
#include "ladderdata.h"

QT_BEGIN_NAMESPACE
class QAction;
class QMenu;
class QScrollBar;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    // 设置梯形图数据
    void setLadderData(LadderData *data);
    
    // 刷新显示
    void refreshDisplay();
    
    // 设置调试模式
    void setDebugMode(bool enabled);
    
    // 设置编辑模式
    void setEditMode(bool enabled);

private slots:
    // 滚动条值改变
    void onVerticalScrollChanged(int value);
    void onHorizontalScrollChanged(int value);
    
    // 工具栏动作
    void onDebugToggled(bool checked);
    void onEditToggled(bool checked);
    void onRefreshTriggered();
    void onFindTriggered();
    
    // 定时器刷新
    void onTimerRefresh();
    
    // 梯形图视图信号
    void onCellSelected(int row, int col);
    void onCellDoubleClicked(int row, int col);

private:
    void setupUI();
    void setupToolBar();
    void setupStatusBar();
    void setupConnections();
    void updateScrollBars();
    void updateStatusBar();

private:
    // UI组件
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_contentLayout;
    QSplitter *m_splitter;
    
    // 梯形图视图
    LadderView *m_ladderView;
    
    // 滚动条
    QScrollBar *m_verticalScrollBar;
    QScrollBar *m_horizontalScrollBar;
    
    // 工具栏和动作
    QToolBar *m_toolBar;
    QAction *m_debugAction;
    QAction *m_editAction;
    QAction *m_refreshAction;
    QAction *m_findAction;
    
    // 状态栏
    QLabel *m_statusLabel;
    QLabel *m_positionLabel;
    QLabel *m_modeLabel;
    
    // 数据和状态
    LadderData *m_ladderData;
    QTimer *m_refreshTimer;
    
    // 显示参数
    int m_currentRow;           // 当前显示起始行
    int m_currentCol;           // 当前显示起始列
    int m_selectedRow;          // 选中行
    int m_selectedCol;          // 选中列
    
    // 模式标志
    bool m_debugMode;           // 调试模式
    bool m_editMode;            // 编辑模式
    bool m_forceRefresh;        // 强制刷新标志
};

#endif // MAINWINDOW_H
