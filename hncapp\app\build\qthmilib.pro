#-------------------------------------------------
#
# Project created by QtCreator 2016-05-24T16:02:41
#
#-------------------------------------------------

QT       += core
QT       += gui
QT       += declarative
QT       += xml
QT       += webkit network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets printsupport


unix:!macx:KVER=$$system(uname -r)

win32: DESTDIR = ../../lib/win32/
win32: TARGET = qthmi
unix:!macx:contains(KVER, 3.4.*):TARGET = ../../lib_3.4/qthmi
unix:!macx:contains(KVER, 3.10.*):TARGET = ../../lib_3.10/qthmi
unix:!macx:contains(KVER, 3.14.*):TARGET = ../../lib/linux/qthmi
OBJECTS_DIR = ../obj
MOC_DIR = ../moc
UI_DIR = ../ui

TRANSLATIONS = ../../lang/cn.ts\
            ../../lang/en.ts\
            ../../lang/ru.ts
CODECFORTR = UTF-8
#DEFAULTCODEC = UTF-8
#CODEC = UTF-8
CODECFORSRC  = UTF-8
TEMPLATE = lib

unix:!macx: QMAKE_CXXFLAGS += -std=c++0x

DEFINES += _HNC_30_
unix:!macx: DEFINES += _LINUX_ _LINUX LINUX3_4
win32: DEFINES += _WIN32 _MBCS

win32: QMAKE_LFLAGS_RELEASE += /NODEFAULTLIB:LIBC.lib /NODEFAULTLIB:LIBCMT.lib
win32: QMAKE_LFLAGS_DEBUG += /NODEFAULTLIB:LIBCD.lib /NODEFAULTLIB:LIBCMTD.lib

QMAKE_CFLAGS_RELEASE = -M -O2 -MT -g
QMAKE_CFLAGS_DEBUG = -M -Zi -MTd -g

INCLUDEPATH =  ../../../include/api ../src/include ../src/wgwidget ../src/gwg_widget ../src/gwg_widget/common_widget ../src/wgwidget/oscservo ../src/nccontrols ../src/wgwidget/workmeas ../src/hmidata ../src/wgwidget/gdblk ../src/plugins/usrdef ../src/wgwidget/thermal ../src/userwidget ../src/tools ../src/hncservice ../src/wgwidget/lnsworkmeas ../src/wgwidget/hncworkmeas
INCLUDEPATH += ../../../include/app ../../../include/plc ../../../include/platform/tinyxml ../../../apidev/include/qrencode
PRECOMPILED_HEADER = stable.h

unix:!macx:contains(KVER, 3.4.*): INCLUDEPATH += /usr/xenomai/include /linux-3.4.6/include
unix:!macx:contains(KVER, 3.10.*): INCLUDEPATH += /usr/xenomai/include
unix:!macx:contains(KVER, 3.14.*): INCLUDEPATH += /usr/xenomai/include

unix:!macx: LIBS += -lm -lpthread -lrt -lts -L/usr/xenomai/lib -lxenomai -lnative

unix:!macx:contains(KVER, 3.4.*): LIBS += -L../../lib_3.4
unix:!macx:contains(KVER, 3.10.*): LIBS += -L../../lib_3.10
unix:!macx:contains(KVER, 3.14.*): LIBS += -L../../lib/linux

win32: LIBS += -lgdi32 -luser32 -lws2_32 -lmov_space

unix:!macx: CONFIG += release + thread

CONFIG  += qaxcontainer
CONFIG += staticlib

SOURCES += \#src/main.cpp\
    ../src/wgwidget/cabstractable.cpp \
    ../src/wgwidget/cabstractqr.cpp \
    ../src/wgwidget/dcmthread.cpp \
    ../src/wgwidget/dlgdescription.cpp \
    ../src/wgwidget/dlgloading.cpp \
    ../src/wgwidget/dlgselfmaintaincofg.cpp \
    ../src/wgwidget/dlgupdate.cpp \
    ../src/wgwidget/dlgwebdownload.cpp \
    ../src/wgwidget/hmicalibrationadjust.cpp \
    ../src/wgwidget/hmiconfig.cpp \
    ../src/wgwidget/containerwidget.cpp \
    ../src/wgwidget/hmidcmconfdata.cpp \
    ../src/wgwidget/hmidcmmanage.cpp \
    ../src/wgwidget/hmimaintaindata.cpp \
    ../src/wgwidget/hmimenudata.cpp \
    ../src/wgwidget/hmimenumanage.cpp \
    ../src/wgwidget/hmimenupage.cpp \
    ../src/wgwidget/hmimain/hnchmi.cpp \
    ../src/wgwidget/infobar.cpp \
    ../src/wgwidget/msgchan.cpp \
    ../src/wgwidget/msgdata.cpp \
    ../src/nccontrols/cJSON.c \
    ../src/wgwidget/oscservo/hmioscsensordatacheck.cpp \
    ../src/wgwidget/oscservo/hmisensorheat.cpp \
    ../src/wgwidget/oscservo/osccompsensorconf.cpp \
    ../src/wgwidget/oscservo/oscservocompsensor.cpp \
    ../src/wgwidget/oscservo/wgoscsensordatacheck.cpp \
    ../src/wgwidget/oscservo/roundscale.cpp \
    ../src/wgwidget/oscservo/wgoscsensorloaddata.cpp \
    ../src/wgwidget/oscservo/wgoscsensorloadrtl.cpp \
    ../src/wgwidget/thermal/hmiconsistencydata.cpp \
    ../src/wgwidget/thermal/hmigroupdata.cpp \
    ../src/wgwidget/thermal/hmirtldata.cpp \
    ../src/wgwidget/thermal/wgthermalevaluatecompensability.cpp \
    ../src/wgwidget/thermal/wgthermalevaluateconsistency.cpp \
    ../src/wgwidget/thermal/wgthermalevaluateselect.cpp \
    ../src/wgwidget/thermal/wgthermalevaluatesensorcheck.cpp \
    ../src/wgwidget/showinit.cpp \
    ../src/common/common.cpp \
    ../src/common/hmicommon.cpp \
    ../src/wgwidget/hmimain/staticdata.cpp \
    ../src/wgwidget/progtime.cpp \
    ../src/wgwidget/messagebar.cpp \
    ../src/wgwidget/mainwidget.cpp \
    ../src/wgwidget/thermal/wgthermalcompen.cpp \
    ../src/wgwidget/thermal/wgthermalconfig.cpp \
    ../src/wgwidget/thermal/wgthermaldatainput.cpp \
    ../src/wgwidget/thermal/wgthermalevaluate.cpp \
    ../src/wgwidget/updatenetthread.cpp \
    ../src/wgwidget/thermal/wgheatcompsensor.cpp \
    ../src/wgwidget/thermal/wgsensorloadrtl.cpp \
    ../src/wgwidget/thermal/wgthermalsampprogview.cpp \
    ../src/wgwidget/thermal/wgthermalsampset.cpp \
    ../src/wgwidget/wgadjuststep1.cpp \
    ../src/wgwidget/wgadjuststep2.cpp \
    ../src/wgwidget/wgadjuststep3.cpp \
    ../src/wgwidget/wgadjuststep4.cpp \
    ../src/wgwidget/wgcalibration.cpp \
    ../src/wgwidget/wgcollisioncheck.cpp \
    ../src/wgwidget/wgcollisioncheckconf.cpp \
    ../src/wgwidget/wgdcmcrdshow.cpp \
    ../src/wgwidget/wgmenubar.cpp \
    ../src/wgwidget/webthread.cpp \
    ../src/wgwidget/wgnetcommweb.cpp \
    ../src/wgwidget/oscservo/wgoscsensordatacomp.cpp \
    ../src/wgwidget/oscservo/wgoscsensordatasel.cpp \
    ../src/wgwidget/wgprogview.cpp \
    ../src/wgwidget/wgselftestmaintain.cpp \
    ../src/wgwidget/wgselftestmaintainhistory.cpp \
    ../src/wgwidget/wggmode.cpp \
    ../src/wgwidget/wgcutinfo.cpp \
    ../src/wgwidget/alarmdata.cpp \
    ../src/wgwidget/ticker.cpp\
    ../src/wgwidget/wgupdatenet.cpp \
    ../src/userwidget/wguser.cpp \
    ../src/wgwidget/wgfileview.cpp \
    ../src/wgwidget/wgprogsel.cpp \
    ../src/wgwidget/prog.cpp \
    ../src/wgwidget/wgcordsyssg.cpp \
    ../src/wgwidget/wgalarminfo.cpp \
    ../src/wgwidget/toollife.cpp \
    ../src/wgwidget/usermacrodata.cpp\
    ../src/wgwidget/corddata.cpp \
    ../src/wgwidget/dlgmsgbox.cpp \
    ../src/wgwidget/udisk.cpp \
    ../src/wgwidget/wgparaman.cpp \
    ../src/wgwidget/wgusermacro.cpp \
    ../src/wgwidget/wgalarmhis.cpp \
    ../src/wgwidget/filemanage.cpp \
    ../src/wgwidget/hisdata.cpp \
    ../src/wgwidget/wguserpara.cpp \
    ../src/wgwidget/wgtoolcrdshow.cpp \
    ../src/wgwidget/dlggetfile.cpp \
    ../src/wgwidget/statexyr.cpp \
    ../src/wgwidget/statebw.cpp \
    ../src/wgwidget/statefg.cpp \
    ../src/wgwidget/wgtimeset.cpp \
    ../src/wgwidget/wgmacro.cpp \
    ../src/wgwidget/wgregister.cpp \
    ../src/wgwidget/wgupdate.cpp \
    ../src/wgwidget/sysupdate.cpp \
    ../src/wgwidget/wgturntabs.cpp \
    ../src/wgwidget/chaxispos.cpp \
    ../src/wgwidget/spposhold.cpp \
    ../src/wgwidget/wgfst.cpp \
    ../src/wgwidget/wgprogman.cpp \
    ../src/wgwidget/wgaccess.cpp \
    ../src/wgwidget/pwddlg.cpp \
    ../src/wgwidget/hmiparaman.cpp \
    ../src/wgwidget/wgeditor.cpp \
    ../src/wgwidget/wgsysinfo.cpp \
    ../src/wgwidget/wggmodemilling.cpp \
    ../src/wgwidget/wgdispparm.cpp \
    ../src/wgwidget/wgpos.cpp \
    ../src/wgwidget/wgunion.cpp \
    ../src/wgwidget/wgladpara.cpp \
    ../src/wgwidget/wgtheadcompen.cpp \
    ../src/wgwidget/wgnetcommip.cpp \
    ../src/wgwidget/wgnetcommshare.cpp \
    ../src/wgwidget/wgnetcommftp.cpp \
    ../src/wgwidget/netcommdata.cpp \
    ../src/wgwidget/wgcordsysstahorizon.cpp \
    ../src/wgwidget/ping.cpp \
    ../src/wgwidget/wgdevconf.cpp \
    ../src/wgwidget/wgloadcompfile.cpp \
    ../src/wgwidget/devshow.cpp \
    ../src/wgwidget/wgstatexyr.cpp \
    ../src/wgwidget/wgrandom.cpp \
    ../src/wgwidget/wgsimuviewmill.cpp \
    ../src/wgwidget/wgplcsw.cpp \
    ../src/wgwidget/dlgsimuviewmillsetparm.cpp \
    ../src/wgwidget/ladview.cpp \
    ../src/wgwidget/ladviewdata.cpp \
    ../src/wgwidget/wgladstl.cpp \
    ../src/wgwidget/wgladreglock.cpp \
    ../src/wgwidget/wgladcross.cpp \
    ../src/wgwidget/wgladregk.cpp \
    ../src/wgwidget/ladtrace.cpp \
    ../src/wgwidget/wgladtrace.cpp \
    ../src/wgwidget/wgladtraceparam.cpp \
    ../src/wgwidget/dlglad.cpp\
    ../src/wgwidget/dlgtoollifeman.cpp \
    ../src/wgwidget/wgncmdi.cpp \
    ../src/wgwidget/wgtoolmag.cpp \
    ../src/wgwidget/dlgdroplist.cpp \
    ../src/wgwidget/mdimanage.cpp \
    ../src/wgwidget/wgrunstats.cpp \
    ../src/wgwidget/runstatdata.cpp \
    ../src/nccontrols/mytable.cpp \
    ../src/wgwidget/wgladproglistedit.cpp \
    ../src/wgwidget/wgladdgn.cpp \
    ../src/wgwidget/wgladedit.cpp\
    ../src/wgwidget/wgsimuviewturn.cpp \
    ../src/wgwidget/dlgsimuviewturnsetparm.cpp \
    ../src/wgwidget/wgladcoil.cpp \
    ../src/wgwidget/wgladcellsel.cpp \
    ../src/wgwidget/wgladtimer.cpp \
    ../src/wgwidget/wgladcounter.cpp \
    ../src/wgwidget/wgladalarm.cpp \
    ../src/wgwidget/wgstated.cpp \
    ../src/wgwidget/wgladinfo.cpp \
    ../src/wgwidget/logdt.cpp \
    ../src/wgwidget/wgcnclog.cpp \
    ../src/wgwidget/loglist.cpp \
    ../src/wgwidget/wgfiletype.cpp \
    ../src/wgwidget/wgladsymbol.cpp \
    ../src/wgwidget/wgladio.cpp \
    ../src/wgwidget/wgrightcfg.cpp \
    ../src/wgwidget/wgfileman.cpp \
    ../src/wgwidget/hmiplcsw.cpp \
    ../src/wgwidget/hmicnclog.cpp \
    ../src/wgwidget/wgstateiq.cpp \
    ../src/wgwidget/stateiq.cpp \
    ../src/wgwidget/resize.cpp \
    ../src/wgwidget/wggeneralfunc.cpp \
    ../src/wgwidget/oscservo/wgoscservo.cpp \
    ../src/wgwidget/oscservo/hmioscservo.cpp \
    ../src/wgwidget/oscservo/qcustomplot.cpp \
    ../src/wgwidget/oscservo/wgoscconf.cpp \
    ../src/wgwidget/oscservo/oscservospe.cpp \
    ../src/wgwidget/oscservo/oscservopos.cpp \
    ../src/wgwidget/oscservo/oscservocir.cpp \
    ../src/wgwidget/oscservo/oscservotap.cpp \
    ../src/wgwidget/oscservo/oscservofreq.cpp \
    ../src/wgwidget/oscservo/oscservosync.cpp \
    ../src/wgwidget/oscservo/oscservospindle.cpp \
    ../src/wgwidget/oscservo/oscservofreqtap.cpp \
    ../src/wgwidget/oscservo/oscservotool.cpp \
    ../src/wgwidget/oscservo/oscservocustom.cpp \
    ../src/wgwidget/oscservo/oscservodiagnose.cpp \
    ../src/wgwidget/oscservo/oscservoreport.cpp \
    ../src/wgwidget/oscservo/hmioscproc.cpp \
    ../src/wgwidget/oscservo/wgoscservoopt.cpp \
    ../src/wgwidget/oscservo/oscwave.cpp \
    ../src/wgwidget/oscservo/wgoscservocolorset.cpp \
    ../src/wgwidget/oscservo/oscspeposconf.cpp \
    ../src/wgwidget/oscservo/wgoscgcodeview.cpp \
    ../src/wgwidget/screensaver.cpp \
    ../src/wgwidget/dlgscreensaver.cpp \
    ../src/wgwidget/oscservo/osctoolconf.cpp \
    ../src/wgwidget/oscservo/osclist.cpp \
    ../src/wgwidget/teachprogramturn.cpp \
    ../src/wgwidget/oscservo/oscservospdlconf.cpp \
    ../src/wgwidget/oscservo/oscfreqtapconf.cpp \
    ../src/wgwidget/oscservo/osccirconf.cpp \
    ../src/wgwidget/oscservo/roundwave.cpp \
    ../src/wgwidget/oscservo/osccustomconf.cpp \
    ../src/wgwidget/wgpreferpara.cpp \
    ../src/wgwidget/oscservo/oscsyncconf.cpp \
    ../src/wgwidget/wgmacinfo.cpp \
    ../src/wgwidget/oscservo/oscfreqconf.cpp \
    ../src/wgwidget/poscheck.cpp \
    ../src/wgwidget/wgfaultguide.cpp \
    ../src/wgwidget/wgaxismonitor.cpp \
    ../src/wgwidget/inputmanage.cpp \
    ../src/wgwidget/impactdefend.cpp \
    ../src/wgwidget/dlgimpact.cpp\
    ../src/wgwidget/deftag.cpp \
    ../src/wgwidget/macstate.cpp \
    ../src/wgwidget/wgstatrecord.cpp \
    ../src/wgwidget/oscservo/oscloopconf.cpp \
    ../src/wgwidget/oscservo/oscservoloop.cpp \
    ../src/wgwidget/wghisfileexport.cpp \
    ../src/wgwidget/wgloadlogo.cpp \
    ../src/nccontrols/nctable.cpp \
    ../src/wgwidget/toolgroup.cpp \
    ../src/wgwidget/wgturningtool.cpp \
    ../src/wgwidget/toolcommon.cpp \
    ../src/wgwidget/turningtoolrecord.cpp \
    ../src/wgwidget/wgturningtoolcrdshow.cpp \
    ../src/wgwidget/wgmacstateexport.cpp \
    ../src/wgwidget/wgrunproginfo.cpp \
    ../src/wgwidget/printscreen.cpp \
    ../src/wgwidget/wgcraftfile.cpp \
    ../src/wgwidget/alarmwindow.cpp \
    ../src/wgwidget/oscservo/oscreportconf.cpp \
    ../src/wgwidget/hmircsclear.cpp \
    ../src/wgwidget/systime.cpp \
    ../src/wgwidget/diagnaoserecord.cpp \
    ../src/wgwidget/wgmcode.cpp \
    ../src/wgwidget/wgtooloffsetnormal.cpp \
    ../src/wgwidget/wgworkmeastcs.cpp \
    ../src/wgwidget/wgturntoolmeasparm.cpp \
    ../src/wgwidget/wgturntoolmeascali.cpp \
    ../src/wgwidget/wgturntoolmeasoffset.cpp \
    ../src/wgwidget/wgturntoolmeas.cpp \
    ../src/wgwidget/turntoolmeas.cpp \
    ../src/wgwidget/editpara.cpp \
    ../src/wgwidget/hmimacstr.cpp \
    ../src/wgwidget/wgmacstr.cpp \
    ../src/wgwidget/wgmacstrrtcp.cpp \
    ../src/wgwidget/dlgturntoolmeas.cpp \
    ../src/wgwidget/oscservo/oscdynamicalconf.cpp \
    ../src/wgwidget/oscservo/oscservodynamical.cpp \
    ../src/wgwidget/wgcloseloopswitch.cpp \
    ../src/wgwidget/wgcloseloopparm.cpp \
    ../src/wgwidget/closeloop.cpp \
    ../src/wgwidget/wgfilemansel.cpp \
    ../src/wgwidget/wgtoolmagcfg.cpp \
    ../src/wgwidget/hfwl.cpp \
    ../src/wgwidget/wgcordposition.cpp \
    ../src/wgwidget/wgcordvalue.cpp \
    ../src/wgwidget/wgcordsys.cpp \
    ../src/wgwidget/acmpman.cpp \
    ../src/wgwidget/oscservo/wgqrencoderepot.cpp \
    ../src/wgwidget/qrencodeshow.cpp \
    ../src/wgwidget/hmiwginfotrans.cpp \
    ../src/wgwidget/hmiloadxml.cpp \
    ../src/wgwidget/wgselftest.cpp \
    ../src/wgwidget/wgselftestconf.cpp \
    ../src/wgwidget/oscservo/oscservozheatcomp.cpp \
    ../src/wgwidget/oscservo/osczcompconf.cpp \
    ../src/wgwidget/oscservo/oscservosheatcomp.cpp \
    ../src/wgwidget/oscservo/oscscompconf.cpp \
    ../src/wgwidget/dlgtoollifeunit.cpp \
    ../src/wgwidget/fileprocessthread.cpp \
    ../src/wgwidget/hmitwincode.cpp \
    ../src/wgwidget/hmiloadparaexp.cpp \
    ../src/wgwidget/msgprompt.cpp \
    ../src/nccontrols/nceditor.cpp \
    ../src/wgwidget/wgprogedit.cpp \
    ../src/wgwidget/wgtoollife.cpp \
    ../src/wgwidget/dlgtoollifeset.cpp \
    ../src/wgwidget/wgtoolbreak.cpp \
    ../src/wgwidget/wgmaindisp.cpp \
    ../src/wgwidget/wggmodecutinfo.cpp \
    ../src/nccontrols/nctableonepage.cpp \
    ../src/wgwidget/hotkeycfg.cpp \
    ../src/wgwidget/oscservo/wgoscapp.cpp \
    ../src/wgwidget/oscservo/hmioscapp.cpp \
    ../src/wgwidget/oscservo/oscwavedraw.cpp \
    ../src/wgwidget/oscservo/oscapptime.cpp \
    ../src/wgwidget/oscservo/wgoscappconf.cpp \
    ../src/wgwidget/hmisweeper.cpp \
    ../src/wgwidget/wgsweeper.cpp \
    ../src/wgwidget/oscservo/hmiscrewwear.cpp \
    ../src/wgwidget/oscservo/wgscrewwear.cpp \
    ../src/wgwidget/hmiestimate.cpp \
    ../src/wgwidget/wgestimate.cpp \
    ../src/wgwidget/hmimcode.cpp \
    ../src/wgwidget/hmitoolbreak.cpp \
    ../src/wgwidget/oscservo/hmireplayrecorder.cpp \
    ../src/wgwidget/dlgosappselect.cpp \
    ../src/wgwidget/wgoscappprog.cpp \
    ../src/wgwidget/oscservo/wgoscappopt.cpp \
    ../src/wgwidget/wgalarmqr.cpp \
    ../src/wgwidget/dlgoscservoselect.cpp \
    ../src/wgwidget/wgtrscompen.cpp \
    ../src/wgwidget/wgtrscompenload.cpp \
    ../src/wgwidget/hmitrscompen.cpp \
    ../src/wgwidget/wgtrscompengraph.cpp \
    ../src/wgwidget/autotesthmi.cpp \
    ../src/wgwidget/wgscrewthread.cpp \
    ../src/wgwidget/hmivarman.cpp \
    ../src/nccontrols/nctableex.cpp \
    ../src/wgwidget/dlgfstset.cpp \
    ../src/wgwidget/hmipaintercolor.cpp \
    ../src/wgwidget/wgqr.cpp \
    ../src/wgwidget/oscservo/dlgoscappconf.cpp \
    ../src/wgwidget/wgladcellhelp.cpp \
    ../src/wgwidget/wgwebview.cpp \
    ../src/wgwidget/wgladeditview.cpp \
    ../src/wgwidget/hmidatarestore.cpp \
    ../src/wgwidget/wgnetping.cpp \
    ../src/nccontrols/dirmovelayout.cpp \
    ../src/wgwidget/wgqrparmbackup.cpp \
    ../src/wgwidget/consistencycheck.cpp \
    ../src/wgwidget/simumacposdata.cpp \
    ../src/wgwidget/simumillcal.cpp \
    ../src/wgwidget/simupara.cpp \
    ../src/wgwidget/simumacposdatarecord.cpp \
    ../src/wgwidget/simuturn.cpp \
    ../src/wgwidget/simuviewmill.cpp \
    ../src/wgwidget/colorcfg.cpp \
    ../src/wgwidget/dlgsimucolorcfg.cpp \
    ../src/wgwidget/simuviewturn.cpp \
    ../src/wgwidget/hmimain/hmimain.cpp \
    ../src/wgwidget/wgphoneappqr.cpp \
    ../src/wgwidget/wgmultichdisp.cpp \
    ../src/wgwidget/wgsigchdisp.cpp \
    ../src/wgwidget/wgfstmultich.cpp \
    ../src/wgwidget/oscservo/wgselfadjusting.cpp \
    ../src/wgwidget/oscservo/hmiselfadjusting.cpp \
    ../src/wgwidget/oscservo/oscbeartapconf.cpp \
    ../src/wgwidget/oscservo/oscservobeartap.cpp \
    ../src/wgwidget/wgtoolshape.cpp \
    ../src/wgwidget/wgturnmilloff.cpp \
    ../src/wgwidget/regbinary.cpp \
    ../src/wgwidget/wgregxyr.cpp \
    ../src/wgwidget/wgturnmillwear.cpp \
    ../src/wgwidget/wgturnmillmag.cpp \
    ../src/wgwidget/wgmilltoollentest.cpp \
    ../src/wgwidget/dlgmilltoollentestseltool.cpp \
    ../src/wgwidget/wgregiq.cpp \
    ../src/wgwidget/oscservo/selfadjuststep1.cpp \
    ../src/wgwidget/oscservo/selfadjuststep2.cpp \
    ../src/wgwidget/oscservo/selfadjuststep3.cpp \
    ../src/wgwidget/oscservo/selfadjuststep4.cpp \
    ../src/wgwidget/extdevmanqthread.cpp \
    ../src/wgwidget/macstatesnapshot.cpp\
    ../src/wgwidget/wgmacstatesnapshotqr.cpp \
    ../src/wgwidget/dlgtoollifetactics.cpp \
    ../src/wgwidget/wgcraftbag.cpp \
    ../src/wgwidget/wgparamcom.cpp \
    ../src/wgwidget/paramsubinfolist.cpp \
    ../src/wgwidget/wguserdefparamcom.cpp \
    ../src/wgwidget/dlgalarmhelp.cpp \
    ../src/wgwidget/hmialarmhelp.cpp \
    ../src/wgwidget/workmeas/hmiworkmeas.cpp \
    ../src/wgwidget/workmeas/workmeaspic.cpp \
    ../src/wgwidget/workmeas/workmeasbase.cpp \
    ../src/wgwidget/workmeas/wgworkmeas.cpp \
    ../src/wgwidget/workmeas/wgworkmeastool.cpp \
    ../src/wgwidget/wgprogtech.cpp \
    ../src/wgwidget/teachprogram.cpp \
    ../src/wgwidget/turningtooloffset.cpp \
    ../src/wgwidget/hmiparmcheck.cpp \
    ../src/wgwidget/wgparmcheck.cpp \
    ../src/wgwidget/wgnoticehis.cpp \
    ../src/wgwidget/twinkle.cpp \
    ../src/nccontrols/nccheckbox.cpp \
    ../src/nccontrols/nccombobox.cpp \
    ../src/wgwidget/wgvirtkey.cpp \
    ../src/wgwidget/oscservo/dlgscrewwearconf.cpp \
    ../src/wgwidget/dlgreplace.cpp \
    ../src/wgwidget/dlglogin.cpp \
    ../src/wgwidget/gdblk/gdblkxml.cpp \
    ../src/wgwidget/gdblk/gdblkitem.cpp \
    ../src/wgwidget/gdblk/gdblkitemman.cpp \
    ../src/wgwidget/gdblk/gdblkcoder.cpp \
    ../src/wgwidget/gdblk/hmigdblk.cpp \
    ../src/wgwidget/gdblk/wggdblkoutline.cpp \
    ../src/wgwidget/gdblk/wgcyc.cpp \
    ../src/wgwidget/gdblk/hmigdblkcurtrajstep.cpp \
    ../src/wgwidget/gdblk/wgoutline.cpp \
    ../src/wgwidget/gdblk/wggdblkoutlinesel.cpp \
    ../src/wgwidget/gdblk/wggdblksel.cpp \
    ../src/wgwidget/gdblk/wggdblkstepsel.cpp \
    ../src/wgwidget/gdblk/gdblkstepselitem.cpp \
    ../src/wgwidget/gdblk/dlggdblksel.cpp \
    ../src/wgwidget/gdblk/dlgbaseoutlinechoose.cpp \
    ../src/wgwidget/gdblk/dlgoutlinesel.cpp \
    ../src/wgwidget/gdblk/analysisfunc.cpp \
    ../src/wgwidget/gdblk/outlinecal.cpp \
    ../src/wgwidget/gdblk/dlggdblkselbase.cpp \
    ../src/wgwidget/dlgtoolpropset.cpp \
    ../src/nccontrols/ncpushbutton.cpp \
    ../src/hmidata/hmitooldata.cpp \
    ../src/wgwidget/rsfile.cpp \
    ../src/wgwidget/wgdncrxtx.cpp \
    ../src/wgwidget/dlgnuminput.cpp \
    ../src/wgwidget/cnclogapi.cpp \
    ../src/wgwidget/wgtoolbasepos.cpp \
    ../src/wgwidget/wgfiletree.cpp \
    ../src/wgwidget/dlgselftestfeattype.cpp \
    ../src/wgwidget/dlgselftestfeature.cpp \
    ../src/wgwidget/wgselftestshowfea.cpp \
    ../src/wgwidget/selftestcalc.cpp \
    ../src/wgwidget/wgselftestextension.cpp \
    ../src/wgwidget/selftestcore.cpp \
    ../src/wgwidget/dlgselftestgratingruler.cpp \
    ../src/wgwidget/selftestlog.cpp \
    ../src/wgwidget/wgselftestqrencode.cpp \
    ../src/wgwidget/wgprogeditmulch.cpp \
    ../src/wgwidget/wgdevwidget.cpp \
    ../src/wgwidget/wgdevcompare.cpp \
    ../src/wgwidget/devparam.cpp \
    ../src/wgwidget/basesigchdisp.cpp \
    ../src/wgwidget/basemultichdisp.cpp\
    ../src/wgwidget/ncmdieditor.cpp \
    ../src/wgwidget/wgfiletypeall.cpp \
    ../src/wgwidget/wgtoolautomeas.cpp \
    ../src/wgwidget/dlgtoolsel.cpp \
    ../src/wgwidget/wgcordsysstavertical.cpp \
    ../src/wgwidget/msgpromptwith3exit.cpp \
    ../src/nccontrols/ncdatalimit.cpp \
    ../src/wgwidget/wgmcp.cpp \
    ../src/wgwidget/wgprogeditall.cpp \
    ../src/wgwidget/apposdepend.cpp \
    ../src/wgwidget/dlgtoolbreakcfg.cpp \
    ../src/wgwidget/serportthread.cpp \
    ../src/wgwidget/rfidballuff.cpp \
    ../src/wgwidget/serportcmd.cpp \
    ../src/wgwidget/rfidsygole.cpp \
    ../src/wgwidget/wgcordsysunion.cpp \
    ../src/wgwidget/wgcordsystable.cpp \
    ../src/wgwidget/dlg5axismeas.cpp \
    ../src/hmidata/hmiratiodata.cpp\
    ../src/wgwidget/wgrationset.cpp \
    ../src/hmidata/hmiparset.cpp \
    ../src/wgwidget/interfaceframe.cpp \
    ../src/hmidata/ncassistantconf.cpp \
    ../src/wgwidget/hidcomm.cpp \
    ../src/wgwidget/usbmcp.cpp \
    ../src/wgwidget/oscservo/oscservoraster.cpp \
    ../src/wgwidget/oscservo/oscrasterconf.cpp \
    ../src/wgwidget/wgsysinfoxml.cpp \
    ../src/wgwidget/dlgmsgsavesv.cpp \
    ../src/wgwidget/oscservo/hmispindlewear.cpp \
    ../src/wgwidget/oscservo/wgspindlewear.cpp \
    ../src/wgwidget/oscservo/wgspindlewearoverlog.cpp \
    ../src/wgwidget/oscservo/wgspindlewearconf.cpp \
    ../src/wgwidget/oscservo/wgoscelewaveview.cpp \
    ../src/wgwidget/oscservo/oscelewave.cpp \
    ../src/wgwidget/dlgcrdrcsclear.cpp \
    ../src/wgwidget/wgdispparmcfg.cpp \
    ../src/wgwidget/oscservo/wgelespectral.cpp \
    ../src/wgwidget/wgfeedselfadapt.cpp \
    ../src/wgwidget/thermal/thermalinit.cpp \
    ../src/wgwidget/thermal/wgsampaxisconf.cpp \
    ../src/wgwidget/thermal/wgspindlethermaldataview.cpp \
    ../src/wgwidget/thermal/wgthermalchooseprogram.cpp \
    ../src/wgwidget/thermal/wgthermaldataacquisition.cpp \
    ../src/wgwidget/thermal/wgthermaldatacommon.cpp \
    ../src/wgwidget/thermal/wgthermaldatasamplesetting.cpp \
    ../src/wgwidget/thermal/wgthermaldatmanage.cpp \
    ../src/wgwidget/thermal/wgthermaldefinedata.cpp \
    ../src/wgwidget/thermal/wgthermalgcodeconf.cpp \
    ../src/wgwidget/thermal/wgthermalprobeinf.cpp \
    ../src/wgwidget/thermal/wgthermalsampconf.cpp \
    ../src/wgwidget/thermal/wgthermalshareddata.cpp \
    ../src/wgwidget/thermal/wgxyzthermaldataview.cpp \
    ../src/wgwidget/thermal/wgyaxisthermaldataview.cpp \
    ../src/wgwidget/thermal/wgzaxisthermaldataview.cpp \
    ../src/wgwidget/thermalset.cpp \
    ../src/wgwidget/thermal/wgdiallog.cpp \
    ../src/wgwidget/thermal/wggraingrulerdata.cpp \
    ../src/wgwidget/TemperConnectRefreshThread.cpp \
    ../src/wgwidget/hmitemperature.cpp \
    ../src/wgwidget/thermal/wgsensorloaddat.cpp \
    ../src/wgwidget/thermal/wgthermalweightbias.cpp \
    ../src/wgwidget/thermal/wgthermallstm.cpp \
    ../src/wgwidget/thermal/wgthermallstmpredict.cpp \
    ../src/wgwidget/postfixexpression.cpp \
    ../src/wgwidget/oscservo/wgelepolyfit.cpp \
    ../src/wgwidget/dataverify.cpp \
    ../src/wgwidget/oscservo/wgoscrastersignalanaly.cpp \
    ../src/wgwidget/oscservo/overlimitloglist.cpp \
    ../src/wgwidget/dlg5axistooloffset.cpp \
    ../src/hmidata/randomrecorddata.cpp \
    ../src/wgwidget/thermal/physicalModelThermal.cpp \
    ../src/wgwidget/thermal/wgphysithermalcompen.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsparamset.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllssel.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsmeassel.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsview.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeaslls.cpp \
    ../src/wgwidget/wgpressuresensor.cpp \
    ../src/nccontrols/ncscrollarea.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsmacro.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsmacset.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsinfoset.cpp \
    ../src/wgwidget/wgloadusericon.cpp \
    ../src/wgwidget/lnsworkmeas/wgllstestmacro.cpp \
    ../src/wgwidget/dlginfo.cpp \
    ../src/wgwidget/hmibreakpointdata.cpp \
    ../src/wgwidget/wgbreakpoint.cpp \
    ../src/wgwidget/dlgbreakpoint.cpp \
    ../src/wgwidget/dlgpoweroffbreakpoint.cpp \
    ../src/wgwidget/ncbreakpoint.cpp \
    ../src/wgwidget/wgnetcommnclink.cpp \
    ../src/wgwidget/hmitoolmeasureitem.cpp \
    ../src/wgwidget/hmitoolmeasurexmlcfg.cpp \
    ../src/wgwidget/wgtoolmeasure.cpp \
    ../src/wgwidget/dlgtoolmeascomparmset.cpp \
    ../src/wgwidget/simumill.cpp \
    ../src/wgwidget/simuviewbase.cpp \
    ../src/wgwidget/simumacposdatadcdblkman.cpp \
    ../src/wgwidget/progview.cpp \
    ../src/wgwidget/wgsimuprogview.cpp \
    ../src/wgwidget/wggraphsimuview.cpp \
    ../src/wgwidget/wgncucparmconfig.cpp \
    ../src/wgwidget/hmincucparmconfig.cpp \
    ../src/wgwidget/wghdhworkmeastool.cpp \
    ../src/wgwidget/hdhworkmeastooldata.cpp \
    ../src/wgwidget/wghdhworkmeastoolshow.cpp \
    ../src/wgwidget/wghdhworkmeastoolall.cpp \
    ../src/wgwidget/wghdhworkmeastoolparam.cpp \
    ../src/wgwidget/workmeas/wgworkmeastoolwithball.cpp \
    ../src/wgwidget/wgmacstrinit.cpp \
    ../src/wgwidget/wgncmacrodirbase.cpp \
    ../src/wgwidget/wgultrasound.cpp \
    ../src/wgwidget/wgultrasoundwave.cpp \
    ../src/wgwidget/wgultrasoundrifa.cpp \
    ../src/wgwidget/ncultrasoundthread.cpp \
    ../src/wgwidget/hmiultrasoundrifa.cpp \
    ../src/wgwidget/wgultrasoundfilerifa.cpp \
    ../src/wgwidget/workmeas/dlg3drotate.cpp \
    ../src/wgwidget/workmeas/dlgworkmeascircle.cpp \
    ../src/wgwidget/workmeas/workmeascircle.cpp \
    ../src/wgwidget/workmeas/wg3drot.cpp \
    ../src/wgwidget/oscservo/oscacceconf.cpp \
    ../src/wgwidget/workmeas/wgworkmeastoollen.cpp \
    ../src/wgwidget/oscservo/oscservoacce.cpp \
    ../src/wgwidget/oscservo/dlgosccurcompare.cpp \
    ../src/wgwidget/oscservo/osccurrentconf.cpp \
    ../src/wgwidget/oscservo/oscservocurrent.cpp \
    ../src/wgwidget/oscservo/hmijourneycurrent.cpp \
    ../src/wgwidget/wgparamgrade.cpp \
    ../src/wgwidget/hmiparamgrade.cpp \
    ../src/nccontrols/ncbusytask.cpp \
    ../src/nccontrols/ncprogress.cpp \
    ../src/wgwidget/ncwidgetload.cpp \
    ../src/wgwidget/hmifiletextcodec.cpp \
    ../src/wgwidget/mdiprogtext.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsparamsetlist.cpp \
    ../src/wgwidget/gdblk/stepselbase.cpp \
    ../src/wgwidget/gdblk/sigstepbase.cpp \
    ../src/wgwidget/gdblk/hmimeastestmacro.cpp \
    ../src/wgwidget/gdblk/steplistselfile.cpp \
    ../src/wgwidget/gdblk/wgmeasconfig.cpp \
    ../src/wgwidget/gdblk/wgmeastestmacro.cpp \
    ../src/wgwidget/gdblk/wggdblksigstep.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllseditfileparam.cpp \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllseditbase.cpp \
    ../src/wgwidget/lnsworkmeas/wgllstitle.cpp \
    ../src/wgwidget/wgparamgradeedit.cpp \
    ../src/wgwidget/emmchealth.cpp \
    ../src/wgwidget/wgsesrvoparamlist.cpp \
    ../src/wgwidget/sesrvoparambackdata.cpp \
    ../src/wgwidget/wgloadadptrecommended.cpp \
    ../src/wgwidget/wgloadadptlinear.cpp \
    ../src/wgwidget/loadadptbackdata.cpp \
    ../src/wgwidget/dlgproggprint.cpp \
    ../src/wgwidget/dlghdhwormeas.cpp \
    ../src/wgwidget/hmicompfile.cpp \
    ../src/wgwidget/wgcputime.cpp \
    ../src/wgwidget/wgcpumonitor.cpp \
    ../src/hncservice/servicedata.cpp \
    ../src/hncservice/sysreset.cpp \
    ../src/wgwidget/hmisysreset.cpp \
    ../src/wgwidget/checkkernelalive.cpp \
    ../src/wgwidget/oscservo/oscinertiaconf.cpp \
    ../src/wgwidget/oscservo/osckcompenconf.cpp \
    ../src/wgwidget/oscservo/oscservokcompen.cpp \
    ../src/wgwidget/oscservo/oscservoinertia.cpp \
    ../src/wgwidget/hncworkmeas/wghncwmsteplist.cpp \
    ../src/wgwidget/hncworkmeas/wghncwmstepedit.cpp \
    ../src/wgwidget/hncworkmeas/wghncwmsteplistsel.cpp \
    ../src/wgwidget/hncworkmeas/wghncmeasconfig.cpp \
    ../src/wgwidget/hncworkmeas/wghncwmstepeditbase.cpp \
    ../src/wgwidget/hncworkmeas/wghncconfigsig.cpp \
    ../src/wgwidget/hncworkmeas/dlghncsteplistsel.cpp \
    ../src/wgwidget/hncworkmeas/wghncwmjogstepedit.cpp \
    ../src/wgwidget/hncworkmeas/wghncwmjogfileview.cpp \
    ../src/wgwidget/hncworkmeas/dlghncstepsel.cpp \
    ../src/wgwidget/hncworkmeas/dlghncmeassel.cpp \
    ../src/wgwidget/hncworkmeas/hmihncmeasres.cpp \
    ../src/wgwidget/hncworkmeas/wghncmeasres.cpp \
    ../src/wgwidget/hncworkmeas/wghnctestmacro.cpp \
    ../src/wgwidget/hncworkmeas/wghncmeassel.cpp \
    ../src/hmidata/hmimacro.cpp \
    ../src/wgwidget/wgposwithfeed.cpp \
    ../src/wgwidget/wgfeed.cpp \
    ../src/wgwidget/wgcrdg5x.cpp \
    ../src/wgwidget/wgfiletab.cpp \
    ../src/wgwidget/wgwidgetlistbase.cpp \
    ../src/wgwidget/cpulog.cpp \
    ../src/hmidata/nccputime.cpp \
    ../src/wgwidget/wgnetnclink.cpp \
    ../src/wgwidget/wghomeparam.cpp \
    ../src/wgwidget/wghomeset.cpp \
    ../src/nccontrols/nclightwidget.cpp \
    ../src/wgwidget/hmihomeparam.cpp \
    ../src/wgwidget/hmihomeconf.cpp \
    ../src/wgwidget/hmiladiq.cpp \
    ../src/wgwidget/hmiredecodedata.cpp \
    ../src/nccontrols/tabconstantstcol.cpp \
    ../src/plugins/usrdef/usrdefdata.cpp \
    ../src/plugins/usrdef/usrdeftablewidgettab.cpp \
    ../src/plugins/usrdef/hmiusrcallback.cpp \
    ../src/wgwidget/oscservo/wgoscservoraster.cpp \
    ../src/gwg_widget/gwg_pos_main.cpp \
    ../src/gwg_widget/common_widget/card.cpp


HEADERS += \
    ../src/wgwidget/cabstractable.h \
    ../src/wgwidget/cabstractqr.h \
    ../src/wgwidget/dcmthread.h \
    ../src/wgwidget/dlgdescription.h \
    ../src/wgwidget/dlgloading.h \
    ../src/wgwidget/dlgselfmaintaincofg.h \
    ../src/wgwidget/dlgupdate.h \
    ../src/wgwidget/dlgwebdownload.h \
    ../src/wgwidget/hmicalibrationadjust.h \
    ../src/wgwidget/hmidcmconfdata.h \
    ../src/wgwidget/hmidcmmanage.h \
    ../src/wgwidget/hmimaintaindata.h \
    ../src/wgwidget/hmimenudata.h \
    ../src/wgwidget/hmimenumanage.h \
    ../src/wgwidget/hmimenupage.h \
    ../src/nccontrols/cJSON.h \
    ../src/wgwidget/oscservo/hmioscsensordatacheck.h \
    ../src/wgwidget/oscservo/hmisensorheat.h \
    ../src/wgwidget/oscservo/osccompsensorconf.h \
    ../src/wgwidget/oscservo/oscservocompsensor.h \
    ../src/wgwidget/oscservo/wgoscsensordatacheck.h \
    ../src/wgwidget/oscservo/roundscale.h \
    ../src/wgwidget/oscservo/wgoscsensorloaddata.h \
    ../src/wgwidget/oscservo/wgoscsensorloadrtl.h \
    ../src/wgwidget/thermal/hmiconsistencydata.h \
    ../src/wgwidget/thermal/hmigroupdata.h \
    ../src/wgwidget/thermal/hmirtldata.h \
    ../src/wgwidget/thermal/wgthermalevaluatecompensability.h \
    ../src/wgwidget/thermal/wgthermalevaluateconsistency.h \
    ../src/wgwidget/thermal/wgthermalevaluateselect.h \
    ../src/wgwidget/thermal/wgthermalevaluatesensorcheck.h \
    ../src/wgwidget/svnrevision.h \
    ../src/wgwidget/hmiconfig.h \
    ../src/wgwidget/containerwidget.h \
    ../src/wgwidget/hmimain/hnchmi.h \
    ../src/wgwidget/infobar.h \
    ../src/wgwidget/msgchan.h \
    ../src/wgwidget/msgdata.h \
    ../src/wgwidget/showinit.h \
    ../src/include/common.h \
    ../src/include/hmicommon.h \
    ../src/include/staticdata.h \
    ../src/wgwidget/progtime.h \
    ../src/wgwidget/messagebar.h \
    ../src/wgwidget/mainwidget.h \
    ../src/wgwidget/thermal/wgthermalcompen.h \
    ../src/wgwidget/thermal/wgthermalconfig.h \
    ../src/wgwidget/thermal/wgthermaldatainput.h \
    ../src/wgwidget/thermal/wgthermalevaluate.h \
    ../src/wgwidget/updatenetthread.h \
    ../src/wgwidget/thermal/wgheatcompsensor.h \
    ../src/wgwidget/thermal/wgsensorloadrtl.h \
    ../src/wgwidget/thermal/wgthermalsampprogview.h \
    ../src/wgwidget/thermal/wgthermalsampset.h \
    ../src/wgwidget/wgadjuststep1.h \
    ../src/wgwidget/wgadjuststep2.h \
    ../src/wgwidget/wgadjuststep3.h \
    ../src/wgwidget/wgadjuststep4.h \
    ../src/wgwidget/wgcalibration.h \
    ../src/wgwidget/wgcollisioncheck.h \
    ../src/wgwidget/wgcollisioncheckconf.h \
    ../src/wgwidget/wgdcmcrdshow.h \
    ../src/wgwidget/wgmenubar.h \
    ../src/wgwidget/webthread.h \
    ../src/wgwidget/wgnetcommweb.h \
    ../src/wgwidget/oscservo/wgoscsensordatacomp.h \
    ../src/wgwidget/oscservo/wgoscsensordatasel.h \
    ../src/wgwidget/wgprogview.h \
    ../src/wgwidget/wgselftestmaintain.h \
    ../src/wgwidget/wgselftestmaintainhistory.h \
    ../src/wgwidget/wggmode.h \
    ../src/wgwidget/wgcutinfo.h \
    ../src/wgwidget/alarmdata.h \
    ../src/wgwidget/ticker.h\
    ../src/wgwidget/wgupdatenet.h \
    ../src/userwidget/wguser.h \
    ../src/wgwidget/wgfileview.h \
    ../src/wgwidget/wgprogsel.h \
    ../src/wgwidget/prog.h \
    ../src/wgwidget/wgcordsyssg.h \
    ../src/wgwidget/wgalarminfo.h \
    ../src/wgwidget/toollife.h \
    ../src/wgwidget/usermacrodata.h \
    ../src/wgwidget/corddata.h \
    ../src/wgwidget/dlgmsgbox.h \
    ../src/wgwidget/udisk.h \
    ../src/wgwidget/wgparaman.h \
    ../src/wgwidget/wgusermacro.h \
    ../src/wgwidget/wgalarmhis.h \
    ../src/wgwidget/filemanage.h \
    ../src/wgwidget/hisdata.h \
    ../src/wgwidget/wguserpara.h \
    ../src/wgwidget/wgtoolcrdshow.h \
    ../src/wgwidget/dlggetfile.h \
    ../src/wgwidget/statebw.h \
    ../src/wgwidget/statexyr.h \
    ../src/wgwidget/statefg.h \
    ../src/wgwidget/wgtimeset.h \
    ../src/wgwidget/wgmacro.h \
    ../src/wgwidget/wgregister.h \
    ../src/wgwidget/hmialarmdef.h \
    ../src/wgwidget/wgupdate.h \
    ../src/wgwidget/sysupdate.h \
    ../src/wgwidget/wgturntabs.h \
    ../src/wgwidget/chaxispos.h \
    ../src/wgwidget/spposhold.h \
    ../src/wgwidget/wgfst.h \
    ../src/wgwidget/wgprogman.h \
    ../src/wgwidget/wgaccess.h \
    ../src/wgwidget/pwddlg.h \
    ../src/wgwidget/hmiparaman.h \
    ../src/wgwidget/wgeditor.h \
    ../src/wgwidget/wgsysinfo.h \
    ../src/wgwidget/wggmodemilling.h \
    ../src/wgwidget/wgdispparm.h \
    ../src/wgwidget/wgpos.h \
    ../src/wgwidget/wgunion.h \
    ../src/wgwidget/wgladpara.h \
    ../src/wgwidget/wgtheadcompen.h \
    ../src/wgwidget/wgnetcommip.h \
    ../src/wgwidget/wgnetcommshare.h \
    ../src/wgwidget/wgnetcommftp.h \
    ../src/wgwidget/netcommdata.h \
    ../src/wgwidget/wgcordsysstahorizon.h \
    ../src/wgwidget/ping.h \
    ../src/wgwidget/wgdevconf.h \
    ../src/wgwidget/wgloadcompfile.h \
    ../src/wgwidget/devshow.h \
    ../src/wgwidget/wgstatexyr.h \
    ../src/wgwidget/wgrandom.h \
    ../src/wgwidget/wgsimuviewmill.h \
    ../src/wgwidget/wgplcsw.h \
    ../src/wgwidget/dlgsimuviewmillsetparm.h \
    ../src/wgwidget/ladview.h \
    ../src/wgwidget/ladviewdata.h \
    ../src/wgwidget/wgladstl.h \
    ../src/wgwidget/ladcommon.h \
    ../src/wgwidget/wgladreglock.h \
    ../src/wgwidget/wgladcross.h \
    ../src/wgwidget/wgladregk.h \
    ../src/wgwidget/ladtrace.h \
    ../src/wgwidget/wgladtrace.h \
    ../src/wgwidget/wgladtraceparam.h \
    ../src/wgwidget/dlglad.h \
    ../src/wgwidget/dlgtoollifeman.h \
    ../src/wgwidget/wgncmdi.h \
    ../src/wgwidget/wgtoolmag.h \
    ../src/wgwidget/dlgdroplist.h \
    ../src/wgwidget/mdimanage.h \
    ../src/wgwidget/wgrunstats.h \
    ../src/wgwidget/runstatdata.h \
    ../src/nccontrols/mytable.h \
    ../src/wgwidget/wgladproglistedit.h \
    ../src/wgwidget/wgladdgn.h \
    ../src/wgwidget/wgladedit.h\
    ../src/wgwidget/wgsimuviewturn.h \
    ../src/wgwidget/dlgsimuviewturnsetparm.h \
    ../src/wgwidget/wgladcoil.h \
    ../src/wgwidget/wgladcellsel.h \
    ../src/wgwidget/wgladtimer.h \
    ../src/wgwidget/wgladcounter.h \
    ../src/wgwidget/wgladalarm.h \
    ../src/wgwidget/wgstated.h \
    ../src/wgwidget/wgladinfo.h \
    ../src/wgwidget/logdt.h \
    ../src/wgwidget/wgcnclog.h \
    ../src/wgwidget/loglist.h \
    ../src/wgwidget/wgfiletype.h \
    ../src/wgwidget/wgladsymbol.h \
    ../src/wgwidget/wgladio.h \
    ../src/wgwidget/wgrightcfg.h \
    ../src/wgwidget/wgfileman.h \
    ../src/wgwidget/hmiplcsw.h \
    ../src/wgwidget/hmicnclog.h \
    ../src/wgwidget/wgstateiq.h \
    ../src/wgwidget/stateiq.h \
    ../src/wgwidget/resize.h \
    ../src/wgwidget/wggeneralfunc.h \
    ../src/wgwidget/oscservo/wgoscservo.h \
    ../src/wgwidget/oscservo/hmioscservo.h \
    ../src/wgwidget/oscservo/qcustomplot.h \
    ../src/wgwidget/oscservo/wgoscconf.h \
    ../src/wgwidget/oscservo/oscservospe.h \
    ../src/wgwidget/oscservo/oscservopos.h \
    ../src/wgwidget/oscservo/oscservocir.h \
    ../src/wgwidget/oscservo/oscservotap.h \
    ../src/wgwidget/oscservo/oscservofreq.h \
    ../src/wgwidget/oscservo/oscservosync.h \
    ../src/wgwidget/oscservo/oscservospindle.h \
    ../src/wgwidget/oscservo/oscservofreqtap.h \
    ../src/wgwidget/oscservo/oscservotool.h \
    ../src/wgwidget/oscservo/oscservocustom.h \
    ../src/wgwidget/oscservo/oscservodiagnose.h \
    ../src/wgwidget/oscservo/oscservoreport.h \
    ../src/wgwidget/oscservo/hmioscproc.h \
    ../src/wgwidget/oscservo/wgoscservoopt.h \
    ../src/wgwidget/oscservo/oscwave.h \
    ../src/wgwidget/oscservo/wgoscservocolorset.h \
    ../src/wgwidget/oscservo/oscspeposconf.h \
    ../src/wgwidget/oscservo/wgoscgcodeview.h \
    ../src/wgwidget/screensaver.h \
    ../src/wgwidget/dlgscreensaver.h \
    ../src/wgwidget/oscservo/osctoolconf.h \
    ../src/wgwidget/oscservo/osclist.h \
    ../src/wgwidget/teachprogramturn.h \
    ../src/wgwidget/oscservo/oscservospdlconf.h \
    ../src/wgwidget/oscservo/oscfreqtapconf.h \
    ../src/wgwidget/oscservo/osccirconf.h \
    ../src/wgwidget/oscservo/roundwave.h \
    ../src/wgwidget/oscservo/osccustomconf.h \
    ../src/wgwidget/wgpreferpara.h \
    ../src/wgwidget/oscservo/oscsyncconf.h \
    ../src/wgwidget/wgmacinfo.h \
    ../src/wgwidget/oscservo/oscfreqconf.h \
    ../src/wgwidget/poscheck.h \
    ../src/wgwidget/wgfaultguide.h \
    ../src/wgwidget/wgaxismonitor.h \
    ../src/wgwidget/inputmanage.h \
    ../src/wgwidget/impactdefend.h \
    ../src/wgwidget/dlgimpact.h \
    ../src/wgwidget/deftag.h \
    ../src/wgwidget/macstate.h \
    ../src/wgwidget/wgstatrecord.h \
    ../src/wgwidget/oscservo/oscloopconf.h \
    ../src/wgwidget/oscservo/oscservoloop.h \
    ../src/nccontrols/nctable.h \
    ../src/wgwidget/toolgroup.h \
    ../src/wgwidget/wghisfileexport.h \
    ../src/wgwidget/wgloadlogo.h \
    ../src/wgwidget/wgturningtool.h \
    ../src/wgwidget/toolcommon.h \
    ../src/wgwidget/turningtoolrecord.h \
    ../src/wgwidget/wgturningtoolcrdshow.h \
    ../src/wgwidget/wgmacstateexport.h \
    ../src/wgwidget/wgrunproginfo.h \
    ../src/wgwidget/printscreen.h \
    ../src/wgwidget/wgcraftfile.h \
    ../src/wgwidget/alarmwindow.h \
    ../src/wgwidget/oscservo/oscreportconf.h \
    ../src/wgwidget/hmircsclear.h \
    ../src/wgwidget/systime.h \
    ../src/wgwidget/diagnaoserecord.h \
    ../src/wgwidget/wgmcode.h \
    ../src/wgwidget/wgtooloffsetnormal.h \
    ../src/wgwidget/wgworkmeastcs.h \
    ../src/wgwidget/editpara.h \
    ../src/wgwidget/wgturntoolmeasparm.h \
    ../src/wgwidget/wgturntoolmeascali.h \
    ../src/wgwidget/wgturntoolmeasoffset.h \
    ../src/wgwidget/wgturntoolmeas.h \
    ../src/wgwidget/turntoolmeas.h \
    ../src/wgwidget/hmimacstr.h \
    ../src/wgwidget/wgmacstr.h \
    ../src/wgwidget/wgmacstrrtcp.h \
    ../src/wgwidget/dlgturntoolmeas.h \
    ../src/wgwidget/oscservo/oscdynamicalconf.h \
    ../src/wgwidget/oscservo/oscservodynamical.h \
    ../src/wgwidget/wgcloseloopswitch.h \
    ../src/wgwidget/wgcloseloopparm.h \
    ../src/wgwidget/closeloop.h \
    ../src/wgwidget/wgfilemansel.h \
    ../src/wgwidget/wgtoolmagcfg.h \
    ../src/wgwidget/hfwl.h \
    ../src/wgwidget/wgcordposition.h \
    ../src/wgwidget/wgcordvalue.h \
    ../src/wgwidget/wgcordsys.h \
    ../src/wgwidget/acmpman.h \
    ../src/wgwidget/oscservo/wgqrencoderepot.h \
    ../src/wgwidget/qrencodeshow.h \
    ../src/wgwidget/hmiwginfotrans.h \
    ../src/wgwidget/hmiloadxml.h \
    ../src/wgwidget/wgselftest.h \
    ../src/wgwidget/wgselftestconf.h \
    ../src/wgwidget/oscservo/oscservozheatcomp.h \
    ../src/wgwidget/oscservo/osczcompconf.h \
    ../src/wgwidget/oscservo/oscservosheatcomp.h \
    ../src/wgwidget/oscservo/oscscompconf.h \
    ../src/wgwidget/dlgtoollifeunit.h \
    ../src/wgwidget/fileprocessthread.h \
    ../src/wgwidget/hmitwincode.h \
    ../src/wgwidget/hmiloadparaexp.h \
    ../src/wgwidget/msgprompt.h \
    ../src/nccontrols/nceditor.h \
    ../src/wgwidget/wgprogedit.h \
    ../src/wgwidget/wgtoollife.h \
    ../src/wgwidget/dlgtoollifeset.h \
    ../src/wgwidget/wgtoolbreak.h \
    ../src/wgwidget/wgmaindisp.h \
    ../src/wgwidget/wggmodecutinfo.h \
    ../src/nccontrols/nctableonepage.h \
    ../src/wgwidget/hotkeycfg.h \
    ../src/wgwidget/oscservo/wgoscapp.h \
    ../src/wgwidget/oscservo/hmioscapp.h \
    ../src/wgwidget/oscservo/oscwavedraw.h \
    ../src/wgwidget/oscservo/oscapptime.h \
    ../src/wgwidget/oscservo/wgoscappconf.h \
    ../src/wgwidget/hmisweeper.h \
    ../src/wgwidget/wgsweeper.h \
    ../src/wgwidget/oscservo/hmiscrewwear.h \
    ../src/wgwidget/oscservo/wgscrewwear.h \
    ../src/wgwidget/hmiestimate.h \
    ../src/wgwidget/wgestimate.h \
    ../src/wgwidget/hmimcode.h \
    ../src/wgwidget/hmitoolbreak.h \
    ../src/wgwidget/oscservo/hmireplayrecorder.h \
    ../src/wgwidget/dlgosappselect.h \
    ../src/wgwidget/wgoscappprog.h \
    ../src/wgwidget/oscservo/wgoscappopt.h \
    ../src/wgwidget/wgalarmqr.h \
    ../src/wgwidget/dlgoscservoselect.h \
    ../src/wgwidget/wgtrscompen.h \
    ../src/wgwidget/wgtrscompenload.h \
    ../src/wgwidget/hmitrscompen.h \
    ../src/wgwidget/wgtrscompengraph.h \
    ../src/wgwidget/autotesthmi.h \
    ../src/wgwidget/wgscrewthread.h \
    ../src/wgwidget/hmivarman.h \
    ../src/nccontrols/nctableex.h \
    ../src/wgwidget/dlgfstset.h \
    ../src/wgwidget/hmipaintercolor.h \
    ../src/wgwidget/wgqr.h \
    ../src/wgwidget/oscservo/dlgoscappconf.h \
    ../src/wgwidget/wgladcellhelp.h \
    ../src/wgwidget/wgwebview.h \
    ../src/wgwidget/wgladeditview.h \
    ../src/wgwidget/wgnetping.h \
    ../src/nccontrols/dirmovelayout.h \
    ../src/wgwidget/hmidatarestore.h \
    ../src/wgwidget/wgqrparmbackup.h \
    ../src/wgwidget/consistencycheck.h \
    ../src/wgwidget/simumacposdata.h \
    ../src/wgwidget/simumillcal.h \
    ../src/wgwidget/simupara.h \
    ../src/wgwidget/simumacposdatarecord.h \
    ../src/wgwidget/simuturn.h \
    ../src/wgwidget/simuviewmill.h \
    ../src/wgwidget/simuviewdef.h \
    ../src/wgwidget/colorcfg.h \
    ../src/wgwidget/dlgsimucolorcfg.h \
    ../src/wgwidget/simuviewturn.h \
    ../src/wgwidget/hmimain/hmimain.h \
    ../src/wgwidget/wgphoneappqr.h \
    ../src/wgwidget/wgmultichdisp.h \
    ../src/wgwidget/wgsigchdisp.h \
    ../src/wgwidget/wgfstmultich.h \
    ../src/wgwidget/oscservo/wgselfadjusting.h \
    ../src/wgwidget/oscservo/hmiselfadjusting.h \
    ../src/wgwidget/oscservo/oscbeartapconf.h \
    ../src/wgwidget/oscservo/oscservobeartap.h \
    ../src/wgwidget/wgtoolshape.h \
    ../src/wgwidget/wgturnmilloff.h \
    ../src/wgwidget/hmivardef.h \
    ../src/wgwidget/regbinary.h \
    ../src/wgwidget/wgregxyr.h \
    ../src/wgwidget/wgturnmillwear.h \
    ../src/wgwidget/wgturnmillmag.h \
    ../src/wgwidget/wgmilltoollentest.h \
    ../src/wgwidget/dlgmilltoollentestseltool.h \
    ../src/wgwidget/wgregiq.h \
    ../src/wgwidget/oscservo/selfadjuststep1.h \
    ../src/wgwidget/oscservo/selfadjuststep2.h \
    ../src/wgwidget/oscservo/selfadjuststep3.h \
    ../src/wgwidget/oscservo/selfadjuststep4.h \
    ../src/wgwidget/extdevmanqthread.h \
    ../src/wgwidget/macstatesnapshot.h \
    ../src/wgwidget/wgmacstatesnapshotqr.h \
    ../src/wgwidget/dlgtoollifetactics.h \
    ../src/wgwidget/wgcraftbag.h \
    ../src/wgwidget/wgparamcom.h \
    ../src/wgwidget/paramsubinfolist.h \
    ../src/wgwidget/wguserdefparamcom.h \
    ../src/wgwidget/dlgalarmhelp.h \
    ../src/wgwidget/hmialarmhelp.h \
    ../src/wgwidget/workmeas/hmiworkmeas.h \
    ../src/wgwidget/workmeas/workmeaspic.h \
    ../src/wgwidget/workmeas/workmeasbase.h \
    ../src/wgwidget/workmeas/wgworkmeas.h \
    ../src/wgwidget/workmeas/wgworkmeastool.h \
    ../src/wgwidget/wgprogtech.h \
    ../src/wgwidget/teachprogram.h \
    ../src/wgwidget/turningtooloffset.h \
    ../src/wgwidget/hmiparmcheck.h \
    ../src/wgwidget/wgparmcheck.h \
    ../src/wgwidget/wgnoticehis.h \
    ../src/wgwidget/twinkle.h \
    ../src/nccontrols/nccheckbox.h \
    ../src/nccontrols/nccombobox.h \
    ../src/wgwidget/wgvirtkey.h \
    ../src/wgwidget/oscservo/dlgscrewwearconf.h \
    ../src/wgwidget/dlgreplace.h \
    ../src/wgwidget/dlglogin.h \
    ../src/wgwidget/gdblk/hmiTrajList.h \
    ../src/wgwidget/gdblk/hmigdblkdef.h \
    ../src/wgwidget/gdblk/hmimeastestmacro.h \
    ../src/wgwidget/gdblk/stepselbase.h \
    ../src/wgwidget/gdblk/steplistselfile.h \
    ../src/wgwidget/gdblk/wgmeasconfig.h \
    ../src/wgwidget/gdblk/wgmeastestmacro.h \
    ../src/wgwidget/gdblk/gdblkxml.h \
    ../src/wgwidget/gdblk/gdblkitem.h \
    ../src/wgwidget/gdblk/gdblkitemman.h \
    ../src/wgwidget/gdblk/gdblkcoder.h \
    ../src/wgwidget/gdblk/hmigdblk.h \
    ../src/wgwidget/gdblk/wggdblkoutline.h \
    ../src/wgwidget/gdblk/hmigdblkcurtrajstep.h \
    ../src/wgwidget/gdblk/wgcyc.h \
    ../src/wgwidget/gdblk/wgoutline.h \
    ../src/wgwidget/gdblk/wggdblkoutlinesel.h \
    ../src/wgwidget/gdblk/wggdblksel.h \
    ../src/wgwidget/gdblk/wggdblkstepsel.h \
    ../src/wgwidget/gdblk/gdblkstepselitem.h \
    ../src/wgwidget/gdblk/dlggdblksel.h \
    ../src/wgwidget/gdblk/dlgbaseoutlinechoose.h \
    ../src/wgwidget/gdblk/dlggdblkselbase.h \
    ../src/wgwidget/gdblk/dlgoutlinesel.h \
    ../src/wgwidget/gdblk/analysisfunc.h \
    ../src/wgwidget/gdblk/outlinecal.h \
    ../src/wgwidget/dlgtoolpropset.h \
    ../src/nccontrols/ncpushbutton.h \
    ../src/hmidata/hmitooldata.h \
    ../src/wgwidget/rsfile.h \
    ../src/wgwidget/wgdncrxtx.h \
    ../src/wgwidget/dlgnuminput.h \
    ../src/wgwidget/cnclogapi.h \
    ../src/wgwidget/sysversion.h \
    ../src/wgwidget/wgtoolbasepos.h \
    ../src/wgwidget/wgfiletree.h \
    ../src/wgwidget/wgprogeditmulch.h \
    ../src/wgwidget/wgdevwidget.h \
    ../src/wgwidget/selftestlog.h \
    ../src/wgwidget/dlgselftestfeattype.h \
    ../src/wgwidget/dlgselftestfeature.h \
    ../src/wgwidget/wgselftestqrencode.h \
    ../src/wgwidget/wgselftestshowfea.h \
    ../src/wgwidget/wgselftestextension.h \
    ../src/wgwidget/selftestcore.h \
    ../src/wgwidget/selftestcalc.h \
    ../src/wgwidget/dlgselftestgratingruler.h \
    ../src/wgwidget/wgdevcompare.h \
    ../src/wgwidget/devparam.h \
    ../src/wgwidget/basesigchdisp.h \
    ../src/wgwidget/basemultichdisp.h\
    ../src/wgwidget/ncmdieditor.h \
    ../src/wgwidget/wgfiletypeall.h \
    ../src/wgwidget/wgtoolautomeas.h \
    ../src/wgwidget/dlgtoolsel.h \
    ../src/wgwidget/wgcordsysstavertical.h \
    ../src/wgwidget/msgpromptwith3exit.h \
    ../src/nccontrols/ncdatalimit.h \
    ../src/wgwidget/wgmcp.h \
    ../src/wgwidget/wgprogeditall.h \
    ../src/wgwidget/apposdepend.h \
    ../src/wgwidget/dlgtoolbreakcfg.h \
    ../src/wgwidget/serportthread.h \
    ../src/wgwidget/rfidballuff.h \
    ../src/wgwidget/serportcmd.h \
    ../src/wgwidget/rfidballuffcmd.h \
    ../src/wgwidget/hfwlcmd.h \
    ../src/wgwidget/rfidsygole.h \
    ../src/wgwidget/rfidsygolecmd.h \
    ../src/wgwidget/rfidtoolbuffer.h \
    ../src/wgwidget/wgcordsysunion.h \
    ../src/wgwidget/wgcordsystable.h \
    ../src/wgwidget/dlg5axismeas.h \
    ../src/hmidata/hmiratiodata.h\
    ../src/wgwidget/wgrationset.h \
    ../src/hmidata/hmiparset.h \
    ../src/wgwidget/interfaceframe.h \
    ../src/hmidata/ncassistantconf.h \
    ../src/wgwidget/hidcomm.h \
    ../src/wgwidget/usbmcp.h \
    ../src/wgwidget/oscservo/oscrasterconf.h \
    ../src/wgwidget/oscservo/oscservoraster.h \
    ../src/wgwidget/wgsysinfoxml.h \
    ../src/wgwidget/dlgmsgsavesv.h \
    ../src/wgwidget/oscservo/hmispindlewear.h \
    ../src/wgwidget/oscservo/wgspindlewear.h \
    ../src/wgwidget/oscservo/wgspindlewearoverlog.h \
    ../src/wgwidget/oscservo/wgspindlewearconf.h \
    ../src/wgwidget/oscservo/wgoscelewaveview.h \
    ../src/wgwidget/oscservo/oscelewave.h \
    ../src/wgwidget/dlgcrdrcsclear.h \
    ../src/wgwidget/wgdispparmcfg.h \
    ../src/wgwidget/oscservo/wgelespectral.h \
    ../src/wgwidget/wgfeedselfadapt.h \
    ../src/wgwidget/thermal/thermalinit.h \
    ../src/wgwidget/thermal/wgsampaxisconf.h \
    ../src/wgwidget/thermal/wgspindlethermaldataview.h \
    ../src/wgwidget/thermal/wgthermalchooseprogram.h \
    ../src/wgwidget/thermal/wgthermaldataacquisition.h \
    ../src/wgwidget/thermal/wgthermaldatacommon.h \
    ../src/wgwidget/thermal/wgthermaldatasamplesetting.h \
    ../src/wgwidget/thermal/wgthermaldatmanage.h \
    ../src/wgwidget/thermal/wgthermaldefinedata.h \
    ../src/wgwidget/thermal/wgthermalgcodeconf.h \
    ../src/wgwidget/thermal/wgthermalprobeinf.h \
    ../src/wgwidget/thermal/wgthermalsampconf.h \
    ../src/wgwidget/thermal/wgthermalshareddata.h \
    ../src/wgwidget/thermal/wgxyzthermaldataview.h \
    ../src/wgwidget/thermal/wgyaxisthermaldataview.h \
    ../src/wgwidget/thermal/wgzaxisthermaldataview.h \
    ../src/wgwidget/thermalset.h \
    ../src/wgwidget/thermal/wgdiallog.h \
    ../src/wgwidget/thermal/wggraingrulerdata.h \
    ../src/wgwidget/TemperConnectRefreshThread.h \
    ../src/wgwidget/hmitemperature.h \
    ../src/wgwidget/thermal/wgsensorloaddat.h \
    ../src/wgwidget/thermal/wgthermalweightbias.h \
    ../src/wgwidget/thermal/wgthermallstm.h \
    ../src/wgwidget/thermal/wgthermallstmpredict.h \
    ../src/wgwidget/postfixexpression.h \
    ../src/wgwidget/oscservo/wgelepolyfit.h \
    ../src/wgwidget/dataverify.h \
    ../src/wgwidget/oscservo/wgoscrastersignalanaly.h \
    ../src/wgwidget/oscservo/overlimitloglist.h \
    ../src/wgwidget/dlg5axistooloffset.h \
    ../src/hmidata/randomrecorddata.h \
    ../src/wgwidget/thermal/physicalModelThermal.h \
    ../src/wgwidget/thermal/wgphysithermalcompen.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsparamset.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllssel.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsmeassel.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsview.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeaslls.h \
    ../src/wgwidget/wgpressuresensor.h \
    ../src/nccontrols/ncscrollarea.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsmacro.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsmacset.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsinfoset.h \
    ../src/wgwidget/wgloadusericon.h \
    ../src/wgwidget/lnsworkmeas/wgllstestmacro.h \
    ../src/wgwidget/dlginfo.h \
    ../src/wgwidget/hmibreakpointdata.h \
    ../src/wgwidget/wgbreakpoint.h \
    ../src/wgwidget/dlgbreakpoint.h \
    ../src/wgwidget/dlgpoweroffbreakpoint.h \
    ../src/wgwidget/ncbreakpoint.h \
    ../src/wgwidget/wgnetcommnclink.h \
    ../src/wgwidget/hmitoolmeasureitem.h \
    ../src/wgwidget/hmitoolmeasurexmlcfg.h \
    ../src/wgwidget/wgtoolmeasure.h \
    ../src/wgwidget/dlgtoolmeascomparmset.h \
    ../src/wgwidget/simumill.h \
    ../src/wgwidget/simuviewbase.h \
    ../src/wgwidget/simumacposdatadcdblkman.h \
    ../src/wgwidget/progview.h \
    ../src/wgwidget/wgsimuprogview.h \
    ../src/wgwidget/wggraphsimuview.h \
    ../src/wgwidget/wgncucparmconfig.h \
    ../src/wgwidget/hmincucparmconfig.h \
    ../src/wgwidget/wghdhworkmeastool.h \
    ../src/wgwidget/hdhworkmeastooldata.h \
    ../src/wgwidget/wghdhworkmeastoolshow.h \
    ../src/wgwidget/wghdhworkmeastoolall.h \
    ../src/wgwidget/wghdhworkmeastoolparam.h \
    ../src/wgwidget/workmeas/wgworkmeastoolwithball.h \
    ../src/wgwidget/wgmacstrinit.h \
    ../src/wgwidget/wgncmacrodirbase.h \
    ../src/wgwidget/wgultrasound.h \
    ../src/wgwidget/wgultrasoundwave.h \
    ../src/wgwidget/wgultrasoundrifa.h \
    ../src/wgwidget/ncultrasoundthread.h \
    ../src/wgwidget/hmiultrasoundrifa.h \
    ../src/wgwidget/wgultrasoundfilerifa.h \
    ../src/wgwidget/workmeas/dlg3drotate.h \
    ../src/wgwidget/workmeas/dlgworkmeascircle.h \
    ../src/wgwidget/workmeas/workmeascircle.h \
    ../src/wgwidget/workmeas/wg3drot.h \
    ../src/wgwidget/oscservo/oscacceconf.h \
    ../src/wgwidget/workmeas/wgworkmeastoollen.h \
    ../src/wgwidget/oscservo/oscservoacce.h \
    ../src/wgwidget/oscservo/dlgosccurcompare.h \
    ../src/wgwidget/oscservo/osccurrentconf.h \
    ../src/wgwidget/oscservo/oscservocurrent.h \
    ../src/wgwidget/oscservo/hmijourneycurrent.h \
    ../src/wgwidget/wgparamgrade.h \
    ../src/wgwidget/hmiparamgrade.h \
    ../src/nccontrols/ncbusytask.h \
    ../src/nccontrols/ncprogress.h \
    ../src/wgwidget/ncwidgetload.h \
    ../src/wgwidget/hmifiletextcodec.h \
    ../src/wgwidget/mdiprogtext.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsparamsetlist.h \
    ../src/wgwidget/gdblk/wggdblksigstep.h \
    ../src/wgwidget/gdblk/sigstepbase.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllseditfileparam.h \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllseditbase.h \
    ../src/wgwidget/lnsworkmeas/wgllstitle.h \
    ../src/wgwidget/wgparamgradeedit.h \
    ../src/wgwidget/emmchealth.h \
    ../src/wgwidget/wgsesrvoparamlist.h \
    ../src/wgwidget/sesrvoparambackdata.h \
    ../src/wgwidget/wgloadadptrecommended.h \
    ../src/wgwidget/wgloadadptlinear.h \
    ../src/wgwidget/loadadptbackdata.h \
    ../src/wgwidget/dlgproggprint.h \
    ../src/wgwidget/dlghdhwormeas.h \
    ../src/wgwidget/hmicompfile.h \
    ../src/wgwidget/wgcputime.h \
    ../src/wgwidget/wgcpumonitor.h \
    ../src/tools/user.h \
    ../src/include/servicedata.h \
    ../src/hncservice/sysreset.h \
    ../src/wgwidget/hmisysreset.h \
    ../src/wgwidget/checkkernelalive.h \
    ../src/wgwidget/oscservo/oscinertiaconf.h \
    ../src/wgwidget/oscservo/osckcompenconf.h \
    ../src/wgwidget/oscservo/oscservokcompen.h \
    ../src/wgwidget/oscservo/oscservoinertia.h \
    ../src/wgwidget/hncworkmeas/wghncwmsteplist.h \
    ../src/wgwidget/hncworkmeas/wghncwmstepedit.h \
    ../src/wgwidget/hncworkmeas/wghncwmsteplistsel.h \
    ../src/wgwidget/hncworkmeas/hmihncmeas.h \
    ../src/wgwidget/hncworkmeas/wghncmeasconfig.h \
    ../src/wgwidget/hncworkmeas/wghncwmstepeditbase.h \
    ../src/wgwidget/hncworkmeas/wghncconfigsig.h \
    ../src/wgwidget/hncworkmeas/dlghncsteplistsel.h \
    ../src/wgwidget/hncworkmeas/wghncwmjogstepedit.h \
    ../src/wgwidget/hncworkmeas/wghncwmjogfileview.h \
    ../src/wgwidget/hncworkmeas/dlghncstepsel.h \
    ../src/wgwidget/hncworkmeas/dlghncmeassel.h \
    ../src/wgwidget/hncworkmeas/hmihncmeasres.h \
    ../src/wgwidget/hncworkmeas/wghncmeasres.h \
    ../src/wgwidget/hncworkmeas/wghnctestmacro.h \
    ../src/wgwidget/hncworkmeas/wghncmeassel.h \
    ../src/hmidata/hmimacro.h \
    ../src/wgwidget/wgposwithfeed.h \
    ../src/wgwidget/wgfeed.h \
    ../src/wgwidget/wgcrdg5x.h \
    ../src/wgwidget/wgfiletab.h \
    ../src/wgwidget/wgwidgetlistbase.h \
    ../src/wgwidget/cpulog.h \
    ../src/hmidata/nccputime.h \
    ../src/wgwidget/wgnetnclink.h \
    ../src/wgwidget/wghomeparam.h \
    ../src/wgwidget/wghomeset.h \
    ../src/nccontrols/nclightwidget.h \
    ../src/wgwidget/hmihomeparam.h \
    ../src/wgwidget/hmihomeconf.h \
    ../src/wgwidget/hmiladiq.h \
    ../src/wgwidget/hmiredecodedata.h \
    ../src/nccontrols/tabconstantstcol.h \
    ../src/plugins/usrdef/usrdefdata.h \
    ../src/plugins/usrdef/usrdeftablewidgettab.h \
    ../src/plugins/usrdef/hmiusrcallback.h \
    ../src/wgwidget/oscservo/wgoscservoraster.h \
    ../src/gwg_widget/gwg_pos_main.h \
    ../src/gwg_widget/common_widget/card.h

FORMS += \
    ../src/wgwidget/containerwidget.ui \
    ../src/wgwidget/dlgdescription.ui \
    ../src/wgwidget/dlgloading.ui \
    ../src/wgwidget/dlgselfmaintaincofg.ui \
    ../src/wgwidget/dlgupdate.ui \
    ../src/wgwidget/dlgwebdownload.ui \
    ../src/wgwidget/hmimain/hnchmi.ui \
    ../src/wgwidget/infobar.ui \
    ../src/wgwidget/oscservo/osccompsensorconf.ui \
    ../src/wgwidget/oscservo/oscservocompsensor.ui \
    ../src/wgwidget/oscservo/wgoscsensordatacheck.ui \
    ../src/wgwidget/oscservo/roundscale.ui \
    ../src/wgwidget/oscservo/wgoscsensorloaddata.ui \
    ../src/wgwidget/oscservo/wgoscsensorloadrtl.ui \
    ../src/wgwidget/thermal/wgthermalevaluatecompensability.ui \
    ../src/wgwidget/thermal/wgthermalevaluateconsistency.ui \
    ../src/wgwidget/thermal/wgthermalevaluateselect.ui \
    ../src/wgwidget/thermal/wgthermalevaluatesensorcheck.ui \
    ../src/wgwidget/showinit.ui \
    ../src/wgwidget/messagebar.ui \
    ../src/wgwidget/mainwidget.ui \
    ../src/wgwidget/thermal/wgheatcompsensor.ui \
    ../src/wgwidget/thermal/wgsensorloadrtl.ui \
    ../src/wgwidget/thermal/wgthermalcompen.ui \
    ../src/wgwidget/thermal/wgthermalconfig.ui \
    ../src/wgwidget/thermal/wgthermaldatainput.ui \
    ../src/wgwidget/thermal/wgthermalevaluate.ui \
    ../src/wgwidget/thermal/wgthermalsampprogview.ui \
    ../src/wgwidget/thermal/wgthermalsampset.ui \
    ../src/wgwidget/wgadjuststep1.ui \
    ../src/wgwidget/wgadjuststep2.ui \
    ../src/wgwidget/wgadjuststep3.ui \
    ../src/wgwidget/wgadjuststep4.ui \
    ../src/wgwidget/wgcalibration.ui \
    ../src/wgwidget/wgcollisioncheck.ui \
    ../src/wgwidget/wgcollisioncheckconf.ui \
    ../src/wgwidget/wgdcmcrdshow.ui \
    ../src/wgwidget/wgmenubar.ui \
    ../src/wgwidget/wgnetcommweb.ui \
    ../src/wgwidget/oscservo/wgoscsensordatacomp.ui \
    ../src/wgwidget/oscservo/wgoscsensordatasel.ui \
    ../src/wgwidget/wgprogview.ui \
    ../src/wgwidget/wgselftestmaintain.ui \
    ../src/wgwidget/wgselftestmaintainhistory.ui \
    ../src/wgwidget/wggmode.ui \
    ../src/wgwidget/wgcutinfo.ui \
    ../src/wgwidget/wgupdatenet.ui \
    ../src/userwidget/wguser.ui \
    ../src/wgwidget/wgfileview.ui \
    ../src/wgwidget/wgprogsel.ui \
    ../src/wgwidget/wgcordsyssg.ui \
    ../src/wgwidget/wgalarminfo.ui \
    ../src/wgwidget/wgparaman.ui \
    ../src/wgwidget/wgalarmhis.ui \
    ../src/wgwidget/wgusermacro.ui \
    ../src/wgwidget/wguserpara.ui \
    ../src/wgwidget/wgtoolcrdshow.ui \
    ../src/wgwidget/dlggetfile.ui \
    ../src/wgwidget/statexyr.ui \
    ../src/wgwidget/statefg.ui \
    ../src/wgwidget/statebw.ui \
    ../src/wgwidget/wgtimeset.ui \
    ../src/wgwidget/wgmacro.ui \
    ../src/wgwidget/wgregister.ui \
    ../src/wgwidget/wgupdate.ui \
    ../src/wgwidget/progview.ui \
    ../src/wgwidget/wgturntabs.ui  \
    ../src/wgwidget/wgfst.ui \
    ../src/wgwidget/wgprogman.ui \
    ../src/wgwidget/wgaccess.ui \
    ../src/wgwidget/pwddlg.ui \
    ../src/wgwidget/wgeditor.ui \
    ../src/wgwidget/wgsysinfo.ui \
    ../src/wgwidget/wggmodemilling.ui \
    ../src/wgwidget/wgdispparm.ui \
    ../src/wgwidget/wgpos.ui \
    ../src/wgwidget/wgunion.ui \
    ../src/wgwidget/wgladpara.ui \
    ../src/wgwidget/wgtheadcompen.ui \
    ../src/wgwidget/wgnetcommip.ui \
    ../src/wgwidget/wgnetcommshare.ui \
    ../src/wgwidget/wgnetcommftp.ui \
    ../src/wgwidget/wgcordsysstahorizon.ui \
    ../src/wgwidget/wgdevconf.ui \
    ../src/wgwidget/wgloadcompfile.ui \
    ../src/wgwidget/devshow.ui \
    ../src/wgwidget/wgstatexyr.ui \
    ../src/wgwidget/wgrandom.ui \
    ../src/wgwidget/wgsimuviewmill.ui \
    ../src/wgwidget/wgplcsw.ui \
    ../src/wgwidget/dlgsimuviewmillsetparm.ui \
    ../src/wgwidget/ladview.ui \
    ../src/wgwidget/wgladstl.ui \
    ../src/wgwidget/wgladreglock.ui \
    ../src/wgwidget/wgladcross.ui \
    ../src/wgwidget/wgladregk.ui \
    ../src/wgwidget/wgladtrace.ui \
    ../src/wgwidget/wgladtraceparam.ui \
    ../src/wgwidget/dlglad.ui \
    ../src/wgwidget/dlgtoollifeman.ui \
    ../src/wgwidget/wgncmdi.ui \
    ../src/wgwidget/wgtoolmag.ui \
    ../src/wgwidget/dlgdroplist.ui \
    ../src/wgwidget/wgrunstats.ui \
    ../src/wgwidget/wgladproglistedit.ui \
    ../src/wgwidget/wgladdgn.ui \
    ../src/wgwidget/wgladedit.ui \
    ../src/wgwidget/wgsimuviewturn.ui \
    ../src/wgwidget/dlgsimuviewturnsetparm.ui \
    ../src/wgwidget/wgladcoil.ui \
    ../src/wgwidget/wgladcellsel.ui \
    ../src/wgwidget/wgladtimer.ui \
    ../src/wgwidget/wgladcounter.ui  \
    ../src/wgwidget/wgladalarm.ui \
    ../src/wgwidget/wgstated.ui \
    ../src/wgwidget/wgladinfo.ui \
    ../src/wgwidget/wgcnclog.ui \
    ../src/wgwidget/loglist.ui \
    ../src/wgwidget/wgfiletype.ui \
    ../src/wgwidget/wgladsymbol.ui \
    ../src/wgwidget/wgladio.ui \
    ../src/wgwidget/wgrightcfg.ui \
    ../src/wgwidget/wgfileman.ui \
    ../src/wgwidget/wgstateiq.ui \
    ../src/wgwidget/wggeneralfunc.ui \
    ../src/wgwidget/oscservo/wgoscservo.ui \
    ../src/wgwidget/oscservo/wgoscconf.ui \
    ../src/wgwidget/oscservo/oscservospe.ui \
    ../src/wgwidget/oscservo/oscservopos.ui \
    ../src/wgwidget/oscservo/oscservocir.ui \
    ../src/wgwidget/oscservo/oscservotap.ui \
    ../src/wgwidget/oscservo/oscservofreq.ui \
    ../src/wgwidget/oscservo/oscservosync.ui \
    ../src/wgwidget/oscservo/oscservospindle.ui \
    ../src/wgwidget/oscservo/oscservofreqtap.ui \
    ../src/wgwidget/oscservo/oscservotool.ui \
    ../src/wgwidget/oscservo/oscservocustom.ui \
    ../src/wgwidget/oscservo/oscservodiagnose.ui \
    ../src/wgwidget/oscservo/oscservoreport.ui \
    ../src/wgwidget/oscservo/wgoscservoopt.ui \
    ../src/wgwidget/oscservo/oscwave.ui \
    ../src/wgwidget/oscservo/wgoscservocolorset.ui \
    ../src/wgwidget/oscservo/oscspeposconf.ui \
    ../src/wgwidget/oscservo/wgoscgcodeview.ui \
    ../src/wgwidget/dlgscreensaver.ui \
    ../src/wgwidget/oscservo/osctoolconf.ui \
    ../src/wgwidget/oscservo/osclist.ui \
    ../src/wgwidget/oscservo/oscservospdlconf.ui \
    ../src/wgwidget/oscservo/oscfreqtapconf.ui \
    ../src/wgwidget/oscservo/osccirconf.ui \
    ../src/wgwidget/oscservo/roundwave.ui \
    ../src/wgwidget/oscservo/osccustomconf.ui \
    ../src/wgwidget/wgpreferpara.ui \
    ../src/wgwidget/oscservo/oscsyncconf.ui \
    ../src/wgwidget/wgmacinfo.ui \
    ../src/wgwidget/oscservo/oscfreqconf.ui \
    ../src/wgwidget/wgfaultguide.ui \
    ../src/wgwidget/wgaxismonitor.ui \
    ../src/wgwidget/inputmanage.ui \
    ../src/wgwidget/dlgimpact.ui \
    ../src/wgwidget/wgstatrecord.ui \
    ../src/wgwidget/oscservo/oscloopconf.ui \
    ../src/wgwidget/oscservo/oscservoloop.ui \
    ../src/wgwidget/wghisfileexport.ui \
    ../src/wgwidget/wgloadlogo.ui \
    ../src/wgwidget/wgmacstateexport.ui \
    ../src/wgwidget/wgturningtool.ui \
    ../src/wgwidget/wgturningtoolcrdshow.ui \
    ../src/wgwidget/wgrunproginfo.ui \
    ../src/wgwidget/wgcraftfile.ui \
    ../src/wgwidget/alarmwindow.ui \
    ../src/wgwidget/oscservo/oscreportconf.ui \
    ../src/wgwidget/wgmcode.ui \
    ../src/wgwidget/wgtooloffsetnormal.ui \
    ../src/wgwidget/wgturntoolmeasparm.ui \
    ../src/wgwidget/wgturntoolmeascali.ui \
    ../src/wgwidget/wgturntoolmeasoffset.ui \
    ../src/wgwidget/wgturntoolmeas.ui \
    ../src/wgwidget/wgworkmeastcs.ui \
    ../src/wgwidget/wgmacstr.ui \
    ../src/wgwidget/wgmacstrrtcp.ui \
    ../src/wgwidget/dlgturntoolmeas.ui \
    ../src/wgwidget/oscservo/oscdynamicalconf.ui \
    ../src/wgwidget/oscservo/oscservodynamical.ui \
    ../src/wgwidget/wgcloseloopswitch.ui \
    ../src/wgwidget/wgcloseloopparm.ui \
    ../src/wgwidget/wgfilemansel.ui \
    ../src/wgwidget/wgtoolmagcfg.ui \
    ../src/wgwidget/wgcordposition.ui \
    ../src/wgwidget/wgcordvalue.ui \
    ../src/wgwidget/wgcordsys.ui \
    ../src/wgwidget/oscservo/wgqrencoderepot.ui \
    ../src/wgwidget/wgselftest.ui \
    ../src/wgwidget/wgselftestconf.ui \
    ../src/wgwidget/oscservo/oscservozheatcomp.ui \
    ../src/wgwidget/oscservo/osczcompconf.ui \
    ../src/wgwidget/oscservo/oscservosheatcomp.ui \
    ../src/wgwidget/oscservo/oscscompconf.ui \
    ../src/wgwidget/dlgtoollifeunit.ui \
    ../src/wgwidget/msgprompt.ui \
    ../src/wgwidget/wgprogedit.ui \
    ../src/wgwidget/wgtoollife.ui \
    ../src/wgwidget/dlgtoollifeset.ui \
    ../src/wgwidget/wgtoolbreak.ui \
    ../src/wgwidget/wgmaindisp.ui \
    ../src/wgwidget/wggmodecutinfo.ui \
    ../src/nccontrols/nctableonepage.ui \
    ../src/wgwidget/oscservo/wgoscapp.ui \
    ../src/wgwidget/oscservo/oscwavedraw.ui \
    ../src/wgwidget/oscservo/oscapptime.ui \
    ../src/wgwidget/oscservo/wgoscappconf.ui \
    ../src/wgwidget/wgsweeper.ui \
    ../src/wgwidget/oscservo/wgscrewwear.ui \
    ../src/wgwidget/wgestimate.ui \
    ../src/wgwidget/dlgosappselect.ui \
    ../src/wgwidget/wgoscappprog.ui \
    ../src/wgwidget/oscservo/wgoscappopt.ui \
    ../src/wgwidget/wgalarmqr.ui \
    ../src/wgwidget/dlgoscservoselect.ui \
    ../src/wgwidget/wgtrscompen.ui \
    ../src/wgwidget/wgtrscompenload.ui \
    ../src/wgwidget/wgtrscompengraph.ui \
    ../src/wgwidget/wgscrewthread.ui \
    ../src/wgwidget/dlgfstset.ui \
    ../src/wgwidget/hmipaintercolor.ui \
    ../src/wgwidget/wgqr.ui \
    ../src/wgwidget/oscservo/dlgoscappconf.ui \
    ../src/wgwidget/wgladcellhelp.ui \
    ../src/wgwidget/wgwebview.ui \
    ../src/wgwidget/wgladeditview.ui \
    ../src/wgwidget/wgnetping.ui \
    ../src/wgwidget/wgqrparmbackup.ui \
    ../src/wgwidget/dlgsimucolorcfg.ui \
    ../src/wgwidget/wgphoneappqr.ui \
    ../src/wgwidget/wgmultichdisp.ui \
    ../src/wgwidget/wgsigchdisp.ui \
    ../src/wgwidget/wgfstmultich.ui \
    ../src/wgwidget/oscservo/wgselfadjusting.ui \
    ../src/wgwidget/oscservo/oscbeartapconf.ui \
    ../src/wgwidget/oscservo/oscservobeartap.ui \
    ../src/wgwidget/wgtoolshape.ui \
    ../src/wgwidget/wgturnmilloff.ui \
    ../src/wgwidget/regbinary.ui \
    ../src/wgwidget/wgregxyr.ui \
    ../src/wgwidget/wgturnmillwear.ui \
    ../src/wgwidget/wgturnmillmag.ui \
    ../src/wgwidget/wgmilltoollentest.ui \
    ../src/wgwidget/dlgmilltoollentestseltool.ui \
    ../src/wgwidget/wgregiq.ui \
    ../src/wgwidget/oscservo/selfadjuststep1.ui \
    ../src/wgwidget/oscservo/selfadjuststep2.ui \
    ../src/wgwidget/oscservo/selfadjuststep3.ui \
    ../src/wgwidget/oscservo/selfadjuststep4.ui \
    ../src/wgwidget/wgmacstatesnapshotqr.ui \
    ../src/wgwidget/dlgtoollifetactics.ui \
    ../src/wgwidget/wgcraftbag.ui \
    ../src/wgwidget/wgparamcom.ui \
    ../src/wgwidget/dlgalarmhelp.ui \
    ../src/wgwidget/workmeas/workmeaspic.ui \
    ../src/wgwidget/workmeas/workmeasbase.ui \
    ../src/wgwidget/workmeas/wgworkmeastool.ui \
    ../src/wgwidget/wgprogtech.ui \
    ../src/wgwidget/wgparmcheck.ui \
    ../src/wgwidget/wgnoticehis.ui \
    ../src/wgwidget/wgvirtkey.ui \
    ../src/wgwidget/oscservo/dlgscrewwearconf.ui \
    ../src/wgwidget/dlgreplace.ui \
    ../src/wgwidget/dlglogin.ui \
    ../src/wgwidget/gdblk/stepselbase.ui \
    ../src/wgwidget/gdblk/steplistselfile.ui \
    ../src/wgwidget/gdblk/wgmeasconfig.ui \
    ../src/wgwidget/gdblk/wggdblksigstep.ui \
    ../src/wgwidget/gdblk/wggdblkoutline.ui \
    ../src/wgwidget/gdblk/wgoutline.ui \
    ../src/wgwidget/gdblk/wgcyc.ui \
    ../src/wgwidget/gdblk/wggdblkoutlinesel.ui \
    ../src/wgwidget/gdblk/wggdblksel.ui \
    ../src/wgwidget/gdblk/wggdblkstepsel.ui\
    ../src/wgwidget/gdblk/dlggdblksel.ui \
    ../src/wgwidget/gdblk/dlgbaseoutlinechoose.ui \
    ../src/wgwidget/gdblk/dlgoutlinesel.ui \
    ../src/wgwidget/dlgtoolpropset.ui \
    ../src/wgwidget/wgdncrxtx.ui\
    ../src/wgwidget/dlgnuminput.ui \
    ../src/wgwidget/wgtoolbasepos.ui \
    ../src/wgwidget/wgfiletree.ui \
    ../src/wgwidget/dlgselftestfeattype.ui \
    ../src/wgwidget/dlgselftestfeature.ui \
    ../src/wgwidget/wgselftestshowfea.ui \
    ../src/wgwidget/wgselftestextension.ui \
    ../src/wgwidget/dlgselftestgratingruler.ui \
    ../src/wgwidget/wgselftestqrencode.ui \
    ../src/wgwidget/wgprogeditmulch.ui \
    ../src/wgwidget/wgdevwidget.ui \
    ../src/wgwidget/wgdevcompare.ui \
    ../src/wgwidget/basesigchdisp.ui \
    ../src/wgwidget/basemultichdisp.ui\
    ../src/wgwidget/ncmdieditor.ui \
    ../src/wgwidget/wgfiletypeall.ui \
    ../src/wgwidget/wgtoolautomeas.ui \
    ../src/wgwidget/dlgtoolsel.ui \
    ../src/wgwidget/wgcordsysstavertical.ui \
    ../src/wgwidget/msgpromptwith3exit.ui\
    ../src/wgwidget/wgprogeditall.ui \
    ../src/wgwidget/wgmcp.ui \
    ../src/wgwidget/dlgtoolbreakcfg.ui \
    ../src/wgwidget/wgcordsysunion.ui \
    ../src/wgwidget/wgcordsystable.ui \
    ../src/wgwidget/dlg5axismeas.ui \
    ../src/wgwidget/wgrationset.ui \
    ../src/wgwidget/oscservo/oscrasterconf.ui \
    ../src/wgwidget/oscservo/oscservoraster.ui \
    ../src/wgwidget/wgsysinfoxml.ui \
    ../src/wgwidget/dlgmsgsavesv.ui \
    ../src/wgwidget/oscservo/wgspindlewear.ui \
    ../src/wgwidget/oscservo/wgspindlewearoverlog.ui \
    ../src/wgwidget/oscservo/wgspindlewearconf.ui \
    ../src/wgwidget/oscservo/wgoscelewaveview.ui \
    ../src/wgwidget/oscservo/oscelewave.ui \
    ../src/wgwidget/dlgcrdrcsclear.ui \
    ../src/wgwidget/wgdispparmcfg.ui \
    ../src/wgwidget/oscservo/wgelespectral.ui \
    ../src/wgwidget/wgfeedselfadapt.ui \
    ../src/wgwidget/thermal/wgsampaxisconf.ui \
    ../src/wgwidget/thermal/wgspindlethermaldataview.ui \
    ../src/wgwidget/thermal/wgthermalchooseprogram.ui \
    ../src/wgwidget/thermal/wgthermaldataacquisition.ui \
    ../src/wgwidget/thermal/wgthermaldatasamplesetting.ui \
    ../src/wgwidget/thermal/wgthermaldatmanage.ui \
    ../src/wgwidget/thermal/wgthermalgcodeconf.ui \
    ../src/wgwidget/thermal/wgthermalprobeinf.ui \
    ../src/wgwidget/thermal/wgthermalsampconf.ui \
    ../src/wgwidget/thermal/wgxyzthermaldataview.ui \
    ../src/wgwidget/thermal/wgyaxisthermaldataview.ui \
    ../src/wgwidget/thermal/wgzaxisthermaldataview.ui \
    ../src/wgwidget/thermal/wgdiallog.ui \
    ../src/wgwidget/thermal/wggraingrulerdata.ui \
    ../src/wgwidget/thermal/wgsensorloaddat.ui \
    ../src/wgwidget/oscservo/wgelepolyfit.ui \
    ../src/wgwidget/oscservo/wgoscrastersignalanaly.ui \
    ../src/wgwidget/oscservo/overlimitloglist.ui \
    ../src/wgwidget/thermal/wgphysithermalcompen.ui \
    ../src/wgwidget/dlg5axistooloffset.ui \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllssel.ui \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsmeassel.ui \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllsview.ui \
    ../src/wgwidget/lnsworkmeas/wgworkmeaslls.ui \
    ../src/wgwidget/wgpressuresensor.ui \
    ../src/nccontrols/ncscrollarea.ui \
    ../src/wgwidget/wgloadusericon.ui \
    ../src/wgwidget/lnsworkmeas/wgllstestmacro.ui \
    ../src/wgwidget/dlginfo.ui \
    ../src/wgwidget/wgbreakpoint.ui \
    ../src/wgwidget/dlgbreakpoint.ui \
    ../src/wgwidget/dlgpoweroffbreakpoint.ui \
    ../src/wgwidget/wgnetcommnclink.ui \
    ../src/wgwidget/wgtoolmeasure.ui \
    ../src/wgwidget/dlgtoolmeascomparmset.ui \
    ../src/wgwidget/wgsimuprogview.ui \
    ../src/wgwidget/wggraphsimuview.ui \
    ../src/wgwidget/wgncucparmconfig.ui \
    ../src/wgwidget/wghdhworkmeastool.ui \
    ../src/wgwidget/wghdhworkmeastoolshow.ui \
    ../src/wgwidget/wghdhworkmeastoolall.ui \
    ../src/wgwidget/wgncmacrodirbase.ui \
    ../src/wgwidget/wghdhworkmeastoolparam.ui \
    ../src/wgwidget/wgultrasound.ui \
    ../src/wgwidget/wgultrasoundwave.ui \
    ../src/wgwidget/wgultrasoundrifa.ui \
    ../src/wgwidget/wgultrasoundfilerifa.ui \
    ../src/wgwidget/workmeas/dlg3drotate.ui \
    ../src/wgwidget/workmeas/dlgworkmeascircle.ui \
    ../src/wgwidget/workmeas/workmeascircle.ui \
    ../src/wgwidget/workmeas/wg3drot.ui \
    ../src/wgwidget/oscservo/oscservoacce.ui \
    ../src/wgwidget/workmeas/wgworkmeastoollen.ui \
    ../src/wgwidget/oscservo/oscacceconf.ui \
    ../src/wgwidget/oscservo/dlgosccurcompare.ui \
    ../src/wgwidget/oscservo/osccurrentconf.ui \
    ../src/wgwidget/oscservo/oscservocurrent.ui \
    ../src/nccontrols/ncprogress.ui \
    ../src/wgwidget/wgparamgrade.ui \
    ../src/wgwidget/lnsworkmeas/wgworkmeasllseditbase.ui \
    ../src/wgwidget/lnsworkmeas/wgllstitle.ui \
    ../src/wgwidget/wgparamgradeedit.ui \
    ../src/wgwidget/wgsesrvoparamlist.ui \
    ../src/wgwidget/wgloadadptrecommended.ui \
    ../src/wgwidget/wgloadadptlinear.ui \
    ../src/wgwidget/dlgproggprint.ui \
    ../src/wgwidget/dlghdhwormeas.ui \
    ../src/wgwidget/wgcputime.ui \
    ../src/wgwidget/wgcpumonitor.ui \
    ../src/wgwidget/oscservo/osckcompenconf.ui \
    ../src/wgwidget/oscservo/oscinertiaconf.ui \
    ../src/wgwidget/oscservo/oscservoinertia.ui \
    ../src/wgwidget/oscservo/oscservokcompen.ui \
    ../src/wgwidget/hncworkmeas/wghncwmstepedit.ui \
    ../src/wgwidget/hncworkmeas/wghncconfigsig.ui \
    ../src/wgwidget/hncworkmeas/dlghncsteplistsel.ui \
    ../src/wgwidget/hncworkmeas/wghncwmjogstepedit.ui \
    ../src/wgwidget/hncworkmeas/dlghncstepsel.ui \
    ../src/wgwidget/hncworkmeas/dlghncmeassel.ui \
    ../src/wgwidget/hncworkmeas/wghncmeasres.ui \
    ../src/wgwidget/hncworkmeas/wghnctestmacro.ui \
    ../src/wgwidget/hncworkmeas/wghncmeassel.ui \
    ../src/wgwidget/wgposwithfeed.ui \
    ../src/wgwidget/wgfeed.ui \
    ../src/wgwidget/wgcrdg5x.ui \
    ../src/wgwidget/wgfiletab.ui \
    ../src/wgwidget/wgnetnclink.ui \
    ../src/wgwidget/wghomeparam.ui \
    ../src/wgwidget/wghomeset.ui \
    ../src/wgwidget/oscservo/wgoscservoraster.ui \
    ../src/gwg_widget/gwg_pos_main.ui


win32 {
    HEADERS += ../src/tools/dlgvirtualkeyboard.h
    SOURCES += ../src/tools/dlgvirtualkeyboard.cpp
    FORMS += ../src/tools/dlgvirtualkeyboard.ui
}

DISTFILES += \
    ../../pic/crd-title.png
