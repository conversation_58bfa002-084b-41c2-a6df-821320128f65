﻿#ifndef OSCSYNCCONF_H
#define OSCSYNCCONF_H

#include "hncdatadef.h"
#include "hncaxis.h"
#include "dirmovelayout.h"

#include "containerwidget.h"

namespace Ui {
class OscSyncConf;
}

QT_BEGIN_NAMESPACE
class QWidget;
class QIntValidator;
QT_END_NAMESPACE

class OscSyncConf : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscSyncConf(QWidget *parent = 0);
    ~OscSyncConf();

    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    bool eventFilter(QObject *wg, QEvent *event);
    //void keyPressEvent(QKeyEvent *ev);

private:
    Ui::OscSyncConf *ui;
    Bit32 curFocusIndex;
    QIntValidator *intValidator;
    QList<QWidget *>wList;
    DirMoveLayout *dirLayout;

    void FocusRedraw();
    void LoadData();
    void CurValSet(Bit32 row);
};

#endif // OSCSYNCCONF_H
