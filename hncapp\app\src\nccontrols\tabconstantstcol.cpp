﻿#include <QKeyEvent>

#include "tabconstantstcol.h"

TabConstantStCol::TabConstantStCol(Bit32 stCol, Bit32 maxShowCol, QWidget *parent) :
    NcTableEx(parent)
{
    m_nStCol = stCol;
    m_nMaxShowColNum = maxShowCol;
    m_nCurColOff = 0;
    m_horTitleList.clear();
    m_colWidthStrech.clear();
}

TabConstantStCol::~TabConstantStCol()
{
}

int TabConstantStCol::FocusOn(int row, int defcol)
{
    Bit32 stCol = 0;
    if (defcol > m_nStCol)
    {
        stCol = defcol - m_nStCol;
    }
    if (stCol < m_nCurColOff)
    {
        SetCurColOff(stCol);
    }
    else if (defcol > GetCurLastCol())
    {
        SetCurColOff(defcol - UnstableColNum() + 1);
    }
    Bit32 col = defcol - m_nCurColOff;

    return NcTableEx::FocusOn(row, col);
}

Bit32 TabConstantStCol::GetColNum()
{
    return m_horTitleList.count();
}

Bit32 TabConstantStCol::UnstableColNum()
{
    Bit32 count = m_nMaxShowColNum;
    if (m_horTitleList.count() < m_nMaxShowColNum)
    {
        count = m_horTitleList.count();
    }
    return count - m_nStCol;
}

Bit32 TabConstantStCol::GetCurStCol() const
{
    return m_nCurColOff + m_nStCol;
}

Bit32 TabConstantStCol::GetCurLastCol() const
{
    Bit32 count = m_nMaxShowColNum;
    if (m_horTitleList.count() < m_nMaxShowColNum)
    {
        count = m_horTitleList.count();
    }
    return m_nCurColOff + count - 1;
}

bool TabConstantStCol::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);

        Bit32 col = GetCurrentColumn();
        Bit32 row = GetCurrentRow();
        QStringList list = RoleStyle(row, col, NcTableEx::SpanRole).toStringList();
        Bit32 spanCol = -1;
        if (list.count() >= 2)
        {
            spanCol = list.at(1).toInt();
        }
        switch(keyEvent->key())
        {
        case Qt::Key_Left:
            if (MoveCurStCol(-1))
            {
               return true;
            }
            break;
        case Qt::Key_Right:
            if (MoveCurStCol(1))
            {
                return true;
            }
            break;
        }
    }
    return  NcTableEx::eventFilter(target, event);
}

void TabConstantStCol::SlotScrollHorizontalBarValueChanged(int val)
{
    if (val != GetCurColOff())
    {
        SetCurColOff(val);
    }
}

void TabConstantStCol::UpdataHorizontalScrollBarHidden()
{
    if (m_pHorizontalScrollBar->maximum() <= 0)
    {
        m_pHorizontalScrollBar->setHidden(true);
    }
    else
    {
        m_pHorizontalScrollBar->setHidden(false);
    }
}

void TabConstantStCol::SetHorizontalScrollBarValue()
{
    disconnect(this->m_pHorizontalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollHorizontalBarValueChanged(int)));
    m_pHorizontalScrollBar->setValue(m_nCurColOff);
    connect(this->m_pHorizontalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollHorizontalBarValueChanged(int)));
}

Bit32 TabConstantStCol::GetCurColOff() const
{
    return m_nCurColOff;
}

int TabConstantStCol::GetDefCol(int col)
{
    if (col < m_nStCol)
    {
        return col;
    }
    return col + m_nCurColOff;
}

Bit32 TabConstantStCol::HorizontalScrollMax()
{
    Bit32 count = m_horTitleList.count() - m_nMaxShowColNum;
    if (count < 0)
    {
        return 0;
    }
    return count;
}

Bit32 TabConstantStCol::SpanCol(Bit32 row, Bit32 defcol)
{
    QStringList list = this->RoleStyle(row, defcol, NcTableEx::SpanRole).toStringList();
    if (list.count() < 2)
    {
        return 0;
    }
    return list.at(1).toInt();
}

bool TabConstantStCol::IsSpanExCol(Bit32 row, Bit32 defcol)
{
    int spanCol = SpanCol(row, defcol);
    if (spanCol > 0)
    {
        return false;
    }
    for (int i = defcol - 1; i >= m_nStCol; i--)
    {
        int spanCol = SpanCol(row, i);
        if (spanCol > 1)
        {
            Bit32 tmp = i + spanCol;
            if (tmp < defcol)
            {
                return false;
            }
            return true;
        }
    }
    return false;
}

bool TabConstantStCol::MoveCurStCol(Bit32 dir)
{
    Bit32 row = GetCurrentRow();
    Bit32 col = this->GetCurrentColumn();
    if (col + dir < m_nStCol || m_nCurColOff == 0 && dir == -1)
    {
        return false;
    }
    for (int i = col + dir; i >= GetCurStCol() && i <= GetCurLastCol(); i += dir)
    {
        int flag = this->RoleStyle(row, i, NcTableEx::FlagRole).toInt();
        if (flag != Qt::NoItemFlags && !IsSpanExCol(row, i))
        {
            return false;
        }
    }

    Bit32 spanCol = SpanCol(row, col);
    for (int i = col + dir; i >= 0 && i < GetColNum(); i += dir)
    {
        int flag = this->RoleStyle(row, i, NcTableEx::FlagRole).toInt();
        bool spanExCol = IsSpanExCol(row, i);
        if (flag == Qt::NoItemFlags)
        {
            continue;
        }
        if (i < m_nStCol && !spanExCol)
        {
            SetCurColOff(0);
            FocusOn(row, i);
            return true;
        }
        else if (dir < 0)
        {
            FocusOn(row, i);
            break;
        }
        else if (dir > 0)
        {
            if (spanExCol)
            {
                if (i <= col + spanCol - 1 && spanCol < UnstableColNum())
                {
                    continue;
                }
                SetCurColOff(i - col + m_nCurColOff);
                if (spanCol < UnstableColNum())
                {
                    FocusOn(row, col);
                }
                break;
            }
            else
            {
                SetCurColOff(i - m_nStCol - UnstableColNum() + 1);
                FocusOn(row, i);
                break;
            }
        }
    }
    return true;
}

void TabConstantStCol::SetCurColOff(Bit32 stCol)
{
    if (stCol < 0)
    {
        stCol = 0;
    }
    if (stCol >= HorizontalScrollMax())
    {
        stCol = HorizontalScrollMax();
    }

    m_nCurColOff = stCol;
    this->RedrawTableTitle();
    SetHorizontalScrollBarValue();
}

void TabConstantStCol::InitProp(const QVector<int> vecWidth, const QStringList horTitle)
{
    m_nCurColOff = 0;
    m_horTitleList = horTitle;

    // 平均分配剩余宽度
    m_colWidthStrech.clear();
    Bit32 widthReg = vecWidth.last() * (UnstableColNum());
    Bit32 widthEach = 0;
    Bit32 count = m_nMaxShowColNum;
    if (m_horTitleList.count() < m_nMaxShowColNum)
    {
        count = m_horTitleList.count();
    }
    if (count - m_nStCol > 0)
    {
        widthEach = widthReg / (count - m_nStCol);
    }
    for(int i = 0; i < count; i++)
    {
        if (i < vecWidth.count() - 1)
        {
            m_colWidthStrech << vecWidth.at(i);
        }
        else
        {
            m_colWidthStrech << widthEach;
        }
    }

    RedrawTableTitle();
    this->setStyleSheet("QHeaderView::section{}");
    disconnect(this->m_pHorizontalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollHorizontalBarValueChanged(int)));
    m_pHorizontalScrollBar->setRange(0, HorizontalScrollMax());
    connect(this->m_pHorizontalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollHorizontalBarValueChanged(int)));

    this->installEventFilter(this);
}

void TabConstantStCol::RedrawTableTitle()
{
    QStringList header;
    header.clear();

    for (int i = 0; i < m_horTitleList.count(); i++)
    {
        if (i >= m_nStCol && i < GetCurStCol())
        {
            continue;
        }
        if (header.count() >= m_nMaxShowColNum)
        {
            break;
        }
        header << m_horTitleList.at(i);
    }

    NcTableEx::InitProp(NcTableEx::ENScaleType, m_colWidthStrech, header);
    this->update(); // 刷新表头显示
    RedrawTableTitleEx();
}

void TabConstantStCol::RedrawTableTitleEx()
{
    return; // 预留:解决在变换m_nCurColOff && 合并框中左右 && 跳过Qt::NoItemFlags项时合并框重绘异常
}
