﻿#ifndef NCPROGRESS_H
#define NCPROGRESS_H

#include <QWidget>
#include <QPainter>
#include <QColor>
#include <QMovie>
#include <QRect>
#include <QLabel>

#include "hncdatatype.h"

namespace Ui {
class NcProgress;
}

class NcProgress : public QWidget
{
    Q_OBJECT
public:
    explicit NcProgress(QWidget *parent = 0);
    ~NcProgress();

    void SetValue(fBit64 val);
    void SetRange(fBit64 min, fBit64 max);
    void SetMovePath(QString path);
private:
    Ui::NcProgress *ui;
    QRect m_paintRect;
    QRect m_perRect;
    QPainter *m_pPaint;
    QPainter *m_pPaintWidget;

    QColor m_backColor;
    QColor m_chunkColor;
    QColor m_chunkFrontColor;

    fBit64 m_fMin;
    fBit64 m_fMax;
    fBit64 m_fVal;
    Bit32 m_nTimer;
    Bit32 m_nPrec;
    bool m_bFirstFlag;
    bool m_bRedrawFlag;

    Bit32 m_nChunkSt;

    bool eventFilter(QObject *wg, QEvent *event);
    void resizeEvent(QResizeEvent *);
    void PaintResize();
    fBit64 GetPer();
    void MovieMove(fBit64 per);
    void Refresh();
    void DrawDiamond(Bit32 st);
    void Redraw();
    void UpdatePerRectWidth();
};

#endif // NCPROGRESS_H
