﻿#include <QKeyEvent>
#include "nccheckbox.h"

NcCheckBox::Nc<PERSON><PERSON><PERSON><PERSON><PERSON>(QWidget *parent):
    QCheckBox(parent)
{
}

NcCheckBox::NcCheckBox(const QString &text, QWidget *parent):
    Q<PERSON><PERSON>ck<PERSON>ox(parent)
{
    setText(text);
}

void NcCheckBox::keyPressEvent(QKeyEvent *e)
{
    if(e == NULL)
    {
        return;
    }
    if (e->key() == Qt::Key_Space
     || e->key() == Qt::Key_Left || e->key() == Qt::Key_Right
     || e->key() == Qt::Key_Up || e->key() == Qt::Key_Down
     || e->key() == Qt::Key_PageUp || e->key() == Qt::Key_PageDown)
    {
        e->accept();
    }
    else
    {
        QCheckBox::keyPressEvent(e);
    }
}
