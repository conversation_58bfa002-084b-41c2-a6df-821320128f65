﻿#ifndef OSCSERVOSPDLCONF_H
#define OSCSERVOSPDLCONF_H

#include "containerwidget.h"
#include "dirmovelayout.h"

namespace Ui {
class OscServoSpdlConf;
}

QT_BEGIN_NAMESPACE
class QIntValidator;
QT_END_NAMESPACE

class OscServoSpdlConf : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoSpdlConf(QWidget *parent = 0);
    ~OscServoSpdlConf();

    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    bool eventFilter(QObject *wg, QEvent *event);
    //void keyPressEvent(QKeyEvent *ev);
private:
    Ui::OscServoSpdlConf *ui;
    void LoadData();
    void FocusRedraw();
    void DataSet(Bit32 idx);

    QList<QWidget *>wList;
    DirMoveLayout *dirLayout;
    Bit32 curFocusIndex;
    QIntValidator *intValidator;
};

#endif // OSCSERVOSPDLCONF_H
