QMAKE_CXX.QT_COMPILER_STDCXX = 201402L
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 8
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 1
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 0
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    C:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ \
    C:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 \
    C:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward \
    C:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include \
    C:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed \
    C:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/x86_64-w64-mingw32/include
QMAKE_CXX.LIBDIRS = \
    C:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0 \
    C:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc \
    C:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/x86_64-w64-mingw32/lib \
    C:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib
