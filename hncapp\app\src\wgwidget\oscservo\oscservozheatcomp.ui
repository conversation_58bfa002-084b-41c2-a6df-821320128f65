<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OscServoZHeatComp</class>
 <widget class="QWidget" name="OscServoZHeatComp">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>710</width>
    <height>442</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,1">
   <property name="spacing">
    <number>2</number>
   </property>
   <property name="leftMargin">
    <number>2</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2"/>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="backgrd3" native="true">
     <property name="selected" stdset="0">
      <bool>false</bool>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout" stretch="1,3,1,4,1,4">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="labelTitle">
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>12</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string> 信息</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout" rowstretch="1,1,1" columnstretch="1,1,1">
        <property name="leftMargin">
         <number>6</number>
        </property>
        <item row="2" column="2">
         <widget class="QLabel" name="label_6">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>（mm）</string>
          </property>
         </widget>
        </item>
        <item row="1" column="0" colspan="2">
         <widget class="QLabel" name="label_3">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>Z轴丝杠最大变形量：</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLabel" name="labelMaxErr">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>0.000</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0" colspan="3">
         <widget class="QLabel" name="warning">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>注：测试过程中禁止退出该界面</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QFrame" name="title">
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>1</number>
         </property>
         <property name="topMargin">
          <number>1</number>
         </property>
         <property name="rightMargin">
          <number>1</number>
         </property>
         <property name="bottomMargin">
          <number>1</number>
         </property>
         <item>
          <widget class="QLabel" name="label_2">
           <property name="minimumSize">
            <size>
             <width>10</width>
             <height>0</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>微软雅黑</family>
             <pointsize>11</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>采样时间（min）</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label">
           <property name="font">
            <font>
             <family>微软雅黑</family>
             <pointsize>11</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>偏移量（um）</string>
           </property>
           <property name="textFormat">
            <enum>Qt::AutoText</enum>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout_5">
        <item row="0" column="0">
         <widget class="QTableView" name="tableView"/>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QLabel" name="labelTitle1">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>0</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>12</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string> 热误差补偿参数</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout_2"/>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
