﻿#ifndef TABCONSTANTSTCOL_H
#define TABCONSTANTSTCOL_H

#include <QWidget>

#include "nctableex.h"

class TabConstantStCol : public NcTableEx
{
    Q_OBJECT

public:
    explicit TabConstantStCol(Bit32 stCol, Bit32 maxShowCol, QWidget *parent = 0);
    ~TabConstantStCol();
    int FocusOn(int row, int defcol);

protected:
    Bit32 m_nMaxShowColNum;
    Bit32 m_nStCol;
    Bit32 m_nCurColOff;
    QStringList m_horTitleList;
    QVector<int> m_colWidthStrech;

    void InitProp(const QVector<int> vecWidth, const QStringList horTitle);

    Bit32 GetCurStCol() const;

    Bit32 GetCurColOff() const;
    void SetCurColOff(Bit32 stCol);

    Bit32 HorizontalScrollMax();
    void UpdataHorizontalScrollBarHidden();

    void RedrawTableTitle();

    bool MoveCurStCol(Bit32 dir);
    bool eventFilter(QObject *target, QEvent *event);

    int GetDefCol(int col);
    Bit32 GetCurLastCol() const;
    Bit32 UnstableColNum(); // 变化的列数
    Bit32 GetColNum();

    bool IsSpanExCol(Bit32 row, Bit32 defcol);
    virtual void RedrawTableTitleEx();

private:
    void SetHorizontalScrollBarValue();
    Bit32 SpanCol(Bit32 row, Bit32 defcol);
private slots:
    void SlotScrollHorizontalBarValueChanged(int val);
};

#endif // TABCONSTANTSTCOL_H
