﻿
#include <QKeyEvent>

#include "hmioscservo.h"
#include "msgchan.h"
#include "chaxispos.h"

#include "oscservospdlconf.h"
#include "ui_oscservospdlconf.h"

OscServoSpdlConf::OscServoSpdlConf(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoSpdlConf)
{
    ui->setupUi(this);
    wList.clear();
    wList << ui->spdlNo
          << ui->labelSpdForwardlM
          //<< ui->labelSpdReverseM
          << ui->labelSpdStopM
          << ui->spdlSpeed
          << ui->spdlHoldTime
          << ui->labelPeriodMulti;

    for(int i = 0; i < wList.count(); i++)
    {
        if (wList.at(i) != NULL)
        {
            wList.at(i)->setFocusPolicy(Qt::StrongFocus);
            wList.at(i)->installEventFilter(this);
        }
    }
    this->dirLayout = new DirMoveLayout(this);
    dirLayout->SetDirMoveLayout(6, 1, this->wList);
    this->curFocusIndex = 0;

    intValidator = new QIntValidator(this);
}

OscServoSpdlConf::~OscServoSpdlConf()
{
    delete ui;
}

void OscServoSpdlConf::LoadData()
{
    Bit32 ch = ActiveChan();
    QString spdlName = ChAxisPos::GetInstance()->GetAxName(HmiOscServo::s_Conf[ch].stSpindleConf.axisNo);

    ui->spdlNo->setText(QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.axisNo));
    ui->spdlSpeed->setText(QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.speed));
    ui->spdlHoldTime->setText(QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.pauseTime));
//    ui->labelPeriod->setText(QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.period));
    ui->labelSpdForwardlM->setText(QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.spdlForwardM));
    //ui->labelSpdReverseM->setText(QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.spdlReverseM));
    ui->labelSpdStopM->setText(QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.spdlStopM));
    ui->label_spdlName->setText(QString("(%1)").arg(spdlName));

    Bit32 ncuCycle = 0;
    Bit32 multi = 0;
    Bit32 ret = HmiOscServo::GetNcuCycleMulti(HmiOscServo::s_Conf[ch].stSpindleConf.period, ncuCycle, multi);
    ncuCycle = ncuCycle / 1000;

    if(ret == -1)
    {
        ui->labelPeriodMulti->setText("");
        ui->labelNcuCycle->setText(QString::number(ncuCycle));
        ui->labelPeriodResult->setText(QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.period));

        MessageOut(QObject::TR("采样周期不是插补周期整数倍，请手动设置采样周期"));
    }
    else
    {
        ui->labelPeriodMulti->setText(QString::number(multi));
        ui->labelNcuCycle->setText(QString::number(ncuCycle));
        ui->labelPeriodResult->setText(QString::number(ncuCycle * multi));
    }
}

void OscServoSpdlConf::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    UNREFERENCED_PARAM(messageValue);
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        this->LoadData();
        this->wList.at(this->curFocusIndex)->setFocus();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::SETFOCUS)
    {
        this->LoadData();
        this->wList.at(this->curFocusIndex)->setFocus();
    }
}

bool OscServoSpdlConf::eventFilter(QObject *wg, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        QWidget *tmp = dynamic_cast<QWidget *>(wg);
        if(this->wList.contains(tmp) && tmp != NULL)
        {
            this->curFocusIndex = this->wList.indexOf(tmp);
            if(keyEvent->key() == Qt::Key_Right || keyEvent->key() == Qt::Key_Left
              || keyEvent->key() == Qt::Key_Down || keyEvent->key() == Qt::Key_Up)
            {
                dirLayout->DirMoveOnKey(tmp, keyEvent->key());
                this->curFocusIndex = dirLayout->GetCurFocusIdx();
                return true;
            }
            else if(keyEvent->key() == Qt::Key_Enter || keyEvent->key() == Qt::Key_Return)
            {
                this->DataSet(this->curFocusIndex);
                return true;
            }

        }
    }
    else if(event->type() == QEvent::FocusIn)
    {
        QWidget *tmp = dynamic_cast<QWidget *>(wg);
        if(this->wList.contains(tmp) && tmp != NULL)
        {
            this->curFocusIndex = this->wList.indexOf(tmp);
        }
    }

    return QObject::eventFilter(wg, event);
}

void OscServoSpdlConf::DataSet(Bit32 idx)
{
    QString editStr = "";
    Bit32 ret = 0;
    Bit32 ch = ActiveChan();
    bool ok = false;
    Bit32 cycle = HmiOscServo::GetNcuCycle() / 1000;
    if(cycle == 0)
    {
        cycle = 1;
    }

    if(idx < 0 || idx >= this->wList.count())
    {
        return;
    }

    if (wList.at(idx) == ui->spdlNo)
    {
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.axisNo);
        intValidator->setBottom(0);
        intValidator->setTop(TOTAL_AXES_NUM - 1);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[主轴轴号]:"), -1, -1, 0, intValidator);
        if(ret == 0)
        {
            Bit32 axisNo = editStr.toLong(&ok);
            if (ok == false || axisNo < 0 || axisNo >= TOTAL_AXES_NUM)
            {
                MessageOut(TR("输入数据无效！"));
                this->wList.at(idx)->setFocus();
                return;
            }
            else
            {
                HmiOscServo::s_Conf[ch].stSpindleConf.axisNo = axisNo;
                this->LoadData();
            }
        }
    }
    else if (wList.at(idx) == ui->labelSpdForwardlM)
    {
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.spdlForwardM);
        intValidator->setBottom(1);
        intValidator->setTop(999);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[主轴旋转M指令]:"), -1, -1, 0, intValidator);
        if(ret == 0)
        {
            Bit32 tmpLong = editStr.toLong(&ok);
            if (ok == false || tmpLong < 1 || tmpLong > 999)
            {
                MessageOut(TR("输入数据无效！范围：%1~%2").arg(1).arg(999));
                this->wList.at(idx)->setFocus();
                return;
            }
            else
            {
                HmiOscServo::s_Conf[ch].stSpindleConf.spdlForwardM = tmpLong;
                this->LoadData();
            }
        }
    }
    /*else if (wList.at(idx) == ui->labelSpdReverseM)
    {
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.spdlReverseM);
        intValidator->setBottom(1);
        intValidator->setTop(999);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[主轴反转M指令]:"), -1, -1, 0, intValidator);
        if(ret == 0)
        {
            Bit32 tmpLong = editStr.toLong(&ok);
            if (ok == false || tmpLong < 1 || tmpLong > 999)
            {
                MessageOut(TR("输入数据无效！范围：%1~%2").arg(1).arg(999));
                this->wList.at(idx)->setFocus();
                return;
            }
            else
            {
                HmiOscServo::s_Conf[ch].stSpindleConf.spdlReverseM = tmpLong;
                this->LoadData();
            }
        }
    }*/
    else if (wList.at(idx) == ui->labelSpdStopM)
    {
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.spdlStopM);
        intValidator->setBottom(1);
        intValidator->setTop(999);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[主轴停转M指令]:"), -1, -1, 0, intValidator);
        if(ret == 0)
        {
            Bit32 tmpLong = editStr.toLong(&ok);
            if (ok == false || tmpLong < 1 || tmpLong > 999)
            {
                MessageOut(TR("输入数据无效！范围：%1~%2").arg(1).arg(999));
                this->wList.at(idx)->setFocus();
                return;
            }
            else
            {
                HmiOscServo::s_Conf[ch].stSpindleConf.spdlStopM = tmpLong;
                this->LoadData();
            }
        }
    }
    else if (wList.at(idx) == ui->spdlSpeed)
    {
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.speed);
        intValidator->setBottom(1);
        intValidator->setTop(100000);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[主轴转速]:"), -1, -1, 0, intValidator);
        if(ret == 0)
        {
            Bit32 tmpLong = editStr.toLong(&ok);
            if (ok == false || tmpLong < 1 || tmpLong > 100000)
            {
                MessageOut(TR("输入数据无效！[主轴转速]设置范围为%1~%2").arg(1).arg(100000));
                this->wList.at(idx)->setFocus();
                return;
            }
            else
            {
                HmiOscServo::s_Conf[ch].stSpindleConf.speed = tmpLong;
                this->LoadData();
            }
        }
    }
    else if (wList.at(idx) == ui->spdlHoldTime)
    {
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.pauseTime);
        intValidator->setBottom(0);
        intValidator->setTop(5000);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[暂停时间]:"), -1, -1, 0, intValidator);
        if(ret == 0)
        {
            Bit32 tmpLong = editStr.toLong(&ok);
            if (ok == false || tmpLong < 0 || tmpLong > 5000)
            {
                MessageOut(TR("输入数据无效！[暂停时间]设置范围为%1~%2").arg(0).arg(5000));
                this->wList.at(idx)->setFocus();
                return;
            }
            else
            {
                HmiOscServo::s_Conf[ch].stSpindleConf.pauseTime = tmpLong;
                this->LoadData();
            }
        }
    }
    else if (wList.at(idx) == ui->labelPeriodMulti)
    {
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.period / cycle);
        intValidator->setBottom(1);
        intValidator->setTop(1000 / cycle);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[采样周期/插补周期 倍率]:"), -1, -1, 0, intValidator);
        if(ret == 0)
        {
            Bit32 tmpLong = editStr.toLong(&ok);
            if (ok == false || tmpLong < 1 || tmpLong > 1000 / cycle)
            {
                MessageOut(TR("输入数据无效！[倍率]设置范围为1~%1").arg(1000 / cycle));
                this->wList.at(idx)->setFocus();
                return;
            }
            else
            {
                HmiOscServo::s_Conf[ch].stSpindleConf.period = tmpLong * cycle;
                this->LoadData();
            }
        }
    }
    else
    {
        return;
    }

    HmiOscServo::OscServoDataSave();
    //this->wList.at(idx)->setFocus();
    MsgChan::Instance().TranMsg(MsgData::SETFOCUS, ""); // 设置焦点
}
