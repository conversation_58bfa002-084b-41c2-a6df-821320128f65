﻿#ifndef OSCSERVOSYNC_H
#define OSCSERVOSYNC_H

#include "containerwidget.h"

namespace Ui {
class OscServoSync;
}

QT_BEGIN_NAMESPACE
class QWidget;
class OscWave;
QT_END_NAMESPACE

class OscServoSync : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoSync(QWidget *parent = 0);
    ~OscServoSync();
    void SetColorStyle();

protected:
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);

private:
    Ui::OscServoSync *ui;

    OscWave *pOscWaveCurrentFlow;
    OscWave *pOscWaveErr;

    Bit32 lastEndPos;

    void LoadInfo();
    void LoadWave();
    void Refresh();
    void Reset();
};

#endif // OSCSERVOSYNC_H
