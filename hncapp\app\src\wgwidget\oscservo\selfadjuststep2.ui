﻿<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SelfAdjustStep2</class>
 <widget class="QWidget" name="SelfAdjustStep2">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>681</width>
    <height>541</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="backgrd">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_2" rowstretch="1,1,13">
      <item row="2" column="0">
       <layout class="QGridLayout" name="gridLayout_4" columnstretch="2,2,1,3">
        <item row="4" column="2">
         <widget class="QLabel" name="label_3">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>(Hz)</string>
          </property>
         </widget>
        </item>
        <item row="8" column="0">
         <widget class="QLabel" name="label_24">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>电机运行加速度：</string>
          </property>
         </widget>
        </item>
        <item row="6" column="0">
         <widget class="QLabel" name="label_5">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>电机运行速度：</string>
          </property>
         </widget>
        </item>
        <item row="6" column="2">
         <widget class="QLabel" name="label_7">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>(rpm)</string>
          </property>
         </widget>
        </item>
        <item row="7" column="0">
         <widget class="QLabel" name="label_8">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>电机运行行程：</string>
          </property>
         </widget>
        </item>
        <item row="8" column="1">
         <widget class="QLabel" name="labelAcc">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="8" column="2">
         <widget class="QLabel" name="label_25">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>(rpm/ms)</string>
          </property>
         </widget>
        </item>
        <item row="5" column="0">
         <widget class="QLabel" name="label_15">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>参数整定运行参数</string>
          </property>
         </widget>
        </item>
        <item row="7" column="1">
         <widget class="QLabel" name="labelDistance">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="label_13">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>自整定过程选择</string>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <widget class="QLabel" name="labelFreq">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="label_14">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>14</pointsize>
           </font>
          </property>
          <property name="text">
           <string>惯量辨识运行参数</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="1" column="0">
         <widget class="NcCheckBox" name="checkBoxInertia">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>惯量辨识</string>
          </property>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QLabel" name="label_17">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>辨识频率：</string>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QLabel" name="labelAmp">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="3" column="2">
         <widget class="QLabel" name="label_18">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="6" column="1">
         <widget class="QLabel" name="labelSpeed">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="7" column="2">
         <widget class="QLabel" name="label_23">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>(0.1转)</string>
          </property>
         </widget>
        </item>
        <item row="3" column="3">
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="label_2">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>辨识幅值：</string>
          </property>
         </widget>
        </item>
        <item row="7" column="3">
         <widget class="QLabel" name="labelDistanceInfo">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="NcCheckBox" name="checkBoxPara">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>参数整定</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="1" column="0">
       <widget class="Line" name="line">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="2,1">
        <item>
         <widget class="QLabel" name="label">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="text">
           <string>整定过程配置 （选择整定过程）</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_26">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="text">
           <string>2 / 4</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>NcCheckBox</class>
   <extends>QCheckBox</extends>
   <header location="global">nccheckbox.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
