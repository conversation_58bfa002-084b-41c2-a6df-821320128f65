﻿/*
* Copyright (c) 2016, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：hmicommon.h
* 文件标识：根据配置管理计划书
* 摘    要：共通功能
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2016年2月14日
* 说    明：
*/

#ifndef __HMICOMMON_H__
#define __HMICOMMON_H__

#include <QFileInfo>
#include <QTextCodec>
#include <QValidator>
#include <QTableWidget>
#include <QDomDocument>

#ifdef _WIN32
#include <stdio.h>
#define snprintf _snprintf
#endif

#include "hncdatatype.h"
#include "hncdatadef.h"
#include "uintp_decoder.h"
#include "progman_def.h"

#include "dlgmsgbox.h"
#include "msgprompt.h"
#include "msgpromptwith3exit.h"
#include "inputmanage.h"
#include "resize.h"
#include "logdt.h"
#include "dlgreplace.h"

#define PLCSW_FILE_NAME "PLCSW.STR" // PLC开关名称定义文本
#define CYCLE_FILE_NAME0 "MILLING.CYC" // 铣床固定循环文件
#define CYCLE_FILE_NAME1 "TURNING.CYC" // 车床固定循环文件
#define CYCLE_FILE_NAME2 "USERDEF.CYC" // 用户自定义固定循环文件
#define MAC_INFO_FILE_NAME  "INFO.XML"  // 机床信息文件

#define LANG_FILE_PATH "../lang" // 多语言文件路径
#define TWIN_CODE_FILE_PATH "../twinprog" // 加工优化代码路径
#define TWIN_CODE_PROFIX "Oprog." // 加工优化代码固定前缀
#define TWIN_PROG_CODE_PATH "../"  //为了兼容第二加工G代码导入和导出，暂时方案为prog和twinprog的上级目录
#define GDBLK_FILE_PATH "../gdblkprog" // 编程引导代码路径
#define LNS_FILE_PATH "lns/" // 雷尼绍配置路径
#define FLAG_FILE_NAME "tar_inf.txt"
#define DATA_TABLE_PATH "../contour_error_compen" // 数据表路径
#define SYS_CYC_PROG_FILE_PATH "../progsys" // 系统内部固定循环路径

const QString TRAFTERDISP_NAME = "W_GENERALFUNC"; // 多语言切换后切到界面名称
const QString SDSKDISP_NAME = "W_SDSKMAN"; // 程序管理界面名称
const QString PORGEDIT = "W_PROGEDT";           // 程序编辑名称
const QString MENU_RETN = "RETN"; // 返回菜单
const QString MENU_MNUR = "MNUR"; // 平级切换
const QString MENU_MSG = "MSG_"; // 消息菜单
const QString MENU_WIDGET = "W_"; // 界面菜单
const QString MENU_DLG = "DLG_";  // 对话框
const QString FILELIST_PROGMAN_NAME = "FILELIST_PROGMAN"; // 程序管理界面文件列表名
const QString MENU_MDI_MULTI = "W_MULTIMDI";        // 多通道MDI
const QString MENU_MAC_NORMAL = "W_POS";		// 常用加工
const QString MENU_MAC_MULTI = "W_MULTICHANNEL";	// 多通道加工
const QString MENU_FILEMAN_0 = "W_FILECOPYVER";         // 文件管理0
const QString MENU_FILEMAN_1 = "W_PROGEDTBACKSTAGEALL";	// 文件管理1
const QString MENU_FILEMAN_2 = "W_SDSK";                // 文件管理2
const QString MENU_MDI_NORMAL = "W_NCMDI";          // 常用MDI
const QString MENU_FORE_PROGEDT = "W_PROGEDTFOREGROUNDALL";  // 编辑程序

const Bit32 POS_MAX_LEN = 11; // 坐标值数据最大长度

const Bit32 HMIREG_D = 18; // 界面用寄存器
const Bit32 HMIREG_D_CONSTANT = 0x1; // 回固定点用
const Bit32 HMIREG_D_MPGCUT_IN = 0x2; // 手轮试切输入
const Bit32 HMIREG_D_MPGCUT_OUT = 0x4; // 手轮试切输出
const Bit32 HMIREG_D_ZERO = 0x8; // 回机械原点用

const Bit32 NORMAL_FONT_SIZE = 13;
const Bit32 SMALL_FONT_SIZE = 10;

// 螺纹修复 再切削是否有效标记
#define SCREW_THRED_REG_ID (76) // REG_CH_CTRL2
#define SCREW_THRED_REG_BIT (15) // CH_CTRL_THREAD_REPAIR

// PAR_NCU_NET_CONNECT_TYPE
#define CONNECT_LOG_IN (0x0001)			/*!< 开机自动联网 */
#define CONNECT_AUTO_CONNECT (0x0002)	/*!< 断网自动重连 */

// 菜单类型
typedef enum _Menu_Type
{
    INVALID_MENUBAR = -1,
    BOTTOM_MENUBAR = 0,	// 横菜单
    RIGHT_MENUBAR,	// 竖菜单
}MenuType;

//时间标志各bit位定义
typedef enum _TIME_CHANGE_
{
    TIME_MAINTAIN_FLAG = 0,                              // 取s_TimeChangeFlag（修改时间标志）的第0位
    TIME_RUNSTATEINFO_FLAG                               // 取s_TimeChangeFlag（修改时间标志）的第1位
}TimeChange;

// 进给速度单位
enum{
    F_TYPE_MINTER = 0,
    F_TYPE_ROUND,

    F_TYPE_NUM
};

extern fBit64 GetValueValidLimit(Bit32 prec);

// 判断2个文件是否同一个文件
extern bool FilePathIsSame(QString file1, QString file2);

///////////////////////////////////////////////////////////////////////////////
//
//    bool ProgIsLoadedCh(int ch, QString absPathName)
//
//    功能：
//            程序是否已是加载在ch通道的主程序
//
//    参数：
//              ch：通道号
//              absPathName：程序路径
//    描述：
//
//
//    返回：
//          ture：已加载 false：未加载
//
//////////////////////////////////////////////////////////////////////////
extern bool ProgIsLoadedCh(int ch, QString absPathName);

///////////////////////////////////////////////////////////////////////////////
//
//    bool ProgIsLoaded(QString absPathName)
//
//    功能：
//            程序是否已加载在任意通道
//
//    参数：
//             absPathName：程序路径
//
//    描述：
//
//
//    返回：
//          ture：已加载 false：未加载
//
//////////////////////////////////////////////////////////////////////////
extern bool ProgIsLoaded(QString absPathName);

/**
 * @brief PixMapToSize 图片适应控件大小
 * @param [in] size:图片目标大小
 * @param [in] path:图片路径
 * @return 返回调整后的pixmap
 */
extern QPixmap PixMapToSize(QSize size, QString path);

extern QPixmap PixMapToSize(QSize size, QPixmap pixmap);

extern Bit8 *QString2Bit8(QString str);

///////////////////////////////////////////////////////////////////////////////
//
//    Bit32 IsNum(Bit8 key)
//
//    功能：
//			判断数字（包括负号和小数点）
//
//    参数：
//			key ：字符
//
//    描述：
//
//
//	  返回：
//			1：数字（包括负号和小数点）
//			0：否
//
//////////////////////////////////////////////////////////////////////////
extern Bit32 IsNum(Bit8 key);

///////////////////////////////////////////////////////////////////////////////
//
//    Bit32 isCoordValueLegal(Bit8 *buf)
//
//    功能：
//            判断坐标数据是否合法
//
//    参数：
//            buf ：数据字符串；
//
//    描述：
//
//
//    返回：
//            1：合法；0：非法；
//
//////////////////////////////////////////////////////////////////////////
extern Bit32 isCoordValueLegal(Bit8 *buf);

// 英制转公制
extern fBit64 BritishToMetric(fBit64 val);

// 公制转英制
extern fBit64 MetricToBritish(fBit64 val);

///////////////////////////////////////////////////////////////////////////////
//
//    void InsertSpace(QString& str)
//
//    功能：
//            在G代码中添加空格
//
//    参数：
//            str : G代码字符串
//
//    描述：
//
//
//    返回：
//            无
//
//////////////////////////////////////////////////////////////////////////
extern void InsertSpace(QString& str);

/**
 * @brief GetMcpKey 获取数据保护状态
 * @return 0:保护 1:未保护
 */
extern Bit32 GetMcpKey();
/**
 * @brief SetTableWidgetRowHeight 根据tableWidget大小设置每一行的高度
 * @param tableWidget tableWidget指针
 */
void SetTableWidgetRowHeight(QTableWidget *tableWidget);

#if 0
// 保留此接口
/**
 * @brief ShowChannel 设置显示通道
 * @param channel 显示通道
 * @return 0：设置成功；-1：设置失败
 */
extern Bit32 SetShowChannel(Bit32 channel);
#endif

//extern void SetInputHanzi(bool hanzi);

/**
 * @brief 快捷键转换为字符串
 * @param keyEvent 快捷键事件
 */
extern QString KeyEventToStr(QKeyEvent keyEvent);

/**
 * @brief IsSysLangChging 系统语言是否切换中
 * @return
 */
extern bool IsSysLangChging();

/**
 * @brief SetSysLangChging  设置系统语言是否切换中状态
 * @param bChging
 */
extern void SetStatus_SysLangChging(bool bChging);

extern bool IsSwapProg(Bit32 totalRow);

extern Bit32 GetChannelAxisId(Bit32 ch, Bit32 idx, QString &axisNameStr);

extern bool PlcIsRunning();

/**
 * @brief RecordToFile 记录信息到文件中
 * @param file 文件路径及名称
 * @param str 信息
 */
extern void RecordToFile(QString file, QString str);

extern void RunRowDebugInfo(Bit32 ch, QString strIn);

extern void GetChineseInBuf(Bit8* srcStr, QList<Bit32> &chiPos);

/**
 * @brief GetScrewState获取螺纹修复,再切削有效标记
 * @param ch 通道号
 * @return
 */
extern bool GetScrewThreadState(Bit32 ch);

extern bool IsLoginConnect();

extern bool IsAutoConnect();

///
/// \brief GetCycForbidState 获取循环启动禁止标记位
/// \return true：禁止循环启动； false：允许循环启动
///
extern bool CommGetCycForbidState();

// 重写xml文件
extern Bit32 SaveXMLElement(QString fullPath, QDomDocument &doc);

extern fBit64 HmiChanGetTransVal(Bit32 type, Bit32 ch, Bit32 index);

extern fBit64 TransAxisUnit2Save(Bit32 axis, fBit64 val);

/**
 * @brief TableNoDefaultMove 是否关闭表格输入后默认焦点移动
 * @return
 */
extern bool TableNoDefaultMove();

/*!
 * \brief GetStringPixedWidth 根据字体计算字符串占用像素宽度
 * \param [in] font 字体
 * \param [in] str 字符串
 * \return 像素宽度
 */
extern Bit32 GetStringPixedWidth(const QFont& font, const QString& str);

/**
 * @brief HasTemperatureSensor 判断是否温度传感器
 * @return true:有;false:无
 */
extern bool HasTemperatureSensor();

/*!
 * \brief SetTimeChangeFlag 设置时间修改标志
 */
extern void SetTimeChangeFlag();

/*!
 * \brief GetTimeChangeFlag 获取时间修改标志
 * \param [in] index 需要判断时间是否修改的模块索引号
 * \return true:时间修改;false:没修改
 */
extern bool GetTimeChangeFlag(TimeChange index);

/*!
 * \brief ResetTimeChangeFlag 清除模块对应的时间修改标志
 * \param [in] index 需要判断时间是否修改的模块索引号
 */
extern void ResetTimeChangeFlag(TimeChange index);

// 数值四舍五入
extern fBit64 CalRound(fBit64 fVal, Bit32 prec);

// 数值四舍五入
extern QString CalRoundStr(fBit64 fVal, Bit32 prec);

// F实际显示F指令的值
extern bool ShowCmdFeedToAct();

/**
 * @brief SetServerIpm 设置上位机启动参数
 * @param serverIp 启动参数
 */
extern void SetServerIp(const char *serverIp);

/**
 * @brief GetServerIp 获取上位机启动参数
 * @return 启动参数
 */
extern QString GetServerIp(void);

/*!
 * \brief FileIsCycle 判断文件是否为固定循环文件
 * \param fullPathName 文件全路径名
 * \return -1：不是固定循环文件；其他固定循环序号
 */
extern Bit32 FileIsCycle(Bit8* fullPathName);

extern bool IsChanFreNCOpen();


/*!
 * \brief IsGcodeNameConflictWithSys 判断当前路径G代码是否和系统已占程序号冲突 
 * \param filePath 文件全路径名
 * \param prog_no 解析出的文件程序号
 * \return 0：没有冲突，1：与系统定义的有冲突
 */
extern Bit32 IsGcodeNameConflictWithSys(QString filePath, Bit32 *prog_no);

extern Bit32 VSystem(const Bit8* cmd);			// 使用vfork实现system功能

extern bool IsGmodeG71(Bit32 ch);

extern bool IsGmodeG36(Bit32 ch);

extern bool IsGmodeG41_4G42_4(Bit32 ch);

extern bool IsModalPop();                       // 判断当前是否存在模态窗口或下拉选项框

extern bool IsInitGifShow();

extern Bit32 HmiRefreshTime();

extern void DoProcessEvents(bool forceProcessFlag = false);

extern void NormalizeChildren(QWidget *pConWidget);

extern Bit32 GetMemUsage(Bit32 &totalMem, Bit32 &freeMem);

extern bool IsProgPrintDlgCountDown();

extern Bit32 ProgPrintCountDownTotalTime();

extern bool IsEStop(Bit32 ch);                              // 该通道是否为急停

extern Bit32 GetPIDByName(const Bit8 *name);

extern Bit32 ParaGetID(Bit32 fileno, Bit32 subno, Bit32 index);

extern bool IsParamanVisible(Bit32 paramId);

extern Bit32 GetFeedUnitType(Bit32 ch);

extern fBit64 GetCmdFeed(Bit32 ch);

extern fBit64 NorAngle(fBit64 fval);

extern bool IsInRotTolerande(fBit64 pos, fBit64 tolerande);
#endif
