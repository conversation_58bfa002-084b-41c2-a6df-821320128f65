#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QTranslator>
#include <QLocale>
#include <QDebug>

#include "mainwindow.h"
#include "ladderdata.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("PLC梯形图监控");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("华中数控");
    app.setOrganizationDomain("hncnc.com");
    
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // 设置应用程序图标
    // app.setWindowIcon(QIcon(":/icons/ladder.png"));
    
    // 加载翻译文件
    QTranslator translator;
    QString locale = QLocale::system().name();
    if (translator.load(QString("ladder_%1").arg(locale), ":/translations")) {
        app.installTranslator(&translator);
    }
    
    // 创建数据管理器
    LadderData *ladderData = new LadderData(&app);
    
    // 尝试加载默认数据文件
    QString dataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(dataPath);
    QString defaultFile = dataPath + "/default.lad";
    
    if (QFile::exists(defaultFile)) {
        if (!ladderData->loadFromFile(defaultFile)) {
            QMessageBox::warning(nullptr, "警告", 
                                QString("无法加载默认数据文件: %1").arg(defaultFile));
        }
    }
    
    // 创建主窗口
    MainWindow window;
    window.setLadderData(ladderData);
    
    // 显示主窗口
    window.show();
    
    // 启动调试模式（用于演示）
    window.setDebugMode(true);
    
    // 运行应用程序
    int result = app.exec();
    
    // 保存数据
    if (!ladderData->saveToFile(defaultFile)) {
        qWarning() << "Failed to save data to" << defaultFile;
    }
    
    return result;
}
