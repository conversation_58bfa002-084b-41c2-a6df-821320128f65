#############################################################################
# Makefile for building: plc_ladder_viewer
# Generated by qmake (3.1) (Qt 5.15.2)
# Project:  ..\plc.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -D_WIN32 -D_MBCS -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -g -std=gnu++11 -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I..\..\new_project -I. -I..\..\include\api -I..\..\include\plc -I..\..\include\app -I..\..\hncapp\app\src\include -IC:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\include -IC:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\include\QtWidgets -IC:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\include\QtGui -IC:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\include\QtANGLE -IC:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\include\QtCore -Idebug\moc -Idebug\ui -I/include -IC:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,windows -mthreads
LIBS        =        C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\lib\libQt5Widgets.a C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\lib\libQt5Gui.a C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\lib\libQt5Core.a  -lmingw32 C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\lib\libqtmain.a -LC:\openssl\lib -LC:\Utils\my_sql\mysql-5.7.25-winx64\lib -LC:\Utils\postgresql\pgsql\lib -lshell32 
QMAKE         = C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = debug\obj

####### Files

SOURCES       = ..\main.cpp \
		..\ladderscene.cpp \
		..\ladderitem.cpp \
		..\ladderview.cpp \
		..\ladderdata.cpp \
		..\mainwindow.cpp debug\qrc_resources.cpp \
		debug\moc\moc_ladderscene.cpp \
		debug\moc\moc_ladderview.cpp \
		debug\moc\moc_ladderdata.cpp \
		debug\moc\moc_mainwindow.cpp
OBJECTS       = debug/obj/main.o \
		debug/obj/ladderscene.o \
		debug/obj/ladderitem.o \
		debug/obj/ladderview.o \
		debug/obj/ladderdata.o \
		debug/obj/mainwindow.o \
		debug/obj/qrc_resources.o \
		debug/obj/moc_ladderscene.o \
		debug/obj/moc_ladderview.o \
		debug/obj/moc_ladderdata.o \
		debug/obj/moc_mainwindow.o

DIST          =  ..\ladderscene.h \
		..\ladderitem.h \
		..\ladderview.h \
		..\ladderdata.h \
		..\mainwindow.h \
		..\lad_def_compat.h ..\main.cpp \
		..\ladderscene.cpp \
		..\ladderitem.cpp \
		..\ladderview.cpp \
		..\ladderdata.cpp \
		..\mainwindow.cpp
QMAKE_TARGET  = plc_ladder_viewer
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = plc_ladder_viewer.exe
DESTDIR_TARGET = debug\plc_ladder_viewer.exe

####### Build rules

first: all
all: Makefile.Debug  debug/plc_ladder_viewer.exe

debug/plc_ladder_viewer.exe: C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/libQt5Widgets.a C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/libQt5Gui.a C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/libQt5Core.a C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/libqtmain.a debug/ui/ui_mainwindow.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @object_script.plc_ladder_viewer.Debug  $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ..\plc.pro

qmake_all: FORCE

dist:
	$(ZIP) plc_ladder_viewer.zip $(SOURCES) $(DIST) ..\..\plc.pro C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\spec_pre.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\qdevice.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\device_config.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\common\sanitize.conf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\common\gcc-base.conf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\common\g++-base.conf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\common\angle.conf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\win32\windows_vulkan_sdk.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\common\windows-vulkan.conf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\common\g++-win32.conf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\common\windows-desktop.conf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\qconfig.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3danimation.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3danimation_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dcore.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dcore_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dextras.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dextras_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dinput.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dinput_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dlogic.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dlogic_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquick.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquick_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickanimation.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickanimation_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickextras.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickextras_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickinput.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickinput_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickrender.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickrender_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickscene2d.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3dquickscene2d_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3drender.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_3drender_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_accessibility_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axbase.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axbase_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axcontainer.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axcontainer_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axserver.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_axserver_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_bluetooth.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_bluetooth_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_bootstrap_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_concurrent.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_concurrent_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_core.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_core_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_dbus.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_dbus_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_designer.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_designer_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_designercomponents_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_edid_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_egl_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_eventdispatcher_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_fb_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_fontdatabase_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_gamepad.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_gamepad_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_gui.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_gui_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_help.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_help_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_location.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_location_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_multimedia.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_multimedia_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_multimediawidgets.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_network.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_network_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_nfc.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_nfc_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_opengl.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_opengl_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_openglextensions.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_openglextensions_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_packetprotocol_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_platformcompositor_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_positioning.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_positioning_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_positioningquick.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_positioningquick_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_printsupport.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_printsupport_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qml.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qml_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmldebug_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmldevtools_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmlmodels.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmlmodels_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmltest.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmltest_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmlworkerscript.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_qtmultimediaquicktools_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quick_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickcontrols2.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickparticles_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickshapes_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quicktemplates2.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickwidgets.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_quickwidgets_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_remoteobjects.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_remoteobjects_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_repparser.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_repparser_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_scxml.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_scxml_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_sensors.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_sensors_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_serialbus.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_serialbus_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_serialport.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_serialport_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_sql.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_sql_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_svg.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_svg_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_testlib.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_testlib_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_texttospeech.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_texttospeech_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_theme_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_uiplugin.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_uitools.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_uitools_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_vulkan_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_webchannel.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_webchannel_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_websockets.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_websockets_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_widgets.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_widgets_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_windowsuiautomation_support_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_winextras.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_winextras_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_xml.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_xml_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_xmlpatterns.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_xmlpatterns_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\modules\qt_lib_zlib_private.pri C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\qt_functions.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\qt_config.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\win32-g++\qmake.conf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\spec_post.prf .qmake.stash C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\exclusive_builds.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\toolchain.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\default_pre.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\win32\default_pre.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\resolve_config.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\exclusive_builds_post.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\default_post.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\build_pass.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\precompile_header.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\warn_on.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\qt.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\resources_functions.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\resources.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\moc.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\win32\opengl.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\uic.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\qmake_use.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\file_copies.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\win32\windows.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\testcase_targets.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\exceptions.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\yacc.prf C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\lex.prf ..\plc.pro ..\resources.qrc C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\lib\Qt5Widgets.prl C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\lib\Qt5Gui.prl C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\lib\Qt5Core.prl C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\lib\qtmain.prl   ..\resources.qrc C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\data\dummy.cpp ..\ladderscene.h ..\ladderitem.h ..\ladderview.h ..\ladderdata.h ..\mainwindow.h ..\lad_def_compat.h  ..\main.cpp ..\ladderscene.cpp ..\ladderitem.cpp ..\ladderview.cpp ..\ladderdata.cpp ..\mainwindow.cpp ..\mainwindow.ui    

clean: compiler_clean 
	-$(DEL_FILE) debug\obj\main.o debug\obj\ladderscene.o debug\obj\ladderitem.o debug\obj\ladderview.o debug\obj\ladderdata.o debug\obj\mainwindow.o debug\obj\qrc_resources.o debug\obj\moc_ladderscene.o debug\obj\moc_ladderview.o debug\obj\moc_ladderdata.o debug\obj\moc_mainwindow.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: debug/qrc_resources.cpp
compiler_rcc_clean:
	-$(DEL_FILE) debug\qrc_resources.cpp
debug/qrc_resources.cpp: ../resources.qrc \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/bin/rcc.exe \
		../translations/ladder_en_US.qm \
		../translations/ladder_zh_CN.qm \
		../icons/save.png \
		../icons/open.png \
		../icons/debug.png \
		../icons/edit.png \
		../icons/zoom_out.png \
		../icons/zoom_in.png \
		../icons/ladder.png \
		../icons/refresh.png
	C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\rcc.exe -name resources ..\resources.qrc -o debug\qrc_resources.cpp

compiler_moc_predefs_make_all: debug/moc/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) debug\moc\moc_predefs.h
debug/moc/moc_predefs.h: C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -g -std=gnu++11 -Wall -Wextra -Wextra -dM -E -o debug\moc\moc_predefs.h C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: debug/moc/moc_ladderscene.cpp debug/moc/moc_ladderview.cpp debug/moc/moc_ladderdata.cpp debug/moc/moc_mainwindow.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc\moc_ladderscene.cpp debug\moc\moc_ladderview.cpp debug\moc\moc_ladderdata.cpp debug\moc\moc_mainwindow.cpp
debug/moc/moc_ladderscene.cpp: ../ladderscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsScene \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qflags.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstring.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qchar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpair.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvector.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrect.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsize.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qregion.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qline.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qimage.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhash.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfont.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpen.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qset.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainterpath.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QPainter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtextoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFont \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFontMetrics \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QTimer \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMap \
		../ladderdata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QObject \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QVector \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QString \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QColor \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMutex \
		../lad_def_compat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		../ladderitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStyleOptionGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyleoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractspinbox.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfile.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvalidator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregularexpression.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qicon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyle.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qrubberband.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qabstractitemmodel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QRectF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QPointF \
		debug/moc/moc_predefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/bin/moc.exe
	C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include C:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/new_project/build/debug/moc/moc_predefs.h -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/win32-g++ -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/new_project -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/api -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/plc -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/app -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/hncapp/app/src/include -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtANGLE -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore -I. -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/x86_64-w64-mingw32/include ..\ladderscene.h -o debug\moc\moc_ladderscene.cpp

debug/moc/moc_ladderview.cpp: ../ladderview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsView \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qflags.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstring.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qchar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpair.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrect.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsize.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvector.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhash.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qimage.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qregion.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qline.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtextoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpen.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfont.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qscrollarea.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractscrollarea.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qset.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfile.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsScene \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainterpath.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QMouseEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QKeyEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QWheelEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QTimer \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../ladderscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QPainter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFont \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFontMetrics \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMap \
		../ladderdata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QObject \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QVector \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QString \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QColor \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMutex \
		../lad_def_compat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		../ladderitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStyleOptionGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyleoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractspinbox.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvalidator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregularexpression.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qicon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyle.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qrubberband.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qabstractitemmodel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QRectF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QPointF \
		debug/moc/moc_predefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/bin/moc.exe
	C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include C:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/new_project/build/debug/moc/moc_predefs.h -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/win32-g++ -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/new_project -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/api -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/plc -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/app -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/hncapp/app/src/include -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtANGLE -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore -I. -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/x86_64-w64-mingw32/include ..\ladderview.h -o debug\moc\moc_ladderview.cpp

debug/moc/moc_ladderdata.cpp: ../ladderdata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QObject \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qflags.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstring.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qchar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpair.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvector.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QVector \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMap \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhash.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qset.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QString \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QColor \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMutex \
		../lad_def_compat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		debug/moc/moc_predefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/bin/moc.exe
	C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include C:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/new_project/build/debug/moc/moc_predefs.h -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/win32-g++ -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/new_project -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/api -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/plc -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/app -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/hncapp/app/src/include -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtANGLE -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore -I. -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/x86_64-w64-mingw32/include ..\ladderdata.h -o debug\moc\moc_ladderdata.cpp

debug/moc/moc_mainwindow.cpp: ../mainwindow.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QMainWindow \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qmainwindow.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qflags.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstring.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qchar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpair.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvector.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrect.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsize.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qregion.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qline.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qimage.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhash.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfont.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qset.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfile.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qicon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QVBoxLayout \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qboxlayout.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qlayout.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qlayoutitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgridlayout.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QHBoxLayout \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QScrollBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qscrollbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QLabel \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qlabel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QToolBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtoolbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qaction.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qactiongroup.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QAction \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStatusBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstatusbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QSplitter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsplitter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QTimer \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../ladderview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsView \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtextoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpen.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qscrollarea.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractscrollarea.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsScene \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainterpath.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QMouseEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QKeyEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QWheelEvent \
		../ladderscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QPainter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFont \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFontMetrics \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMap \
		../ladderdata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QObject \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QVector \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QString \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QColor \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMutex \
		../lad_def_compat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		../ladderitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStyleOptionGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyleoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractspinbox.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvalidator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregularexpression.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyle.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qrubberband.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qabstractitemmodel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QRectF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QPointF \
		debug/moc/moc_predefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/bin/moc.exe
	C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\moc.exe $(DEFINES) --include C:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/new_project/build/debug/moc/moc_predefs.h -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/win32-g++ -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/new_project -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/api -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/plc -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/include/app -IC:/home/<USER>/SoftPLC/code/other/hz-cnc-secondary-dev-master/hncapp/app/src/include -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtANGLE -IC:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore -I. -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++ -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32 -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed -IC:/home/<USER>/Qt/Qt5.15.2/Tools/mingw810_64/x86_64-w64-mingw32/include ..\mainwindow.h -o debug\moc\moc_mainwindow.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: debug/ui/ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) debug\ui\ui_mainwindow.h
debug/ui/ui_mainwindow.h: ../mainwindow.ui \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/bin/uic.exe
	C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\uic.exe ..\mainwindow.ui -o debug\ui\ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

debug/obj/main.o: ../main.cpp C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QApplication \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qapplication.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qflags.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreapplication.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstring.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qchar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpair.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvector.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qeventloop.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsize.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qdesktopwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrect.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qregion.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qline.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qimage.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhash.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfont.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qset.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfile.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qguiapplication.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qinputmethod.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStyleFactory \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstylefactory.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QDir \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdir.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfileinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QStandardPaths \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstandardpaths.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QMessageBox \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qmessagebox.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qdialog.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QTranslator \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtranslator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QLocale \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QDebug \
		../mainwindow.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QMainWindow \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qmainwindow.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qicon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QVBoxLayout \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qboxlayout.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qlayout.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qlayoutitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgridlayout.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QHBoxLayout \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QScrollBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qscrollbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QLabel \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qlabel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QToolBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtoolbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qaction.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qactiongroup.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QAction \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStatusBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstatusbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QSplitter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsplitter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QTimer \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../ladderview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsView \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtextoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpen.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qscrollarea.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractscrollarea.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsScene \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainterpath.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QMouseEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QKeyEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QWheelEvent \
		../ladderscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QPainter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFont \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFontMetrics \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMap \
		../ladderdata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QObject \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QVector \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QString \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QColor \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMutex \
		../lad_def_compat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		../ladderitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStyleOptionGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyleoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractspinbox.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvalidator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregularexpression.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyle.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qrubberband.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qabstractitemmodel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QRectF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QPointF
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\main.o ..\main.cpp

debug/obj/ladderscene.o: ../ladderscene.cpp ../ladderscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsScene \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qflags.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstring.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qchar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpair.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvector.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrect.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsize.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qregion.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qline.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qimage.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhash.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfont.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpen.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qset.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainterpath.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QPainter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtextoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFont \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFontMetrics \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QTimer \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMap \
		../ladderdata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QObject \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QVector \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QString \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QColor \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMutex \
		../lad_def_compat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		../ladderitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStyleOptionGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyleoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractspinbox.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfile.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvalidator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregularexpression.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qicon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyle.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qrubberband.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qabstractitemmodel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QRectF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QPointF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsSceneMouseEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicssceneevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QDebug \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmath.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\ladderscene.o ..\ladderscene.cpp

debug/obj/ladderitem.o: ../ladderitem.cpp ../ladderitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qflags.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstring.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qchar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpair.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvector.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhash.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qset.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrect.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsize.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainterpath.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qregion.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qline.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qimage.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QPainter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtextoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpen.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfont.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStyleOptionGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyleoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractspinbox.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfile.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvalidator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregularexpression.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qicon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyle.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qrubberband.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qabstractitemmodel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QRectF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QPointF \
		../lad_def_compat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsSceneMouseEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicssceneevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsSceneHoverEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QPainterPath \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFont \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFontMetrics \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmath.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\ladderitem.o ..\ladderitem.cpp

debug/obj/ladderview.o: ../ladderview.cpp ../ladderview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsView \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qflags.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstring.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qchar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpair.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrect.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsize.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvector.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhash.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qimage.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qregion.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qline.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtextoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpen.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfont.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qscrollarea.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractscrollarea.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qset.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfile.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsScene \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainterpath.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QMouseEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QKeyEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QWheelEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QTimer \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../ladderscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QPainter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFont \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFontMetrics \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMap \
		../ladderdata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QObject \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QVector \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QString \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QColor \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMutex \
		../lad_def_compat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		../ladderitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStyleOptionGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyleoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractspinbox.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvalidator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregularexpression.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qicon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyle.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qrubberband.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qabstractitemmodel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QRectF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QPointF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QScrollBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qscrollbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QContextMenuEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QMenu \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qmenu.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qaction.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qactiongroup.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QApplication \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qapplication.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreapplication.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qeventloop.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qdesktopwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qguiapplication.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qinputmethod.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QDebug \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmath.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\ladderview.o ..\ladderview.cpp

debug/obj/ladderdata.o: ../ladderdata.cpp ../ladderdata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QObject \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qflags.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstring.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qchar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpair.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvector.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QVector \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMap \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhash.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qset.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QString \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QColor \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMutex \
		../lad_def_compat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QFile \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfile.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QDataStream \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QDebug \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QRegularExpression \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregularexpression.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMutexLocker \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QTime \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdatetime.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QCoreApplication \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreapplication.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qeventloop.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\ladderdata.o ..\ladderdata.cpp

debug/obj/mainwindow.o: ../mainwindow.cpp ../mainwindow.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QMainWindow \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qmainwindow.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtguiglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobal.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig-bootstrapped.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qconfig.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtcore-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsystemdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qprocessordetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcompilerdetection.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtypeinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsysinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlogging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qflags.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasicatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_bootstrap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qgenericatomic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_cxx11.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qatomic_msvc.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qglobalstatic.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmutex.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnumeric.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qversiontagging.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtgui-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtwidgets-config.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qnamespace.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobjectdefs_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qwindowdefs_win.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstring.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qchar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrefcount.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qarraydata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringliteral.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringbuilder.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qalgorithms.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiterator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhashfunctions.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpair.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvector.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainertools_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qpoint.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbytearraylist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringlist.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregexp.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qstringmatcher.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qscopedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmetatype.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvarlengtharray.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontainerfwd.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qobject_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmargins.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpaintdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qrect.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsize.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpalette.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcolor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgb.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qrgba64.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qbrush.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qmatrix.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpolygon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qregion.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdatastream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qiodevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qline.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtransform.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qimage.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixelformat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpixmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qshareddata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qhash.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qsharedpointer_impl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfont.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontmetrics.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qfontinfo.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsizepolicy.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qcursor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qkeysequence.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qevent.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qvariant.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qmap.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qdebug.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtextstream.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qlocale.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qset.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcontiguouscache.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurl.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qurlquery.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfile.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qfiledevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvector2d.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtouchdevice.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qicon.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QVBoxLayout \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qboxlayout.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qlayout.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qlayoutitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgridlayout.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QHBoxLayout \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QScrollBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qscrollbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QLabel \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qlabel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qframe.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QToolBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtoolbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qaction.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qactiongroup.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QAction \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStatusBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstatusbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QSplitter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qsplitter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QTimer \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qtimer.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qbasictimer.h \
		../ladderview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsView \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsview.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainter.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtextoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpen.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qscrollarea.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractscrollarea.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsScene \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qgraphicsitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qpainterpath.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QMouseEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QKeyEvent \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QWheelEvent \
		../ladderscene.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QPainter \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFont \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QFontMetrics \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMap \
		../ladderdata.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QObject \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QVector \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QString \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/QColor \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QMutex \
		../lad_def_compat.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QtGlobal \
		../ladderitem.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QStyleOptionGraphicsItem \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyleoption.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qabstractspinbox.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qvalidator.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qregularexpression.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qslider.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qstyle.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qtabbar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qrubberband.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qabstractitemmodel.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QWidget \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QRectF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/QPointF \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QApplication \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qapplication.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qcoreapplication.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtCore/qeventloop.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qdesktopwidget.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qguiapplication.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qinputmethod.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QMenuBar \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qmenubar.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qmenu.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QMessageBox \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qmessagebox.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qdialog.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/QInputDialog \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qinputdialog.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtWidgets/qlineedit.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtextcursor.h \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/include/QtGui/qtextformat.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\mainwindow.o ..\mainwindow.cpp

debug/obj/qrc_resources.o: debug/qrc_resources.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\qrc_resources.o debug\qrc_resources.cpp

debug/obj/moc_ladderscene.o: debug/moc/moc_ladderscene.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\moc_ladderscene.o debug\moc\moc_ladderscene.cpp

debug/obj/moc_ladderview.o: debug/moc/moc_ladderview.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\moc_ladderview.o debug\moc\moc_ladderview.cpp

debug/obj/moc_ladderdata.o: debug/moc/moc_ladderdata.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\moc_ladderdata.o debug\moc\moc_ladderdata.cpp

debug/obj/moc_mainwindow.o: debug/moc/moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\obj\moc_mainwindow.o debug\moc\moc_mainwindow.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

