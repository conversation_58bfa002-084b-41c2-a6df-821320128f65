﻿#ifndef NCTABLEONEPAGE_H
#define NCTABLEONEPAGE_H

#include <QWidget>
#include <QTableWidget>
#include <QScrollBar>
#include "hncdatatype.h"
#include "msgdata.h"

namespace Ui {
class NcTableOnePage;
}

class NcTableOnePage : public QWidget
{
    Q_OBJECT

public:
    enum ItemStyle
    {
        ComboBoxStyle = Qt::UserRole + 1, // 单元格是否为下拉框
    };

    explicit NcTableOnePage(QWidget *parent = 0);
    ~NcTableOnePage();

    void SetTotalDataRowNum(int row); // 设置数据总数
    void SetTableRowNum(int row); // 设置表格行数
    Bit32 GetTotalDataRowNum(); // 获取数据总数
    void SetTableItemRole(int row, int col, int role, QVariant &val);
    void TableConnectSlot();
    void GoToDataRow(int idx);
    QTableWidget *GetTable();
    QScrollBar *GetScrollBar();
    bool eventFilter(QObject *, QEvent *);
    bool KeyPressResponce(int key);
    void SetCurrentIdx(Bit32 idx);
    void TableItemSetFocus();


signals:
    void StartIdxChg(int idx); // 首行改变后需重新载入表格数据
private slots:
    void ScrollValChgResponse(int row);
    void TableSelRowSet(int curRow, int , int previousRow, int);

private:
    Ui::NcTableOnePage *ui;

    void CheckTableData();
//    void TableItemSetFocus();
    void ScrollBarReset();
    void resizeTableRowHeight();
//    bool KeyPressResponce(int key);

    int pageRowNum;
    int totalRowNum;
    int selIdx;
    int startIdx;
};

#endif // NCTABLEONEPAGE_H
