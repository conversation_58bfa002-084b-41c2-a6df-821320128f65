#############################################################################
# Makefile for building: plc_ladder_viewer
# Generated by qmake (3.1) (Qt 5.15.2)
# Project:  ..\plc.pro
# Template: app
# Command: C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\qmake.exe -o Makefile ..\plc.pro
#############################################################################

MAKEFILE      = Makefile

EQ            = =

first: release
install: release-install
uninstall: release-uninstall
QMAKE         = C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = C:\home\software\Qt\Qt5.15.2\5.15.2\mingw81_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move
SUBTARGETS    =  \
		release \
		debug


release: FORCE
	$(MAKE) -f $(MAKEFILE).Release
release-make_first: FORCE
	$(MAKE) -f $(MAKEFILE).Release 
release-all: FORCE
	$(MAKE) -f $(MAKEFILE).Release all
release-clean: FORCE
	$(MAKE) -f $(MAKEFILE).Release clean
release-distclean: FORCE
	$(MAKE) -f $(MAKEFILE).Release distclean
release-install: FORCE
	$(MAKE) -f $(MAKEFILE).Release install
release-uninstall: FORCE
	$(MAKE) -f $(MAKEFILE).Release uninstall
debug: FORCE
	$(MAKE) -f $(MAKEFILE).Debug
debug-make_first: FORCE
	$(MAKE) -f $(MAKEFILE).Debug 
debug-all: FORCE
	$(MAKE) -f $(MAKEFILE).Debug all
debug-clean: FORCE
	$(MAKE) -f $(MAKEFILE).Debug clean
debug-distclean: FORCE
	$(MAKE) -f $(MAKEFILE).Debug distclean
debug-install: FORCE
	$(MAKE) -f $(MAKEFILE).Debug install
debug-uninstall: FORCE
	$(MAKE) -f $(MAKEFILE).Debug uninstall

Makefile: ../plc.pro C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/win32-g++/qmake.conf C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/spec_pre.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/qdevice.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/device_config.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/sanitize.conf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/gcc-base.conf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/g++-base.conf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/angle.conf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/win32/windows_vulkan_sdk.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/windows-vulkan.conf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/g++-win32.conf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/windows-desktop.conf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/qconfig.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3danimation.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3danimation_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dcore.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dcore_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dextras.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dextras_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dinput.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dinput_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dlogic.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dlogic_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquick.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquick_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickanimation.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickextras.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickextras_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickinput.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickinput_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickrender.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickrender_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickscene2d.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3drender.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3drender_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axbase.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axbase_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axcontainer.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axcontainer_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axserver.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axserver_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_bluetooth.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_bluetooth_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_bootstrap_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_concurrent.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_concurrent_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_core.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_core_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_dbus.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_dbus_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_designer.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_designer_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_designercomponents_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_edid_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_egl_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_fb_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_gamepad.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_gamepad_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_gui.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_gui_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_help.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_help_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_location.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_location_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_multimedia.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_multimedia_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_multimediawidgets.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_network.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_network_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_nfc.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_nfc_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_opengl.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_opengl_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_openglextensions.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_openglextensions_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_packetprotocol_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_positioning.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_positioning_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_positioningquick.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_positioningquick_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_printsupport.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_printsupport_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qml.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qml_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmldebug_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmldevtools_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmlmodels.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmlmodels_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmltest.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmltest_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmlworkerscript.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quick.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quick_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickcontrols2.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickparticles_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickshapes_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quicktemplates2.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickwidgets.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickwidgets_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_remoteobjects.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_remoteobjects_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_repparser.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_repparser_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_scxml.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_scxml_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_sensors.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_sensors_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_serialbus.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_serialbus_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_serialport.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_serialport_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_sql.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_sql_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_svg.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_svg_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_testlib.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_testlib_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_texttospeech.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_texttospeech_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_theme_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_uiplugin.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_uitools.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_uitools_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_webchannel.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_webchannel_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_websockets.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_websockets_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_widgets.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_widgets_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_windowsuiautomation_support_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_winextras.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_winextras_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_xml.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_xml_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_xmlpatterns.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_zlib_private.pri \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/qt_functions.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/qt_config.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/win32-g++/qmake.conf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/spec_post.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/exclusive_builds.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/toolchain.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/default_pre.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/win32/default_pre.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/resolve_config.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/exclusive_builds_post.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/default_post.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/precompile_header.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/warn_on.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/qt.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/resources_functions.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/resources.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/moc.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/win32/opengl.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/uic.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/qmake_use.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/file_copies.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/win32/windows.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/testcase_targets.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/exceptions.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/yacc.prf \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/lex.prf \
		../plc.pro \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/Qt5Widgets.prl \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/Qt5Gui.prl \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/Qt5Core.prl \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/qtmain.prl \
		.qmake.stash \
		C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/build_pass.prf \
		../resources.qrc
	$(QMAKE) -o Makefile ..\plc.pro
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/spec_pre.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/qdevice.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/device_config.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/sanitize.conf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/gcc-base.conf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/g++-base.conf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/angle.conf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/win32/windows_vulkan_sdk.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/windows-vulkan.conf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/g++-win32.conf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/common/windows-desktop.conf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/qconfig.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3danimation.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3danimation_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dcore.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dcore_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dextras.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dextras_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dinput.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dinput_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dlogic.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dlogic_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquick.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquick_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickanimation.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickanimation_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickextras.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickextras_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickinput.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickinput_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickrender.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickrender_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickscene2d.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3dquickscene2d_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3drender.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_3drender_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_accessibility_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axbase.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axbase_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axcontainer.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axcontainer_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axserver.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_axserver_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_bluetooth.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_bluetooth_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_bootstrap_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_concurrent.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_concurrent_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_core.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_core_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_dbus.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_dbus_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_designer.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_designer_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_designercomponents_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_edid_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_egl_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_fb_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_gamepad.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_gamepad_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_gui.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_gui_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_help.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_help_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_location.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_location_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_multimedia.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_multimedia_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_multimediawidgets.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_multimediawidgets_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_network.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_network_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_nfc.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_nfc_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_opengl.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_opengl_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_openglextensions.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_openglextensions_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_packetprotocol_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_positioning.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_positioning_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_positioningquick.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_positioningquick_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_printsupport.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_printsupport_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qml.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qml_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmldebug_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmldevtools_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmlmodels.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmlmodels_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmltest.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmltest_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmlworkerscript.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qmlworkerscript_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_qtmultimediaquicktools_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quick.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quick_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickcontrols2.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickcontrols2_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickparticles_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickshapes_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quicktemplates2.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quicktemplates2_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickwidgets.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_quickwidgets_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_remoteobjects.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_remoteobjects_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_repparser.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_repparser_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_scxml.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_scxml_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_sensors.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_sensors_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_serialbus.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_serialbus_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_serialport.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_serialport_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_sql.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_sql_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_svg.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_svg_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_testlib.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_testlib_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_texttospeech.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_texttospeech_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_theme_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_uiplugin.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_uitools.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_uitools_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_vulkan_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_webchannel.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_webchannel_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_websockets.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_websockets_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_widgets.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_widgets_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_windowsuiautomation_support_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_winextras.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_winextras_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_xml.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_xml_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_xmlpatterns.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_xmlpatterns_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/modules/qt_lib_zlib_private.pri:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/qt_functions.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/qt_config.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/win32-g++/qmake.conf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/spec_post.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/exclusive_builds.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/toolchain.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/default_pre.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/win32/default_pre.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/resolve_config.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/exclusive_builds_post.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/default_post.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/precompile_header.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/warn_on.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/qt.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/resources_functions.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/resources.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/moc.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/win32/opengl.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/uic.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/qmake_use.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/file_copies.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/win32/windows.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/testcase_targets.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/exceptions.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/yacc.prf:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/lex.prf:
../plc.pro:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/Qt5Widgets.prl:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/Qt5Gui.prl:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/Qt5Core.prl:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/lib/qtmain.prl:
.qmake.stash:
C:/home/<USER>/Qt/Qt5.15.2/5.15.2/mingw81_64/mkspecs/features/build_pass.prf:
../resources.qrc:
qmake: FORCE
	@$(QMAKE) -o Makefile ..\plc.pro

qmake_all: FORCE

make_first: release-make_first debug-make_first  FORCE
all: release-all debug-all  FORCE
clean: release-clean debug-clean  FORCE
distclean: release-distclean debug-distclean  FORCE
	-$(DEL_FILE) Makefile
	-$(DEL_FILE) .qmake.stash

release-mocclean:
	$(MAKE) -f $(MAKEFILE).Release mocclean
debug-mocclean:
	$(MAKE) -f $(MAKEFILE).Debug mocclean
mocclean: release-mocclean debug-mocclean

release-mocables:
	$(MAKE) -f $(MAKEFILE).Release mocables
debug-mocables:
	$(MAKE) -f $(MAKEFILE).Debug mocables
mocables: release-mocables debug-mocables

check: first

benchmark: first
FORCE:

$(MAKEFILE).Release: Makefile
$(MAKEFILE).Debug: Makefile
