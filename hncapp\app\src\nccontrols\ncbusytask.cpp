﻿#include "ncbusytask.h"

NcBusyTask::NcBusyTask(QObject *parent)
    : QThread(parent)
{
    m_nTaskSleep = 10;

    m_bStop = false;
}

NcBusyTask::~NcBusyTask()
{

}

void NcBusyTask::run()
{
    while (!m_bStop)
    {
        TaskDo();

        if (TaskIsFinished())
        {
            m_bStop = true;
            emit SignalTaskFinish();
            TaskEnd();
            this->exit();
            break;
        }

        QThread::msleep(m_nTaskSleep);
    }
}

void NcBusyTask::TaskInit()
{

}

void NcBusyTask::TaskEnd()
{

}

void NcBusyTask::TaskPreEnd()
{

}

QVariant NcBusyTask::TaskProgress()
{
    return QVariant();
}

void NcBusyTask::RunTask()
{
    if (!this->isRunning())
    {
        m_bStop = false;
        TaskInit();

        this->start();
    }
}

bool NcBusyTask::IsRunning()
{
    if (this->isRunning() && m_bStop == false)
    {
        return true;
    }
    return false;
}

void NcBusyTask::StopTask()
{
    if (this->IsRunning())
    {
        m_bStop = true;
        TaskPreEnd();

        this->exit();
        emit SignalTaskForceEnd();
        TaskEnd();
    }
}

void NcBusyTask::SetTaskSleep(int ms)
{
    if (ms <= 0)
    {
        ms = 1;
    }
    m_nTaskSleep = ms;
}

QVariant NcBusyTask::GetTaskProgress()
{
    return TaskProgress();
}
