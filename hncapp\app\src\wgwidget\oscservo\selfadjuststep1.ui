﻿<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SelfAdjustStep1</class>
 <widget class="QWidget" name="SelfAdjustStep1">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>681</width>
    <height>541</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <property name="spacing">
    <number>0</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="backgrd">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout_2" rowstretch="1,1,1,1,7,1,3">
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,1">
        <item>
         <widget class="QLabel" name="label">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="text">
           <string>轴选择 （上下键选择自整定的轴，并手动移动轴到安全位置）</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_4">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>75</weight>
            <bold>true</bold>
           </font>
          </property>
          <property name="text">
           <string>1 / 4</string>
          </property>
          <property name="textFormat">
           <enum>Qt::AutoText</enum>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="1" column="0">
       <widget class="Line" name="line">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QSlider" name="horizontalSlider">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="styleSheet">
         <string notr="true">border-image: url(:/pic/adjust/pos.png)</string>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="2,2,1">
        <item>
         <widget class="QLabel" name="label_2">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>负限位</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_5">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="text">
           <string>当前位置</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_3">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="layoutDirection">
           <enum>Qt::LeftToRight</enum>
          </property>
          <property name="text">
           <string>正限位</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item row="4" column="0">
       <widget class="QTableWidget" name="tableWidget"/>
      </item>
      <item row="5" column="0">
       <widget class="QLabel" name="labelTitle">
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>12</pointsize>
         </font>
        </property>
        <property name="text">
         <string>示意图</string>
        </property>
       </widget>
      </item>
      <item row="6" column="0">
       <widget class="QLabel" name="picLb">
        <property name="text">
         <string>TextLabel</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
