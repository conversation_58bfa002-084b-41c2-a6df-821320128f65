#include "ladderdata.h"
#include <QFile>
#include <QDataStream>
#include <QDebug>
#include <QRegularExpression>
#include <QMutexLocker>
#include <QTime>
#include <QCoreApplication>
#include <cstring>

LadderData::LadderData(QObject *parent)
    : QObject(parent)
    , m_totalRows(0)
    , m_currentSub(0)
    , m_debugMode(false)
    , m_editMode(false)
{
    initializeData();
    initializeColors();
    generateTestData(); // 生成测试数据
}

LadderData::~LadderData()
{
    clearData();
}

void LadderData::initializeData()
{
    m_totalRows = 100; // 默认100行
    m_ladderRows.resize(m_totalRows);
    m_changeFlags.resize(m_totalRows);
    
    for (int i = 0; i < m_totalRows; ++i) {
        m_ladderRows[i].cells.resize(CELL_PER_ROW);
        m_ladderRows[i].hasData = false;
        m_changeFlags[i].resize(CELL_PER_ROW);
        m_changeFlags[i].fill(false);
        
        // 初始化每个单元格
        for (int j = 0; j < CELL_PER_ROW; ++j) {
            CellData &cellData = m_ladderRows[i].cells[j];
            memset(&cellData.cell, 0, sizeof(SLadCell));
            cellData.state = CELL_STATE_OFF;
            cellData.forced = false;
            cellData.forceState = CELL_STATE_OFF;
            cellData.lastValue = 0;
            cellData.changed = false;
        }
    }
}

void LadderData::initializeColors()
{
    m_colors[LAD_COLOR_DEFAULT] = QColor(0, 0, 0);          // 黑色
    m_colors[LAD_COLOR_BACKGROUND] = QColor(255, 255, 255); // 白色
    m_colors[LAD_COLOR_ACTIVE] = QColor(255, 0, 0);         // 红色
    m_colors[LAD_COLOR_INACTIVE] = QColor(128, 128, 128);   // 灰色
    m_colors[LAD_COLOR_ALLOW] = QColor(0, 255, 0);          // 绿色
    m_colors[LAD_COLOR_FORBID] = QColor(255, 165, 0);       // 橙色
    m_colors[LAD_COLOR_SELECTED] = QColor(0, 0, 255);       // 蓝色
}

bool LadderData::loadFromFile(const QString &filename)
{
    QMutexLocker locker(&m_dataMutex);
    
    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Cannot open file for reading:" << filename;
        return false;
    }
    
    QDataStream stream(&file);
    
    // 读取文件头
    quint32 magic;
    stream >> magic;
    if (magic != 0x4C414444) { // "LADD"
        qWarning() << "Invalid file format";
        return false;
    }
    
    // 读取版本和基本信息
    quint16 version;
    stream >> version >> m_totalRows >> m_currentSub;
    
    // 重新初始化数据结构
    initializeData();
    
    // 读取梯形图数据
    for (int i = 0; i < m_totalRows; ++i) {
        bool hasData;
        stream >> hasData;
        m_ladderRows[i].hasData = hasData;
        
        if (hasData) {
            for (int j = 0; j < CELL_PER_ROW; ++j) {
                SLadCell &cell = m_ladderRows[i].cells[j].cell;
                stream >> cell.parmmask >> cell.parallel >> cell.rectmask
                       >> cell.w_pos >> cell.h_pos >> cell.cmdID >> cell.cmd_idx
                       >> cell.reg.index >> cell.reg.reg_type >> cell.reg.bit;
            }
        }
    }
    
    return true;
}

bool LadderData::saveToFile(const QString &filename)
{
    QMutexLocker locker(&m_dataMutex);
    
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Cannot open file for writing:" << filename;
        return false;
    }
    
    QDataStream stream(&file);
    
    // 写入文件头
    stream << quint32(0x4C414444); // "LADD"
    stream << quint16(1) << m_totalRows << m_currentSub;
    
    // 写入梯形图数据
    for (int i = 0; i < m_totalRows; ++i) {
        stream << m_ladderRows[i].hasData;
        
        if (m_ladderRows[i].hasData) {
            for (int j = 0; j < CELL_PER_ROW; ++j) {
                const SLadCell &cell = m_ladderRows[i].cells[j].cell;
                stream << cell.parmmask << cell.parallel << cell.rectmask
                       << cell.w_pos << cell.h_pos << cell.cmdID << cell.cmd_idx
                       << cell.reg.index << cell.reg.reg_type << cell.reg.bit;
            }
        }
    }
    
    return true;
}

void LadderData::clearData()
{
    QMutexLocker locker(&m_dataMutex);
    
    m_ladderRows.clear();
    m_symbols.clear();
    m_registers.clear();
    m_changeFlags.clear();
    m_totalRows = 0;
}

void LadderData::setCurrentSubProgram(int sub)
{
    if (m_currentSub != sub) {
        m_currentSub = sub;
        emit subProgramChanged(sub);
    }
}

bool LadderData::getLadderRow(int row, SLadRow &ladRow)
{
    QMutexLocker locker(&m_dataMutex);
    
    if (row < 0 || row >= m_totalRows) {
        return false;
    }
    
    if (!m_ladderRows[row].hasData) {
        memset(&ladRow, 0, sizeof(SLadRow));
        return true;
    }
    
    for (int i = 0; i < CELL_PER_ROW; ++i) {
        ladRow.dft_cell[i] = m_ladderRows[row].cells[i].cell;
    }
    
    return true;
}

bool LadderData::setLadderRow(int row, const SLadRow &ladRow)
{
    QMutexLocker locker(&m_dataMutex);
    
    if (row < 0 || row >= m_totalRows) {
        return false;
    }
    
    m_ladderRows[row].hasData = true;
    for (int i = 0; i < CELL_PER_ROW; ++i) {
        m_ladderRows[row].cells[i].cell = ladRow.dft_cell[i];
    }
    
    emit rowDataChanged(row);
    return true;
}

bool LadderData::getLadderCell(int row, int col, SLadCell &cell)
{
    QMutexLocker locker(&m_dataMutex);
    
    if (row < 0 || row >= m_totalRows || col < 0 || col >= CELL_PER_ROW) {
        return false;
    }
    
    cell = m_ladderRows[row].cells[col].cell;
    return true;
}

bool LadderData::setLadderCell(int row, int col, const SLadCell &cell)
{
    QMutexLocker locker(&m_dataMutex);
    
    if (row < 0 || row >= m_totalRows || col < 0 || col >= CELL_PER_ROW) {
        return false;
    }
    
    m_ladderRows[row].cells[col].cell = cell;
    m_ladderRows[row].hasData = true;
    
    emit dataChanged(row, col);
    return true;
}

CellState LadderData::getCellState(int row, int col)
{
    QMutexLocker locker(&m_dataMutex);
    
    if (row < 0 || row >= m_totalRows || col < 0 || col >= CELL_PER_ROW) {
        return CELL_STATE_OFF;
    }
    
    const CellData &cellData = m_ladderRows[row].cells[col];
    
    // 如果有强制状态，返回强制状态
    if (cellData.forced) {
        return cellData.forceState;
    }
    
    // 否则返回计算的状态
    return calculateCellState(cellData.cell);
}

QColor LadderData::getCellColor(int row, int col)
{
    CellState state = getCellState(row, col);
    return getCellStateColor(state);
}

bool LadderData::isCellActive(int row, int col)
{
    CellState state = getCellState(row, col);
    return (state == CELL_STATE_ON || state == CELL_STATE_FORCE_ON);
}

quint32 LadderData::getRegisterValue(const SLadReg &reg)
{
    QString regKey = formatRegisterText(reg);
    return m_registers.value(regKey, 0);
}

bool LadderData::setRegisterValue(const SLadReg &reg, quint32 value)
{
    QString regKey = formatRegisterText(reg);
    m_registers[regKey] = value;
    return true;
}

CellState LadderData::calculateCellState(const SLadCell &cell)
{
    if (cell.cmdID == I_VOR || cell.cmdID == 0) {
        return CELL_STATE_OFF;
    }
    
    // 获取寄存器值
    quint32 regValue = getRegisterValue(cell.reg);
    
    // 根据指令类型计算状态
    switch (cell.cmdID) {
        case I_LD:      // 常开触点
        case I_AND:     // 与
        case I_OR:      // 或
            return (regValue != 0) ? CELL_STATE_ON : CELL_STATE_OFF;
            
        case I_LDI:     // 常闭触点
        case I_ANI:     // 与非
        case I_ORI:     // 或非
            return (regValue == 0) ? CELL_STATE_ON : CELL_STATE_OFF;
            
        case I_OUT:     // 输出线圈
        case I_SET:     // 置位
        case I_RST:     // 复位
            return (regValue != 0) ? CELL_STATE_ON : CELL_STATE_OFF;
            
        default:
            return CELL_STATE_OFF;
    }
}

QColor LadderData::getCellStateColor(CellState state, bool selected)
{
    if (selected) {
        return m_colors[LAD_COLOR_SELECTED];
    }
    
    switch (state) {
        case CELL_STATE_ON:
            return m_colors[LAD_COLOR_ACTIVE];
        case CELL_STATE_FORCE_ON:
            return m_colors[LAD_COLOR_ALLOW];
        case CELL_STATE_FORCE_OFF:
            return m_colors[LAD_COLOR_FORBID];
        default:
            return m_colors[LAD_COLOR_INACTIVE];
    }
}

QString LadderData::formatRegisterText(const SLadReg &reg)
{
    QString regText;
    
    // 根据寄存器类型格式化
    switch (reg.reg_type) {
        case 'X': case 'x':
            regText = QString("X%1").arg(reg.index);
            break;
        case 'Y': case 'y':
            regText = QString("Y%1").arg(reg.index);
            break;
        case 'R': case 'r':
            regText = QString("R%1").arg(reg.index);
            break;
        case 'D': case 'd':
            regText = QString("D%1").arg(reg.index);
            break;
        default:
            regText = QString("%1%2").arg(QChar(reg.reg_type)).arg(reg.index);
            break;
    }
    
    // 添加位号
    if (reg.bit >= 0 && reg.bit < 16) {
        regText += QString(".%1").arg(reg.bit);
    }
    
    return regText;
}

void LadderData::generateTestData()
{
    // 生成一些测试数据
    for (int row = 0; row < 20; ++row) {
        m_ladderRows[row].hasData = true;
        
        for (int col = 0; col < CELL_PER_ROW; ++col) {
            SLadCell &cell = m_ladderRows[row].cells[col].cell;
            
            if (col == 0) {
                // 第一列放常开触点
                cell.cmdID = I_LD;
                cell.reg.reg_type = 'X';
                cell.reg.index = row * 10 + col;
                cell.reg.bit = -1;
            } else if (col == CELL_PER_ROW - 1) {
                // 最后一列放输出线圈
                cell.cmdID = I_OUT;
                cell.reg.reg_type = 'Y';
                cell.reg.index = row;
                cell.reg.bit = -1;
            } else if (col % 3 == 1) {
                // 中间放一些与触点
                cell.cmdID = I_AND;
                cell.reg.reg_type = 'X';
                cell.reg.index = row * 10 + col;
                cell.reg.bit = -1;
            } else {
                // 其他位置放连线
                cell.cmdID = I_LINE;
            }
            
            cell.parmmask = (cell.cmdID != I_VOR && cell.cmdID != I_LINE) ? 1 : 0;
            cell.parallel = 0;
            cell.rectmask = 0;
            cell.w_pos = col;
            cell.h_pos = 0;
            cell.cmd_idx = row * CELL_PER_ROW + col;
        }
    }
    
    // 模拟一些寄存器值
    simulateRegisterValues();
}

void LadderData::simulateRegisterValues()
{
    // 使用当前时间作为随机种子
    qsrand(QTime::currentTime().msec());

    // 模拟X寄存器值
    for (int i = 0; i < 200; ++i) {
        QString regKey = QString("X%1").arg(i);
        m_registers[regKey] = qrand() % 2; // 0或1
    }

    // 模拟Y寄存器值
    for (int i = 0; i < 50; ++i) {
        QString regKey = QString("Y%1").arg(i);
        m_registers[regKey] = qrand() % 2; // 0或1
    }
}
