<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>NcProgress</class>
 <widget class="QWidget" name="NcProgress">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>80</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>40</width>
    <height>20</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="autoFillBackground">
   <bool>true</bool>
  </property>
  <widget class="QWidget" name="widget" native="true">
   <property name="geometry">
    <rect>
     <x>-10</x>
     <y>-30</y>
     <width>400</width>
     <height>80</height>
    </rect>
   </property>
  </widget>
  <widget class="QLabel" name="label_per">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>40</y>
     <width>382</width>
     <height>28</height>
    </rect>
   </property>
   <property name="palette">
    <palette>
     <active>
      <colorrole role="WindowText">
       <brush brushstyle="SolidPattern">
        <color alpha="255">
         <red>255</red>
         <green>255</green>
         <blue>255</blue>
        </color>
       </brush>
      </colorrole>
     </active>
     <inactive>
      <colorrole role="WindowText">
       <brush brushstyle="SolidPattern">
        <color alpha="255">
         <red>255</red>
         <green>255</green>
         <blue>255</blue>
        </color>
       </brush>
      </colorrole>
     </inactive>
     <disabled>
      <colorrole role="WindowText">
       <brush brushstyle="SolidPattern">
        <color alpha="255">
         <red>120</red>
         <green>120</green>
         <blue>120</blue>
        </color>
       </brush>
      </colorrole>
     </disabled>
    </palette>
   </property>
   <property name="font">
    <font>
     <family>微软雅黑</family>
     <pointsize>12</pointsize>
    </font>
   </property>
   <property name="text">
    <string/>
   </property>
   <property name="alignment">
    <set>Qt::AlignCenter</set>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
