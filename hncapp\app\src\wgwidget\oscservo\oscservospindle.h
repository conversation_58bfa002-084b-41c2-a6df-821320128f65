﻿#ifndef OSCSERVOSPINDLE_H
#define OSCSERVOSPINDLE_H

#include "containerwidget.h"

namespace Ui {
class OscServoSpindle;
}

QT_BEGIN_NAMESPACE
class DlgMsgBox;
class OscWave;
class OscList;
QT_END_NAMESPACE

class OscServoSpindle : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoSpindle(QWidget *parent = 0);
    ~OscServoSpindle();

    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
//#ifdef _NCUPPER_
//    bool eventFilter(QObject *target, QEvent *event);
//    void resizeEvent(QResizeEvent *);
//#endif

private:
    Ui::OscServoSpindle *ui;

    OscWave *pOscSpdl;
    OscList *spdlOscList;
    Bit32 lastEndPos;
    DlgMsgBox *msgBox;
    bool m_bStartFlag;          // 采样启动标志
//#ifdef _NCUPPER_
//    bool firstFlag;
//#endif

    void SetColorStyle();
    void LoadInfo(bool flag);
    void Reset();
    void Refresh();
    QStringList GetParmList();
};

#endif // OSCSERVOSPINDLE_H
