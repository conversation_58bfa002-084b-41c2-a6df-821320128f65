﻿#ifndef NCLIGHTWIDGET_H
#define NCLIGHTWIDGET_H

#include <QLabel>
#include <QPixmap>

#include "hncdatatype.h"

class NcLightWidget : public QLabel
{
    Q_OBJECT
public:
    typedef enum _NCLIGHT_TYPE
    {
        LIGHT_DEF,
        LIGHT_GREEN,
        LIGHT_RED,
        LIGHT_GRAY,
    }NCLIGHT_TYPE;
    explicit NcLightWidget(QWidget *parent = 0);
    void SetColor(NCLIGHT_TYPE t);
private:
    Bit32 m_lightType;

    void CreatePixmap(QColor color, QPixmap **pixmap);
    void CreateRingPixmap(QColor color, QPixmap **pixmap);
};

#endif // NCLIGHTWIDGET_H
