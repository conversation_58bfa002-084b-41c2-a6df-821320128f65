﻿#ifndef OSCSERVORASTER_H
#define OSCSERVORASTER_H

#include <QWidget>
#include <QComboBox>
#include <QDateTime>

#include "osclist.h"
#include "nccombobox.h"
#include "nccheckbox.h"

#include "containerwidget.h"

namespace Ui {
class OscServoRaster;
}

QT_BEGIN_NAMESPACE
class OscWave;
class RoundWave;
QT_END_NAMESPACE

class OscServoRaster : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoRaster(QWidget *parent = 0);
    ~OscServoRaster();

    void SetColorStyle();

    static bool GetSmplFlag();

    void SetSamplingState(Bit32 state);

signals:
    void SignalClearFocus();

protected:
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);

    bool eventFilter(QObject *target, QEvent *event);
    void resizeEvent(QResizeEvent *);

    void WidgetIn();
    void WidgetExit();

    Bit32 WidgetInChk();

private:
    Ui::OscServoRaster *ui;

    enum _FOCUS_
    {
        NOFOCUS = 0,
        CHECKBOX,
        COMBOBOX,
        TABLELIST,
    };

    enum _SAMPLING_
    {
        NONE = 0,
        MANUAL,
        AUTO,
    };

    enum _ENCODE_COMPENSATE
    {
        NONEENCODE = 0,
        FIRSTENCODE,
        SECONDENCODE,
    };

    //OscList *posOscList;
    QVector<fBit64> vec_OldDataX;
    QVector<fBit64> vec_OldDataSin;
    QVector<fBit64> vec_OldDataCos;
    QVector<fBit64> vec_OldRoundX;
    QVector<fBit64> vec_OldRoundY;
    QVector<fBit64> vec_DataX;
    QVector<fBit64> vec_DataSin;
    QVector<fBit64> vec_DataCos;
    QVector<fBit64> vec_RoundX;
    QVector<fBit64> vec_RoundY;

    OscWave *pOscWavePos;
    RoundWave *pOscWaveRound;
    RoundWave *pOscWaveRoundAfter;
    Bit32 lastEndPos;
    OscList *m_pRasterOscList;
    NcCheckBox *m_pUnit;
    NcComboBox *m_pEnCode;
    Bit32 m_nCurFocus;
    Bit32 m_nSamplingState;
    bool m_bSampleStart;		// 采样开始

    Bit32 m_nCompensate;

    static bool m_bSampleFlag;     // 采样标志

    bool firstFlag;
    Bit32 m_nOldCount;

    void Refresh(bool flag = false);
    void Reset();
    //QStringList GetParmList();
    void LoadAxisVal(Bit32 type);
    void OnBtFlagChange();
    void ResetInfo(Bit32 flag = -1); // -1:全部重置 0:重置补偿前 1:重置补偿后
    void TestAddPoint();
    void TestWriteFile(QVector<fBit64> posActSin, QVector<fBit64> posActCos,
                       QVector<fBit64> sinAmpl, QVector<fBit64> sinZero,
                       QVector<fBit64> cosAmpl, QVector<fBit64> cosZero);
    void TestReadFile();
    void TestLoadInfo();

    fBit64 GetListAve(QVector<fBit64> list);
    void SetOffValToParam(fBit64 sinOffVal, fBit64 cosOffVal, fBit64 offVal);
    void RefresWidget();
    QStringList GetParmList();

    void RecordData();

    void GetMaxAndMin(QVector<fBit64> vec, fBit64 &max, fBit64 &min);
    QString GetShowInfo(fBit64 max = 0.0, fBit64 min = 0.0, Bit32 prec = 1);
    void SetLabelText(QString trackErr, QString sinZero, QString cosZero, QString sinAmpl, QString cosAmpl, QString phaseCh, Bit32 flag); // flag:0 补偿前 1:补偿后
    void RedrawTitleLabel();
    void SetValue();
    void LoadInfo(QVector<fBit64> sin, QVector<fBit64> cos, Bit32 flag);// flag:0 补偿前 1:补偿后

    void ExportPos(QVector<fBit64> oldSin, QVector<fBit64> oldCos, QVector<fBit64> sin, QVector<fBit64> cos);

    void InitComboBox();
    void ChangeEnCode();

    bool CheckMotorVersion();
    void GetCompensate(bool flag = false);
    void SetFocus();
private slots:

    void on_leftBtn_clicked();
    void on_rightBtn_clicked();

    void SlotUnitChange();
    void SlotEnCodeChange();
    void RedrawLineAndRound();
};

#endif // OSCSERVORASTER_H
