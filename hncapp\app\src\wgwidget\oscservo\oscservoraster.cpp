﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservoraster.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-光栅信号检测界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年5月24日
* 说    明：
*/

#include <qmath.h>
#include <algorithm>
#include <QVector>
//#include <complex>

#include "hncaxis.h"
#include "hncchan.h"
#include "hncchandef.h"
#include "hncmath.h"
#include "hncparaman.h"
#include "hncsmpl.h"
#include "hncsys.h"
#include "hncsysctrl.h"
#include "hncsysctrldef.h"
#include "loadsave.h"
#include "ncassert.h"
#include "passwd.h"
#include "smplcalc.h"

#include "common.h"
#include "hotkeycfg.h"
#include "hmiparaman.h"
#include "hmioscproc.h"
#include "hmioscservo.h"
#include "hmimenumanage.h"
#include "filemanage.h"
#include "oscwave.h"
#include "roundwave.h"

#include "oscservoraster.h"
#include "ui_oscservoraster.h"

#define UNITAGE (1800.0)
#define ROUND_UNITAGE (1700.0)

bool OscServoRaster::m_bSampleFlag = false;

OscServoRaster::OscServoRaster(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoRaster)
{
    ui->setupUi(this);

    m_nCompensate = NONEENCODE;
    m_nCurFocus = NOFOCUS;
    m_nSamplingState = NONE;
    vec_OldDataX.clear();
    vec_OldDataSin.clear();
    vec_OldDataCos.clear();
    vec_DataX.clear();
    vec_DataSin.clear();
    vec_DataCos.clear();
    vec_OldRoundX.clear();
    vec_OldRoundY.clear();
    vec_RoundX.clear();
    vec_RoundY.clear();
    pOscWavePos = new OscWave(this, HmiOscServo::OSC_SERVO_RASTER, "POS");
    pOscWaveRound = new RoundWave(this);
    pOscWaveRoundAfter = new RoundWave(this);
    m_pRasterOscList = new OscList(this);
    m_pRasterOscList->SetEditAgent(true);
    ui->verticalLayout_5->addWidget(m_pRasterOscList);
//    ui->widget_3->setHidden(true);
    pOscWaveRound->SetAxiNameX(TR(""));
    pOscWaveRound->SetAxiNameY(TR(""));

    Bit32 ch = ActiveChan();
    pOscWaveRound->SetParm(TR(""), TR(""));
    pOscWaveRound->SetScaleAreaRadius(HmiOscServo::s_Conf[ch].stRasterConf.rad);
    pOscWaveRound->LineSetRadius(1);
    pOscWaveRound->SetStdCircleRadius(40);
    pOscWaveRound->SetScale(100);
    pOscWaveRound->SetScaleAreaText(TR("补偿前"));
    pOscWaveRound->replot();

    pOscWaveRoundAfter->SetAxiNameX(TR(""));
    pOscWaveRoundAfter->SetAxiNameY(TR(""));

    pOscWaveRoundAfter->SetParm(TR(""), TR(""));
    pOscWaveRoundAfter->SetScaleAreaRadius(HmiOscServo::s_Conf[ch].stRasterConf.rad);
    pOscWaveRoundAfter->LineSetRadius(1);
    pOscWaveRoundAfter->SetStdCircleRadius(40);
    pOscWaveRoundAfter->SetScale(100);
    pOscWaveRoundAfter->SetScaleAreaText(TR("补偿后"));
    pOscWaveRoundAfter->replot();

    ui->verticalLayout_6->addWidget(pOscWavePos);
    ui->horizontalLayout_3->addWidget(pOscWaveRound);
    ui->horizontalLayout_3->addWidget(pOscWaveRoundAfter);
//    ui->verticalLayout_2->addWidget(pOscWaveRound);
//    ui->verticalLayout_2->setStretch(0, 1);
//    ui->verticalLayout_2->setStretch(1, 1);
//    ui->verticalLayout_2->setContentsMargins(0, 0, 0, 0);
//    ui->verticalLayout_2->setSpacing(5);

    //pOscWavePos->CreateGroup(pOscWaveTrackErr->GetWavePlot());

    // 参数初始化
    this->firstFlag = false;
    m_bSampleFlag = false;
    m_bSampleStart = false;
    m_nOldCount = 0;

    ui->labelAxisName->setAlignment(Qt::AlignCenter);
    ui->labelImg->setAlignment(Qt::AlignCenter);
    ui->labelImg->setText(HotKeyCfg::GetDirChgMsg());
    ui->labelImg->setFont(QFont(FONT_TYPE, 10));

//    ui->leftBtn->setShortcut(QKeySequence(Qt::AltModifier + Qt::Key_Left));
//    ui->rightBtn->setShortcut(QKeySequence(Qt::AltModifier + Qt::Key_Right));
    ui->leftBtn->setFocusPolicy(Qt::NoFocus);
    ui->rightBtn->setFocusPolicy(Qt::NoFocus);
    m_pRasterOscList->installEventFilter(this);

    m_pUnit = new NcCheckBox(this);
    m_pUnit->setText(TR("单位(V)"));

    InitComboBox();
    ChangeEnCode();
    this->GetCompensate(true);

    ui->horizontalLayout_2->addWidget(m_pUnit);
    ui->horizontalLayout_2->addWidget(m_pEnCode);

    pOscWavePos->SetYAxis2Visible(true);

    ui->label->setText(TR("黄色:最小值 橙色:理想值 紫色:最大值"));

    RedrawTitleLabel();

    m_pUnit->installEventFilter(this);
    m_pEnCode->installEventFilter(this);
    this->installEventFilter(this);

    connect(m_pUnit, SIGNAL(stateChanged(int)), this, SLOT(SlotUnitChange()));
    connect(m_pEnCode, SIGNAL(currentIndexChanged(int)), this, SLOT(SlotEnCodeChange()));
}

OscServoRaster::~OscServoRaster()
{
    delete ui;
}

void OscServoRaster::LoadInfo(QVector<fBit64> sin, QVector<fBit64> cos, Bit32 flag)
{
    if (sin.count() == 0 && cos.count() == 0)
    {
        ResetInfo(flag);
        return;
    }
    QVector<fBit64> phaseDiff;

    QVector<fBit64> sinMax;
    QVector<fBit64> sinMin;
    QVector<fBit64> cosMax;
    QVector<fBit64> cosMin;
    QVector<fBit64> posActSin;
    QVector<fBit64> posActCos;
    QVector<fBit64> posPhaseDiff;

    QVector<fBit64> sinAmpl;
    QVector<fBit64> cosAmpl;
    QVector<fBit64> sinZero;
    QVector<fBit64> cosZero;

    QString unit = "";

    phaseDiff.clear();
    sinMax.clear();
    sinMin.clear();
    cosMax.clear();
    cosMin.clear();
    posActSin.clear();
    posActCos.clear();
    posPhaseDiff.clear();

    sinAmpl.clear();
    cosAmpl.clear();
    sinZero.clear();
    cosZero.clear();

    fBit64 trackErrMaxVal = 0.000;
    fBit64 trackErrMinVal = 0.000;

    fBit64 sinAmplMaxComplex = 0.000; // 正弦幅值max
    fBit64 cosAmplMaxComplex = 0.000; // 余弦幅值max
    fBit64 sinAmplMinComplex = 0.000; // 正弦幅值min
    fBit64 cosAmplMinComplex = 0.000; // 余弦幅值min

    fBit64 phaseChMaxRange = 0.000;
    fBit64 phaseChMinRange = 0.000;

    fBit64 sinZeroChMaxRange = 0.000;
    fBit64 sinZeroChMinRange = 0.000;
    fBit64 cosZeroChMaxRange = 0.000;
    fBit64 cosZeroChMinRange = 0.000;

    QString trackErrStr = "-";
    QString sinZeroStr = "-";
    QString cosZeroStr = "-";
    QString sinAmplStr = "-";
    QString cosAmplStr = "-";
    QString phaseChStr = "-";


    Bit32 start_pos = 0;
    Bit32 stop_pos = 0;

    start_pos = sin.count() * 0.5;
    stop_pos = sin.count() * 0.7;

    QVector<fBit64> dataSin;
    QVector<fBit64> dataCos;
    QVector<fBit64> data;

    dataSin.clear();
    dataCos.clear();
    data.clear();

    Bit32 count = 0;

    if(sin.count() > 0)
    {
        m_bSampleFlag = true;

        for (Bit32 i = start_pos; i < stop_pos; i++)
        {
            dataSin.append(sin.at(i));
            dataCos.append(cos.at(i));
            data.append(sqrt(static_cast<fBit64>((sin.at(i)) * (sin.at(i)) + (cos.at(i)) * (cos.at(i)))));

        }

        count = (dataSin.count() < dataCos.count()) ? (dataSin.count()) : (dataCos.count());
        for(Bit32 i = 1; i < (count - 1); i++)
        {

            if(dataSin[i-1] < dataSin[i] && dataSin[i] > dataSin[i+1])  // 正弦波峰
            {
                sinMax.append(dataSin[i]);
            }
            if(dataSin[i-1] > dataSin[i] && dataSin[i] < dataSin[i+1])  // 正弦波谷
            {
                sinMin.append(dataSin[i]);
            }
            if(dataCos[i-1] < dataCos[i] && dataCos[i] > dataCos[i+1])  // 余弦波峰
            {
                cosMax.append(dataCos[i]);
            }
            if(dataCos[i-1] > dataCos[i] && dataCos[i] < dataCos[i+1])  // 余弦波谷
            {
                cosMin.append(dataCos[i]);
            }

            if (dataCos[i] != 0)
            {
                phaseDiff.append(atan(dataSin[i] / dataCos[i]));    // 相位差
            }
        }

        count = (sinMax.count() < sinMin.count()) ? (sinMax.count()) : (sinMin.count());
        for (Bit32 i = 0; i < (count - 1); i++)
        {
            sinAmpl.append(sinMax.at(i) - sinMin.at(i));
            sinZero.append(fabs(sinMax.at(i)) - fabs(sinMin.at(i)));
        }

        count = (cosMax.count() < cosMin.count()) ? (cosMax.count()) : (cosMin.count());
        for(Bit32 i = 0; i < (count - 1); i++)
        {
            cosAmpl.append(cosMax.at(i) - cosMin.at(i));
            cosZero.append(fabs(cosMax.at(i)) - fabs(cosMin.at(i)));
        }

        fBit64 temp = 0.0;
        GetMaxAndMin(sinMax, sinAmplMaxComplex, temp);
        GetMaxAndMin(sinMin, temp, sinAmplMinComplex);

        GetMaxAndMin(cosMax, cosAmplMaxComplex, temp);
        GetMaxAndMin(cosMin, temp, cosAmplMinComplex);

        GetMaxAndMin(sinZero, sinZeroChMaxRange, sinZeroChMinRange);

        GetMaxAndMin(cosZero, cosZeroChMaxRange, cosZeroChMinRange);

//        GetMaxAndMin(phaseDiff, phaseChMaxRange, phaseChMinRange);

        GetMaxAndMin(data, trackErrMaxVal, trackErrMinVal);

        Bit32 tmp = 0;
        tmp = std::max_element(phaseDiff.begin(), phaseDiff.end()) - phaseDiff.begin();
        if (tmp >= 0 && tmp < phaseDiff.count())
        {
            phaseChMaxRange = phaseDiff.at(tmp);
        }
        tmp = std::min_element(phaseDiff.begin(), phaseDiff.end()) - phaseDiff.begin();
        if (tmp >= 0 && tmp < phaseDiff.count())
        {
            phaseChMinRange = phaseDiff.at(tmp);
        }
    }

    trackErrStr = GetShowInfo(trackErrMaxVal, trackErrMinVal);
    sinZeroStr = GetShowInfo(sinZeroChMaxRange, sinZeroChMinRange, 0);
    cosZeroStr = GetShowInfo(cosZeroChMaxRange, cosZeroChMinRange, 0);
    sinAmplStr = GetShowInfo(sinAmplMaxComplex, sinAmplMinComplex, 0);
    cosAmplStr = GetShowInfo(cosAmplMaxComplex, cosAmplMinComplex, 0);
    phaseChStr = GetShowInfo(phaseChMaxRange, phaseChMinRange);

    SetLabelText(trackErrStr, sinZeroStr, cosZeroStr, sinAmplStr, cosAmplStr, phaseChStr, flag);

    RefresWidget();
}

void OscServoRaster::ExportPos(QVector<fBit64> oldSin, QVector<fBit64> oldCos, QVector<fBit64> sin, QVector<fBit64> cos)
{
    Bit8 filename[PATH_NAME_LEN] = {0};
    QString str = "";
    FILE *fp = NULL;
    Bit32 i = 0;
    Bit32 ret = 0;
    Bit8 tmp_path[PATH_NAME_LEN] = {'\0'};

    ret = HNC_SysCtrlGetConfig(HNC_SYS_CFG_PROG_PATH, tmp_path);

    QDir dir(tmp_path);
    if(!dir.exists())
    {
        FileManage::DirNew(FileManage::DRIVE_SYS, FileManage::TMP_FILE_TYPE, QString(tmp_path));
    }

    if (ret != 0)
    {
        return;
    }

    str = "pos";

    snprintf(filename, PATH_NAME_LEN, "%s%c%s.txt", tmp_path, DIR_SEPARATOR, str.toStdString().data());

    fp = fopen(filename, "w");
    if (fp == NULL)
    {
        MessageOut(QObject::TR("创建采样数据文件失败!"));
        return;
    }

    MessageOut(QObject::TR("正在导出..."));
    fprintf(fp, "oldSin data :\n");
    for (i = 0; i < oldSin.count(); i++)
    {
        fprintf(fp, "%f\t", oldSin.at(i));
        fprintf(fp, "\n");
    }

    fprintf(fp, "oldCos data :\n");
    for (i = 0; i < oldCos.count(); i++)
    {
        fprintf(fp, "%f\t", oldCos.at(i));
        fprintf(fp, "\n");
    }

    fprintf(fp, "sin data :\n");
    for (i = 0; i < sin.count(); i++)
    {
        fprintf(fp, "%f\t", sin.at(i));
        fprintf(fp, "\n");
    }

    fprintf(fp, "cos data :\n");
    for (i = 0; i < cos.count(); i++)
    {
        fprintf(fp, "%f\t", cos.at(i));
        fprintf(fp, "\n");
    }

    fclose(fp);
    fp = NULL;
    NcSync();
    MessageOut(QObject::TR("导出成功!"));
}

void OscServoRaster::InitComboBox()
{
    m_pEnCode = new NcComboBox(this);
    QStringList list;
    list.clear();
    list << TR("无") << TR("第一编码器") << TR("第二编码器");
    m_pEnCode->addItems(list);
}


void OscServoRaster::ChangeEnCode()
{
    Bit8 enCode[DATA_STRING_LEN] = {'\0'};
    Bit32 ax_no = HmiOscServo::GetCurAxesNoRaster();
    HmiParmMan::paraman_get_user_value(PARAMAN_FILE_AXIS, ax_no, PAR_ENCODER_CHECK, enCode);
    if (QString(enCode) == "3")
    {
        m_pEnCode->setCurrentIndex(1);
    }
    else if (QString(enCode) == "2")
    {
        m_pEnCode->setCurrentIndex(2);
    }
}

bool OscServoRaster::CheckMotorVersion()
{
    Bit32 flag = 0;
    Bit32 ch = ActiveChan();
    Bit32 logicAx = -1;
    Bit32 axis_type = 0;
    Bit8 type[32] = {'\0'}; // 伺服类型
    Bit8 ver[32] = {'\0'}; // 伺服驱动版本
    QVector<Bit32> vecAxisNo;
    bool ret = false;

    vecAxisNo.clear();

    for (int chAx = 0; chAx < (CHAN_AXES_NUM + CHAN_SPDL_NUM); chAx++)
    {
        ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_XINDEX + chAx, &logicAx);
        if (logicAx >= 0 && logicAx < TOTAL_AXES_NUM)
        {
            HNC_AxisGetValue(HNC_AXIS_TYPE, logicAx, &axis_type); // 轴类型
            if (axis_type == 10) // 主轴
            {
                vecAxisNo.append(logicAx);
            }
        }
    }

    for (int i = 0;i < vecAxisNo.count(); i++)
    {
         HNC_AxisGetValue(HNC_AXIS_MOTOR_TYPE_FLAG, vecAxisNo.at(i), &flag);
         if(0 == flag)
         {
             HNC_AxisGetValue(HNC_AXIS_MOTOR_TYPE, vecAxisNo.at(i), &type); // 伺服类型
             HNC_AxisGetValue(HNC_AXIS_DRIVE_VER, vecAxisNo.at(i), &ver); // 伺服驱动版本
             QString typeStr = QString(type);
             typeStr = typeStr.left(typeStr.indexOf('-'));
             QString version = QString(ver);
             if ((typeStr == "180US") && (version == "3.723"))
             {
                ret = true;
             }
         }
    }
    return ret;
}

void OscServoRaster::GetCompensate(bool flag)
{
    if (flag == true || HmiOscServo::GetSaveFlag() != 0)
    {
        Bit32 ax_no = HmiOscServo::GetCurAxesNoRaster();
        Bit8 enCode[DATA_STRING_LEN] = {'\0'};
        HmiParmMan::paraman_get_user_value(PARAMAN_FILE_AXIS, ax_no, PAR_RASTER_DETEC_COMP_ENABLE, enCode);

        if (QString(enCode) == "0")
        {
            m_nCompensate = NONEENCODE;
        }
        if (QString(enCode) == "1")
        {
            m_nCompensate = FIRSTENCODE;
        }
        if (QString(enCode) == "2")
        {
            m_nCompensate = SECONDENCODE;
        }
    }
}

void OscServoRaster::SetFocus()
{
    if (m_nCurFocus == TABLELIST)
    {
        m_pRasterOscList->SetTableFocus();
    }
    else if (m_nCurFocus == CHECKBOX)
    {
        m_pUnit->setFocus();
    }
    else if (m_nCurFocus == COMBOBOX)
    {
        m_pEnCode->setFocus();
    }
    else
    {
        m_nCurFocus = CHECKBOX;
        m_pUnit->setFocus();
    }
}

void OscServoRaster::RefresWidget()
{
    Bit32 ch = ActiveChan();
    QStringList strList = GetParmList();
    m_pRasterOscList->RefresWidget(strList);
    m_pRasterOscList->LoadWidget();
    pOscWaveRound->SetScaleAreaRadius(HmiOscServo::s_Conf[ch].stRasterConf.rad);
    pOscWavePos->AutoRangParm(false, true);
}

QStringList OscServoRaster::GetParmList()
{
    Bit32 ax_no = HmiOscServo::GetCurAxesNoRaster();
    QStringList strList;
    strList.clear();

    SDataProperty prop;
    HNC_ParamanGetParaProp(PARAMAN_FILE_AXIS, ax_no, PAR_RASTER_DETEC_COMP_ENABLE, PARA_PROP_ID, &prop);
    strList << QString::number(prop.value.val_int);

    HNC_ParamanGetParaProp(PARAMAN_FILE_AXIS, ax_no, PAR_FIRST_ENCODER_SIN_AMPLITUDE_OFF, PARA_PROP_ID, &prop);
    strList << QString::number(prop.value.val_int);

    HNC_ParamanGetParaProp(PARAMAN_FILE_AXIS, ax_no, PAR_FIRST_ENCODER_COS_AMPLITUDE_OFF, PARA_PROP_ID, &prop);
    strList << QString::number(prop.value.val_int);

    HNC_ParamanGetParaProp(PARAMAN_FILE_AXIS, ax_no, PAR_FIRST_ENCODER_SIN_COS_MISMATCH, PARA_PROP_ID, &prop);
    strList << QString::number(prop.value.val_int);

    HNC_ParamanGetParaProp(PARAMAN_FILE_AXIS, ax_no, PAR_SECOND_ENCODER_SIN_AMPLITUDE_OFF, PARA_PROP_ID, &prop);
    strList << QString::number(prop.value.val_int);

    HNC_ParamanGetParaProp(PARAMAN_FILE_AXIS, ax_no, PAR_SECOND_ENCODER_COS_AMPLITUDE_OFF, PARA_PROP_ID, &prop);
    strList << QString::number(prop.value.val_int);

    HNC_ParamanGetParaProp(PARAMAN_FILE_AXIS, ax_no, PAR_SECOND_ENCODER_SIN_COS_MISMATCH, PARA_PROP_ID, &prop);
    strList << QString::number(prop.value.val_int);

    return strList;
}

void OscServoRaster::RecordData()
{
    if (m_nCompensate == NONEENCODE)
    {
        vec_OldDataSin.clear();
        vec_OldDataCos.clear();
        vec_OldDataX.clear();
        vec_OldRoundX.clear();
        vec_OldRoundY.clear();
    }
    else if(m_nCompensate == FIRSTENCODE || m_nCompensate == SECONDENCODE)
    {
        vec_DataSin.clear();
        vec_DataCos.clear();
        vec_DataX.clear();
        vec_RoundX.clear();
        vec_RoundY.clear();
    }


    Bit32 start_pos = 1;
    Bit32 stop_pos = oscproc_get_total();

    Bit64 *pdata1 = NULL;
    Bit64 *pdata2 = NULL;
    Bit64 *pdata3 = NULL;

    if(oscproc_get_total() > 0)
    {
        pdata1 = oscproc_get_smpldata(0);
        pdata2 = oscproc_get_smpldata(1);
        pdata3 = oscproc_get_smpldata(2);

        // 保护，防止越界
        if (NULL == pdata1 || NULL == pdata2 || NULL == pdata3)
        {
            return;
        }

        if (m_nCompensate == NONEENCODE)
        {
            for (Bit32 i = start_pos; i < stop_pos; i++)
            {
                vec_OldDataSin.append(pdata1[i] / UNITAGE);
                vec_OldDataCos.append(pdata2[i] / UNITAGE);
                vec_OldDataX.append(i * oscproc_get_smpl_period());
                vec_OldRoundX.append(pdata1[i] / UNITAGE);
                vec_OldRoundY.append(pdata2[i] / UNITAGE);
            }

        }
        else if (m_nCompensate == FIRSTENCODE || m_nCompensate == SECONDENCODE)
        {

            for (Bit32 i = start_pos; i < stop_pos; i++)
            {
                vec_DataSin.append(pdata1[i] / UNITAGE);
                vec_DataCos.append(pdata2[i] / UNITAGE);
                vec_DataX.append(i * oscproc_get_smpl_period());
                vec_RoundX.append(pdata1[i] / UNITAGE);
                vec_RoundY.append(pdata2[i] / UNITAGE);
            }

        }
    }
}

void OscServoRaster::GetMaxAndMin(QVector<fBit64> vec, fBit64 &max, fBit64 &min)
{
    fBit64 scaler = 1.0;
    if (m_pUnit->checkState() == false)
    {
        scaler = UNITAGE;
    }
    Bit32 tmp = 0;
    tmp = std::max_element(vec.begin(), vec.end()) - vec.begin();
    if (tmp >= 0 && tmp < vec.count())
    {
        max = vec.at(tmp) * scaler;
    }
    tmp = std::min_element(vec.begin(), vec.end()) - vec.begin();
    if (tmp >= 0 && tmp < vec.count())
    {
        min = vec.at(tmp) * scaler;
    }
}

QString OscServoRaster::GetShowInfo(fBit64 max, fBit64 min, Bit32 prec)
{
    QString str = "";
    str = QString::number(min, 'f', prec) + "~" + QString::number(max, 'f', prec);

    return str;
}

void OscServoRaster::SetLabelText(QString trackErr, QString sinZero, QString cosZero, QString sinAmpl, QString cosAmpl, QString phaseCh, Bit32 flag)
{
    if (flag == 0)
    {
        ui->trackErrSinBefore->setText(trackErr);
        ui->sinZeroBefore->setText(sinZero);
        ui->cosZeroBefore->setText(cosZero);
        ui->sinAmplBefore->setText(sinAmpl);
        ui->cosAmplBefore->setText(cosAmpl);
        ui->phaseBefore->setText(phaseCh);
    }
    else if (flag == 1)
    {
        ui->trackErrSinAfter->setText(trackErr);
        ui->sinZeroAfter->setText(sinZero);
        ui->cosZeroAfter->setText(cosZero);
        ui->sinAmplAfter->setText(sinAmpl);
        ui->cosAmplAfter->setText(cosAmpl);
        ui->phaseAfter->setText(phaseCh);
    }
}

void OscServoRaster::RedrawTitleLabel()
{
    if (m_pUnit->isChecked() == true)
    {
        ui->trackErrLabel->setText(TR("合成幅值:"));
        ui->sinZeroLabel->setText(TR("正弦零点偏移:"));
        ui->cosZeroLabel->setText(TR("余弦零点偏移:"));
        ui->sinAmplLabel->setText(TR("正弦幅值:"));
        ui->cosAmplLabel->setText(TR("余弦幅值:"));
    }
    else
    {
        ui->trackErrLabel->setText(TR("合成幅值:"));
        ui->sinZeroLabel->setText(TR("正弦零点偏移:"));
        ui->cosZeroLabel->setText(TR("余弦零点偏移:"));
        ui->sinAmplLabel->setText(TR("正弦幅值:"));
        ui->cosAmplLabel->setText(TR("余弦幅值:"));
    }
}

void OscServoRaster::SetValue()
{
    QVector<fBit64> phaseDiff;

    QVector<fBit64> sinMax;
    QVector<fBit64> sinMin;
    QVector<fBit64> cosMax;
    QVector<fBit64> cosMin;
    QVector<fBit64> posActSin;
    QVector<fBit64> posActCos;
    QVector<fBit64> posPhaseDiff;

    QVector<fBit64> sinAmpl;
    QVector<fBit64> cosAmpl;
    QVector<fBit64> sinZero;
    QVector<fBit64> cosZero;

    phaseDiff.clear();
    sinMax.clear();
    sinMin.clear();
    cosMax.clear();
    cosMin.clear();
    posActSin.clear();
    posActCos.clear();
    posPhaseDiff.clear();

    sinAmpl.clear();
    cosAmpl.clear();
    sinZero.clear();
    cosZero.clear();

    Bit32 start_pos = 0;
    Bit32 stop_pos = 0;

    start_pos = vec_OldDataSin.count() * 0.5;
    stop_pos = vec_OldDataSin.count() * 0.7;

    QVector<fBit64> dataSin;
    QVector<fBit64> dataCos;
    QVector<fBit64> data;

    dataSin.clear();
    dataCos.clear();
    data.clear();

    Bit32 count = 0;

    if(vec_OldDataSin.count() > 0)
    {
        m_bSampleFlag = true;

        for (Bit32 i = start_pos; i < stop_pos; i++)
        {
            dataSin.append(vec_OldDataSin.at(i));
            dataCos.append(vec_OldDataCos.at(i));
            data.append(sqrt(static_cast<fBit64>((vec_OldDataSin.at(i)) * (vec_OldDataSin.at(i)) + (vec_OldDataCos.at(i)) * (vec_OldDataCos.at(i)))));

        }

        count = (dataSin.count() < dataCos.count()) ? (dataSin.count()) : (dataCos.count());
        for(Bit32 i = 1; i < (count - 1); i++)
        {

            if(dataSin[i-1] < dataSin[i] && dataSin[i] > dataSin[i+1])  // 正弦波峰
            {
                sinMax.append(dataSin[i]);
            }
            if(dataSin[i-1] > dataSin[i] && dataSin[i] < dataSin[i+1])  // 正弦波谷
            {
                sinMin.append(dataSin[i]);
            }
            if(dataCos[i-1] < dataCos[i] && dataCos[i] > dataCos[i+1])  // 余弦波峰
            {
                cosMax.append(dataCos[i]);
            }
            if(dataCos[i-1] > dataCos[i] && dataCos[i] < dataCos[i+1])  // 余弦波谷
            {
                cosMin.append(dataCos[i]);
            }

            if (dataCos[i] != 0)
            {
                phaseDiff.append(atan(dataSin[i] / dataCos[i]));    // 相位差
            }
        }

        count = (sinMax.count() < sinMin.count()) ? (sinMax.count()) : (sinMin.count());
        for (Bit32 i = 0; i < (count - 1); i++)
        {
            sinAmpl.append(sinMax.at(i) - sinMin.at(i));
            sinZero.append(fabs(sinMax.at(i)) - fabs(sinMin.at(i)));
        }

        count = (cosMax.count() < cosMin.count()) ? (cosMax.count()) : (cosMin.count());
        for(Bit32 i = 0; i < (count - 1); i++)
        {
            cosAmpl.append(cosMax.at(i) - cosMin.at(i));
            cosZero.append(fabs(cosMax.at(i)) - fabs(cosMin.at(i)));
        }

        fBit64 sinMaxAve = 0.0;
        fBit64 sinMinAve = 0.0;
        fBit64 cosMaxAve = 0.0;
        fBit64 cosMinAve = 0.0;

        Bit32 temp = 0;

        temp = std::max_element(sinMax.begin(), sinMax.end()) - sinMax.begin();
        if (temp >= 0 && temp < sinMax.count())
        {
            sinMaxAve = sinMax.at(temp) * UNITAGE;
        }

        temp = std::max_element(cosMax.begin(), cosMax.end()) - cosMax.begin();
        if (temp >= 0 && temp < cosMax.count())
        {
            cosMaxAve = cosMax.at(temp) * UNITAGE;
        }

        temp = std::min_element(sinMin.begin(), sinMin.end()) - sinMin.begin();
        if (temp >= 0 && temp < sinMin.count())
        {
            sinMinAve = sinMin.at(temp) * UNITAGE;
        }

        temp = std::min_element(cosMin.begin(), cosMin.end()) - cosMin.begin();
        if (temp >= 0 && temp < cosMin.count())
        {
            cosMinAve = cosMin.at(temp) * UNITAGE;
        }

        // 取平均值
//        fBit64 sinMaxAve = GetListAve(sinMax);
//        fBit64 sinMinAve = GetListAve(sinMin);
//        fBit64 cosMaxAve = GetListAve(cosMax);
//        fBit64 cosMinAve = GetListAve(cosMin);
        fBit64 sinOffVal = (fabs(sinMaxAve) - fabs(sinMinAve)) / 2;
        fBit64 cosOffVal = (fabs(cosMaxAve) - fabs(cosMinAve)) / 2;
        fBit64 offVal = 0.0;
        fBit64 tmp = (fabs(cosMaxAve) + fabs(cosMinAve));
        if (tmp != 0)
        {
            offVal = ((fabs(sinMaxAve) + fabs(sinMinAve)) * 1024) / tmp;
        }
        if (m_nOldCount != sinMax.count() && sinMax.count() > 0)
        {
            m_nOldCount = sinMax.count();
            SetOffValToParam(sinOffVal, cosOffVal, offVal);
            QStringList strList = GetParmList();
            m_pRasterOscList->RefresWidget(strList);
            m_pRasterOscList->LoadWidget();
        }
    }
}

void OscServoRaster::RedrawLineAndRound()
{
    pOscWavePos->ClearPoint();
    pOscWaveRound->ClearData();

    pOscWavePos->LineZeroAddPoint(vec_OldDataX, vec_OldDataSin);
    pOscWavePos->LineOneAddPoint(vec_OldDataX, vec_OldDataCos);
    pOscWavePos->WaveReplot();
    pOscWaveRound->LineZeroAddPoint(vec_OldRoundX, vec_OldRoundY);
    pOscWaveRound->replot();
}

fBit64 OscServoRaster::GetListAve(QVector<fBit64> list)
{
    if (list.count() <= 0)
    {
        return 0.0;
    }

    fBit64 count = 0.0;
    for(Bit32 i = 0; i < list.count(); i++)
    {
        count += list.at(i) * UNITAGE;
    }
    fBit64 ave = count / list.count();
    return ave;
}

void OscServoRaster::SetOffValToParam(fBit64 sinOffVal, fBit64 cosOffVal, fBit64 offVal)
{
    Bit8 buf[DATA_STRING_LEN] = {'\0'};
    Bit32 val = 0;
    Bit32 ax_no = HmiOscServo::GetCurAxesNoRaster();

    if (m_pEnCode->currentIndex() == 1)
    {
        val = sinOffVal;
        snprintf(buf, DATA_STRING_LEN, "%d", val);

        HmiParmMan::paraman_set_value_direct(PARAMAN_FILE_AXIS, ax_no,
                                           PAR_FIRST_ENCODER_SIN_AMPLITUDE_OFF, buf);

        val = cosOffVal;
        snprintf(buf, DATA_STRING_LEN, "%d", val);
        HmiParmMan::paraman_set_value_direct(PARAMAN_FILE_AXIS, ax_no,
                                           PAR_FIRST_ENCODER_COS_AMPLITUDE_OFF, buf);

        val = offVal;
        snprintf(buf, DATA_STRING_LEN, "%d", val);
        HmiParmMan::paraman_set_value_direct(PARAMAN_FILE_AXIS, ax_no,
                                           PAR_FIRST_ENCODER_SIN_COS_MISMATCH, buf);
    }
    else if (m_pEnCode->currentIndex() == 2)
    {
        val = sinOffVal;
        snprintf(buf, DATA_STRING_LEN, "%d", val);

        HmiParmMan::paraman_set_value_direct(PARAMAN_FILE_AXIS, ax_no,
                                           PAR_SECOND_ENCODER_SIN_AMPLITUDE_OFF, buf);

        val = cosOffVal;
        snprintf(buf, DATA_STRING_LEN, "%d", val);
        HmiParmMan::paraman_set_value_direct(PARAMAN_FILE_AXIS, ax_no,
                                           PAR_SECOND_ENCODER_COS_AMPLITUDE_OFF, buf);

        val = offVal;
        snprintf(buf, DATA_STRING_LEN, "%d", val);
        HmiParmMan::paraman_set_value_direct(PARAMAN_FILE_AXIS, ax_no,
                                           PAR_SECOND_ENCODER_SIN_COS_MISMATCH, buf);
    }

}

void OscServoRaster::LoadAxisVal(Bit32 type)
{
    QString axisName = "";
//    QString unit1 = "";
//    QString unit2 = "";
//    QStringList strList;
//    Bit32 axisType = 0;

//    HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetCurAxesNoRaster(), &axisType);

//    strList.clear();

    axisName = HmiOscServo::OscAxisToName(HmiOscServo::GetCurAxesNoRaster());
    ui->labelAxisName->setText(TR("%1轴").arg(axisName));

//    if(axisType == 1)
//    {
//        unit1 = TR("mm");
//        unit2 = TR("um");
//    }
//    else
//    {
//        unit1 = TR("deg");
//        unit2 = TR("deg/1000");
//    }

//    ui->label_14->setText(unit1);
//    ui->label_16->setText(unit1);
    pOscWavePos->SetAxisName(QStringList() << axisName);
    //pOscWavePos->SetAxisName(QStringList() << axisName << unit1);
    //pOscWaveRound->SetAxisName(QStringList() << axisName);

    if(type == 1)
    {
        RefresWidget();
    }
}

void OscServoRaster::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::SETFOCUS)
    {
        if(messageValue == "CLEARFOCUS")
        {
            m_pRasterOscList->ClearTableFocus();
        }
        else
        {
            SetFocus();
        }
    }
    else if(messageid == MsgData::REDRAWALL || messageid == MsgData::REDRAW)
    {
        this->ChangeEnCode();
        if(messageValue == "INIT") // 初始化，清除上次在该界面记住的当前行
        {
            QStringList strList = GetParmList();
            m_pRasterOscList->RefresWidget(strList);
        }
//        TestReadFile();

//        pOscWaveRound->LineZeroAddPoint(vec_OldRoundX, vec_OldRoundY);
//        pOscWaveRound->replot();
//        pOscWaveRoundAfter->LineZeroAddPoint(vec_RoundX, vec_RoundY);
//        pOscWaveRoundAfter->replot();
        this->LoadInfo(vec_OldDataSin, vec_OldDataCos, 0);
        this->LoadInfo(vec_DataSin, vec_DataCos, 1);

//        SetValue();


        this->OnBtFlagChange();
        m_pRasterOscList->LoadWidget();
        this->LoadAxisVal(0);
        this->SetColorStyle();
        this->ResetInfo();

        if(HmiOscServo::GetConfChgFlag())
        {
            this->Reset();
            HmiOscServo::SetConfChgFlag(false);
        }
//        TestAddPoint();
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_OSCSERVOSTART")
        {
            this->Reset();
            this->ResetInfo();
            m_bSampleStart = true;
            if (m_nSamplingState == NONE)
            {
                SetSamplingState(AUTO);
            }
        }
        else if(messageValue == "MSG_OSCSERVOSAVE")
        {
            HmiOscServo::ParmSave();
            m_pRasterOscList->LoadWidget();
        }
        else if (messageValue == "MSG_OSCSERVOSTOP")
        {
            // ResetInfo();
            this->RecordData();
            if (m_nSamplingState == MANUAL)
            {
                this->LoadInfo(vec_OldDataSin, vec_OldDataCos, 0);
            }

            if (m_nCompensate == NONEENCODE)
            {
                this->LoadInfo(vec_OldDataSin, vec_OldDataCos, 0);
                this->SetValue();
            }
            else if (m_nCompensate == FIRSTENCODE || m_nCompensate == SECONDENCODE)
            {
                this->LoadInfo(vec_OldDataSin, vec_OldDataCos, 0);
                this->LoadInfo(vec_DataSin, vec_DataCos, 1);
            }

            SetSamplingState(NONE);
            m_bSampleStart = false;
            // TestLoadInfo();
        }
        else if(messageValue == "OSCSERVOCOLOR")
        {
            this->SetColorStyle();
        }
        else if (messageValue == "ZOOMUP")
        {
            pOscWavePos->SetZoomXUp();
            pOscWavePos->WaveReplot();
        }
        else if (messageValue == "ZOOMDOWN")
        {
            pOscWavePos->SetZoomXDn();
           pOscWavePos->WaveReplot();
        }
        else if (messageValue == "KICKZOOMUP")
        {
           pOscWavePos->SetZoomXLeft();
           pOscWavePos->WaveReplot();
        }
        else if (messageValue == "KICKZOOMDOWN")
        {

           pOscWavePos->SetZoomXRight();
           pOscWavePos->WaveReplot();
        }
        else if (messageValue == "MSG_OSCSERVOMANUALSTART")
        {
            SetSamplingState(MANUAL);
            this->Reset();
            this->ResetInfo();
            m_bSampleStart = true;
        }
        else if(messageValue == "MSG_OSCSERVOEXPORT")
        {
//            this->ExportPos(vec_OldDataSin, vec_OldDataCos, vec_DataSin, vec_DataCos);
            HmiOscServo::OscservoExport();
        }
    }
    else if (messageid == MsgData::REFRESH)
    {
//        RefreshTime();
        this->Refresh();
        this->GetCompensate();
//        TestAddPoint();
    }
    else if(messageid == MsgData::KEYBOARD && messageValue == "TURNTABSLEFT")
    {
        this->on_leftBtn_clicked();
    }
    else if(messageid == MsgData::KEYBOARD && messageValue == "TURNTABSRIGHT")
    {
        this->on_rightBtn_clicked();
    }

}

void OscServoRaster::resizeEvent(QResizeEvent *)
{
    this->firstFlag = false;
}

bool OscServoRaster::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::Paint && !firstFlag) // Paint事件在ReSize事件之后响应，用于图片第一次重绘
    {
        ui->leftBtn->setIconSize(ui->leftBtn->size());
        ui->rightBtn->setIconSize(ui->rightBtn->size());
        //ui->leftBtn->setIcon(PixMapToSize(ui->leftBtn->size(), "../pic/left-2.png"));
        //ui->rightBtn->setIcon(PixMapToSize(ui->rightBtn->size(), "../pic/right-1.png"));

        this->firstFlag = true;
        this->OnBtFlagChange(); // 解决初次进入界面时，redraw消息在paint事件前响应，导致界面刷新错误
    }
    else if (event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        switch (keyEvent->key())
        {
        case Qt::Key_Up:
        {
            if (m_nCurFocus == TABLELIST)
            {
                m_pRasterOscList->ClearTableFocus();
                m_nCurFocus = CHECKBOX;
                m_pUnit->setFocus();
            }
            return true;
//            break;
        }
        case Qt::Key_Down:
        {
            if (m_nCurFocus == CHECKBOX || m_nCurFocus == COMBOBOX)
            {
                m_nCurFocus = TABLELIST;
                bool ret = m_pRasterOscList->SetTableFocus();
                if(ret == false)
                {
                    m_pRasterOscList->ClearTableFocus();
                }
                return true;
            }
            break;
        }
        case Qt::Key_Right:
        {
            if (m_nCurFocus == NOFOCUS)
            {
                m_nCurFocus = CHECKBOX;
                m_pUnit->setFocus();
                return true;
            }
            else if (m_nCurFocus == CHECKBOX)
            {
                m_nCurFocus = COMBOBOX;
                m_pEnCode->setFocus();
                return true;
            }
            break;
        }
        case Qt::Key_Left:
        {
            if (m_nCurFocus == CHECKBOX || m_nCurFocus == TABLELIST)
            {
                emit SignalClearFocus();
                return true;
            }
            else if (m_nCurFocus == COMBOBOX)
            {
                m_nCurFocus = CHECKBOX;
                m_pUnit->setFocus();
                return true;
            }
            break;
        }
        case Qt::Key_Enter:
        case Qt::Key_Return:
        {
            if (m_nCurFocus == CHECKBOX)
            {
                bool state = m_pUnit->isChecked();
                m_pUnit->setChecked(!state);
                pOscWavePos->SetYAxis2Visible(state);
                return true;
            }
            else if (m_nCurFocus == COMBOBOX)
            {
                m_pEnCode->showPopup();
            }
        }
        default:
            break;
        }
    }
    return QObject::eventFilter(target, event);
}

void OscServoRaster::WidgetIn()
{
    HmiMenuManage::Instance().SetMenuValid("W_OSCRASTERSIGNALANALY", true);
    HmiMenuManage::Instance().MenuRedraw();
}

void OscServoRaster::WidgetExit()
{
    HmiMenuManage::Instance().SetMenuValid("W_OSCRASTERSIGNALANALY", false);
    HmiMenuManage::Instance().MenuRedraw();
}

Bit32 OscServoRaster::WidgetInChk()
{
    if (CheckMotorVersion() == false)
    {
         MessageOut(TR("伺服版本或类型错误,无法使用该功能!"));
         return 1;
    }

    return 0;
}

void OscServoRaster::OnBtFlagChange()
{
    bool leftState = true;
    bool rightState = true;
	HmiOscServo::OscservoSamplReset();
	HmiOscServo::OscservoInit();
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndexRaster();
    if(curAxesIndex <= 0) // 在起始位置
    {
        HmiOscServo::SetCurAxesIndexRaster(0);
        leftState = false;
    }
    if(HmiOscServo::GetIndexAxesConfRaster(curAxesIndex + 1) <= 0) // 下一个位置轴无效
    {
        rightState = false;
    }
    ui->leftBtn->setProperty("valid",leftState);
    ui->rightBtn->setProperty("valid",rightState);
    ui->leftBtn->style()->polish(ui->leftBtn);
    ui->rightBtn->style()->polish(ui->rightBtn);
}

void OscServoRaster::on_leftBtn_clicked()
{
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndexRaster();

    if (oscproc_get_stat() == OSC_PROC_START)
    {
        MessageOut(TR("采样中禁止轴切换!"));
        return;
    }

    if(curAxesIndex <= 0)
    {
        HmiOscServo::SetCurAxesIndexRaster(0);
    }
    else
    {
        curAxesIndex--;
        HmiOscServo::SetCurAxesIndexRaster(curAxesIndex);
    }
    this->LoadAxisVal(1);
    this->OnBtFlagChange();
    oscproc_smpldata_reset();
    //HmiOscServo::OscservoLoadGcode(1); // 重新生成并加载G代码
    this->Reset(); // 清除图形
    ResetInfo();        // 清除调机报表的显示数据
}

void OscServoRaster::on_rightBtn_clicked()
{
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndexRaster();

    if (oscproc_get_stat() == OSC_PROC_START)
    {
        MessageOut(TR("采样中禁止轴切换!"));
        return;
    }

    if(curAxesIndex >= (TOTAL_AXES_PER_CHN - 1))
    {
        curAxesIndex = TOTAL_AXES_PER_CHN - 1;
        HmiOscServo::SetCurAxesIndexRaster(curAxesIndex);
    }
    else if(HmiOscServo::GetIndexAxesConfRaster(curAxesIndex + 1) < 0)
    {
        return;
    }
    else
    {
        curAxesIndex++;
        HmiOscServo::SetCurAxesIndexRaster(curAxesIndex);
    }
    this->LoadAxisVal(1);
    this->OnBtFlagChange();
    oscproc_smpldata_reset();
    //HmiOscServo::OscservoLoadGcode(1); // 重新生成并加载G代码
    this->Reset(); // 清除图形
    ResetInfo();        // 清除调机报表的显示数据
}

void OscServoRaster::SlotUnitChange()
{
//    RedrawTitleLabel();
    LoadInfo(vec_OldDataSin, vec_OldDataCos, 0);
    LoadInfo(vec_DataSin, vec_DataCos, 1);
}

void OscServoRaster::SlotEnCodeChange()
{
    Bit8 enCode[DATA_STRING_LEN] = {'\0'};
    Bit32 ax_no = HmiOscServo::GetCurAxesNoRaster();
    if (m_pEnCode->currentIndex() == 1)
    {
        snprintf(enCode, DATA_STRING_LEN, "3");
    }
    else if (m_pEnCode->currentIndex() == 2)
    {
        snprintf(enCode, DATA_STRING_LEN, "2");
    }
    else if (m_pEnCode->currentIndex() == 0)
    {
        return;
    }
    HmiParmMan::paraman_set_value_direct(PARAMAN_FILE_AXIS, ax_no,PAR_ENCODER_CHECK, enCode);
}

void OscServoRaster::Reset()
{
    // 清空数据

    m_bSampleFlag = false;
    m_bSampleStart = false;

      // 清空数据和图形
    if (m_nCompensate == NONEENCODE)
    {
        vec_OldDataSin.clear();
        vec_OldDataCos.clear();
        vec_OldDataX.clear();
        vec_OldRoundX.clear();
        vec_OldRoundY.clear();

        vec_DataSin.clear();
        vec_DataCos.clear();
        vec_DataX.clear();
        vec_RoundX.clear();
        vec_RoundY.clear();

        pOscWavePos->ClearPoint();
        pOscWaveRound->ClearData();
        pOscWaveRoundAfter->ClearData();
    }
//    pOscWaveRoundLow->ClearData();
    this->lastEndPos = 0;
}

void OscServoRaster::ResetInfo(Bit32 flag)
{
    QString str = TR("-");
    if (flag == -1)
    {
        ui->trackErrSinBefore->setText(str);
        ui->trackErrSinAfter->setText(str);

        ui->sinZeroBefore->setText(str);
        ui->sinZeroAfter->setText(str);

        ui->cosZeroBefore->setText(str);
        ui->cosZeroAfter->setText(str);

        ui->sinAmplBefore->setText(str);
        ui->sinAmplAfter->setText(str);

        ui->cosAmplBefore->setText(str);
        ui->cosAmplAfter->setText(str);

        ui->phaseBefore->setText(str);
        ui->phaseAfter->setText(str);
    }
    else if (flag == 0)
    {
        ui->trackErrSinBefore->setText(str);
        ui->sinZeroBefore->setText(str);
        ui->cosZeroBefore->setText(str);
        ui->sinAmplBefore->setText(str);
        ui->cosAmplBefore->setText(str);
        ui->phaseBefore->setText(str);
    }
    else if (flag == 1)
    {
        ui->trackErrSinAfter->setText(str);
        ui->sinZeroAfter->setText(str);
        ui->cosZeroAfter->setText(str);
        ui->sinAmplAfter->setText(str);
        ui->cosAmplAfter->setText(str);
        ui->phaseAfter->setText(str);
    }
}

void OscServoRaster::Refresh(bool flag)
{
    QVector<double> x;
    QVector<double> y0;
    QVector<double> y1;
    QVector<double> roundX;
    QVector<double> roundY;

    Bit32 i = 0;
    Bit32 stPos = 0;
    Bit32 edPos= 0;
    Bit64 *ch0_addr = NULL;
    Bit64 *ch1_addr = NULL;
    Bit64 *ch2_addr = NULL;

    if (oscproc_get_stat() != OSC_PROC_START)
    {
        this->lastEndPos = 0; // 停止后需要置零
        return;
    }

    x.clear();
    y0.clear();
    y1.clear();
    roundX.clear();
    roundY.clear();

    stPos = lastEndPos;
    edPos = oscproc_get_pos();
    this->lastEndPos = edPos;

    ch0_addr = oscproc_get_smpldata(0);
    ch1_addr = oscproc_get_smpldata(1);
    ch2_addr = oscproc_get_smpldata(2);

    if (NULL == ch0_addr || NULL == ch1_addr || NULL == ch2_addr)
    {
        return;
    }

    if (m_nCompensate == NONEENCODE)
    {
        for (i = stPos+1; i < edPos; ++i)
        {
            // 正余弦
            y0.append(ch0_addr[i] / UNITAGE); // G32
            y1.append(ch1_addr[i] / UNITAGE); // G33

            x.append(i * oscproc_get_smpl_period());

            // 圆
            roundX.append(ch0_addr[i] / ROUND_UNITAGE);
            roundY.append(ch1_addr[i] / ROUND_UNITAGE);
        }

        pOscWavePos->LineZeroAddPoint(x, y0);
        pOscWavePos->LineOneAddPoint(x, y1);
        pOscWavePos->WaveReplot();
        pOscWaveRound->LineZeroAddPoint(roundX, roundY);
        pOscWaveRound->replot();
    }
    else if (m_nCompensate == FIRSTENCODE || m_nCompensate == SECONDENCODE)
    {
        for (i = stPos+1; i < edPos; ++i)
        {
            // 正余弦
            y0.append(ch0_addr[i] / UNITAGE); // G32
            y1.append(ch1_addr[i] / UNITAGE); // G33

            x.append(i * oscproc_get_smpl_period());

            // 圆
            roundX.append(ch0_addr[i] / ROUND_UNITAGE);
            roundY.append(ch1_addr[i] / ROUND_UNITAGE);
        }

        pOscWavePos->LineZeroAddPoint(x, y0);
        pOscWavePos->LineOneAddPoint(x, y1);
        pOscWavePos->WaveReplot();
        pOscWaveRoundAfter->LineZeroAddPoint(roundX, roundY);
        pOscWaveRoundAfter->replot();
    }

}

void OscServoRaster::SetColorStyle()
{
    // 默认黑色风格
    QColor bk(0, 0, 0); // 背景
    QColor gd(0, 0, 0); // 网格
    QColor ft(0, 0, 0); // 字体颜色
    QColor c1(0, 0, 0); // 曲线1
    QColor c2(0, 0, 0); // 曲线2
    QColor c3(0, 0, 0); // 曲线3
    QColor c4(0, 0, 0); // 曲线4

    HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

    QPalette palette;
    palette.setColor(QPalette::Background, bk);
    ui->frame->setAutoFillBackground(true);
    ui->frame->setPalette(palette);

    pOscWavePos->SetColor(bk, gd, ft, c1, c2, c3, c4);
    if (HmiOscServo::GetColorStyle() == 0)
    { // 黑
        pOscWaveRound->SetColor(bk, ft, c1, c2);
        pOscWaveRoundAfter->SetColor(bk, ft, c1, c2);
    }
    else if (HmiOscServo::GetColorStyle() == 1)
    { // 白
        pOscWaveRound->SetColor(bk, ft, c1, c2);
        pOscWaveRoundAfter->SetColor(bk, ft, c1, c2);
    }
    else
    { // 蓝
        pOscWaveRound->SetColor(bk, ft, c1, c2);
        pOscWaveRoundAfter->SetColor(bk, ft, c1, c2);
    }
}

bool OscServoRaster::GetSmplFlag()
{
    return m_bSampleFlag;
}

void OscServoRaster::SetSamplingState(Bit32 state)
{
    m_nSamplingState = state;
}

void OscServoRaster::TestAddPoint()
{
    QString str = "";
    fBit64 trackErrMaxVal = 0.000;
    fBit64 trackErrMinVal = 0.000;

    fBit64 sinAmplMaxComplex = 0.000;
    fBit64 cosAmplMaxComplex = 0.000;
    fBit64 sinAmplMinComplex = 0.000;
    fBit64 cosAmplMinComplex = 0.000;

    fBit64 phaseChMaxRange = 0.000;
    fBit64 phaseChMinRange = 0.000;

    fBit64 sinZeroChMaxRange = 0.000;
    fBit64 sinZeroChMinRange = 0.000;
    fBit64 cosZeroChMaxRange = 0.000;
    fBit64 cosZeroChMinRange = 0.000;

    Bit32 max = 0;
    Bit32 min = 0;

    QVector<fBit64> phaseDiff;

    QVector<fBit64> sinMax;
    QVector<fBit64> sinMin;
    QVector<fBit64> cosMax;
    QVector<fBit64> cosMin;

    QVector<fBit64> sinAmpl;
    QVector<fBit64> cosAmpl;
    QVector<fBit64> sinZero;
    QVector<fBit64> cosZero;

    m_bSampleFlag = true;
    // 测试正余弦曲线
    QVector<fBit64> x;
    QVector<fBit64> y0;
    QVector<fBit64> y1;
    QVector<fBit64> roundX;
    QVector<fBit64> roundY;
    QVector<fBit64> roundX2;
    QVector<fBit64> roundY2;
    fBit64 angle = 0;
    Bit32 m_period = 500; // 周期
    Bit32 m_radius = 1; // 半径
    QVector<fBit64> data;

    for (Bit32 i = 0; i < 6000; i++)
    {
        angle = (fBit64) i / m_period * 2 * 3.1415926;
        vec_OldDataX.append(i);
        vec_OldDataSin.append(m_radius * sin(angle));
        vec_OldDataCos.append(m_radius * cos(angle));

        vec_OldRoundX.append(m_radius * cos(angle));
        vec_OldRoundY.append(m_radius * sin(angle));

//        vec_DataX.append(i);
//        vec_DataSin.append(m_radius * sin(angle) * 1800.0 / 2);
//        vec_DataCos.append(m_radius * cos(angle) * 1800.0 / 2);

//        vec_RoundX.append(m_radius * cos(angle) * 1.5);
//        vec_RoundY.append(m_radius * sin(angle) * 1.5);

//        roundX2.append(/*m_radius * */sin(angle) * 1.1);
//        roundY2.append(/*m_radius * */cos(angle) * 1.1);

        data.append(sqrt((m_radius * sin(angle)) * (m_radius * sin(angle)) + (m_radius * cos(angle)) * (m_radius * cos(angle))));
    }

    for(Bit32 i = 1; i < 6000-1; i++)
    {
        if(vec_OldDataSin[i-1] < vec_OldDataSin[i] && vec_OldDataSin[i] > vec_OldDataSin[i+1])  // 正弦幅值Max
        {
            sinMax.append(vec_OldDataSin[i]);
        }
        if(vec_OldDataSin[i-1] > vec_OldDataSin[i] && vec_OldDataSin[i] < vec_OldDataSin[i+1])  // 正弦幅值Min
        {
            sinMin.append(vec_OldDataSin[i]);
        }
        if(vec_OldDataCos[i-1] < vec_OldDataCos[i] && vec_OldDataCos[i] > vec_OldDataCos[i+1])  // 余弦幅值Max
        {
            cosMax.append(vec_OldDataCos[i]);
        }
        if(vec_OldDataCos[i-1] > vec_OldDataCos[i] && vec_OldDataCos[i] < vec_OldDataCos[i+1])  // 余弦幅值Min
        {
            cosMin.append(vec_OldDataCos[i]);
        }
        phaseDiff.append(atan(vec_OldDataSin[i] / vec_OldDataCos[i]));    // 相位差
    }

    for(Bit32 i = 0; i < sinMax.count(); i++)
    {
        sinAmpl.append(sinMax.at(i) - sinMin.at(i));
        sinZero.append(fabs(sinMax.at(i)) - fabs(sinMin.at(i)));
    }
    for(Bit32 i = 0; i < cosMax.count(); i++)
    {
        cosAmpl.append(cosMax.at(i) - cosMin.at(i));
        cosZero.append(fabs(cosMax.at(i)) - fabs(cosMin.at(i)));
    }

    max = std::max_element(sinAmpl.begin(), sinAmpl.end()) - sinAmpl.begin();
    min = std::min_element(sinAmpl.begin(), sinAmpl.end()) - sinAmpl.begin();
    sinAmplMaxComplex = sinAmpl.at(max);
    sinAmplMinComplex = sinAmpl.at(min);

    max = std::max_element(cosAmpl.begin(), cosAmpl.end()) - cosAmpl.begin();
    min = std::min_element(cosAmpl.begin(), cosAmpl.end()) - cosAmpl.begin();
    cosAmplMaxComplex = cosAmpl.at(max);
    cosAmplMinComplex = cosAmpl.at(min);

    max = std::max_element(sinZero.begin(), sinZero.end()) - sinZero.begin();
    min = std::min_element(sinZero.begin(), sinZero.end()) - sinZero.begin();
    sinZeroChMaxRange = sinZero.at(max);
    sinZeroChMinRange = sinZero.at(min);

    max = std::max_element(cosZero.begin(), cosZero.end()) - cosZero.begin();
    min = std::min_element(cosZero.begin(), cosZero.end()) - cosZero.begin();
    cosZeroChMaxRange = cosZero.at(max);
    cosZeroChMinRange = cosZero.at(min);

    max = std::max_element(phaseDiff.begin(), phaseDiff.end()) - phaseDiff.begin();
    min = std::min_element(phaseDiff.begin(), phaseDiff.end()) - phaseDiff.begin();
    phaseChMaxRange = phaseDiff.at(max);
    phaseChMinRange = phaseDiff.at(min);

    max = std::max_element(data.begin(), data.end()) - data.begin();
    min = std::min_element(data.begin(), data.end()) - data.begin();

    trackErrMaxVal = data.at(max);
    trackErrMinVal = data.at(min);

    str = GetShowInfo(trackErrMaxVal, trackErrMinVal);
    ui->trackErrSinBefore->setText(str);
    ui->trackErrSinAfter->setText(str);

    str = GetShowInfo(sinZeroChMaxRange, sinZeroChMinRange);
    ui->sinZeroBefore->setText(str);
    ui->sinZeroAfter->setText(str);

    str = GetShowInfo(cosZeroChMaxRange, cosZeroChMinRange);
    ui->cosZeroBefore->setText(str);
    ui->cosZeroAfter->setText(str);

    str = GetShowInfo(sinAmplMaxComplex, sinAmplMinComplex);
    ui->sinAmplBefore->setText(str);
    ui->sinAmplAfter->setText(str);

    str = GetShowInfo(cosAmplMaxComplex, cosAmplMinComplex);
    ui->cosAmplBefore->setText(str);
    ui->cosAmplAfter->setText(str);

    str = GetShowInfo(phaseChMaxRange, phaseChMinRange);
    ui->phaseBefore->setText(str);
    ui->phaseAfter->setText(str);


    pOscWavePos->LineZeroAddPoint(vec_OldDataX, vec_OldDataSin);
    pOscWavePos->LineOneAddPoint(vec_OldDataX, vec_OldDataCos);
    pOscWavePos->WaveReplot();
    pOscWaveRound->LineZeroAddPoint(vec_OldRoundX, vec_OldRoundY);
//    pOscWaveRound->LineOneAddPoint(roundX2, roundY2);
//    pOscWaveRound->replot();
    // 测试正余弦曲线
}

void OscServoRaster::TestWriteFile(QVector<fBit64> posActSin, QVector<fBit64> posActCos,
                                   QVector<fBit64> sinAmpl, QVector<fBit64> sinZero,
                                   QVector<fBit64> cosAmpl, QVector<fBit64> cosZero)
{
    Bit8 filename[PATH_NAME_LEN] = {0};
    QString str = "";
    FILE *fp = NULL;
    Bit32 i = 0;
    Bit32 ret = 0;
    Bit8 tmp_path[PATH_NAME_LEN] = {'\0'};

    ret = HNC_SysCtrlGetConfig(HNC_SYS_CFG_TEMP_PATH, tmp_path);

    QDir dir(tmp_path);
    if(!dir.exists())
    {
        FileManage::DirNew(FileManage::DRIVE_SYS, FileManage::TMP_FILE_TYPE, QString(tmp_path));
    }

    if (ret != 0)
    {
        return;
    }

    str = "pos";

    snprintf(filename, PATH_NAME_LEN, "%s%c%s.SV", tmp_path, DIR_SEPARATOR, str.toStdString().data());

    fp = fopen(filename, "w");
    if (fp == NULL)
    {
        MessageOut(QObject::TR("创建采样数据文件失败!"));
        return;
    }

    if(posActCos.count() < posActSin.count())
    {
        for (i = 0; i < posActCos.count(); i++)
        {
            fprintf(fp, "%f\t", posActSin.at(i));
            fprintf(fp, "%f\t", posActCos.at(i));
            fprintf(fp, "%f\t", sinAmpl.at(i));
            fprintf(fp, "%f\t", cosAmpl.at(i));
            fprintf(fp, "%f\t", sinZero.at(i));
            fprintf(fp, "%f\t", cosZero.at(i));
            fprintf(fp, "\n");
        }
    }
    else
    {
        for (i = 0; i < posActSin.count(); i++)
        {
            fprintf(fp, "%f\t", posActSin.at(i));
            fprintf(fp, "%f\t", posActCos.at(i));
            fprintf(fp, "%f\t", sinAmpl.at(i));
            fprintf(fp, "%f\t", cosAmpl.at(i));
            fprintf(fp, "%f\t", sinZero.at(i));
            fprintf(fp, "%f\t", cosZero.at(i));
            fprintf(fp, "\n");
        }
    }

    fclose(fp);
    fp = NULL;
    NcSync();
}

void OscServoRaster::TestReadFile()
{
    vec_OldDataSin.clear();
    vec_OldDataCos.clear();
    vec_OldRoundX.clear();
    vec_OldRoundY.clear();
    vec_DataSin.clear();
    vec_DataCos.clear();
    vec_RoundX.clear();
    vec_RoundY.clear();

    fBit64 val = 0.000;
    Bit32 state = 0;
    bool ok = false;
    Bit32 ret = 0;
    Bit8 tmp_path[PATH_NAME_LEN] = {'\0'};
    Bit8 filename[PATH_NAME_LEN] = {0};
    ret = HNC_SysCtrlGetConfig(HNC_SYS_CFG_PROG_PATH, tmp_path);

    snprintf(filename, PATH_NAME_LEN, "%s%cpos.txt", tmp_path, DIR_SEPARATOR);

    QFile file(filename);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QString line = "";
        QTextStream in(&file);
        line = in.readLine();
        while (line.isNull() == false)
        {
            if (line == "oldSin data :")
            {
                state = 0;
            }
            else if (line == "oldCos data :")
            {
                state = 1;
            }
            else if (line == "sin data :")
            {
                state = 2;
            }
            else if (line == "cos data :")
            {
                state = 3;
            }

            val = line.toDouble(&ok);
            if (state == 0)
            {
                if (ok == true)
                {
                    vec_OldDataSin.append(val);
                    vec_OldRoundX.append(val);
                }
            }
            if (state == 1)
            {
                if (ok == true)
                {
                    vec_OldDataCos.append(val);
                    vec_OldRoundY.append(val);
                }
            }
            if (state == 2)
            {
                if (ok == true)
                {
                    vec_DataSin.append(val);
                    vec_RoundX.append(val);
                }
            }
            if (state == 3)
            {
                if (ok == true)
                {
                    vec_DataCos.append(val);
                    vec_RoundY.append(val);
                }
            }

            line = in.readLine();
        }
    }

}
