﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservoreport.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-调机报表界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年5月24日
* 说    明：
*/

#include <QHeaderView>
#include <QKeyEvent>
#include <QTableWidget>

#include "hncaxis.h"
#include "passwd.h"

#include "common.h"
#include "hmioscservo.h"
#include "hmipaintercolor.h"

#include "oscservoreport.h"
#include "ui_oscservoreport.h"

OscServoReport::OscServoReport(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoReport)
{
    ui->setupUi(this);

    firstFlag = false;
    curRow = 0;
    axisNum = 0;

    listAxisName1.clear();
    listAxisName2.clear();
    listWidget.clear();
    listAxisName1.append(ui->label_7);
    listAxisName1.append(ui->label_8);
    listAxisName1.append(ui->label_9);
    listAxisName2.append(ui->label_10);
    listAxisName2.append(ui->label_11);
    listAxisName2.append(ui->label_12);
    listWidget.append(ui->widget);
    listWidget.append(ui->widget_2);
    listWidget.append(ui->widget_3);
    listWidget.append(ui->widget_4);
    listWidget.append(ui->widget_5);
    listWidget.append(ui->widget_6);

    ui->tableWidget->setRowCount(REPORT_TABLE_ROW_MAX);
    ui->tableWidget->setColumnCount(REPORT_TABLE_COL);
    ui->tableWidget->setFont(QFont(FONT_TYPE, 12));
    ui->tableWidget->verticalHeader()->setVisible(false);
    ui->tableWidget->horizontalHeader()->setVisible(false);
    ui->tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection); // 只能单选
    ui->tableWidget->setAlternatingRowColors(true); // 设置交替行颜色
    ui->tableWidget->verticalHeader()->setResizeMode(QHeaderView::Stretch);
    ui->tableWidget->installEventFilter(this);

    for(Bit32 i = 0; i < REPORT_TABLE_ROW_MAX; i++)
    {
        for(Bit32 j = 0; j < REPORT_TABLE_COL; j++)
        {
            ui->tableWidget->setItem(i, j, new QTableWidgetItem());

            switch (j)
            {
            case 0:
            case 1:
                ui->tableWidget->item(i, j)->setTextAlignment(Qt::AlignVCenter | Qt::AlignRight);
                ui->tableWidget->item(i, j)->setFlags(Qt::NoItemFlags);
                break;
            case 2:
            case 4:
                ui->tableWidget->item(i, j)->setTextAlignment(Qt::AlignVCenter | Qt::AlignRight);
                break;
            case 3:
                ui->tableWidget->item(i, j)->setTextAlignment(Qt::AlignCenter);
                ui->tableWidget->item(i, j)->setText("～");
                ui->tableWidget->item(i, j)->setFlags(Qt::NoItemFlags);
                break;
            default:
                break;
            }
            ui->tableWidget->item(i, j)->setTextColor(HmiPainterColor::GetInstance()->GetTableNoFlagColor());
        }
    }
    doubleValidator = new QDoubleValidator();
    doubleValidator->setNotation(QDoubleValidator::StandardNotation);
}

OscServoReport::~OscServoReport()
{
    delete ui;
}

void OscServoReport::LoadTable()
{
    axisNum = 0;
    Bit32 axisType = 0;
    Bit8 axisName[PARAM_STR_LEN] = {'\0'};
    Bit32 i = 0;
    Bit32 ch = ActiveChan();

    for (i = 0; i < MAX_REPORT_AXIS; i++)
    {
        listAxisName1[i]->hide();
        listAxisName2[i]->hide();
        listWidget[i]->hide();
        listWidget[i + MAX_REPORT_AXIS]->hide();
    }

    for (i = 0; i < CHAN_AXES_NUM; i++)
    {
        HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetIndexAxesConf(i), &axisType);
        if (HmiOscServo::GetIndexAxesConf(i) > -1 && (axisType == 1 || axisType == 7) && axisNum < MAX_REPORT_AXIS) //  7(主轴做进给轴使用)和1一样按直线轴处理
        {
            HNC_AxisGetValue(HNC_AXIS_NAME, HmiOscServo::GetIndexAxesConf(i), axisName);
            listAxisName1[axisNum]->setText(QString(axisName));
            listAxisName2[axisNum]->setText(QString(axisName));
            listAxisName1[axisNum]->show();
            listAxisName2[axisNum]->show();
            listWidget[axisNum]->show();
            listWidget[axisNum + MAX_REPORT_AXIS]->show();
            axisNum++;
        }
        else
        {
            continue;
        }
    }

    // 速度波动×axisNum
    // 跟随误差×axisNum
    // 圆度不匹配度
    // 同步误差
    // 龙门同步
    // 数据列表
    Bit32 j = 0;
    for(Bit32 i = 0; i < REPORT_TABLE_ROW_MAX; i++)
    {
        if(i < (axisNum * 2 + 4))
        {
            ui->tableWidget->setRowHidden(i, false);

            if(i < axisNum)     // 速度波动
            {
                j = HmiOscServo::GetChanAxisIndex(HmiOscServo::GetIndexAxesConf(i));
            }
            else if(i < 2 * axisNum)    // 跟随误差
            {
                j = HmiOscServo::GetChanAxisIndex(HmiOscServo::GetIndexAxesConf(i - axisNum)) + MAX_REPORT_AXIS;
            }
            else    // 圆度不匹配度、同步误差、龙门同步（2个数据）
            {
                j = i - (2 * axisNum) + (2 * MAX_REPORT_AXIS);
            }

            if(j < 0 || j >= REPORT_STRUCT_COUNT)   // 重新计算调机报表数据存储位置后，判断是否在范围内
            {
                ui->tableWidget->setRowHidden(i, true);
                continue;
            }
            ui->tableWidget->item(i, 0)->setText(QString::number(HmiOscServo::s_ReportConf[ch][j].firstVal, 'f', 3));
            ui->tableWidget->item(i, 1)->setText(QString::number(HmiOscServo::s_ReportConf[ch][j].lastVal, 'f', 3));
            ui->tableWidget->item(i, 2)->setText(QString::number(HmiOscServo::s_ReportConf[ch][j].refValMin, 'f', 3));
            ui->tableWidget->item(i, 4)->setText(QString::number(HmiOscServo::s_ReportConf[ch][j].refValMax, 'f', 3));
        }
        else
        {
            ui->tableWidget->setRowHidden(i, true);
        }
    }

    // 主轴升降速
    ui->labelSpeUpName->setText(TR("主轴升速时间(%1 rpm)：")
                                  .arg(QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.speed * HmiOscServo::s_Conf[ch].stSpindleConf.spdlRate / 100)));
    ui->labelSpeUp->setText(QString::number(HmiOscServo::s_ReportConf[ch][(MAX_REPORT_AXIS * 2 + 4)].firstVal, 'f', 3));
    ui->labelSpeDownName->setText(TR("主轴降速时间(%1 rpm)：")
                                  .arg(QString::number(HmiOscServo::s_Conf[ch].stSpindleConf.speed * HmiOscServo::s_Conf[ch].stSpindleConf.spdlRate / 100)));
    ui->labelSpeDown->setText(QString::number(HmiOscServo::s_ReportConf[ch][(MAX_REPORT_AXIS * 2 + 4)].lastVal, 'f', 3));
    // 最远刀换刀时间
    ui->labelToolName->setText(TR("换刀时间(%1 -> %2)：").arg(QString::number(HmiOscServo::s_Conf[ch].stToolConf.curTool))
                               .arg(QString::number(HmiOscServo::s_Conf[ch].stToolConf.toolNo)));
    ui->labelTool->setText(QString::number(HmiOscServo::s_ReportConf[ch][(MAX_REPORT_AXIS * 2 + 5)].firstVal, 'f', 3));
}

void OscServoReport::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        this->LoadTable();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::SETFOCUS)
    {
        this->SetTableFoucs();
        if(messageValue == "CLEARFOCUS")
        {
            this->ClearTableFoucs();
        }
    }
}

void OscServoReport::resizeEvent(QResizeEvent *)
{
    this->firstFlag = false;
}

bool OscServoReport::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::Paint && !firstFlag) // Paint事件在ReSize事件之后响应，用于图片第一次重绘
    {
        firstFlag =true;
        Bit32 width = ui->tableWidget->width();
 
        ui->tableWidget->setColumnWidth(0, (width / 13) * 3);
        ui->tableWidget->setColumnWidth(1, (width / 13) * 3);
        ui->tableWidget->setColumnWidth(2, (width / 13) * 3);
        ui->tableWidget->horizontalHeader()->setResizeMode(3, QHeaderView::Stretch);
        ui->tableWidget->setColumnWidth(4, (width / 13) * 3);
    }
    if(target == ui->tableWidget && event->type() == QEvent::KeyPress)
    {
        Bit32 row = ui->tableWidget->currentRow();
        Bit32 col = ui->tableWidget->currentColumn();
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        switch(keyEvent->key())
        {
        case Qt::Key_Enter:
        case Qt::Key_Return:
            this->CurItemSet(row, col, "");
            return true;
        default:
            QString strKey = keyEvent->text();
            if(strKey.isEmpty() || strKey.length() > 1)
            {
                return false;
            }
            int pos = 0;
            if(doubleValidator->validate(strKey, pos) != QDoubleValidator::Invalid)   // 按键成功匹配doubleValidator验证器
            {
                this->CurItemSet(row, col, strKey);
                return true;
            }
        }
    }
    return QObject::eventFilter(target, event);
}

///////////////////////////////////////////////////////////////////////////////
//
//    Bit32 PowerCheck(void)
//
//    功能：
//			  参数调整权限检查
//
//    参数：
//
//    描述：
//
//    返回：
//			  -1: 权限不够，或密码输入错误 0：权限够，或输入正确
//
/////////////////////////////////////////////////////////////////////////////
Bit32 OscServoReport::PowerCheck()
{
    Bit32 curRight = passwd_get_rights(); // 获取当前用户权限
    if (curRight < MAC_RIGHTS) // 权限不足
    {
        QString password = "";
        Bit32 ret = PasswordInput(&password, DTYPE_STRING_(PASSWORD_SIZE), TR("请输入【机床厂家】或以上权限:"));
        if (ret == -2)
        {
            return -1;
        }

        Bit8 passBuf[STR_BUF_LEN] = {'\0'};
        snprintf(passBuf, sizeof(passBuf), "%s", password.toStdString().data());

        ret = 0;
        for(Bit32 ii = 0; ii < RD_RIGHTS; ii++)
        {
            ret = passwd_check(passBuf, ii); // 校验口令
            if(ret > 0) // 口令正确
            {
                break;
            }
        }

        if(ret > 0) // 口令正确
        {
            passwd_set_rights(ret);
            curRight = passwd_get_rights(); // 获取当前用户权限
        }
        else
        {
            MessageOut(TR("口令错误!"));
            return -1;
        }
    }

    if (curRight < MAC_RIGHTS) // 权限不足
    {
        MessageOut(TR("权限不足"));
        return -1;
    }
    MessageOut(TR(""));
    return 0;
}

void OscServoReport::CurItemSet(Bit32 row, Bit32 col, QString strVal)
{
    Bit32 ret = 0;
    Bit32 ch = ActiveChan();
    MessageTopic(TR("设置"));

     // 是否有通道正在运行
    if (AnyChanIsRunning() == 1)
    {
        MessageOut(TR("加工中不可修改参数"));
        return;
    }

    ret = this->PowerCheck();
    ui->tableWidget->setFocus();
    if (ret == -1)
    {
        return;
    }

    if(row < 0 || row >= (axisNum * 2 + 4)) // 激活时判断行号是否在范围内
    {
        return;
    }

    if(row < axisNum)     // 速度波动
    {
        row = HmiOscServo::GetChanAxisIndex(HmiOscServo::GetIndexAxesConf(row));
    }
    else if(row < 2 * axisNum)    // 跟随误差
    {
        row = HmiOscServo::GetChanAxisIndex(HmiOscServo::GetIndexAxesConf(row - axisNum)) + MAX_REPORT_AXIS;
    }
    else    // 圆度不匹配度、同步误差、龙门同步（2个数据）
    {
        row = row - (2 * axisNum) + (2 * MAX_REPORT_AXIS);
    }

    if(row < 0 || row >= REPORT_STRUCT_COUNT)   // 重新计算调机报表数据存储位置后，再次判断是否在范围内
    {
        return;
    }

    if(strVal == "")    // 回车按键
    {
        if(col == 2)
        {
            strVal = QString::number(HmiOscServo::s_ReportConf[ch][row].refValMin, 'f', 3);
        }
        else if(col == 4)
        {
            strVal = QString::number(HmiOscServo::s_ReportConf[ch][row].refValMax, 'f', 3);
        }

        ret = MessageInput(&strVal, DTYPE_FLOAT, "", 14, 4);
    }
    else                // 有效输入范围值按键
    {
        ret = MessageInput(&strVal, DTYPE_FLOAT, "", 14, 4, 0, doubleValidator, false);
    }
    if(ret < 0)
    {
        return;
    }

    bool ok = false;
    fBit64 fval = strVal.toDouble(&ok);
    if(ok == false)
    {
        return;
    }

    if(col == 2)
    {
        HmiOscServo::s_ReportConf[ch][row].refValMin = fval;
    }
    else if(col == 4)
    {
        HmiOscServo::s_ReportConf[ch][row].refValMax = fval;
    }
    HmiOscServo::OscServoDataSave();
    this->LoadTable();
    ui->tableWidget->setFocus();
}

void OscServoReport::SetTableFoucs()
{
    Bit32 row = ui->tableWidget->currentRow();
    Bit32 col = ui->tableWidget->currentColumn();

    if(row < 0 || row >= ui->tableWidget->rowCount())
    {
        row = 0;
    }
    if (col != 2 && col != 4)
    {
        col = 2;
    }
    ui->tableWidget->setCurrentCell(row, col);
    ui->tableWidget->setFocus();
}

void OscServoReport::ClearTableFoucs()
{
    ui->tableWidget->clearSelection();
    ui->tableWidget->clearFocus();
}
