﻿#ifndef NCTABLE_H
#define NCTABLE_H

#include <QModelIndex>
#include <QTableView>

class NcTable;
class NcTableModel:public QAbstractTableModel
{
    Q_OBJECT
public:
    NcTableModel(QObject *parent = 0):QAbstractTableModel(parent){
        colNum = 0;
    }

    int rowCount(const QModelIndex &parent) const;
    int columnCount(const QModelIndex &parent) const;
    QVariant data(const QModelIndex &index, int role) const;
    QVariant headerData(int section, Qt::Orientation orientation, int role) const;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);

    void SetColNum(int colNum);
    QStringList title;
    NcTable *tab;
    Qt::ItemFlags flags(const QModelIndex &index) const;
    void UpdateView(int tlrow, int tlcol, int brrow, int brcol);
    void ResetModel();
private:
    int colNum;
};

class NcTable : public QTableView
{
    Q_OBJECT
public:
    enum NcTableStyle
    {
        ComboBoxRole = Qt::UserRole + 1,
        FlagRole = Qt::UserRole + 2, // 是否可编辑，是否可选择
        ProgRunningRole = Qt::UserRole + 3, // 标志有效，程序运行时，可编辑；
        ProgHoldRole = Qt::UserRole + 4, // 标志有效，程序进给保持时可编辑；
    };
    explicit NcTable(QWidget *parent = 0);
    virtual void ContentSet(int row, int col, const QString &str);
    virtual void ContentGet(int row, int col, QString &str) const;
    virtual int RowNum(void);
    virtual QVariant RoleStyle(int row, int col, int role);
    virtual void InputType(int row, int col, long &ebxType, QString &info, long &len, long &prec, long &exitType);

    void resizeEvent(QResizeEvent *event);
    void InitProp(const QList<int> &colstrech, int pagerow, const QStringList &title);
    bool eventFilter(QObject *t, QEvent *ev);
    int FocusOn(int row, int col);
    void FocusOff();
    void Refresh();
    void RefreshSingleItem(int row, int col);
    void Redraw(void);
    virtual void InitTab(void); // 初始化标题栏(重载用)

signals:

public slots:
    void SlotDoubleClicked(QModelIndex index);
private:
    QList<int>colstrech;
    int colnum;
    int pagerow;
    int curRow;
    int curCol;
    NcTableModel *tabMode;
};



#endif // NCTABLE_H
