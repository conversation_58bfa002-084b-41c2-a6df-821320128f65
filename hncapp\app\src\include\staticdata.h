﻿#ifndef STATICDATA_H
#define STATICDATA_H

#include <QWidget>
#include <QString>

#include "alarmdata.h"
#include "progtime.h"
#include "toollife.h"
#include "usermacrodata.h"
#include "ladviewdata.h"
#include "ladtrace.h"
#include "runstatdata.h"
#include "macstate.h"
#include "turningtoolrecord.h"
#include "systime.h"
#include "acmpman.h"
#include "qrencodeshow.h"
#include "hmiloadparaexp.h"
#include "hmimcode.h"
#include "hmitoolbreak.h"
#include "simumacposdata.h"
#include "simumill.h"
#include "simuturn.h"
#include "hmialarmhelp.h"
#include "hmiworkmeas.h"
#include "teachprogram.h"
#include "turningtooloffset.h"
#include "hmiparmcheck.h"
#include "hmigdblkcurtrajstep.h"
#include "wgqr.h"
#include "cabstractqr.h"
#include "updatenetthread.h"
#include "randomrecorddata.h"
#include "hmimacro.h"
#include "hmipaintercolor.h"
#include "hmidcmmanage.h"
#include "dlgcrdrcsclear.h"
#include "hmiparamgrade.h"
#include "nccputime.h"
#include "cpulog.h"

class StaticData
{
public:
    StaticData();
    ~StaticData();
    static StaticData& Instance();
    void StaticDataRefresh();
    void StaticDataProgLoad(Bit32 ch);
    void StaticDataProgEnd(Bit32 ch);
    void QRDataSave();

public:
    QString editName; // 程序选择界面时，存焦点文件名，准备进入编辑
    Bit32 editType; // 程序选择界面时，存焦点文件类型 0:非ftp 1:ftp
    QString m_selfTestEditName; // 自检文件名，准备进入编辑
    Bit32 m_selfTestEditType; // 自检文件类型
    QString m_progTechEditName; // 示教文件名
    Bit32 compAxno; // 螺距补偿界面选中的轴
    QWidget *mainWg; // 主窗口
    QString m_thermalGcodeEditName; // 热误差G代码文件名，准备进入编辑//GTSMASK，添加新变量m_thermalGcodeEditName
    Bit32 multiChLeftChNo;
    QString m_sFileTypePath; // 批量调试,准备设备参数调整的tar
    ProgTime progTime;
    LadViewData ladViewData;
    LadTrace ladTrace;
    RunStatData runStatData;
    MacState macState;
    TurningToolRecord turningRecord;
    AcmpMan acmpman;
    QrencodeShow qrencode;
    HmiLoadParaExp loadParaExp;
    HmiToolBreak *pToolBreak;
    HmiAlarmHelp m_alarmHelp;
    TeachProgram *m_pTeachProgram;
    TurningToolOffset m_turningToolOffset;
	HmiParmCheck m_parmCheck;
    GbditemTrajOutLine m_hmiGdblkCurTrajOutLine;
    GbditemTrajStep m_hmiGdblkCurTrajStep;
    GbditemWorkMeasStep m_hmiWorkMeasStep; // 设置下雷尼绍测量
    GbditemWorkMeasStep m_hmiWorkMeasParamStep;
    GbditemWorkMeasStep m_hmiWorkMeasStepProg; // G代码编辑下雷尼绍测量
    GbditemHncMeasListStep m_hmiHncWmStepList;
    GbditemHncJogMeasStep m_hmiHncWmJogStep;
    Bit32 m_hmiGdblkMenuChange;
    HmiGdbItemFactory *m_hmiGdblkFactoryStep;
    HmiGdbItemFactory *m_hmiGdblkFactoryCyc;
    HmiWmLNSFactory *m_hmiGdblkFactoryWorkMeas;
    HmiHncMeasFactory *m_hmiHncWmGdblkFactory;
    HmiGdbItemCyc *m_hmiGdbItemCyc;
    HmiDCMManage m_hmiDCMManage;
    HmiWorkMeas m_hmiWorkMeas;
    QTime m_BackGroundLoadProcessTime;
    UpdateNetThread* m_pUpdateThread;
    RandomRecordData m_RandomRecordData;
	CAbstractQr* m_pHmiQr[WgQR::FILE_TYPE_COUNT];
    DlgCrdRcsClear* m_pDlgCrdRcsClear;
    QString m_sLastWgMask;
    QTime m_WidgetChangedTime;
    NcCpuTime m_NcCpu;
    CpuLog m_CpuLog;
};

#ifdef _NCUPPER_
class StaticData2
{
public:
    StaticData2();
    ~StaticData2();
    static StaticData2& Instance();

public:
    HmiPainterColor *painterColor;
};
#endif
#endif // STATICDATA_H
