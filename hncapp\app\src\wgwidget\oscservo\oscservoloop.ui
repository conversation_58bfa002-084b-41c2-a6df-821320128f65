<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OscServoLoop</class>
 <widget class="QWidget" name="OscServoLoop">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>706</width>
    <height>466</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QGridLayout" name="gridLayout_3" columnstretch="2,1">
   <property name="leftMargin">
    <number>2</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <property name="spacing">
    <number>2</number>
   </property>
   <item row="0" column="0">
    <widget class="QFrame" name="frame">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
   <item row="0" column="1">
    <widget class="QWidget" name="widget_2" native="true">
     <layout class="QVBoxLayout" name="verticalLayout_4" stretch="2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="backgrd3" native="true">
        <property name="selected" stdset="0">
         <bool>false</bool>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_6" stretch="1,18">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_10" stretch="1,6,1">
           <property name="spacing">
            <number>2</number>
           </property>
           <property name="leftMargin">
            <number>4</number>
           </property>
           <property name="topMargin">
            <number>2</number>
           </property>
           <property name="rightMargin">
            <number>4</number>
           </property>
           <item>
            <widget class="NcPushButton" name="leftBtn">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Ignored" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="flat">
              <bool>true</bool>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_11">
             <property name="spacing">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="labelAxisName">
               <property name="font">
                <font>
                 <family>微软雅黑</family>
                 <pointsize>12</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="text">
                <string>X轴</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="labelImg">
               <property name="text">
                <string>TextLabel</string>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <widget class="NcPushButton" name="rightBtn">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Ignored" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="flat">
              <bool>true</bool>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QGridLayout" name="gridLayout_2">
           <property name="spacing">
            <number>0</number>
           </property>
           <item row="0" column="0">
            <widget class="QTableView" name="tableView"/>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>NcPushButton</class>
   <extends>QPushButton</extends>
   <header location="global">ncpushbutton.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
