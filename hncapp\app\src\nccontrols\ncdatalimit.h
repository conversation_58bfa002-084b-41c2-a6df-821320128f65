﻿#ifndef NCDATALIMIT_H
#define NCDATALIMIT_H

#include <QValidator>

#include "hncdatatype.h"


typedef struct _NcDataLimit_
{
    Bit32 dateType;
    Bit32 mixInt;
    Bit32 maxInt;
    fBit64 mixFloat;
    fBit64 maxFloat;
    uBit8 len;
    uBit8 prec;
}NcDataLimit;


extern NcDataLimit DataTypeLimit(Bit32 ebxType, Bit32 len, Bit32 prec);
#define DTYPE_NULL_()     DataTypeLimit(DTYPE_NULL, 0, 0)

// 整型
extern NcDataLimit DataTypeLimitInt(Bit32 mix, Bit32 max, uBit8 len);
#define DTYPE_INT_(mixInt, maxInt, len)     DataTypeLimitInt(mixInt, maxInt, len)

// 无符号整形
extern NcDataLimit DataTypeLimitUInt(Bit32 mix, Bit32 max, uBit8 len);
#define DTYPE_UINT_(mixInt, maxInt, len)     DataTypeLimitUInt(mixInt, maxInt, len)

// 实型
extern NcDataLimit DataTypeLimitFloat(fBit64 mix, fBit64 max, uBit8 len, uBit8 prec);
#define DTYPE_FLOAT_(mix, max, len, prec)  DataTypeLimitFloat(mix, max, len, prec)

// 无符号实型
extern NcDataLimit DataTypeLimitUFloat(fBit64 mix, fBit64 max, uBit8 len, uBit8 prec);
#define DTYPE_UFLOAT_(mix, max, len, prec)  DataTypeLimitUFloat(mix, max, len, prec)

// 布尔
extern NcDataLimit DataTypeLimitBool();
#define DTYPE_BOOL_()    DataTypeLimitBool()

// 字符串
extern NcDataLimit DataTypeLimitStr(uBit8 len);
#define DTYPE_STRING_(len)    DataTypeLimitStr(len)

// 十六进制格式输入设置
extern NcDataLimit DataTypeLimitHex(uBit8 len);
#define DTYPE_HEX_(len)         DataTypeLimitHex(len)

// 数组格式输入设置
extern NcDataLimit DataTypeLimitArr(uBit8 len);
#define DTYPE_ARR_(len)    DataTypeLimitArr(len)

#endif // NCDATALIMIT_H
