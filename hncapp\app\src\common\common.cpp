﻿/*!
 * @file common.cpp
 * @brief 共通功能
 * @note
 *
 * @version V1.00
 * @date 2023/6/30
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */
#ifdef _LINUX
#include <errno.h>
#include <sys/time.h>
#include <sys/wait.h>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/vfs.h>
#include <linux/if.h>
#include <sys/types.h>
#endif
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <time.h>
#include <math.h>

#include <QDialog>
#include <QTextStream>
#include <QDateTime>
#include <QIODevice>
#include <QDir>

#include "datadef.h"
#include "hncaxis.h"
#include "hncchan.h"
#include "hncdatadef.h"
#include "hncdatatype.h"
#include "hncfprogman.h"
#include "hncmath.h"
#include "hncparaman.h"
#include "hncparamandef.h"
#include "hncsys.h"
#include "hncvar.h"
#include "hncsysctrl.h"
#include "hncalarm.h"
#include "hncreg.h"
#include "hncmst.h"
#include "hnccrds.h"
#include "hncverify.h"
#include "hncmodal.h"

#include "inputmanage.h"
#include "msgdata.h"
#include "msgchan.h"
#include "hmiconfig.h"
#include "ncdatalimit.h"
#include "apposdepend.h"
#include "servicedata.h"

#include "common.h"

// 输入框用
static InputManage *s_pInputMng = NULL; // 信息栏输入框
static MsgPrompt *s_pMsgPrompt;// 界面确认对话框
static MsgPromptWith3Exit *s_pMsgPromptWith3Exit;// 界面确认对话框
static bool s_bInputDlgExec = false;   // 是否正在输入框

// 替换输入框
static DlgFindAndReplace *s_pDlgFindAndReplace = NULL;

// hmi显示参数
static Bit32 s_hmiDispParm = 0x00;
static Bit32 s_IsBackGroundLoadingOff = -1;

void CommonInit(InputManage *pInputMng, MsgPrompt *pMsgPrompt,
                 MsgPromptWith3Exit *pMsgPromptWith3Exit)
{
    s_pInputMng = pInputMng;
    s_pMsgPrompt = pMsgPrompt;
    s_pMsgPromptWith3Exit = pMsgPromptWith3Exit;
}

InputManage *CommonGetInputDlg()
{
    return s_pInputMng;
}

void CommonInitFindDlg(DlgFindAndReplace *pFindDlg)
{
    s_pDlgFindAndReplace = pFindDlg;
}

DlgFindAndReplace *CommonGetFindDlg()
{
    return s_pDlgFindAndReplace;
}

size_t strlcpy(Bit8 *dst, const Bit8 *src, size_t dst_sz)
{
    size_t n;

    for(n = 0; n < dst_sz; n++)
    {
        *dst = *src;
        if ((*dst) == '\0')
        {
            break;
        }
        *dst++;
        *src++;
    }
    if (n < dst_sz)
    {
        return n;
    }
    if (n > 0)
    {
        *(dst - 1) = '\0';
    }
    return (n + strlen (src));
}

size_t strlcat(Bit8 *dst, const Bit8 *src, size_t dst_sz)
{
    size_t len = strlen(dst);
    size_t ret = 0;

    if (dst_sz < len)
    {
        /* the total size of dst is less than the string it contains; this could be considered bad input, but we might as well handle it */
        return (len + strlen(src));
    }

    ret = len + strlcpy (dst + len, src, dst_sz - len);
    return ret;
}

Bit32 CommGetTimeNum(void)
{
    time_t td;
    tm *tt;
    Bit32 timeVal = 0;

    time(&td);
    tt = localtime(&td);
    if (NULL == tt)
    {
        return 0;
    }

    timeVal = (tt->tm_hour) * 10000 + (tt->tm_min) * 100 + (tt->tm_sec);

    return timeVal;
}

Bit32 CommGetDateNum(void)
{
    time_t td;
    tm *tt;
    Bit32 dateVal = 0;

    time(&td);
    tt = localtime(&td);
    if (NULL == tt)
    {
        return 0;
    }

    dateVal = (tt->tm_year+1900) * 10000 + (tt->tm_mon+1) * 100 + (tt->tm_mday); // 时间叠加，例：2015-04-19，则dateVal = 20150419；

    return dateVal;
}

fBit64 GetValueLimit(void)
{
    fBit64 limit = 0.0;
    Bit32 unit = 0;
    Bit32 metric = GetMetricDisp();

    // 设置数据范围限制
    HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &unit);
    if (1000000 == unit)
    {
        limit = 2147.4836; // 纳米分辨率限制为1米以内（不含边界）
    }
    else if (100000 == unit)
    {
        limit = 21474.8364; // 其它分辨率限制为10米以内（不含边界）
    }
    else
    {
        limit = 214748.3647;
    }
    if (metric == 0) // 英制
    {
        limit = limit / METRIC_DISP_COEF;
    }

    return limit;
}

int IsRunning(int ch)
{
    Bit32 isHold = 0;
    Bit32 isCycle = 0;

    HNC_ChannelGetValue(HNC_CHAN_CYCLE, ch, 0, &isCycle);
    HNC_ChannelGetValue(HNC_CHAN_HOLD, ch, 0, &isHold);

    if (isHold || isCycle)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int AnyChanIsRunning()
{
    Bit32 chanNum = 0;
    Bit32 ch = 0;

    HNC_SystemGetValue(HNC_SYS_CHAN_NUM, &chanNum); //获取通道数
    for (ch = 0; ch < chanNum; ch++)
    {
        if (IsRunning(ch))
        {
            return 1;
        }
    }

    return 0;
}

int AnyChanIsRandom()
{
	Bit32 chanNum = 0;
	Bit32 ch = 0;
	Bit32 isRandomState = 0;

	HNC_SystemGetValue(HNC_SYS_CHAN_NUM, &chanNum); //获取通道数
	for (ch = 0; ch < chanNum; ch++)
	{
		HNC_MstGetRandomState(ch, &isRandomState);
		if (isRandomState != 0)
		{
			return 1;
		}
	}

	return 0;
}

bool AnyChanIsCycing()
{
    Bit32 chanNum = 0;
    Bit32 ch = 0;
    HNC_SystemGetValue(HNC_SYS_CHAN_NUM, &chanNum); //获取通道数
    for (ch = 0; ch < chanNum; ch++)
    {
        Bit32 isCycle = 0;
        HNC_ChannelGetValue(HNC_CHAN_CYCLE, ch, 0, &isCycle);
        if (isCycle != 0)
        {
            return true;
        }
    }

    return false;
}

bool IsCycingState(Bit32 ch)
{
    Bit32 isCycle = 0;
    HNC_ChannelGetValue(HNC_CHAN_CYCLE, ch, 0, &isCycle);
    if (isCycle != 0)
    {
        return true;
    }

    return false;
}

int ChanState()
{
    Bit32 chanNum = 0;
    Bit32 ch = 0;
    Bit32 isHold = 0;
    Bit32 isCycle = 0;

    HNC_SystemGetValue(HNC_SYS_CHAN_NUM, &chanNum); //获取通道数
    for (ch = 0; ch < chanNum; ch++)
    {
        HNC_ChannelGetValue(HNC_CHAN_CYCLE, ch, 0, &isCycle);
        HNC_ChannelGetValue(HNC_CHAN_HOLD, ch, 0, &isHold);

        if (isCycle == 1)
        {
            return 2;
        }
        else if (isHold == 1)
        {
            return 1;
        }
    }

    return 0;
}

int ChannalState(Bit32 ch)
{
    Bit32 chanNum = 0;
    Bit32 isHold = 0;
    Bit32 isCycle = 0;

    HNC_SystemGetValue(HNC_SYS_CHAN_NUM, &chanNum); //获取通道数
    if (ch >= 0 && ch < chanNum && ch < SYS_CHAN_NUM)
    {
        HNC_ChannelGetValue(HNC_CHAN_CYCLE, ch, 0, &isCycle);
        HNC_ChannelGetValue(HNC_CHAN_HOLD, ch, 0, &isHold);

        if (isCycle == 1)
        {
            return 2;
        }
        else if (isHold == 1)
        {
            return 1;
        }
    }

    return 0;
}

/**
 * @brief FProgLoad 加载程序
 * @param [in] ch 通道号
 * @param [in] progName 程序名
 * @return 0：加载成功 -1：加载失败 -20：参数错误 -21：通道有程序在运行
 */
Bit32 FProgLoad(Bit32 ch, Bit8 *progName)
{
    Bit32 chanNum = 0;

    HNC_SystemGetValue(HNC_SYS_CHAN_NUM, &chanNum); //获取通道数
    if (ch < 0 || ch >= chanNum || progName == NULL)
    {
        return -20;
    }

    if (IsRunning(ch) == 1)
    {
        return -21;
    }

    return HNC_FprogLoad(ch, progName);
}

int ActiveChan()
{
    Bit32 ch = 0;

    HNC_SystemGetValue(HNC_SYS_ACTIVE_CHAN, &ch);
    if (ch < 0 || ch >= SYS_CHAN_NUM)
    {
        return 0;
    }

    return ch;
}

Bit32 GetChannelNum()
{
    Bit32 channelNum = 0;
    HNC_SystemGetValue(HNC_SYS_CHAN_NUM, &channelNum); //获取通道数
    return channelNum;
}

// 显示提示信息
void MessageOut(QString message)
{
    MsgChan::Instance().TranMsg(MsgData::MSGINFO, message);
}

// 显示提示标题
void MessageTopic(QString buf)
{
    s_pInputMng->SetTopicText(buf);
}

// 从输入栏获得输入内容
Bit32 MessageInput(QString *str, Bit32 ebxType, QString info, Bit32 len, Bit32 prec, Bit32 exitType, QValidator *pVld, bool selStr, bool emptyAble)
{
    return MessageInput(str, DataTypeLimit(ebxType, len, prec), info, exitType, pVld, selStr, emptyAble);
}

Bit32 MessageInput(QString *str, NcDataLimit dataLimit, QString info, Bit32 exitType, QValidator *pVld, bool selStr, bool emptyAble)
{
    // 清除后台的提示信息，否则可能input弹框消失后，又显示后台的message信息,bug6764
    MessageOut("");

    s_bInputDlgExec = true;
    int ret = s_pInputMng->ExecAndRet(*str, dataLimit, info, exitType, pVld, false, selStr, emptyAble); // 显示输入框
    if(ret == QDialog::Accepted)
    {
        QString tmpV = s_pInputMng->GetInputText();
        bool ok = IsStrToNumOK(tmpV, dataLimit.dateType);
        if(!ok && pVld == NULL)
        {
            s_bInputDlgExec = false;
            return -1;
        }
        *str = s_pInputMng->GetInputText(); // 获取输入内容
        s_bInputDlgExec = false;
        return s_pInputMng->GetExitRet();
    }
    else
    {
        s_bInputDlgExec = false;
        return -1;
    }
}

void SetMessageInputHide()
{
    s_pInputMng->ResetInputStatus();
    s_pInputMng->hide();
}

// 从输入栏获得密码方式显示的内容
Bit32 PasswordInput(QString *str, NcDataLimit dataLimit, QString info, Bit32 exitType, QValidator *pVld, bool selStr)
{
    s_bInputDlgExec = true;

    int ret = s_pInputMng->ExecAndRet(*str, dataLimit, info, exitType, pVld, true, selStr); // 显示输入框
    if(ret == QDialog::Accepted)
    {
        *str = s_pInputMng->GetInputText(); // 获取输入内容
    }

    s_bInputDlgExec = false;
    return s_pInputMng->GetExitRet();
}

// 提示确认或取消的显示
Bit32 MessagePrompt(QString str, Bit32 style)
{
   // s_pDlgMsg->setText();
    style = style; // 暂不使用
    s_bInputDlgExec = true;
    if(s_pMsgPrompt->ExecAndRet(str) == QDialog::Accepted)
    {
        s_bInputDlgExec = false;
        return 1;
    }
    else
    {
        s_bInputDlgExec = false;
        return 0;
    }
}

int MessagePromptWith3Exit(QString str, QList<int> keyList, Bit32 style)
{
   // s_pDlgMsg->setText();
    style = style; // 暂不使用
    s_bInputDlgExec = true;
    s_pMsgPromptWith3Exit->SetKeyList(keyList);
    int key = s_pMsgPromptWith3Exit->ExecAndRet(str);
    s_bInputDlgExec = true;

    return key;
}

// 设置信息栏输入框的位置和大小
void MessageResize(Bit32 x, Bit32 y, Bit32 width, Bit32 height)
{
    if(s_pInputMng != NULL)
    {
        Bit32 lang = ComGetInputLang();
        Bit32 tmpH = height;
        if(lang == INPUT_CHN)
        {
            tmpH *= 2;
        }
        s_pInputMng->setGeometry(x, y, width, tmpH);
        s_pInputMng->setFixedHeight(tmpH); // 防止中文切回英文时花屏

    }
    if (s_pMsgPrompt != NULL)
    {
        s_pMsgPrompt->setGeometry(x, y, width, height);
        s_pMsgPrompt->setFixedHeight(height);
    }
    if(s_pDlgFindAndReplace != NULL)
    {
        s_pDlgFindAndReplace->setGeometry(x, y, width, height);
        s_pDlgFindAndReplace->setFixedHeight(height);
    }
    if(s_pMsgPromptWith3Exit != NULL)
    {
        s_pMsgPromptWith3Exit->setGeometry(x, y, width, height);
        s_pMsgPromptWith3Exit->setFixedHeight(height);
    }
}

// 获取当前输入方式 0:英文 1:汉字
Bit32 ComGetInputLang()
{
    return s_pInputMng->GetInputLang();
}

Bit32 GetPosPrec()
{
    Bit32 prec = 0;

    HNC_SystemGetValue(HNC_SYS_PREC, &prec);

    return prec;
}

Bit32 GetFPrec()
{
    Bit32 prec = 0;

    HNC_SystemGetValue(HNC_SYS_F_PREC, &prec);

    return prec;
}

Bit32 GetSpPrec()
{
    Bit32 prec = 0;

    HNC_SystemGetValue(HNC_SYS_S_PREC, &prec);

    return prec;
}

Bit32 GetMetricDisp()
{
    Bit32 metric = 0;

    HNC_SystemGetValue(HNC_SYS_METRIC_DISP, &metric);

    return metric;
}

// 是否有UPS 0:没有 1:有
Bit32 UpsExist()
{
    Bit32 tmpval = 0;
    SDataProperty prop;

    memset(&prop, 0, sizeof(prop));

    HNC_ParamanGetParaProp(PARAMAN_FILE_NCU, 0, PAR_NCU_SAVE_TYPE, PARA_PROP_VALUE, &prop);
    tmpval = prop.value.val_bool;

    return tmpval;
}

Bit32 IsVerifyState(Bit32 ch)
{
    Bit32 verifyState = 0;
    HNC_ChannelGetValue(HNC_CHAN_VERIFY, ch, 0, &verifyState);  // 获取校验模式

    if (verifyState == 1)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

Bit32 IsAnyChanIsVerify()
{
    Bit32 chanNum = 0;
    Bit32 ch = 0;

    HNC_SystemGetValue(HNC_SYS_CHAN_NUM, &chanNum); //获取通道数
    for (ch = 0; ch < chanNum; ch++)
    {
        if (IsVerifyState(ch) == 1)
        {
            return 1;
        }
    }
    return 0;

}

bool IsSimuRegistered()
{
    if (ServiceData::Instance().m_pSimuMill->GetWidRegisterFlag()
     || ServiceData::Instance().m_pSimuTurn->GetWidRegisterFlag())
    {
        return true;
    }
    return false;
}

Bit32 IsPreviewEn()
{
    if (!IsSimuRegistered())
    {
        return 0;
    }
    Bit32 previewEn = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_PREVIEW_EN, &previewEn);
    return previewEn;
}

Bit32 IsFastVerifyState(Bit32 ch)
{
    if (!IsSimuRegistered())
    {
        return 0;
    }
    Bit32 fastVerifyFlag967 = HNC_GetFastVerify(ch);
    Bit32 fastVerifyFlag = 0;

    HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_CHAN_SIMU_FAST_VERIFY_RUN, &fastVerifyFlag);  // VAR_CHAN_SIMU_FAST_VERIFY_RUN = 1134

    if (fastVerifyFlag967 == HMI_FAST_VERIFY_STOP)
    {
        if (fastVerifyFlag != 0)
        {
            fastVerifyFlag = 0;
            HNC_VarSetValue(VAR_TYPE_CHANNEL, ch, VAR_CHAN_SIMU_FAST_VERIFY_RUN, &fastVerifyFlag);  // VAR_CHAN_SIMU_FAST_VERIFY_RUN = 1134
        }
        return 0;
    }
    else if (fastVerifyFlag == 0)
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

void SetFastVerify(Bit32 ch, Bit32 flag)
{
    if (!IsSimuRegistered())
    {
        return;
    }
    Bit32 fastVerifyFlag = 1;
    HNC_VarSetValue(VAR_TYPE_CHANNEL, ch, VAR_CHAN_SIMU_FAST_VERIFY_RUN, &fastVerifyFlag);  // VAR_CHAN_SIMU_FAST_VERIFY_RUN = 1134

    HNC_SetFastVerify(ch, flag);
}

void StopFastVerify(Bit32 ch)
{
    Bit32 v = 0;

    // 清除快速校验完成标记
    v = 0;
    HNC_VarSetValue(VAR_TYPE_CHANNEL, ch, VAR_FAST_VERIFY_FINISH, &v);  // 1133  VAR_FAST_VERIFY_FINISH

    // 快速校验开启，需要清除快速校验标记
    v = 0;
    HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_CHAN_SIMU_FAST_VERIFY_RUN, &v);  // VAR_CHAN_SIMU_FAST_VERIFY_RUN = 1134
    if (v != 0)
    {
        v = 0;
        HNC_VarSetValue(VAR_TYPE_CHANNEL, ch, VAR_CHAN_SIMU_FAST_VERIFY_RUN, &v);  // VAR_CHAN_SIMU_FAST_VERIFY_RUN = 1134

		HNC_SetFastVerify(ch, HMI_FAST_VERIFY_STOP);
    }
}

// 通过参数类别，子类号，索引获取整型参数值
Bit32 ParaGetIntVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit32* value)
{
    Bit32 ret = 0;
    SParamValue sPvalue;

    if(value == NULL)
    {
        return -1;
    }

    memset(&sPvalue, 0, sizeof(sPvalue));

    ret = HNC_ParamanGetItem(fileno, subno, index, &sPvalue);
    if(ret != 0)
    {
        return -1;
    }

    *value = sPvalue.i;
    return 0;
}

// 通过参数类别，子类号，索引设置整型参数值
Bit32 ParaSetIntVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit32 value)
{
    Bit32 ret = 0;
    SParamValue sPvalue;

    memset(&sPvalue, 0, sizeof(sPvalue));

    ret = HNC_ParamanGetItem(fileno, subno, index, &sPvalue);
    if(ret != 0)
    {
        return -1;
    }

    sPvalue.i = value;

    ret = HNC_ParamanSetItem(fileno, subno, index, &sPvalue);
    if(ret != 0)
    {
        return -1;
    }

    return 0;
}

// 通过参数类别，子类号，索引获取浮点型参数值
Bit32 ParaGetFloatVal(Bit32 fileno, Bit32 subno, Bit32 index, fBit64* value)
{
    Bit32 ret = 0;
    SDataProperty prop;

    if(value == NULL)
    {
        return -1;
    }

    memset(&prop, 0, sizeof(prop));

    ret = HNC_ParamanGetParaProp(fileno, subno, index, PARA_PROP_VALUE, &prop);
    if(ret != 0)
    {
        return -1;
    }

    *value = prop.value.val_real;
    return 0;
}

// 通过参数类别，子类号，索引设置浮点型参数值
Bit32 ParaSetFloatVal(Bit32 fileno, Bit32 subno, Bit32 index, fBit64 value)
{
    Bit32 ret = 0;
    SDataProperty prop;

    memset(&prop, 0, sizeof(prop));

    ret = HNC_ParamanGetParaProp(fileno, subno, index, PARA_PROP_VALUE, &prop);
    if(ret != 0)
    {
        return -1;
    }

    prop.value.val_real = value;

    ret = HNC_ParamanSetParaProp(fileno, subno, index, PARA_PROP_VALUE, &prop);
    if(ret != 0)
    {
        return -1;
    }

    return 0;
}

// 通过参数类别，子类号，索引获取字符串型参数值
Bit32 ParaGetStrVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit8* value)
{
    Bit32 ret = 0;
    SDataProperty prop;

    memset(&prop, 0, sizeof(prop));

    if(value == NULL)
    {
        return -1;
    }

    ret = HNC_ParamanGetParaProp(fileno, subno, index, PARA_PROP_VALUE, &prop);
    if(ret != 0)
    {
        return -1;
    }

    memcpy(value, prop.value.val_byte, PARAM_STR_LEN);

    return 0;
}

// 通过参数类别，子类号，索引设置字符串参数值
Bit32 ParaSetStrVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit8* value)
{
    Bit32 ret = 0;
    SDataProperty prop;

    if(value == NULL)
    {
        return -1;
    }

    memset(&prop, 0, sizeof(prop));

    ret = HNC_ParamanGetParaProp(fileno, subno, index, PARA_PROP_VALUE, &prop);
    if(ret != 0)
    {
        return -1;
    }

    memcpy(prop.value.val_byte, value, PARAM_STR_LEN);

    ret = HNC_ParamanSetParaProp(fileno, subno, index, PARA_PROP_VALUE, &prop);
    if(ret != 0)
    {
        return -1;
    }

    return 0;
}

QByteArray StrToQByte(QString str, const Bit8 *unicode)
{
    QTextCodec *codec = QTextCodec::codecForName(unicode);
    QByteArray ba;
    ba = codec->fromUnicode(str);

    return ba;
}

QString CharToStr(const Bit8 *chars, const Bit8 *unicode)
{
    QTextCodec *codec = QTextCodec::codecForName(unicode);

    if(chars == NULL)
    {
        return "";
    }

    return codec->toUnicode(chars);
}

Bit32 ffputs(const Bit8 *str, FILE *fp)
{
	Bit32 ret = 0;
	ret = fputs(str, fp);

#ifdef _LINUX
	if (ret == 1) // Linux平台上返回值 1 代表写入成功
	{
		return 0;
	}
	else
	{
		return -1;
	}
#else
	if (ret == 0)  // windows平台上返回值 0 代表写入成功
	{
		return 0;
	}
	else
	{
		return -1;
	}
#endif

}

/**
 * @brief AxisCheckAbs 检查逻辑轴是否是绝对式编码器
 * @param ax：逻辑轴号；
 * @return true：是；false：否；
 */
bool AxisCheckAbs(Bit32 ax)
{
    Bit32 dev = -1;
    Bit32 type = 0;
    Bit32 axIdx = -1;
    Bit32 encType = 0;

    for (dev = 0; dev < SYS_PART_NUM; ++dev)
    {
        HNC_ParamanGetI32(PARAMAN_FILE_CFG, dev, PAR_DEV_TYPE, &type);
        if (type == DEV_NCOBJ_AXIS_NET || type == DEV_NCOBJ_AXIS_LOC)
        {
            HNC_ParamanGetI32(PARAMAN_FILE_CFG, dev, PAR_DEV_AX_IDX, &axIdx);
            if (axIdx == ax)
            {
                HNC_ParamanGetI32(PARAMAN_FILE_CFG, dev, PAR_DEV_AX_ENCOD_TYPE, &encType);
                if (encType == 3) // 3表示绝对式编码器
                {
                    return true;
                }
            }
        }
    }

    return false;
}

/**
 * @brief GetMdiType 获取切换MDI方式
 * @brief 在参数初始化之后
 * @return NC_MDI_TYPE(0)：NCMDI; MCP_MDI_TYPE(1)：MCPMDI
 */
ENMdiType GetMdiType()
{
    ENMdiType type = NC_MDI_TYPE;
    Bit32 val = 0;

    // VAR_SYS_MCP_MDI 3635 // 是否是MDI面板
    HNC_VarGetValue(VAR_TYPE_SYSTEM, 0, VAR_SYS_MCP_MDI, &val);
    if (val == 1)
    {
        type = MCP_MDI_TYPE;
    }

    return type;
}

/**
 * @brief GetMdiState 获取指定通道的MDI状态
 * @return 0:非MDI模式, 1:MDI模式
 */
ENMdiState GetMdiState(Bit32 ch)
{
    ENMdiState state = NOT_MDI_STATE;
    Bit32 mdiState = 0;

    HNC_ChannelGetValue(HNC_CHAN_IS_MDI, ch, 0, &mdiState);
    if (mdiState == 1)
    {
        state = MDI_STATE;
    }

    return state;
}

/**
 * @brief GetMdiState 获取活动通道的MDI状态
 * @return 0:非MDI模式, 1:MDI模式
 */
ENMdiState GetMdiState()
{
    Bit32 ch = ActiveChan();

    return GetMdiState(ch);
}

// 获取智能化功能开关
Bit32 GetCompEnableEn(Bit32 places)
{
    Bit32 flag = 0;

    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_COMP_ENABLE, &flag);
    if((flag & places) != 0)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

// 获取健康保障功能开关
Bit32 GetSelfTestEn()
{
    return GetCompEnableEn(HMI_PARM_0_PLACES);
}

// 获取热误差调试功能开关
Bit32 GetCompEn()
{
    return GetCompEnableEn(HMI_PARM_1_PLACES);
}

// 获取故障记录功能开关
Bit32 GetOscAppEn()
{
    return GetCompEnableEn(HMI_PARM_2_PLACES);
}

// 获取主轴振动规避功能开关
Bit32 GetSweeperEn()
{
    // 强制关掉本功能，因为功能内部违规强制使用了R寄存器赋值。石江勇，2020.5.6
    return 0;//GetCompEnableEn(HMI_PARM_3_PLACES);
}

// 获取全生命周期负荷图功能开关
Bit32 GetScrewwearEn()
{
    return GetCompEnableEn(HMI_PARM_4_PLACES);
}

// 获取工艺参数评估功能开关
Bit32 GetEstimateEn()
{
    return GetCompEnableEn(HMI_PARM_5_PLACES);
}

// 获取断刀检测功能开关
Bit32 GetToolBreakEn()
{
    return GetCompEnableEn(HMI_PARM_6_PLACES);
}

// 获取一键还原参数/PLC，备份伺服参数功能开关
Bit32 GetDataRestoreEn()
{
    return GetCompEnableEn(HMI_PARM_7_PLACES);
}

//获取是否打开开机一致性检测参数功能开关
Bit32 GetConsistencyCheckEn()
{
    return GetCompEnableEn(HMI_PARM_8_PLACES);
}

// 获取自整定功能开关
Bit32 GetSelfAdjustEn()
{
    return GetCompEnableEn(HMI_PARM_9_PLACES);
}

// 获取主轴负荷图功能开关
Bit32 GetSpindleWearEn()
{
    return GetCompEnableEn(HMI_PARM_10_PLACES);
}

// 获取运动仿真功能开关
Bit32 GetEmulateEn()
{
    return GetCompEnableEn(HMI_PARM_11_PLACES);
}

// 如果为从动轴,则获取从动的引导轴号
Bit32 GetLeadAxNo(Bit32 logicalAxis)
{
    if (logicalAxis < 0 || logicalAxis >= TOTAL_AXES_NUM)
    {
        return -1;
    }

    Bit32 axisRegBase = 0;
    Bit32 val = 0;
    Bit32 axis = logicalAxis;

    HNC_RegGetFGBase(REG_FG_AXIS_BASE, &axisRegBase);
    HNC_RegGetValue(REG_TYPE_F, axisRegBase * 2 + axis * AXIS_REG_NUM + REG_AX_LEAD_NO, &val); // #define REG_AX_LEAD_NO  75  //从动轴的导引轴轴号

    // #define REG_AX_LEAD_EXIST	0xE000 //此轴存在导引轴
    if ((val & 0xE000) != 0) // 轴存在导引轴
    {
        axis = val & (~0xE000);  // 计算导引轴轴号
    }
    else
    {
        axis = logicalAxis;
    }

    return axis;
}

Bit32 IsDiameter(Bit32 chAxis, Bit32 ch)
{
    if (ch < 0 || ch >= SYS_CHAN_NUM || chAxis < 0 || chAxis >= CHAN_AXES_NUM)
    {
        return 0;
    }

    if(GetChanMacTypeState(ch) == 0)
    {
        return 0;
    }

    Bit32 diameter = 0;
    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_DIAPROG, &diameter);// 直半径参数按位处理
    if ((diameter & (0x01 << chAxis)) == (0x01 << chAxis))
    {
        return 1;
    }
    return 0;
}

void ProgCurRunInfoRefresh()
{
    Bit32 val = 0;
    for (int ch = 0; ch < SYS_CHAN_NUM; ch++)
    {
        HNC_ChannelGetValue(HNC_CHAN_REFRESH_RUN_INFO, ch, 0, &val);
    }
}

/**
 * @brief DebugOutput linux系统下的调试信息输出接口
 * @param str 输出信息
 */
void DebugOutput(const QString& str)
{
    static int no = 0;
    bool fileExist = false;

    Bit8 path[PATH_NAME_LEN] = "\0";
    HNC_SysCtrlGetConfig(HNC_SYS_CFG_DATA_PATH, path); // data路径
    QString debugDataPath = QString("%1/DebugData.txt").arg(path);
    debugDataPath = QDir::toNativeSeparators(debugDataPath); // 转化为本地路径

    QFileInfo fileInfo(debugDataPath);
    fileExist = fileInfo.isFile();

    QFile debugFile(debugDataPath);
    if (!debugFile.open(QIODevice::WriteOnly|QIODevice::Append))
    {
        return;
    }

    QDateTime curTime = QDateTime::currentDateTime();

    ++no;
    QTextStream textStream(&debugFile);
    if (fileExist && no == 1)
    {
        textStream << "\n**************************************************\
                      \n**************************************************\n";
    }
    textStream << QString("No:%1").arg(no) << "(time:" << curTime.toString("yyyy-MM-dd hh:mm:ss:zzz") << ") " << str << endl;
//    textStream << "***" << str << "***" << endl;
//    textStream << "\n";

    debugFile.flush();
    debugFile.close();

    NcSync();
	FileStrFSync(debugDataPath);
}

QString TransPicName(QString path)
{
    QString styleType = QString("_") + HmiConfig::Instance().GetStyleType().toLower();
    if (path == "")
    {
        return path;
    }

    Bit8 pathCfg[PATH_NAME_LEN] = "\0";
    HNC_SysCtrlGetConfig(HNC_SYS_CFG_PIC_PATH, pathCfg);

    QString picPath = QString("%1/").arg(pathCfg);
    path = path.split("pic/").last();

    QStringList strList = path.split(".");
    QString stylePath = "";

    if (strList.count() == 1)
    {
        stylePath = picPath + strList.first() + styleType + QString(".png");
    }
    else
    {
        stylePath = picPath + strList.first() + styleType + QString(".") + strList.last();
    }

    if (QFile(stylePath).exists())
    {
        stylePath = QDir::toNativeSeparators(stylePath);
    }
    else
    {
        stylePath = QDir::toNativeSeparators(picPath + path);
    }

    return stylePath;
}

void GetFFormat(Bit32 size, Bit8* fFormat)
{
    Bit32 prec = 0;
    if(size > 0)
    {
        HNC_SystemGetValue(HNC_SYS_PREC, &prec);
        snprintf(fFormat, size, "%%.%df", prec);
    }
}

void GetSFormat(Bit32 size, Bit8* sFormat)
{
    Bit32 prec = 0;
    if(size > 0)
    {
        HNC_SystemGetValue(HNC_SYS_S_PREC, &prec);
        snprintf(sFormat, size, "%%.%df", prec);
    }
}

Bit32 QueryChannelNo(const Bit32 alarmNo)
{
    Bit32 levelRadix = 10000000;
    Bit32 subRadix = 10000;

    return (alarmNo % levelRadix) / subRadix;
}

bool ChanHaveSyntaxAlarm(Bit32 ch)
{
    Bit32 syntaxAlarmNum = 0;
    Bit32 alarmNo = 0;
    Bit8 alarmTxt[128] = {0};

    if (ch < 0 || ch >= SYS_CHAN_NUM)
    {
        return false;
    }

    HNC_AlarmGetNum(ALARM_PS, ALARM_ERR, &syntaxAlarmNum);
    if (syntaxAlarmNum > 0)
    {
        for (Bit32 i32 = 0; i32 < syntaxAlarmNum; i32++)
        {
            HNC_AlarmGetData(ALARM_PS, ALARM_ERR, i32, &alarmNo, alarmTxt);
            if (QueryChannelNo(alarmNo) == ch)
            {
                return true;
            }
        }
    }

    return false;
}

bool IsInputDlgExec()
{
    return s_bInputDlgExec;
}

void GetCurProgName(Bit32 ch, Bit8 progName[PATH_NAME_LEN])
{
    Bit32 runProgId = 0;
    HNC_ChannelGetValue(HNC_CHAN_RUN_PROG, ch, 0, &runProgId);
    if (runProgId == (MDI_PROG_IDX+ch))//55对应于progman.h中MDI_PROG_IDX (使用_HNC_30_宏，SYS_CHAN_NUM=4)
    {
        snprintf(progName, PATH_NAME_LEN, "MDI");
    }
    else if (runProgId == (GIVEN_PROG_IDX+ch))//51对应于progman.h中GIVEN_PROG_IDX(使用_HNC_30_宏，SYS_CHAN_NUM=4)
    {
        snprintf(progName, PATH_NAME_LEN, "omst.nc");
    }
    else
    {
        HNC_FprogGetProgPathByIdx(runProgId, progName);
    }
}

void GetCurDcdProgName(Bit32 ch, Bit8 progName[PATH_NAME_LEN])
{
    Bit32 runProgId = 0;
    HNC_ChannelGetValue(HNC_PROG_DCD_IDX, ch, 0, &runProgId);
    if (runProgId == (MDI_PROG_IDX+ch))//55对应于progman.h中MDI_PROG_IDX (使用_HNC_30_宏，SYS_CHAN_NUM=4)
    {
        snprintf(progName, PATH_NAME_LEN, "MDI");
    }
    else if (runProgId == (GIVEN_PROG_IDX+ch))//51对应于progman.h中GIVEN_PROG_IDX(使用_HNC_30_宏，SYS_CHAN_NUM=4)
    {
        snprintf(progName, PATH_NAME_LEN, "omst.nc");
    }
    else
    {
        HNC_FprogGetProgPathByIdx(runProgId, progName);
    }
}

bool IsStrToNumOK(QString str, Bit32 type)
{
    bool ok;
    switch (type) {
    case DTYPE_INT:
        str.toInt(&ok);
        break;
    case DTYPE_FLOAT:
        str.toDouble(&ok);
        break;
    case DTYPE_UINT:
        str.toUInt(&ok);
        break;
    default:
        ok = true;
        break;
    }

    return ok;
}

bool IsPosNameFanuc()
{
    if((s_hmiDispParm & 0x02) == 0x02)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool IsProgHeiAutoChg()
{
    if((s_hmiDispParm & 0x01) == 0x01)
    {
        return true;
    }
    else
    {
        return false;
    }
}

/*!
 * \brief GetFilelistCodeType filelist编码格式
 * \return 0:GBK 1:UTF-8
 */
Bit32 GetFilelistCodeType()
{
    if((s_hmiDispParm & 0x0010) == 0x0010)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

void SetHmiDispParm(Bit32 val)
{
    s_hmiDispParm = val;
}

void ShowReplaceDlg()
{
    s_pDlgFindAndReplace->InitAndExec();
}

/**
 * @brief IsChValid 判断通道是否有效
 * @param [in] ch 通道值
 * @return true:通道值有效；false：通道值无效
 */
bool IsChValid(Bit32 ch)
{
    if (ch >= 0 && ch < SYS_CHAN_NUM)
    {
        return true;
    }

    return false;
}

void CommSetCycForbid(bool f)
{
    Bit32 base = 0;

    HNC_RegGetFGBase(REG_FG_SYS_BASE, &base);
    if (f)
    {
        HNC_RegSetBit(REG_TYPE_F, base * 2 + 0, 13);    // #define SYS_FORBID_RUN	0x2000 //系统禁止循环启动标志
    }
    else
    {
        HNC_RegClrBit(REG_TYPE_F, base * 2 + 0, 13);    // #define SYS_FORBID_RUN	0x2000 //系统禁止循环启动标志
    }
}

QString TeachPosStr(fBit64 fval)
{
    QString str = RealPos2Str(fval);
    QString str2 = RemoveExtraZeroFormStr(str);

    return str2;
}

QString RemoveExtraZeroFormStr(QString str)
{
    Bit32 index = str.indexOf('.');
    if (index <= 0)
    {
        return str;
    }

    Bit32 len = str.length();
    Bit32 count = 0;
    for (int i = len - 1; i >= index; i--)
    {
        if (str.at(i) == '0')
        {
            count++;
        }
        else if (str.at(i) == '.')
        {
            break;
        }
        else
        {
            break;
        }
    }
    Bit32 pos = len - count;

    return str.left(pos);
}

QString RealPos2Str(fBit64 fval) // "UM方式输入"要求G代码中浮点类型必须带小数点
{
    QString valStr = Pos2Str(fval);
    if (valStr.contains(".") == false)
    {
        valStr += QString(".");
    }
    return valStr;
}

QString Feed2Str(fBit64 fval) // "UM方式输入"要求G代码中浮点类型必须带小数点
{
    Bit32 fPrec = 0;
    HNC_SystemGetValue(HNC_SYS_F_PREC, &fPrec);
    QString valStr = QString("%1").arg(fval, 0, 'f', fPrec);
    if (valStr.contains(".") == false)
    {
        valStr += QString(".");
    }
    return valStr;
}

Bit32 GetXmlDoc(Bit32 pathType, QString path, QString name, QDomDocument &doc)
{
     QString errorMsg("");
     int errorLine = 0;
     int errorColumn = 0;
     QString docPath = path + name;

    Bit32 ret = GetXMLElement(pathType, docPath, doc,
                              &errorMsg, &errorLine, &errorColumn);
    if (ret == -2)
    {
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("%1文件打开失败").arg(name));
    }
    else if (ret == -3)
    {
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("%1文件出错(行:%2,列:%3,%4)").arg(name)
                         .arg(errorLine).arg(errorColumn).arg(errorMsg));
    }
    return ret;
}

Bit32 GetXMLElement(Bit32 pathType, QString XMLName, QDomDocument &doc, QString *errorMsg, int *errorLine, int *errorColumn)
{
    Bit8 dataPath[PATH_NAME_LEN] = {0};
    QString filepath = "";

    HNC_SysCtrlGetConfig(pathType, dataPath);
    filepath = QString("%1/%2").arg(dataPath).arg(XMLName);
    filepath = QDir::toNativeSeparators(filepath); // 转化为本地路径

    QFile file(filepath);
    if(!file.exists())
    {
        return -1;
    }
    if (!file.open(QFile::ReadOnly | QFile::Text))
    {
        return -2;
    }

    if (!doc.setContent(&file, false, errorMsg, errorLine, errorColumn))
    {
        return -3;
    }

    file.close();
    return 0;
}

Bit32 GetXMLElement(QString fullPath, QDomDocument &doc, QString *errorMsg, int *errorLine, int *errorColumn)
{
    QString filepath = "";
    filepath = QDir::toNativeSeparators(fullPath); // 转化为本地路径

    QFile file(filepath);
    if(!file.exists())
    {
        return -1;
    }
    if (!file.open(QFile::ReadOnly | QFile::Text))
    {
        return -2;
    }

    if (!doc.setContent(&file, false, errorMsg, errorLine, errorColumn))
    {
        return -3;
    }

    file.close();
    return 0;
}

fBit64 TransMetric2Show(fBit64 val)
{
    Bit32 metric = GetMetricDisp();

    if(metric == 0)
    {
        val /= METRIC_DISP_COEF;
    }
    return val;
}

fBit64 TransMetric2Save(fBit64 val)
{
    Bit32 metric = GetMetricDisp();

    if(metric == 0)
    {
        val *= METRIC_DISP_COEF;
    }

    return val;
}

fBit64 TransAxisUnit2Show(Bit32 axis, fBit64 val)
{
    Bit32 transUnit = 1;
    Bit32 axType = 0;
    HNC_AxisGetValue(HNC_AXIS_TYPE, axis, &axType);
    if(axis >= 0 && axType < 0)
    {
        return val;
    }
    else if(axis < 0 || 1 == axType || 7 == axType)
    {
        HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &transUnit);
    }
    else
    {
        HNC_SystemGetValue(HNC_SYS_TURN_UNIT, &transUnit);
    }

    if(transUnit == 0) // 除零保护
    {
        transUnit = 1;
    }

    val /= transUnit;
    return val;
}

QString Pos2Str(fBit64 fval)
{
    Bit32 prec = 0;
    HNC_SystemGetValue(HNC_SYS_PREC, &prec);

    QString valStr = QString("%1").arg(fval, 0, 'f', prec);
    if (HNC_DoubleCompare(valStr.toDouble(), 0.0) == 0)
    {
        fval = 0.0;
        valStr = QString("%1").arg(fval, 0, 'f', prec); // 解决显示-0.000问题
    }
    return valStr;
}

Bit32 GetMacChCurType(Bit32 ch)
{
    Bit32 macType = 0;
    Bit32 macCurType = -1;

    HNC_ChannelGetValue(HNC_CHAN_MAC_TYPE, ch, 0, &macType);

    if (macType == 2)
    {
        HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_TM_MAC_CUR_TYPE, &macCurType); //  1148
    }

    return macCurType;
}

Bit32 SwitchNoByMask(Bit32 ch, Bit32 axList[CHAN_AXES_NUM], Bit32 flag)
{
    Bit32 mask = 0;
    Bit32 src1 = 0;
    Bit32 src2 = 0;
    Bit32 dstIdx1 = -1;
    Bit32 dstIdx2 = -1;
    Bit32 ret = 0;

    HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_CHANGE_AX_MASK, &mask); // VAR_CHANGE_AX_MASK 动态切换的两轴掩码

    if(!IsAxisChangeCord(ch))
    {
        return 0;
    }

    if(mask == 6) // yz互换
    {
        if(flag == 0)
        {
            ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_YINDEX, &src1);
            ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_ZINDEX, &src2);
        }
        else
        {
            src1 = 1;
            src2 = 2;
        }
    }
    else if(mask == 3) // xy互换
    {
        if(flag == 0)
        {
            ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_XINDEX, &src1);
            ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_YINDEX, &src2);
        }
        else
        {
            src1 = 0;
            src2 = 1;
        }
    }
    else if(mask == 5) // xz切换
    {
        if(flag == 0)
        {
            ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_XINDEX, &src1);
            ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_ZINDEX, &src2);
        }
        else
        {
            src1 = 0;
            src2 = 2;
        }
    }

    if(src1 == -1 || src2 == -1)
    {
        return 0;
    }

    for(int i = 0; i < CHAN_AXES_NUM; i++)
    {
        if(axList[i] == src1)
        {
            dstIdx1 = i;
        }
        else if(axList[i] == src2)
        {
            dstIdx2 = i;
        }

        if(dstIdx1 >= 0 && dstIdx2 >= 0)
        {
            Bit32 tmp = axList[dstIdx1];
            axList[dstIdx1] = axList[dstIdx2];
            axList[dstIdx2] = tmp;
            ret = 1;

            break;
        }
    }

    if(ret == 1) // 若存在轴交换，则寻找交换的轴是否是其他从动轴的主动轴
    {
        for(int i = 0; i < CHAN_AXES_NUM; i++)
        {
            Bit32 leadAx = GetLeadAxNo(axList[i]);
            if(leadAx != axList[i]) // 该轴是从动轴
            {
                if(leadAx == src1)
                {
                    axList[i] = axList[dstIdx1];
                }
                else if(leadAx == src2)
                {
                    axList[i] = axList[dstIdx2];
                }
            }
        }
    }

    return ret;
}

bool SimuTurnIsMirrored(Bit32 ch)
{
    Bit32 turnType = 0;
    ParaGetIntVal(PARAMAN_FILE_CHAN, ch, PAR_CH_MACTURN, &turnType);
    if (turnType == 1)
    {
        return false;
    }

    Bit32 mirrored = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_HMISHOW, &mirrored);
    if ((mirrored & TURN_SIMU_MIRRORED) == TURN_SIMU_MIRRORED)
    {
        return true;
    }
    return false;
}

/**
 * @brief GetAxisType 获取轴类型（主轴，直线轴，旋转轴等）
 * @param [in] logicAxisNo 逻辑轴号
 * @return 轴类型，-1代表无效轴
 */
Bit32 GetAxisType(Bit32 logicAxisNo)
{
    Bit32 axType = -1;                      // 默认为无效轴
    if (logicAxisNo < 0 || logicAxisNo >= TOTAL_AXES_NUM)
    {
        return axType;
    }

    ParaGetIntVal(PARAMAN_FILE_AXIS, logicAxisNo, PAR_AX_TYPE, &axType);
    return axType;
}

void Num2Asc(int num, uBit8 *buf)
{
    if (num < 0 || num >= 10000)
    {
        return;
    }

    int tmp[4] = {0};
    tmp[0] = num / 1000;                    // 千位
    tmp[1] = (num - tmp[0] * 1000) / 100;   // 百位
    tmp[2] = num % 100 / 10;                // 十位
    tmp[3] = num % 10;                      // 个位

    for (int ii = 0; ii < 4; ii++)
    {
        buf[ii] = tmp[ii] + '0';
    }
}

Bit32 GetChanMacTypeState(Bit32 ch)
{
    if(ch < 0 || ch >= GetChannelNum())
    {
        return 0;
    }

    Bit32 macType = 0;
    Bit32 macCurType = 0;
    ParaGetIntVal(PARAMAN_FILE_MAC, 0, PAR_MAC_CHAN_TYPE + ch, &macType);
    HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_TM_MAC_CUR_TYPE, &macCurType); // VAR_TM_MAC_CUR_TYPE 1148

    if (0 == macType || (macType == 2 && macCurType == 0))
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

bool IsAxisChangeCord(Bit32 ch)
{
    Bit32 type = GetMacChCurType(ch);
    Bit32 mask = 0;
    Bit32 tmp = 0;

    ParaGetIntVal(PARAMAN_FILE_MAC, 0, PAR_MAC_NEW_FUCTION_TEST2, &tmp); // GETD_AXIS_CHANGE_COORD 抓取坐标系
    HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_CHANGE_AX_MASK, &mask); // 989 动态切换的两轴掩码
    if(mask != 0 && type >= 0 && ((tmp & GETD_AXIS_CHANGE_COORD) != 0)) // 车铣复合专机需求,此处坐标系的通道轴已经交换了
    {
        return true;
    }

    return false;
}

Bit32 GetMacType(Bit32 ch)
{
    Bit32 macType = 0;
    Bit32 macCurType = -1;

    HNC_ChannelGetValue(HNC_CHAN_MAC_TYPE, ch, 0, &macType);

    if (macType == 2)
    {
        HNC_VarGetValue(VAR_TYPE_CHANNEL, ch, VAR_TM_MAC_CUR_TYPE, &macCurType); // VAR_TM_MAC_CUR_TYPE 1148
        return macCurType;
    }

    return macType;
}

bool IsBackGroundLoadingOff()
{
    if (s_IsBackGroundLoadingOff < 0)
    {
        ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_HMISHOW, &s_IsBackGroundLoadingOff);
    }
    if((s_IsBackGroundLoadingOff & HMI_BACKGROUND_LOADING_OFF) == HMI_BACKGROUND_LOADING_OFF)
    {
        return true;
    }
    else
    {
        return false;
    }
}

ENServoType GetServoType(Bit32 logicAxisNo)
{
    ENServoType servoType = SERVO_INVALID;
	if (!IsLogicAxisNoValid(logicAxisNo))
	{
		return servoType;
	}
    
#if 0 // 因伺服上传的伺服类型一直为2002，无法通过设备类型判断伺服为进给伺服还是主轴伺服
	Bit8 devName[STR_BUF_LEN] = "";
	Bit32 devType = 0;
	Bit32 axisId = 0;

	for (Bit32 subNo = 0; subNo < SYS_NCOBJ_NUM; subNo++) // 获取有对应设备的轴,设置伺服参数
	{
		ParaGetStrVal(PARAMAN_FILE_CFG, subNo, PAR_DEV_NAME, devName);
		if ((strcmp(devName, "ECAT-AX") == 0) || // EtherCat设备
			(strcmp(devName, "M3-AX") == 0)) // M3设备
		{
			continue;
		}

		ParaGetIntVal(PARAMAN_FILE_CFG, subNo, PAR_DEV_TYPE, &devType);
		if (devType != DEV_NCOBJ_SPDL_LOC && devType != DEV_NCOBJ_SPDL_NET
			&& devType != DEV_NCOBJ_AXIS_LOC && devType != DEV_NCOBJ_AXIS_NET)
		{
			continue;
		}

		ParaGetIntVal(PARAMAN_FILE_CFG, subNo, PAR_DEV_AX_IDX, &axisId);
		if (axisId == logicAxisNo)
		{
			if (devType == DEV_NCOBJ_SPDL_LOC || devType == DEV_NCOBJ_SPDL_NET)
			{
				servoType = SERVO_SPDL;
			}
			else if (devType == DEV_NCOBJ_AXIS_LOC || devType == DEV_NCOBJ_AXIS_NET)
			{
				servoType = SERVO_FEED;
			}

			break;
		}
	}
#endif

    // 通过轴类型来判断是进给轴伺服还是主轴伺服
	Bit32 axType = 0;
	HNC_AxisGetValue(HNC_AXIS_TYPE, logicAxisNo, &axType);

	if (axType == 1 || axType == 2 || axType == 3 || axType == 9) // 直线轴/旋转轴/进给主轴
	{
        servoType = SERVO_FEED;
	}
	else if (axType == 7 || axType == 8 || axType == 10) // 主轴进给/主轴
	{
        servoType = SERVO_SPDL;
	}
	else
	{
        servoType = SERVO_FEED;
	}

	return servoType;
}

/*!
 * \brief GetAxisDeviceNo 获取轴对应的设备号
 * \return 设备号
 */
Bit32 GetAxisDeviceNo(Bit32 logicAxisNo)
{
	Bit32 devNo = -1;
	if (!IsLogicAxisNoValid(logicAxisNo))
	{
		return devNo;
	}

	Bit32 devType = 0;
	Bit32 axisId = 0;

	for (Bit32 subNo = 0; subNo < SYS_NCOBJ_NUM; subNo++) // 获取有对应设备的轴,设置伺服参数
	{
		ParaGetIntVal(PARAMAN_FILE_CFG, subNo, PAR_DEV_TYPE, &devType);
		if (devType != DEV_NCOBJ_SPDL_LOC && devType != DEV_NCOBJ_SPDL_NET
			&& devType != DEV_NCOBJ_AXIS_LOC && devType != DEV_NCOBJ_AXIS_NET)
		{
			continue;
		}

		ParaGetIntVal(PARAMAN_FILE_CFG, subNo, PAR_DEV_AX_IDX, &axisId);
		if (axisId == logicAxisNo)
		{
            devNo = subNo;
			break;
		}
	}

	return devNo;
}

/*!
 * \brief IsNCUCBusAxis 轴是否为NCUC总线
 * \return true:是;false:否
 */
bool IsNCUCBusAxis(Bit32 logicAxisNo)
{
	Bit32 devNo = -1;
	if (!IsLogicAxisNoValid(logicAxisNo))
	{
		return false;
	}

    devNo = GetAxisDeviceNo(logicAxisNo);
	Bit8 devName[STR_BUF_LEN] = "";

    if (devNo < 0)
    {
        return false;
    }

	ParaGetStrVal(PARAMAN_FILE_CFG, devNo, PAR_DEV_NAME, devName);
	if ((strcmp(devName, "ECAT-AX") == 0) || // EtherCat设备
		(strcmp(devName, "M3-AX") == 0)) // M3设备
	{
		return false;
	}

	return true;
}

bool IsChannelValid(Bit32 ch)
{
	if (ch < 0 || ch >= SYS_CHAN_NUM)
	{
		return false;
	}

    Bit32 nChNum = 0;
    ParaGetIntVal(PARAMAN_FILE_MAC, 0, PAR_MAC_CHAN_NUM, &nChNum);
    if (ch >= nChNum)
    {
        return false;
    }

	return true;
}

bool IsLogicAxisNoValid(Bit32 logicAxisNo)
{
	if (logicAxisNo < 0 || logicAxisNo >= TOTAL_AXES_NUM)
	{
		return false;
	}

	return true;
}

bool IsProgPrintOn()
{
    Bit32 val = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_G_PRINT_TYPE, &val);
    if (val > 0)
    {
        return true;
    }
    return false;
}

bool IsProgPrintShowDlg()
{
    Bit32 val = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_G_PRINT_TYPE, &val);
    if (val > 1)
    {
        return true;
    }
    return false;
}

bool IsOnRtcp(Bit32 ch)
{
    Bit32 val = 0;
    Bit32 regFBase = 0; // f寄存器基址
    HNC_RegGetFGBase(REG_FG_CHAN_BASE, &regFBase);
    HNC_RegGetValue(REG_TYPE_F, regFBase * 2 + ch * CHAN_REG_NUM + REG_CH_STAT1, &val); // REG_CH_STAT1 = 1, CH_RTCP_ON = 0x2000

    if ((val & CH_RTCP_ON) == CH_RTCP_ON)
    {
        return true;
    }
    return false;
}

/**
 * @brief IsGcodeNameForbit 传入文件名/路径是否可用
 * @param filePath 文件名或文件路径
 * @return true if name is forbit, otherwise return false
 */
bool IsGcodeNameForbid(QString filePath)
{
    //返回1:表示当前文件名不能使用，不能用来保存，重命名G代码
    //返回0:表示当前文件名可以使用，例如保存和重命名均可以
    Bit8 filePathName[PATH_NAME_LEN] = {0};
    const Bit8 *SYS_PROGNAME_TAG = "CYC_";//progman.h HNC_SYS_PROGNAME_TAG
    Bit32 filePathNameLen = 0;
    Bit32 i = 0, dir_idx = 0, offset = 0;

    filePathNameLen = sizeof(filePath.toStdString().data());
    if (filePathNameLen >= PATH_NAME_LEN)
    {
        return true;
    }

    strcpy(filePathName, filePath.toStdString().data());
    filePathNameLen = strlen(filePathName);
    if (strstr(filePathName, SYS_PROGNAME_TAG) != NULL)
    {
        for (i = 0; i < filePathNameLen, i < PATH_NAME_LEN; i++)
        {
            if (filePathName[i] == DIR_SEPARATOR)
            {
                dir_idx = i;
            }
        }

        //传入的可能是带路径的文件名，也可能直接就是文件名
        offset = (dir_idx == 0)?0:dir_idx+1;
        if (memcmp(filePathName + offset, SYS_PROGNAME_TAG, 4) == 0)
        {
            return true;
        }
    }

    return false;
}

bool Is5AxisOn()
{
    Bit32 val = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_5AXIS_ENABLE, &val);
    if (val == 1)
    {
        return true;
    }
    else
    {
        return false;
    }
}

bool Is5AxisFoolProofingOn()
{
    Bit32 val = 0;
    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_HMISHOW, &val);
    if ((val & IS_5AXIS_FOOL_PROOFING_ON) == IS_5AXIS_FOOL_PROOFING_ON)
    {
        return true;
    }
    else
    {
        return false;
    }
}

/*!
 * \brief GetLanguageFilePath 获得支持多语言文件路径
 * \param filePath  输出文件路径
 * \param fileName  输入文件名
 * \param dirType   输入目录类型SysConfigType
 * \return 返回文件路径对应的系统语言  例如：输入USERP.STR文件，若当前为英文时输出../parm/USERP_en.STR文件路径；若当前为俄文时输出../parm/USERP_ru.STR文件路径
 */
Bit32 GetLanguageFilePath(Bit8* filePath, Bit8* fileName, Bit32 dirType)
{
    if (strlen(fileName) == 0)
    {
        return -1;
    }
    if (dirType < HNC_SYS_CFG_BIN_PATH || dirType >= HNC_SYS_CONFIG_TOTAL)
    {
        return -1;
    }

    Bit32 tmp = 0;
    Bit32 lang = 0;
    Bit8 dirPath[PATH_NAME_LEN] = { '\0' };
    Bit8 firstName[PATH_NAME_LEN] = "\0";		// 前缀
    Bit8 expName[PATH_NAME_LEN] = "\0";			// 后缀
    Bit8 tmpFileName[PATH_NAME_LEN] = "\0";	// 文件名

    snprintf(tmpFileName, PATH_NAME_LEN, "%s", fileName);

    ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_LANGUAGE, &lang);
    switch (lang)
    {
    case LANG_EN:
        tmp = sscanf(fileName, "%[^.].%[^.]", firstName, expName);
        if (tmp == 2)
        {
            snprintf(tmpFileName, PATH_NAME_LEN, "%s_en.%s", firstName, expName);
        }
        break;
    case LANG_RU:
        tmp = sscanf(fileName, "%[^.].%[^.]", firstName, expName);
        if (tmp == 2)
        {
            snprintf(tmpFileName, PATH_NAME_LEN, "%s_ru.%s", firstName, expName);
        }
        break;
    default:
        lang = LANG_CH;
        break;
    }

    HNC_SysCtrlGetConfig(dirType, dirPath);
    snprintf(filePath, PATH_NAME_LEN, "%s%c%s", dirPath, DIR_SEPARATOR, tmpFileName);

    // 取当前系统语言对应文件时，检测文件是否存在，不存在时返回中文语言文件
    if (access(filePath, 0) != 0)
    {
        lang = LANG_CH;
        snprintf(filePath, PATH_NAME_LEN, "%s%c%s", dirPath, DIR_SEPARATOR, fileName);
    }

    return lang;
}
