﻿#include <QKeyEvent>
#include <QScrollBar>

#include "nccombobox.h"
#include "common.h"

#include "ncscrollarea.h"
#include "ui_ncscrollarea.h"

NcScrollArea::NcScrollArea(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::NcScrollArea)
{
    ui->setupUi(this);
    m_pDirMoveLayout = new DirMoveLayout(this);

    m_nWidgetIdx.clear();
    m_scrollAreaItems.clear();
    m_nItemIdx.clear();
    m_titleFont = QFont(FONT_TYPE, 12);
    m_titleFont.setBold(true);
    m_nameFont = QFont(FONT_TYPE, 10);
    m_nameFont.setBold(true);
    m_editFont = QFont(FONT_TYPE, 10);
    m_tipsFont = QFont(FONT_TYPE, 10);
    m_pHorizontalSpacer = new QSpacerItem(1, 1, QSizePolicy::Expanding, QSizePolicy::Minimum);

    connect(m_pDirMoveLayout, SIGNAL(FoucuChanged(int)), this, SLOT(FocusChangedSlot(int)));

    this->installEventFilter(this);
}

NcScrollArea::~NcScrollArea()
{
    for (int i = m_scrollAreaItems.count() - 1; i >= 0; i--)
    {
        QWidget* w = m_scrollAreaItems.takeAt(i);
        if (w != NULL)
        {
            delete w;
        }
    }
    delete ui;
}

void NcScrollArea::InstallInsideObjEventFilter(QWidget *wg)
{
    ui->scrollArea->installEventFilter(wg);
}

bool NcScrollArea::eventFilter(QObject *target, QEvent *event)
{
    if (event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        QWidget *wg = static_cast<QWidget*>(target);

        QList<QWidget* > l = this->findChildren< QWidget *>();
        if (target == this || (l.contains(wg) && m_widgets.contains(wg) == false))
        {
            return true;
        }
        if (keyEvent->key() == Qt::Key_Up)
        {
            if (m_pDirMoveLayout->GetCurFocusIdx() == 0)
            {
                ui->scrollArea->verticalScrollBar()->setValue(0);
            }
        }
        if (keyEvent->key() == Qt::Key_PageUp)
        {
            PageUp();
            return true;
        }
        if (keyEvent->key() == Qt::Key_PageDown)
        {
            PageDown();
            return true;
        }
    }
    return QObject::eventFilter(target, event);
}

void NcScrollArea::PageUp()
{
    QWidget *w = ui->scrollArea->widget()->focusWidget();
    if (w == NULL || m_widgets.count() == 0)
    {
        return;
    }
    Bit32 idx = m_widgets.indexOf(w);
    if (idx < 0)
    {
        return;
    }

    Bit32 val = ui->scrollArea->verticalScrollBar()->value();
    Bit32 widgetY = w->pos().y();
    Bit32 areaHeight = this->height() - w->height();
    for (int i = idx ; i >= 0; i--)
    {
        QWidget *w = m_widgets.at(i);
        if (w != NULL && w->isVisible() && w->pos().y() <= widgetY - areaHeight)
        {
            ui->scrollArea->verticalScrollBar()->setValue(val - areaHeight);
            m_pDirMoveLayout->FocusOn(i, 0);
            return;
        }
    }
    ui->scrollArea->verticalScrollBar()->setValue(0);
    m_pDirMoveLayout->FocusOn(0, 0);
}

void NcScrollArea::PageDown()
{
    QWidget *w = ui->scrollArea->widget()->focusWidget();
    if (w == NULL || m_widgets.count() == 0)
    {
        return;
    }
    Bit32 idx = m_widgets.indexOf(w);
    if (idx < 0)
    {
        return;
    }

    Bit32 val = ui->scrollArea->verticalScrollBar()->value();
    Bit32 widgetY = w->pos().y();
    Bit32 areaHeight = this->height() - w->height();
    for (int i = idx ; i < m_widgets.count(); i++)
    {
        QWidget *w = m_widgets.at(i);
        if (w != NULL && w->isVisible() && w->pos().y() > widgetY + areaHeight)
        {
            ui->scrollArea->verticalScrollBar()->setValue(val + areaHeight);
            m_pDirMoveLayout->FocusOn(i, 0);
            return;
        }
    }
    m_pDirMoveLayout->FocusOn(m_widgets.count() - 1, 0);
}


void NcScrollArea::Redraw()
{
    if (GetItemsCount() == m_nItemIdx.count())
    {
        if (this->Refresh())
        {
            return;
        }
    }
    for (int i = m_scrollAreaItems.count() - 1; i >= 0; i--)
    {
        QWidget *w = m_scrollAreaItems.takeAt(i);
        ui->gridLayout->removeWidget(w);
        delete w;
    }
    ui->gridLayout->removeItem(m_pHorizontalSpacer);
    m_widgets.clear();
    m_nItemIdx.clear();
    m_nWidgetIdx.clear();

    Bit32 itemIdx = 0;
    for(Bit32 i = 0; i < GetItemsCount(); i++)
    {
        Bit32 showType = GetItemsType(i);
        if (showType == BLK_EDIT || showType == BLK_COMBOBOX)
        {
            QLabel* nameLabel = new QLabel(this);
            nameLabel->setFont(m_nameFont);
            ui->gridLayout->addWidget(nameLabel, itemIdx, 0);
            m_scrollAreaItems.append(nameLabel);
            itemIdx++;
        }
        else if (showType == BLK_LABEL)
        {
            QLabel* nameLabel = new QLabel(this);
            nameLabel->setFont(m_titleFont);
            ui->gridLayout->addWidget(nameLabel, itemIdx, 0, 1, 2);
            m_scrollAreaItems.append(nameLabel);
            itemIdx++;
        }
        else if (showType == BLK_TIPS)
        {
            QLabel* nameLabel = new QLabel(this);
            nameLabel->setFont(m_tipsFont);
            nameLabel->setWordWrap(true);
            ui->gridLayout->addWidget(nameLabel, itemIdx, 0, 1, 2);
            m_scrollAreaItems.append(nameLabel);
            itemIdx++;
        }
        else if (showType == BLK_LINE)
        {
            QFrame *line = new QFrame(this);
            line->setFrameShape(QFrame::HLine);
            line->setFrameShadow(QFrame::Sunken);
            ui->gridLayout->addWidget(line, itemIdx, 0, 1, 2);
            m_scrollAreaItems.append(line);
            itemIdx++;
        }
        m_nItemIdx.append(itemIdx - 1);

        if (showType == BLK_EDIT)
        {
            QLabel* l = new QLabel(this);
            l->setFocusPolicy(Qt::StrongFocus);
            l->setFont(m_editFont);
            l->setProperty("editable", true);
            l->style()->polish(l);
            ui->gridLayout->addWidget(l, itemIdx, 0);
            m_scrollAreaItems.append(l);
            m_nWidgetIdx.append(m_widgets.count());
            m_widgets.append(l);
            itemIdx++;
        }
        else if (showType == BLK_COMBOBOX)
        {
            NcComboBox* c = new NcComboBox(this);
            QStringList list = GetItemsComboxList(i);
            for (int j = 0; j < list.count(); j++)
            {
                c->addItem(list.at(j));
            }
            c->setFont(m_editFont);
            ui->gridLayout->addWidget(c, itemIdx, 0);
            m_scrollAreaItems.append(c);
            m_nWidgetIdx.append(m_widgets.count());
            m_widgets.append(c);
            itemIdx++;
        }
        else
        {
            m_nWidgetIdx.append(-1);
        }
    }
    ui->gridLayout->addItem(m_pHorizontalSpacer, itemIdx - 1, 1);
    ui->gridLayout->setColumnStretch(0, 1);
    ui->gridLayout->setColumnStretch(1, 1);

    m_pDirMoveLayout->SetDirMoveLayout(m_widgets.count(), 1, m_widgets, true, MOVE_DOWN);
    m_pDirMoveLayout->SetInputEmptyAble(true);
    this->Refresh();
    ui->scrollArea->verticalScrollBar()->setValue(0);
}

bool NcScrollArea::Refresh()
{
    m_pDirMoveLayout->SetComboBoxDisconnect();
    for (int i = 0; i < m_nItemIdx.count(); i++)
    {
        bool isVisible = GetItemsVisible(i);
        Bit32 idx = m_nItemIdx.at(i);
        if (idx >= 0 && idx < m_scrollAreaItems.count())
        {
            m_scrollAreaItems.at(idx)->setVisible(isVisible);
        }
        Bit32 showType = GetItemsType(i);
        if (showType == BLK_EDIT || showType == BLK_COMBOBOX)
        {
            Bit32 idxInfo = m_nItemIdx.at(i) + 1;
            if (idxInfo >= 0 && idxInfo < m_scrollAreaItems.count())
            {
                m_scrollAreaItems.at(idxInfo)->setVisible(isVisible);
            }
        }
        if (isVisible == false)
        {
            continue;
        }

        QWidget *w = m_scrollAreaItems.at(m_nItemIdx.at(i));
        if (showType == BLK_EDIT  || showType == BLK_COMBOBOX
         || showType == BLK_LABEL || showType == BLK_TIPS)
        {
            QLabel* l = dynamic_cast<QLabel*>(w);
            if (l == NULL)
            {
                m_pDirMoveLayout->SetComboBoxConnect();
                return false;
            }
            QString name = GetItemsName(i);
            l->setText(name);
        }

        if (showType == BLK_EDIT)
        {
            w = m_scrollAreaItems.at(m_nItemIdx.at(i) + 1);
            QLabel* l = dynamic_cast<QLabel*>(w);
            if (l == NULL)
            {
                m_pDirMoveLayout->SetComboBoxConnect();
                return false;
            }
            l->setText(GetItemsVal(i));
        }
        else if (showType == BLK_COMBOBOX)
        {
            w = m_scrollAreaItems.at(m_nItemIdx.at(i) + 1);
            NcComboBox* c = dynamic_cast<NcComboBox*>(w);
            if (c == NULL)
            {
                m_pDirMoveLayout->SetComboBoxConnect();
                return false;
            }
            QStringList list = GetItemsComboxList(i);
            if (c->count() != list.count())
            {
                m_pDirMoveLayout->SetComboBoxConnect();
                return false;
            }
            for (int j = 0; j < list.count(); j++)
            {
                if (c->itemText(j) != list.at(j))
                {
                    m_pDirMoveLayout->SetComboBoxConnect();
                    return false;
                }
            }

            Bit32 idx = GetItemsVal(i).toInt();
            if (idx != c->currentIndex())
            {
                c->setCurrentIndex(idx);
            }
        }
        SetItemsProperty(i, w);
    }
    m_pDirMoveLayout->SetComboBoxConnect();
    return true;
}

bool NcScrollArea::FocusOn(int row, int col)
{
    return m_pDirMoveLayout->FocusOn(row, col);
}

void NcScrollArea::ClearFoucs()
{
    m_pDirMoveLayout->ClearFoucs();
}

int NcScrollArea::GetCurFocusIdx()
{
    return m_pDirMoveLayout->GetCurFocusIdx();
}

void NcScrollArea::SetLastFocus()
{
    m_pDirMoveLayout->SetLastFocus();
    this->FocusChangedSlot(GetCurFocusIdx());
}

void NcScrollArea::FocusChangedSlot(int idxInput)
{
    QWidget *w = ui->scrollArea->widget()->focusWidget();
    if (w == NULL || m_scrollAreaItems.count() == 0)
    {
        return;
    }
    Bit32 idx = m_scrollAreaItems.indexOf(w);
    if (idx <= 0)
    {
        return;
    }
    Bit32 y = w->pos().y();
    Bit32 ybottom = (y + m_scrollAreaItems.at(idx)->height() + ui->gridLayout->margin());
    Bit32 yTop = m_scrollAreaItems.at(idx - 1)->pos().y();

    Bit32 widgetY = 0 - ui->scrollArea->widget()->pos().y();
    Bit32 widgetHeight = ui->scrollArea->widget()->height();
    Bit32 areaHeight = ui->scrollArea->height();
    Bit32 widgetBottom = widgetY + areaHeight;

    Bit32 height = ((widgetHeight - areaHeight) > 0) ? (widgetHeight - areaHeight) : 0;
    Bit32 val = 0;
    Bit32 scrollMax = ui->scrollArea->verticalScrollBar()->maximum();

    if (yTop > widgetY && ybottom < widgetBottom)
    {
        emit FoucuChanged(idxInput);
        return;
    }
    else if (height <= 0)
    {
        ;
    }
    else if (ybottom >= widgetBottom)
    {
        Bit32 y2 = (ybottom - areaHeight > 0)? (ybottom - areaHeight) : 0;
        val = (fBit64)y2 / (fBit64)height * (fBit64)scrollMax;
    }
    else
    {
        Bit32 y2 = (yTop <= ui->gridLayout->margin())? (0) : yTop;
        val = (fBit64)y2 / (fBit64)height * (fBit64)scrollMax;
    }

    ui->scrollArea->verticalScrollBar()->setValue(val);
    emit FoucuChanged(idxInput);
}

void NcScrollArea::SetItemsProperty(Bit32 idx, QWidget *w)
{
    UNREFERENCED_PARAM(idx);
    UNREFERENCED_PARAM(w);
}

Bit32 NcScrollArea::GetScrollAreaIdx(Bit32 row)
{
    for (int i = 0; i < m_nWidgetIdx.count(); i++)
    {
        if (row == m_nWidgetIdx.at(i))
        {
            return i;
        }
    }
    return -1;
}

ENMoveDirection NcScrollArea::DirMoveContentChanged(Bit32 row, Bit32 col, QString value)
{
    UNREFERENCED_PARAM(col);
    Bit32 idx = GetScrollAreaIdx(row);
    if (idx < 0)
    {
        return NOTMOVE;
    }
    return this->DirMoveContentChanged(idx, value);
}

QValidator *NcScrollArea::GetValidator(Bit32 row, Bit32 col)
{
    UNREFERENCED_PARAM(col);
    Bit32 idx = GetScrollAreaIdx(row);
    if (idx < 0)
    {
        return NULL;
    }
    return this->GetValidator(idx);
}

QVariant NcScrollArea::RoleStyle(Bit32 row, Bit32 col, int role)
{
    UNREFERENCED_PARAM(col);
    Bit32 idx = GetScrollAreaIdx(row);
    if (idx < 0)
    {
        return QVariant();
    }
    return this->RoleStyle(idx, role);
}

QString NcScrollArea::GetDataInfo(Bit32 row, Bit32 col)
{
    UNREFERENCED_PARAM(col);
    Bit32 idx = GetScrollAreaIdx(row);
    if (idx < 0)
    {
        return "";
    }
    return this->GetDataInfo(idx);
}

NcDataLimit NcScrollArea::InputDataLimit(Bit32 row, Bit32 col)
{
    UNREFERENCED_PARAM(col);
    Bit32 idx = GetScrollAreaIdx(row);
    if (idx < 0)
    {
        return DataTypeLimit(DTYPE_NULL, -1, -1);
    }
    return this->InputDataLimit(idx);
}


ENMoveDirection NcScrollArea::DirMoveContentChanged(Bit32 idx, QString value)
{
    UNREFERENCED_PARAM(idx);
    UNREFERENCED_PARAM(value);

    return NOTMOVE;
}

QValidator *NcScrollArea::GetValidator(Bit32 idx)
{
    UNREFERENCED_PARAM(idx);
    return NULL;
}

QVariant NcScrollArea::RoleStyle(Bit32 idx, int role)
{
    UNREFERENCED_PARAM(role);
    UNREFERENCED_PARAM(idx);
    return QVariant();
}

QString NcScrollArea::GetDataInfo(Bit32 idx)
{
    UNREFERENCED_PARAM(idx);

    return "";
}

NcDataLimit NcScrollArea::InputDataLimit(Bit32 idx)
{
    UNREFERENCED_PARAM(idx);
    return DataTypeLimit(DTYPE_NULL, -1, -1);
}

bool NcScrollArea::GetItemsVisible(Bit32 idx)
{
    UNREFERENCED_PARAM(idx);
    return true;
}
