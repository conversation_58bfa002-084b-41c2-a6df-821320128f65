#include "ladderview.h"
#include <QScrollBar>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QWheelEvent>
#include <QContextMenuEvent>
#include <QMenu>
#include <QApplication>
#include <QPainter>
#include <QDebug>
#include <QtMath>

LadderView::LadderView(QWidget *parent)
    : QGraphicsView(parent)
    , m_scene(nullptr)
    , m_ladderData(nullptr)
    , m_startRow(0)
    , m_startCol(0)
    , m_visibleRows(CELL_PER_COL)
    , m_visibleCols(CELL_PER_ROW)
    , m_selectedRow(-1)
    , m_selectedCol(-1)
    , m_selectionVisible(false)
    , m_debugMode(false)
    , m_editMode(false)
    , m_forceRefresh(false)
    , m_zoomFactor(1.0)
    , m_minZoom(0.5)
    , m_maxZoom(3.0)
    , m_dragging(false)
    , m_refreshTimer(nullptr)
    , m_needsRefresh(false)
{
    setupView();
    setupScene();
    setupConnections();
    
    // 创建刷新定时器
    m_refreshTimer = new QTimer(this);
    m_refreshTimer->setSingleShot(true);
    m_refreshTimer->setInterval(50); // 50ms延迟刷新
    connect(m_refreshTimer, &QTimer::timeout, [this]() {
        if (m_needsRefresh) {
            m_scene->refreshDisplay();
            m_needsRefresh = false;
        }
    });
}

LadderView::~LadderView()
{
    if (m_refreshTimer) {
        m_refreshTimer->stop();
    }
}

void LadderView::setupView()
{
    // 设置视图属性
    setRenderHint(QPainter::Antialiasing, true);
    setRenderHint(QPainter::TextAntialiasing, true);
    setDragMode(QGraphicsView::NoDrag);
    setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    
    // 设置背景
    setBackgroundBrush(QBrush(QColor(240, 240, 240)));
    
    // 启用鼠标跟踪
    setMouseTracking(true);
    
    // 设置焦点策略
    setFocusPolicy(Qt::StrongFocus);
}

void LadderView::setupScene()
{
    m_scene = new LadderScene(this);
    setScene(m_scene);
    
    // 设置场景大小
    updateSceneRect();
}

void LadderView::setupConnections()
{
    if (m_scene) {
        connect(m_scene, &QGraphicsScene::changed,
                this, &LadderView::onSceneChanged);
    }
}

void LadderView::setLadderData(LadderData *data)
{
    if (m_ladderData) {
        disconnect(m_ladderData, nullptr, this, nullptr);
    }
    
    m_ladderData = data;
    
    if (m_ladderData) {
        connect(m_ladderData, &LadderData::dataChanged,
                this, &LadderView::onDataChanged);
        connect(m_ladderData, &LadderData::rowDataChanged,
                this, &LadderView::onRowDataChanged);
    }
    
    if (m_scene) {
        m_scene->setLadderData(data);
    }
    
    updateSceneRect();
    refreshDisplay();
}

void LadderView::setStartRow(int row)
{
    if (m_startRow != row) {
        m_startRow = qMax(0, row);
        updateVisibleArea();
        emit viewScrolled(m_startRow, m_startCol);
    }
}

void LadderView::setStartCol(int col)
{
    if (m_startCol != col) {
        m_startCol = qMax(0, col);
        updateVisibleArea();
        emit viewScrolled(m_startRow, m_startCol);
    }
}

void LadderView::refreshDisplay()
{
    if (m_scene) {
        m_scene->setVisibleArea(m_startRow, m_startCol, m_visibleRows, m_visibleCols);
        m_scene->setDebugMode(m_debugMode);
        m_scene->setEditMode(m_editMode);
        
        if (m_forceRefresh) {
            m_scene->forceRefresh();
        } else {
            // 延迟刷新以提高性能
            m_needsRefresh = true;
            m_refreshTimer->start();
        }
    }
    
    // 更新选择显示
    updateSelection();
    
    // 触发重绘
    viewport()->update();
}

void LadderView::forceRefresh()
{
    m_forceRefresh = true;
    refreshDisplay();
    m_forceRefresh = false;
}

void LadderView::setDebugMode(bool enabled)
{
    if (m_debugMode != enabled) {
        m_debugMode = enabled;
        refreshDisplay();
    }
}

void LadderView::setEditMode(bool enabled)
{
    if (m_editMode != enabled) {
        m_editMode = enabled;
        refreshDisplay();
    }
}

void LadderView::selectCell(int row, int col)
{
    if (m_selectedRow != row || m_selectedCol != col) {
        // 清除旧选择
        if (m_selectedRow >= 0 && m_selectedCol >= 0) {
            highlightCell(m_selectedRow, m_selectedCol, false);
        }
        
        m_selectedRow = row;
        m_selectedCol = col;
        m_selectionVisible = (row >= 0 && col >= 0);
        
        // 高亮新选择
        if (m_selectionVisible) {
            highlightCell(m_selectedRow, m_selectedCol, true);
            ensureCellVisible(row, col);
        }
        
        emit cellSelected(row, col);
        viewport()->update();
    }
}

void LadderView::clearSelection()
{
    selectCell(-1, -1);
}

void LadderView::zoomIn()
{
    setZoomFactor(m_zoomFactor * 1.2);
}

void LadderView::zoomOut()
{
    setZoomFactor(m_zoomFactor / 1.2);
}

void LadderView::zoomReset()
{
    setZoomFactor(1.0);
}

void LadderView::setZoomFactor(qreal factor)
{
    factor = qBound(m_minZoom, factor, m_maxZoom);
    if (qAbs(factor - m_zoomFactor) > 0.01) {
        m_zoomFactor = factor;
        
        // 应用缩放变换
        QTransform transform;
        transform.scale(factor, factor);
        setTransform(transform);
        
        updateVisibleArea();
        refreshDisplay();
    }
}

void LadderView::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        QPoint cellPos = viewToCell(event->pos());
        if (cellPos.x() >= 0 && cellPos.y() >= 0) {
            selectCell(cellPos.y(), cellPos.x());
        } else {
            clearSelection();
        }
        
        m_lastMousePos = event->pos();
        m_dragging = true;
    }
    
    QGraphicsView::mousePressEvent(event);
}

void LadderView::mouseDoubleClickEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        QPoint cellPos = viewToCell(event->pos());
        if (cellPos.x() >= 0 && cellPos.y() >= 0) {
            emit cellDoubleClicked(cellPos.y(), cellPos.x());
        }
    }
    
    QGraphicsView::mouseDoubleClickEvent(event);
}

void LadderView::mouseMoveEvent(QMouseEvent *event)
{
    if (m_dragging && (event->buttons() & Qt::LeftButton)) {
        // 实现拖拽滚动
        QPoint delta = event->pos() - m_lastMousePos;
        if (qAbs(delta.x()) > 5 || qAbs(delta.y()) > 5) {
            // 根据拖拽距离调整显示位置
            int rowDelta = -delta.y() / CELL_HEIGHT;
            int colDelta = -delta.x() / CELL_WIDTH;
            
            if (rowDelta != 0) {
                setStartRow(m_startRow + rowDelta);
            }
            if (colDelta != 0) {
                setStartCol(m_startCol + colDelta);
            }
            
            m_lastMousePos = event->pos();
        }
    } else {
        m_dragging = false;
    }

    QGraphicsView::mouseMoveEvent(event);
}

void LadderView::wheelEvent(QWheelEvent *event)
{
    if (event->modifiers() & Qt::ControlModifier) {
        // Ctrl+滚轮进行缩放
        const qreal scaleFactor = 1.15;
        if (event->angleDelta().y() > 0) {
            zoomIn();
        } else {
            zoomOut();
        }
        event->accept();
    } else {
        // 普通滚轮进行滚动
        int delta = event->angleDelta().y() / 120;
        setStartRow(m_startRow - delta);
        event->accept();
    }
}

void LadderView::keyPressEvent(QKeyEvent *event)
{
    switch (event->key()) {
        case Qt::Key_Up:
            if (m_selectedRow > 0) {
                selectCell(m_selectedRow - 1, m_selectedCol);
            }
            break;
            
        case Qt::Key_Down:
            if (m_ladderData && m_selectedRow < m_ladderData->getTotalRows() - 1) {
                selectCell(m_selectedRow + 1, m_selectedCol);
            }
            break;
            
        case Qt::Key_Left:
            if (m_selectedCol > 0) {
                selectCell(m_selectedRow, m_selectedCol - 1);
            }
            break;
            
        case Qt::Key_Right:
            if (m_selectedCol < CELL_PER_ROW - 1) {
                selectCell(m_selectedRow, m_selectedCol + 1);
            }
            break;
            
        case Qt::Key_PageUp:
            setStartRow(m_startRow - m_visibleRows);
            break;
            
        case Qt::Key_PageDown:
            setStartRow(m_startRow + m_visibleRows);
            break;
            
        case Qt::Key_Home:
            setStartRow(0);
            break;
            
        case Qt::Key_End:
            if (m_ladderData) {
                setStartRow(m_ladderData->getTotalRows() - m_visibleRows);
            }
            break;
            
        case Qt::Key_F5:
            forceRefresh();
            break;
            
        default:
            QGraphicsView::keyPressEvent(event);
            break;
    }
}

void LadderView::resizeEvent(QResizeEvent *event)
{
    QGraphicsView::resizeEvent(event);
    updateVisibleArea();
    refreshDisplay();
}

void LadderView::paintEvent(QPaintEvent *event)
{
    QGraphicsView::paintEvent(event);
    
    // 绘制选择框
    if (m_selectionVisible && m_selectedRow >= 0 && m_selectedCol >= 0) {
        QPainter painter(viewport());
        painter.setRenderHint(QPainter::Antialiasing);
        
        QRectF cellRect = getCellRect(m_selectedRow, m_selectedCol);
        QRect viewRect = mapFromScene(cellRect).boundingRect();
        
        if (viewRect.intersects(event->rect())) {
            painter.setPen(QPen(QColor(0, 0, 255), 2, Qt::DashLine));
            painter.setBrush(Qt::NoBrush);
            painter.drawRect(viewRect);
        }
    }
}

void LadderView::contextMenuEvent(QContextMenuEvent *event)
{
    QPoint cellPos = viewToCell(event->pos());
    if (cellPos.x() >= 0 && cellPos.y() >= 0) {
        emit cellRightClicked(cellPos.y(), cellPos.x(), event->globalPos());
    }
}

QPoint LadderView::viewToCell(const QPoint &viewPos)
{
    QPointF scenePos = mapToScene(viewPos);
    
    int col = static_cast<int>((scenePos.x() - LEFT_BLANK) / CELL_WIDTH);
    int row = static_cast<int>((scenePos.y() - TOP_BLANK) / CELL_HEIGHT) + m_startRow;
    
    if (col >= 0 && col < CELL_PER_ROW && row >= 0 && 
        m_ladderData && row < m_ladderData->getTotalRows()) {
        return QPoint(col, row);
    }
    
    return QPoint(-1, -1);
}

QPoint LadderView::cellToView(int row, int col)
{
    QPointF scenePos(LEFT_BLANK + col * CELL_WIDTH + CELL_WIDTH / 2,
                     TOP_BLANK + (row - m_startRow) * CELL_HEIGHT + CELL_HEIGHT / 2);
    return mapFromScene(scenePos).toPoint();
}

QRectF LadderView::getCellRect(int row, int col)
{
    return QRectF(LEFT_BLANK + col * CELL_WIDTH,
                  TOP_BLANK + (row - m_startRow) * CELL_HEIGHT,
                  CELL_WIDTH, CELL_HEIGHT);
}

void LadderView::updateVisibleArea()
{
    QSize viewSize = viewport()->size();
    m_visibleRows = qMax(1, (viewSize.height() - TOP_BLANK) / CELL_HEIGHT + 1);
    m_visibleCols = qMax(1, (viewSize.width() - LEFT_BLANK) / CELL_WIDTH + 1);
    
    // 确保不超出数据范围
    if (m_ladderData) {
        int maxStartRow = qMax(0, m_ladderData->getTotalRows() - m_visibleRows);
        m_startRow = qMin(m_startRow, maxStartRow);
    }
    
    m_startCol = qMax(0, qMin(m_startCol, CELL_PER_ROW - m_visibleCols));
}

void LadderView::updateSceneRect()
{
    if (m_ladderData) {
        int totalHeight = TOP_BLANK + m_ladderData->getTotalRows() * CELL_HEIGHT;
        int totalWidth = LEFT_BLANK + CELL_PER_ROW * CELL_WIDTH;
        m_scene->setSceneRect(0, 0, totalWidth, totalHeight);
    }
}

void LadderView::ensureCellVisible(int row, int col)
{
    if (row < m_startRow) {
        setStartRow(row);
    } else if (row >= m_startRow + m_visibleRows) {
        setStartRow(row - m_visibleRows + 1);
    }
    
    if (col < m_startCol) {
        setStartCol(col);
    } else if (col >= m_startCol + m_visibleCols) {
        setStartCol(col - m_visibleCols + 1);
    }
}

void LadderView::updateSelection()
{
    // 更新选择状态到场景
    if (m_scene) {
        m_scene->setSelection(m_selectedRow, m_selectedCol);
    }
}

void LadderView::highlightCell(int row, int col, bool highlight)
{
    if (m_scene) {
        m_scene->highlightCell(row, col, highlight);
    }
}

void LadderView::onSceneChanged(const QList<QRectF> &region)
{
    Q_UNUSED(region)
    // 场景变化时的处理
}

void LadderView::onDataChanged(int row, int col)
{
    // 数据变化时刷新对应区域
    if (row >= m_startRow && row < m_startRow + m_visibleRows &&
        col >= m_startCol && col < m_startCol + m_visibleCols) {
        
        QRectF cellRect = getCellRect(row, col);
        m_scene->update(cellRect);
    }
}

void LadderView::onRowDataChanged(int row)
{
    // 整行数据变化时刷新整行
    if (row >= m_startRow && row < m_startRow + m_visibleRows) {
        for (int col = m_startCol; col < m_startCol + m_visibleCols && col < CELL_PER_ROW; ++col) {
            onDataChanged(row, col);
        }
    }
}
