﻿#ifndef OSCTOOLCONF_H
#define OSCTOOLCONF_H

#include "dirmovelayout.h"

#include "containerwidget.h"

namespace Ui {
class OscToolConf;
}

QT_BEGIN_NAMESPACE
class QWidget;
class QIntValidator;
class QValidator;
QT_END_NAMESPACE

class OscToolConf : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscToolConf(QWidget *parent = 0);
    ~OscToolConf();

    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    bool eventFilter(QObject *wg, QEvent *event);
    //void keyPressEvent(QKeyEvent *ev);
private:
    Ui::OscToolConf *ui;
    Bit32 curFocusIndex;
    void LoadData();
    void FocusRedraw();
    void CurValSet(Bit32 row);

    QList<QWidget *>wList;
    DirMoveLayout *dirLayout;
    QIntValidator *intValidatorToolNo;
    QIntValidator *intValidatorPeriod;
    QValidator *validatorReg;
};



#endif // OSCTOOLCONF_H
