﻿#include <QCoreApplication>
#include <QHeaderView>
#include <QKeyEvent>
#include <QScrollBar>

#include "common.h"
#include "nctable.h"

NcTable::NcTable(QWidget *parent) : QTableView(parent)
{
    this->curRow = 0;
    this->curCol = 0;

    tabMode = new NcTableModel(this);
    tabMode->tab = this;
    this->setModel(tabMode);
    this->verticalHeader()->setVisible(false);
    this->setVerticalScrollMode(ScrollPerPixel);
    this->setSelectionMode(QAbstractItemView::SingleSelection);
    this->setEditTriggers(QAbstractItemView::NoEditTriggers);
    this->setFocusPolicy(Qt::StrongFocus);
    this->horizontalHeader()->setStretchLastSection(true); //设置充满表宽度
    this->horizontalHeader()->setHighlightSections(false);
    this->horizontalHeader()->setResizeMode(QHeaderView::Fixed);    // 设置列宽为固定列宽不可调整
    this->setAlternatingRowColors(true);
    this->setFont(QFont(FONT_TYPE, 12)); // 设置表格单元的字体大小
    this->horizontalHeader()->setFont(QFont(FONT_TYPE, 12));
    this->installEventFilter(this);
    this->verticalScrollBar()->installEventFilter(this);
    this->horizontalHeader()->installEventFilter(this);

    //connect(tabMode->tab, SIGNAL(doubleClicked(QModelIndex)), this, SLOT(SlotDoubleClicked(QModelIndex)));
}

void NcTable::ContentSet(int , int , const QString &)
{
}

void NcTable::ContentGet(int , int , QString &) const
{
}

int NcTable::RowNum()
{
    return 0;
}

QVariant NcTable::RoleStyle(int , int , int )
{
    return 0;
}

void NcTable::InputType(int , int , long &, QString &, long &, long &, long &)
{
    ;
}

int NcTable::FocusOn(int row, int col)
{
    if (this->focusPolicy() != Qt::NoFocus)
    {
        this->setFocus();
        this->setCurrentIndex(this->model()->index(row,col));
    }

    return 0;
}

void NcTable::FocusOff()
{
    this->clearSelection();
    this->clearFocus();
}

void NcTable::Refresh()
{
    int row = this->RowNum();

    this->tabMode->UpdateView(0, 0, 0, row); // 刷新界面已变更数据
}

void NcTable::RefreshSingleItem(int row, int col)
{
    this->tabMode->UpdateView(row, col, row, col); // 刷新界面已变更数据
}

void NcTable::Redraw()
{ // 主要处理行数变了的情况
    this->tabMode->ResetModel();
    QResizeEvent *ev = NULL;
    this->resizeEvent(ev); // 行数变了，可能导致滚动条变化，需要resize
}

// 初始化标题栏(重载用)
void NcTable::InitTab()
{
    // 重新设置InitProp里的内容
}

void NcTable::InitProp(const QList<int>&colstrech, int pagerow, const QStringList &title)
{
    this->colstrech = colstrech;
    this->pagerow = pagerow;
    // 设置到模式
    this->tabMode->SetColNum(colstrech.count());
    this->tabMode->title = title;
}

void NcTable::resizeEvent(QResizeEvent *)
{
    int sum = 0;
    double width = 0;
    for (int i = 0; i < this->colstrech.count(); i++)
    {
        sum += this->colstrech.at(i);
    }

    if (0 == sum)
    {
        sum = 1;
    }
    width = ((double)(this->width() - this->verticalScrollBar()->width() - 2)) / sum;
    for (int i = 0; i < this->colstrech.count(); i++)
    {
        this->setColumnWidth(i, (int)(width * this->colstrech.at(i)));
    }

    if (this->pagerow != 0)
    {
        int rowNum = this->RowNum();
        for (int i = 0; i < rowNum; i++)
        {
            this->setRowHeight(i, (this->height()-this->horizontalHeader()->height())/this->pagerow);
        }
    }
}

void NcTable::SlotDoubleClicked(QModelIndex index)
{
    UNREFERENCED_PARAM(index);
    QKeyEvent keyEvent(QEvent::KeyPress, Qt::Key_Enter, Qt::NoModifier);
    QCoreApplication::sendEvent(this, &keyEvent);
}

bool NcTable::eventFilter(QObject *t, QEvent *ev)
{
    if (t == this)
    {
        if (ev->type() == QEvent::KeyPress)
        {
            QKeyEvent *keyEv = static_cast<QKeyEvent *>(ev);
            if (keyEv->key() == Qt::Key_Enter || keyEv->key() == Qt::Key_Return)
            {
                int curR = this->currentIndex().row();
                int curC = this->currentIndex().column();
                QString oldStr;
                this->ContentGet(curR, curC, oldStr);
                QString str = oldStr;

                if ((this->RoleStyle(curR, curC, NcTable::FlagRole).toUInt() & Qt::ItemIsEditable) == 0)
                {
                    return true;
                }

                if (this->RoleStyle(curR, curC, NcTable::ProgRunningRole).toBool() != true)
                {
                    if (this->RoleStyle(curR, curC, NcTable::ProgHoldRole).toBool() != true)
                    { // ProgHoldRole有效时，进给保持还是可以编辑的
                        if (0 != AnyChanIsRunning())
                        {
                            MessageOut(QObject::TR("程序运行中，禁止编辑!"));
                            return true;
                        }
                    }
                    else
                    {
                        if (true == AnyChanIsCycing())
                        {
                            MessageOut(QObject::TR("循环启动中，禁止编辑!"));
                            return true;
                        }
                    }
                }
                if (this->RoleStyle(curR, curC, NcTable::ComboBoxRole).toBool())
                { // 下拉框处理不了，交给界面自己处理
                    this->ContentSet(curR, curC, str);
                    return true;
                }
                else
                {
                    long ebxType = DTYPE_STRING;
                    QString info = "";
                    long len = 0;
                    long prec = 0;
                    long exitType = 0;
                    this->InputType(curR, curC, ebxType, info, len, prec, exitType);
                    MessageOut(""); // 先清除提示
                    if (0 != MessageInput(&str, ebxType, info, len, prec, exitType))
                    {
                        return true;
                    }
                    if (str != oldStr && (!str.isEmpty()))
                    {
                        this->ContentSet(curR, curC, str);
                    }
                }
            }
        }
    }

    if (t == this->verticalScrollBar() && ev->type() == QEvent::Resize)
    { // 列表resize后，滚动条还要resize，在滚动条resize时，重新设置列表宽度，避免横向滚动条的出现
        QResizeEvent *ev = NULL;
        this->resizeEvent(ev);
    }

    if (t == this->horizontalHeader() && ev->type() == QEvent::Resize)
    {
        QResizeEvent *ev = NULL;
        this->resizeEvent(ev);
    }

    return QObject::eventFilter(t,ev);
}

int NcTableModel::rowCount(const QModelIndex &) const
{
    return this->tab->RowNum();
}

int NcTableModel::columnCount(const QModelIndex &) const
{
    return colNum;
}

QVariant NcTableModel::data(const QModelIndex &index, int role) const
{
    if (role == Qt::DisplayRole)
    {
        QString val;
        tab->ContentGet(index.row(), index.column(), val);
        return val;
    }
    else
    {
        return tab->RoleStyle(index.row(), index.column(), role);
    }

    return QVariant();
}

QVariant NcTableModel::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole)
    {
        if (section >= 0 && section < this->title.count())
        {
            return this->title.at(section);
        }
    }
    else if (section == 0 && orientation == Qt::Horizontal && role == Qt::TextAlignmentRole)
    {
        return int(Qt::AlignCenter);
    }

    return QVariant();
}

bool NcTableModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    if (index.isValid() && role == Qt::EditRole)
    {
        tab->ContentSet(index.row(), index.column(), value.toString());
        emit dataChanged(index, index);
    }

    return false;
}

Qt::ItemFlags NcTableModel::flags(const QModelIndex &index) const
{ //return Qt::ItemIsEditable | Qt::ItemIsEnabled | Qt::ItemIsSelectable;
    if (!index.isValid())
        return 0;

    Qt::ItemFlag itemFlag = Qt::ItemIsEnabled;
    int flag = this->tab->RoleStyle(index.row(), index.column(), NcTable::FlagRole).toInt();
    return (Qt::ItemFlag)flag;
    switch (flag)
    {
    case Qt::NoItemFlags:
        itemFlag = Qt::NoItemFlags;
        break;
    case Qt::ItemIsSelectable:
        itemFlag = Qt::ItemIsSelectable;
        break;
    case Qt::ItemIsEditable:
        itemFlag = Qt::ItemIsEditable;
        break;
    case Qt::ItemIsDragEnabled:
        itemFlag = Qt::ItemIsDragEnabled;
        break;
    case Qt::ItemIsDropEnabled:
        itemFlag = Qt::ItemIsDropEnabled;
        break;
    case Qt::ItemIsUserCheckable:
        itemFlag = Qt::ItemIsUserCheckable;
        break;
    case Qt::ItemIsEnabled:
        itemFlag = Qt::ItemIsEnabled;
        break;
    case Qt::ItemIsTristate:
        itemFlag = Qt::ItemIsTristate;
        break;
    default:
        break;
    }

    return itemFlag;
}

void NcTableModel::SetColNum(int colNum)
{
    this->colNum = colNum;
}

void NcTableModel::UpdateView(int tlrow, int tlcol, int brrow, int brcol)
{
    QModelIndex t1 = this->index(tlrow, tlcol);
    QModelIndex t2 = this->index(brrow, brcol);
    emit this->dataChanged(t1, t2);
}

void NcTableModel::ResetModel()
{
    this->beginResetModel();
    this->reset();
    this->endResetModel();
}

