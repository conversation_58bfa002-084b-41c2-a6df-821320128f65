﻿#ifndef HMIPARSET_H
#define HMIPARSET_H

#include <QList>

#include "hncparaman.h"
#include "hncdatatype.h"

typedef struct _ParamIndex
{
    Bit32 m_nFileno;
    Bit32 m_nSubno;
    Bit32 m_nIndex;
}ParamIndex;

class HmiParSet
{
public:
    static HmiParSet *GetInstance()
    {
        if(m_pInstance == NULL)
        {
            m_pInstance = new HmiParSet();
        }
        return m_pInstance;
    }

    Bit32 SetIntVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit32 value, QString title = "");
    Bit32 SetFloatVal(Bit32 fileno, Bit32 subno, Bit32 index, fBit64 value, QString title = "");
    Bit32 SetStrVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit8* value, QString title = "");

    Bit32 SaveParamChanges();
    static Bit32 TransParamId(Bit32 paramId, Bit32 &fileno, Bit32 &subno, Bit32 &index);

    static Bit32 GetParam(Bit32 fileno, Bit32 subno, Bit32 index, QString &val);
    static Bit32 GetParam(Bit32 paramId, QString &val);

    static Bit32 GetParamId(Bit32 fileno, Bit32 subno, Bit32 index);

    Bit32 SetParaPropWithLog(Bit32 fileno, Bit32 subno, Bit32 index, QString value, QString title);
    Bit32 SetParaPropWithLog(Bit32 paramId, QString value, QString title);

    static Bit32 GetParamDType(Bit32 fileno, Bit32 subno, Bit32 index);
    static Bit32 GetParamDType(Bit32 paramId);
private:
    QList<ParamIndex> m_nParamNoList;
    FILE *m_pFp;
    ParmFileHead m_fileHead;
    QString m_sErrorStr;

    static HmiParSet *m_pInstance;

    HmiParSet();
    ~HmiParSet();
    void CloseFile();   // 关闭文件

    Bit32 ParamanGetAddr(Bit32 fileno, Bit32 nsubno, Bit32 nid);
    Bit32 ParamanGetCount(Bit32 fileno);

    Bit32 SeekFileByAddr(Bit32 nAddr);
    Bit32 WriteParamByAddr(Bit32 nAddr, SParamValue *paramItem, Bit32 count);
    Bit32 SaveParam(Bit8 *paramTmpPath);

    Bit32 SetParaPropWithLog(Bit32 paramId, SDataProperty value, QString title = "");
};

#endif // HMIPARSET_H
