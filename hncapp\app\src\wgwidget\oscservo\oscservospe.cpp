﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservospe.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-速度环界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年5月24日
* 说    明：
*/

#include <qmath.h>

#include "hncaxis.h"
#include "hncchan.h"
#include "hncchandef.h"
#include "hncparaman.h"
#include "hncsmpl.h"
#include "hncsys.h"
#include "hmiparaman.h"
#include "loadsave.h"
#include "ncassert.h"
#include "passwd.h"
#include "smplcalc.h"

#include "hmicommon.h"
#include "hotkeycfg.h"
#include "hmioscproc.h"
#include "hmioscservo.h"
#include "oscwave.h"
#include "osclist.h"

#include "oscservospe.h"
#include "ui_oscservospe.h"

OscServoSpe::OscServoSpe(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoSpe)
{
    ui->setupUi(this);

    m_bSampleStart = false;
    pOscWaveSpd = new OscWave(this, HmiOscServo::OSC_SERVO_SPE, "SPD");
    pOscWaveAcc = new OscWave(this, HmiOscServo::OSC_SERVO_SPE, "ACC");

    ui->verticalLayout_2->addWidget(pOscWaveSpd);
    ui->verticalLayout_2->addWidget(pOscWaveAcc);
    ui->verticalLayout_2->setStretch(0, 1);
    ui->verticalLayout_2->setStretch(1, 1);
    ui->verticalLayout_2->setContentsMargins(0, 0, 0, 0);
    ui->verticalLayout_2->setSpacing(5);

    pOscWaveSpd->CreateGroup(pOscWaveAcc->GetWavePlot());

    // 参数初始化
    this->firstFlag = false;

    // 信息区设置
    ui->label_10->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    ui->label_14->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    ui->label_16->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    ui->speLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    ui->speRateLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    ui->accMaxLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    ui->accMinLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
//    ui->label->setText(TR("s\u00B2")); // \u00B2为平方符号

    ui->labelAxisName->setAlignment(Qt::AlignCenter);
    ui->labelImg->setAlignment(Qt::AlignCenter);
    ui->labelImg->setText(HotKeyCfg::GetDirChgMsg());

    ui->labelImg->setFont(QFont(FONT_TYPE, 10));

    // 列表设置
    speOscList = new OscList(this);
    speOscList->installEventFilter(this);
    ui->gridLayout->addWidget(speOscList);
    speOscList->SetEditAgent(true);

//    ui->leftBtn->setShortcut(QKeySequence(Qt::AltModifier + Qt::Key_Left));
//    ui->rightBtn->setShortcut(QKeySequence(Qt::AltModifier + Qt::Key_Right));
    ui->leftBtn->setFocusPolicy(Qt::NoFocus);
    ui->rightBtn->setFocusPolicy(Qt::NoFocus);
}

OscServoSpe::~OscServoSpe()
{
    delete ui;
}

void OscServoSpe::LoadInfo()
{
    Bit32 max =0, min = 0, tmp = 1;
    Bit32 chn = 0;
    Bit32 axis_no = 0;
    Bit32 dist = 0, pulse = 0;
    Bit32 ncu_cycle = 1000, moving_unit = 0;
    fBit64 ftmp = 0;
    Spdfluc spdfluc;
    Bit64 *startP1 = NULL;
    Bit64 *startP2 = NULL;
    Bit32 type = 0;
    Bit32 offset = 0;
    Bit32 len = 0;
    fBit64 speVal = 0.000;
    fBit64 speRateVal = 0.000;
    fBit64 accMaxVal = 0.000;
    fBit64 accMinVal = 0.000;
    memset(&spdfluc, 0, sizeof(spdfluc));

    if(oscproc_get_total() > 0)
    {
        HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &moving_unit);
        ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_CYCLE, &ncu_cycle);
//        HNC_SystemGetValue(HNC_SYS_ACTIVE_CHAN, &ch);

        chn = 0;
        Bit32 client = HmiOscServo::oscservo_get_sampl_client();
        HNC_SamplGetConfig(client, chn, type, axis_no, offset, len);
        ParaGetIntVal(PARAMAN_FILE_AXIS, axis_no, PAR_AX_PM_MUNIT, &dist);
        ParaGetIntVal(PARAMAN_FILE_AXIS, axis_no, PAR_AX_PM_PULSE, &pulse);
        if (0 != moving_unit && 0 != dist)      // 除0保护
        {
            ftmp = (1000.0 * (fBit64)pulse) / (moving_unit * dist);
        }

        startP1 = oscproc_get_smpldata(chn);
        startP2 = oscproc_get_smpldata(chn+1);
        if (startP1 == NULL || startP2 == NULL)
        {
            return;
        }
        tmp = smpl_calc_spd_fluc(startP1, startP2, oscproc_get_total()%SMPL_DATA_NUM, ftmp, spdfluc);

        if (tmp == -1)
        {
    #ifdef _DEBUG
            MessageOut(TR("速度波动无法计算!请增大配置中的行程(或降低速度,或缩短采样周期)!"));
    #endif
            return;
        }

        ftmp = fabs(smpl_calc_spd_coef(axis_no, 5)); // 速度系数(m/s)
        speVal = ftmp * spdfluc.fluc * 1000 * 60 ; // 毫米/分(速度波动)
        speRateVal = spdfluc.perct; // 速度波动百分比

        HmiOscServo::SmplCalcDiffMaxmin(chn+1, &max, &min, 0, oscproc_get_total());
        accMaxVal = smpl_calc_diff(ftmp*max, 0); // 加速度最大值
        accMinVal = smpl_calc_diff(ftmp*min, 0); // 加速度最小值
        if (min < 0)
        {
            min = -min;
        }
        max = max>min?max:min;
//        if (max == 0)
//        {
//            s_data_info[4] = 0;
//        }
//        else
//        {
//            s_data_info[4] = 50.0/(3*smpl_calc_diff(ftmp*max, 0)); // 最大快移加减速时间(ms)
//        }

        HmiOscServo::OscservoReportRecord(speRateVal, 0);//传入百分比 1.26 27389
    }
    ui->speLabel->setText(QString::number(speVal, 'f',3));
    ui->speRateLabel->setText(QString("%1%").arg(QString::number(speRateVal, 'f',3)));
    ui->accMaxLabel->setText(QString::number(accMaxVal, 'f',3));
    ui->accMinLabel->setText(QString::number(accMinVal, 'f',3));
}

QStringList OscServoSpe::GetParmList()
{
    Bit32 totalNo = 0;
    Bit32 paramId = 0;
    QStringList list;
    list.clear();

    totalNo = HmiOscServo::ServoParmGetCount(HmiOscServo::OSC_SERVO_SPE);

    for(Bit32 i = 0; i < totalNo; i++)
    {
        paramId = HmiOscServo::ServoParRow2Id(i);
        if (IsParamanVisible(paramId))
        {
            list.append(QString::number(paramId));
        }
    }
    return list;
}

void OscServoSpe::LoadAxisVal(Bit32 type)
{
    QString axisName = "";
    QString unit1 = "";
    QStringList strList;
    Bit32 axisType = 0;

    HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetCurAxesConf(), &axisType);
    strList.clear();
    axisName = HmiOscServo::OscAxisToName(HmiOscServo::GetCurAxesConf());
    ui->labelAxisName->setText(TR("%1轴").arg(axisName));
    if(axisType == 1 || axisType == 7) // 7(主轴做进给轴使用)和1一样按直线轴处理
    {
        unit1 = TR("mm");
    }
    else
    {
        unit1 = TR("deg");
    }

    ui->label_10->setText(TR("%1/min").arg(unit1));

    pOscWaveSpd->SetAxisName(QStringList() << axisName << unit1);

    if(type == 1)
    {
        strList = GetParmList();
        speOscList->RefresWidget(strList);
        speOscList->LoadWidget();
    }
}

void OscServoSpe::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::SETFOCUS)
    {
        bool ret = speOscList->SetTableFocus();
        if(messageValue == "CLEARFOCUS" || ret == false)
        {
            speOscList->ClearTableFocus();
        }
    }
    else if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        QStringList strList;
        strList.clear();
        if(messageValue == "INIT") // 初始化，清除上次在该界面记住的当前行
        {
            strList = GetParmList();
            speOscList->RefresWidget(strList);
        }
        this->LoadInfo();
        speOscList->LoadWidget();
        this->LoadAxisVal(0);
        this->SetColorStyle();
        this->OnBtFlagChange();
        if(HmiOscServo::GetConfChgFlag())
        {
            this->Reset();
            HmiOscServo::SetConfChgFlag(false);
        }
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_OSCSERVOSTART")
        {
			m_bSampleStart = true;
            this->Reset(); // 开始采样时才清除上一次的图形
        }
        else if(messageValue == "MSG_OSCSERVOSAVE")
        {
            HmiOscServo::ParmSave();
            speOscList->LoadWidget();
        }
        else if (messageValue == "MSG_OSCSERVOSTOP")
        {
            if (m_bSampleStart == true)
			{
                m_bSampleStart = false;
				this->LoadInfo();
			}
        }
        else if(messageValue == "OSCSERVOCOLOR")
        {
            this->SetColorStyle();
        }
    }
    else if (messageid == MsgData::REFRESH)
    {
        this->Refresh();

		Bit32 logicAxisNo = HmiOscServo::GetCurAxesNo();
		Bit32 devAxisType = HNC_ParamanGetAxisDevType(logicAxisNo);
		if (devAxisType == DEV_ETHERCAT_AXIS || devAxisType == DEV_NCUC_AXIS)
		{
            speOscList->Refresh();
		}
    }
    else if(messageid == MsgData::KEYBOARD && messageValue == "TURNTABSLEFT")
    {
        this->on_leftBtn_clicked();
    }
    else if(messageid == MsgData::KEYBOARD && messageValue == "TURNTABSRIGHT")
    {
        this->on_rightBtn_clicked();
    }
}

void OscServoSpe::resizeEvent(QResizeEvent *)
{
    this->firstFlag = false;
}

bool OscServoSpe::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::Paint && !firstFlag) // Paint事件在ReSize事件之后响应，用于图片第一次重绘
    {
        ui->leftBtn->setIconSize(ui->leftBtn->size());
        ui->rightBtn->setIconSize(ui->rightBtn->size());
        //ui->leftBtn->setIcon(PixMapToSize(ui->leftBtn->size(), "../pic/left-2.png"));
       // ui->rightBtn->setIcon(PixMapToSize(ui->rightBtn->size(), "../pic/right-1.png"));

        this->firstFlag = true;
        this->OnBtFlagChange(); // 解决初次进入界面时，redraw消息在paint事件前响应，导致界面刷新错误
    }
    return QObject::eventFilter(target, event);
}

void OscServoSpe::OnBtFlagChange()
{
	HmiOscServo::OscservoSamplReset();
	HmiOscServo::OscservoInit();
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();
    if(curAxesIndex <= 0)
    {
        HmiOscServo::SetCurAxesIndex(0);
        ui->leftBtn->setProperty("valid",false);
        ui->rightBtn->setProperty("valid",true);
        ui->leftBtn->style()->polish(ui->leftBtn);
        ui->rightBtn->style()->polish(ui->rightBtn);
        return;
    }
    if(HmiOscServo::GetIndexAxesConf(curAxesIndex + 1) < 0)
    {
        ui->leftBtn->setProperty("valid",true);
        ui->rightBtn->setProperty("valid",false);
        ui->leftBtn->style()->polish(ui->leftBtn);
        ui->rightBtn->style()->polish(ui->rightBtn);
        return;
    }
    ui->leftBtn->setProperty("valid",true);
    ui->rightBtn->setProperty("valid",true);
    ui->leftBtn->style()->polish(ui->leftBtn);
    ui->rightBtn->style()->polish(ui->rightBtn);
}

void OscServoSpe::on_leftBtn_clicked()
{
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();

    if (oscproc_get_stat() == OSC_PROC_START)
    {
        MessageOut(TR("采样中禁止轴切换!"));
        return;
    }
    if(curAxesIndex <= 0)
    {
        HmiOscServo::SetCurAxesIndex(0);
    }
    else
    {
        curAxesIndex--;
        HmiOscServo::SetCurAxesIndex(curAxesIndex);
    }
    this->LoadAxisVal(1);
    this->OnBtFlagChange();
    oscproc_smpldata_reset();
    //HmiOscServo::OscservoLoadGcode(1); // 重新生成并加载G代码
    this->Reset();      // 清除图形
    ResetInfo();        // 清除调机报表的显示数据
}

void OscServoSpe::on_rightBtn_clicked()
{
    Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();

    if (oscproc_get_stat() == OSC_PROC_START)
    {
        MessageOut(TR("采样中禁止轴切换!"));
        return;
    }

    if(curAxesIndex >= (TOTAL_AXES_PER_CHN - 1))
    {
        curAxesIndex = TOTAL_AXES_PER_CHN - 1;
        HmiOscServo::SetCurAxesIndex(curAxesIndex);
    }
    else if(HmiOscServo::GetIndexAxesConf(curAxesIndex + 1) < 0)
    {
        return;
    }
    else
    {
        curAxesIndex++;
        HmiOscServo::SetCurAxesIndex(curAxesIndex);
    }
    this->LoadAxisVal(1);
    this->OnBtFlagChange();
    oscproc_smpldata_reset();
    //HmiOscServo::OscservoLoadGcode(1); // 重新生成并加载G代码
    this->Reset();      // 清除图形
    ResetInfo();        // 清除调机报表的显示数据
}

void OscServoSpe::Reset()
{ // 清空图形
    pOscWaveSpd->ClearPoint();
    pOscWaveAcc->ClearPoint();
    this->lastEndPos = 0;
}

void OscServoSpe::ResetInfo()
{
    fBit64 speVal = 0.000;
    fBit64 speRateVal = 0.000;
    fBit64 accMaxVal = 0.000;
    fBit64 accMinVal = 0.000;

    ui->speLabel->setText(QString::number(speVal, 'f',3));
    ui->speRateLabel->setText(QString("%1%").arg(QString::number(speRateVal, 'f',3)));
    ui->accMaxLabel->setText(QString::number(accMaxVal, 'f',3));
    ui->accMinLabel->setText(QString::number(accMinVal, 'f',3));
}

void OscServoSpe::Refresh()
{
    QVector<double> x;
    QVector<double> y0;
    QVector<double> y1;
    QVector<double> y2;

    Bit32 i = 0;
    Bit32 stPos = 0;
    Bit32 edPos= 0;
    Bit64 *ch0_addr = NULL;
    Bit64 *ch1_addr = NULL;

    Bit32 client = HmiOscServo::oscservo_get_sampl_client();
    Bit32 type = 0;
    Bit32 offset = 0;
    Bit32 len = 0;
    Bit32 axisNo = 0;

    if (oscproc_get_stat() != OSC_PROC_START)
    {
        this->lastEndPos = 0; // 停止后需要置零
        return;
    }

    x.clear();
    y0.clear();
    y1.clear();
    y2.clear();

    stPos = this->lastEndPos;
    edPos = oscproc_get_pos();
    this->lastEndPos = edPos;

    ch0_addr = oscproc_get_smpldata(0);
    ch1_addr = oscproc_get_smpldata(1);
    if (NULL == ch0_addr || NULL == ch1_addr)
    {
        return;
    }

    HNC_SamplGetConfig(client, 0, type, axisNo, offset, len);
    for (i = stPos+1; i < edPos; ++i)
    {
        y0.append(ch0_addr[i] * smpl_calc_spd_coef(axisNo, 4)*60000.0);
        y1.append(ch1_addr[i] * smpl_calc_spd_coef(axisNo, 5)*60000.0);
        if (i > 0)
        {
            y2.append((ch1_addr[i] - ch1_addr[i-1]) * HmiOscServo::SmplCalcSpddiffCoef(axisNo, 5)); // ACCVEL_MUTI2INT
        }

        x.append(i * oscproc_get_smpl_period());
    }

    pOscWaveSpd->LineZeroAddPoint(x, y0);
    pOscWaveSpd->LineOneAddPoint(x, y1);
    pOscWaveSpd->WaveReplot();
    pOscWaveAcc->LineZeroAddPoint(x, y2);
    pOscWaveAcc->WaveReplot();
}

void OscServoSpe::SetColorStyle()
{
    // 默认黑色风格
    QColor bk(0,0,0); // 背景
    QColor gd(0,0,0); // 网格
    QColor ft(0,0,0); // 字体颜色
    QColor c1(0,0,0); // 曲线1
    QColor c2(0,0,0); // 曲线2
    QColor c3(0,0,0); // 曲线3
    QColor c4(0,0,0); // 曲线4

    HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

    QPalette palette;
    palette.setColor(QPalette::Background, bk);
    ui->frame->setAutoFillBackground(true);
    ui->frame->setPalette(palette);

    pOscWaveSpd->SetColor(bk, gd, ft, c1, c2, c3, c4);
    pOscWaveAcc->SetColor(bk, gd, ft, c3, c4, c3, c4);
}
