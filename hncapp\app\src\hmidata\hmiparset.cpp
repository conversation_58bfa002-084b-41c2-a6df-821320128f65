﻿
#include "hncfileverify.h"
#include "hncsysctrl.h"

#include "common.h"
#include "hmiparaman.h"

#include "hmiparset.h"

//各部分参数个数定义[无视型号，统一]
#define NCU_PARAM_NUM		2000	//数控单元参数个数 通讯
#define MAC_PARAM_NUM		2000	//机床/用户参数个数（含用户报警信息）
#define CHAN_PARAM_NUM		1000	//通道参数个数
#define AXIS_PARAM_NUM		1000	//轴及伺服参数个数
#define CPAX_PARAM_NUM		500		//轴补偿描述参数个数
#define NCBRD_PARAM_NUM		200		//主站参数个数
#define NCOBJ_PARAM_NUM		32		//从站参数个数
#define USER_DEFINE_PARM_OFF 800

#ifdef _HNC_80_ //12(2)通道，80轴(64进给+16主轴)，8主站，160部件
    #define TABLE_PARAM_NUM		40000 //总数据表参数个数
#endif //_HNC_80_

#ifdef _HNC_30_ //4(5)通道，32轴(24进给+8主轴)，4主站，80部件
    #define TABLE_PARAM_NUM		20000 //总数据表参数个数
#endif //_HNC_30_


#ifdef _HNC_20_	//2通道，8轴，4主轴，2主站，30部件
    #define TABLE_PARAM_NUM		8000 //总数据表参数个数
#endif //_HNC_20_


#ifdef _HNC_10_	//1通道，3 轴，2主轴，1主站，15部件
    #define TABLE_PARAM_NUM		5000 //总数据表参数个数
#endif //_HNC_10_

//各类参数基地址
#define PAR_NCU_BASE 0	//数控单元参数基地址
#define PAR_MAC_BASE (PAR_NCU_BASE+NCU_PARAM_NUM)	//机床参数基地址
#define PAR_CHAN_BASE (PAR_MAC_BASE+MAC_PARAM_NUM)	//通道参数基地址
#define PAR_AXIS_BASE (PAR_CHAN_BASE+CHAN_PARAM_NUM*SYS_CHAN_NUM)	//轴及伺服参数基地址
#define PAR_CMPE_BASE (PAR_AXIS_BASE+AXIS_PARAM_NUM*TOTAL_AXES_NUM)  //轴补偿参数基地址 从单独的存储区开始
#define PAR_BOARD_BASE (PAR_CMPE_BASE+CPAX_PARAM_NUM*TOTAL_AXES_NUM) //
#define PAR_PART_BASE (PAR_BOARD_BASE+NCBRD_PARAM_NUM*SYS_NCBRD_NUM)  //控制对象参数基地址
#define PAR_TABLE_BASE (PAR_PART_BASE+ NCOBJ_PARAM_NUM*SYS_NCOBJ_NUM) //数据表参数基地址

#define TOTAL_PARAM_NUM	(PAR_TABLE_BASE+TABLE_PARAM_NUM) //总参数个数

HmiParSet *HmiParSet::m_pInstance = NULL;
HmiParSet::HmiParSet()
{
    m_sErrorStr = "";
    m_nParamNoList.clear();
    memset(&m_fileHead, 0, sizeof(m_fileHead));
    m_pFp = NULL;
}

HmiParSet::~HmiParSet()
{
    CloseFile();
}

Bit32 HmiParSet::SetIntVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit32 value, QString title)
{
    Bit32 ret = ParaSetIntValWithLog(fileno, subno, index, value, title);
    if (ret == 0)
    {
        ParamIndex idx = {fileno, subno, index};
        m_nParamNoList.append(idx);
    }
    return ret;
}

Bit32 HmiParSet::SetFloatVal(Bit32 fileno, Bit32 subno, Bit32 index, fBit64 value, QString title)
{
    Bit32 ret = ParaSetFloatValWithLog(fileno, subno, index, value, title);
    if (ret == 0)
    {
        ParamIndex idx = {fileno, subno, index};
        m_nParamNoList.append(idx);
    }

    return ret;
}

Bit32 HmiParSet::SetStrVal(Bit32 fileno, Bit32 subno, Bit32 index, Bit8 *value, QString title)
{
    Bit32 ret = ParaSetStrValWithLog(fileno, subno, index, value, title);
    if (ret == 0)
    {
        ParamIndex idx = {fileno, subno, index};
        m_nParamNoList.append(idx);
    }

    return ret;
}

Bit32 HmiParSet::TransParamId(Bit32 paramId, Bit32 &fileno, Bit32 &subno, Bit32 &index)
{
    Bit32 nRowNo = 0;
    Bit32 nSumIndex = 0;
    Bit16 dupnum = 0;
    Bit16 dupno = 0;

    if (0 != HNC_ParamanTransId2Rowx(paramId, &nSumIndex))
    {
        return -1;
    }

    HNC_ParamanTransRowx2Row (nSumIndex, &fileno ,&subno, &nRowNo);
    HNC_ParamanTransRow2Index(fileno, subno, nRowNo, &index, &dupnum, &dupno);

    return 0;
}

Bit32 HmiParSet::GetParamId(Bit32 fileno, Bit32 subno, Bit32 index)
{
    Bit32 paramId = 0;
    SDataProperty propId;
    memset(&propId, 0, sizeof(propId));

    HNC_ParamanGetParaProp(fileno, subno, index, PARA_PROP_ID, &propId);
    paramId = propId.value.val_int;

    return paramId;
}

Bit32 HmiParSet::GetParamDType(Bit32 paramId)
{
    Bit32 type = 0;
    SDataProperty propId;
    memset(&propId, 0, sizeof(propId));

    HNC_ParamanGetParaPropEx(paramId, PARA_PROP_STORE, &propId);
    type = propId.value.val_int;

    return type;
}

Bit32 HmiParSet::GetParamDType(Bit32 fileno, Bit32 subno, Bit32 index)
{
    Bit32 type = 0;
    SDataProperty propId;
    memset(&propId, 0, sizeof(propId));

    HNC_ParamanGetParaProp(fileno, subno, index, PARA_PROP_STORE, &propId);
    type = propId.value.val_int;

    return type;
}

Bit32 HmiParSet::GetParam(Bit32 fileno, Bit32 subno, Bit32 index, QString &val)
{
    Bit32 datatype = 0;
    SDataProperty prop;
    Bit8 buf[128] = { 0 };
    memset(&buf, 0, sizeof(buf));
    memset(&prop, 0, sizeof(prop));

    HNC_ParamanGetParaProp(fileno, subno, index, PARA_PROP_STORE, &prop);
    datatype = prop.value.val_int;
    HNC_ParamanGetParaProp(fileno, subno, index, PARA_PROP_VALUE, &prop);

    Bit32 ret = HmiParmMan::paramman_val2str(datatype, &prop, buf);
    val = QString(buf);

    return ret;
}

Bit32 HmiParSet::GetParam(Bit32 paramId, QString &val)
{
    Bit32 datatype = 0;
    SDataProperty prop;
    Bit8 buf[128] = { 0 };
    memset(&buf, 0, sizeof(buf));
    memset(&prop, 0, sizeof(prop));

    HNC_ParamanGetParaPropEx(paramId, PARA_PROP_STORE, &prop);
    datatype = prop.value.val_int;
    HNC_ParamanGetParaPropEx(paramId, PARA_PROP_VALUE, &prop);

    Bit32 ret = HmiParmMan::paramman_val2str(datatype, &prop, buf);
    val = QString(buf);

    return ret;
}

Bit32 HmiParSet::SetParaPropWithLog(Bit32 fileno, Bit32 subno, Bit32 index, QString value, QString title)
{
    Bit32 paramId = GetParamId(fileno, subno, index);
    return SetParaPropWithLog(paramId, value, title);
}

Bit32 HmiParSet::SetParaPropWithLog(Bit32 paramId, QString value, QString title)
{
    SDataProperty paradata;
    memset(&paradata, 0, sizeof(paradata));
    Bit8 buf[128] = { 0 };
    memcpy(buf, value.toStdString().data(), sizeof(buf));
    if (HmiParmMan::setting_paradata(paramId, buf, &paradata) < 0)
    {
        return -1;
    }

    return SetParaPropWithLog(paramId, paradata, title);
}

Bit32 HmiParSet::SetParaPropWithLog(Bit32 paramId, SDataProperty value, QString title)
{
    Bit32 fileno = 0;
    Bit32 subno = 0;
    Bit32 index = 0;
    if (TransParamId(paramId, fileno, subno, index) < 0)
    {
        return -1;
    }
    QString oldVal("");
    if (GetParam(paramId, oldVal) < 0)
    {
        return -1;
    }
    Bit32 ret = HNC_ParamanSetParaPropEx(paramId, PARA_PROP_VALUE, &value);
    if (ret == 0)
    {
        ParamIndex idx = {fileno, subno, index};
        m_nParamNoList.append(idx);
    }

    QString newVal("");
    GetParam(paramId, newVal);
    QString log = QObject::TR("%1号参数由%2更改为%3")
            .arg(QString::number(paramId)).arg(oldVal).arg(newVal);
    if (!title.isEmpty())
    {
        log = QString("%1--").arg(title) + log;
    }

    Logdt::LogdtInput(LOG_FILECHANGE, log);
    return ret;
}

void HmiParSet::CloseFile()
{
    if(m_pFp)
    {
        fclose(m_pFp);
        m_pFp = NULL;
    }
}

Bit32 HmiParSet::SeekFileByAddr(Bit32 nAddr)
{
    if (nAddr < 0 || m_pFp == NULL)
    {
        return PARA_FILE_DATA_FAIL;
    }

    Bit32 nRet = fseek(m_pFp, sizeof(FileHead) + m_fileHead.HeadInfoSize+sizeof(SParamValue)*nAddr, SEEK_SET);
    if(nRet != 0)
    {
        return PARA_FILE_SEEK_FAIL;
    }
    return 0;
}

Bit32 HmiParSet::WriteParamByAddr(Bit32 nAddr, SParamValue *paramItem, Bit32 count)
{
    Bit32 nRet = SeekFileByAddr(nAddr);
    if (nRet < 0 || m_pFp == NULL)
    {
        return nRet;
    }

    nRet = fwrite(paramItem, sizeof(SParamValue), count, m_pFp);
    if(nRet != count)
    {
        return 	PARA_FILE_READ_FAIL;
    }

    return 0;
}

Bit32 HmiParSet::SaveParamChanges()
{
    if (m_nParamNoList.count() == 0)
    {
        return 1;
    }
    Bit8 parmPath[PATH_NAME_LEN] = "\0";
    Bit8 parmName[PATH_NAME_LEN] = "\0";
    HNC_SysCtrlGetConfig(HNC_SYS_CFG_PARM_PATH, parmPath);
    HNC_SysCtrlGetConfig(HNC_SYS_CFG_PARM_FILE, parmName);

    Bit8 paramanPath[PATH_NAME_LEN] = "\0";
    snprintf(paramanPath, PATH_NAME_LEN, "%s%c%s.DAT", parmPath, DIR_SEPARATOR, parmName);

    Bit8 paramTmpPath[PATH_NAME_LEN] = "\0";
    snprintf(paramTmpPath, PATH_NAME_LEN, "%s%c%s.tmp", parmPath, DIR_SEPARATOR, parmName);

    QFile::copy(QString(paramanPath), QString(paramTmpPath));

    Bit32 ret = SaveParam(paramTmpPath);
    if (ret == 0)
    {
        m_sErrorStr = "";
        unlink(paramanPath);
#ifdef _LINUX
        sync();
#endif
        rename(paramTmpPath, paramanPath);
#ifdef _LINUX
        sync();
#endif
    }
    else if (ret == PARA_FILE_OPEN_FAIL)
    {
        m_sErrorStr = QObject::TR("参数文件打开失败");
    }
    else if (ret == PARA_FILE_DATA_FAIL)
    {
        m_sErrorStr = QObject::TR("参数文件数据错误");
    }
    return ret;
}

Bit32 HmiParSet::SaveParam(Bit8 *paramTmpPath)
{
    if (paramTmpPath == NULL)
    {
        return PARA_FILE_OPEN_FAIL;
    }
    Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("参数保存开始"));
    CloseFile();
    m_pFp = fopen(paramTmpPath, "r+b");
    if(m_pFp == NULL)
    {
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("参数打开失败"));
        return PARA_FILE_OPEN_FAIL;
    }
    // 读取文件头
    FileHead fHead;
    Bit32 ret = fread(&fHead, sizeof(FileHead), 1, m_pFp);
    if (ret < 0)
    {
        CloseFile();
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("参数读取文件头失败"));
        return PARA_FILE_DATA_FAIL;
    }
    fseek(m_pFp, sizeof(FileHead), SEEK_SET);
    fread(&m_fileHead.HeadInfoSize, sizeof(m_fileHead.HeadInfoSize), 1, m_pFp);

    // 写入参数
    for(int i = 0; i < m_nParamNoList.count(); i++)
    {
        ParamIndex param = m_nParamNoList.at(i);
        Bit32 nAddr = ParamanGetAddr(param.m_nFileno, param.m_nSubno, param.m_nIndex);
        SParamValue paramItem;
        HNC_ParamanGetItem(param.m_nFileno, param.m_nSubno, param.m_nIndex, &paramItem);
        Bit32 ret = WriteParamByAddr(nAddr, &paramItem, 1);
        if (ret < 0)
        {
            CloseFile();
            Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("参数保存失败"));
            return PARA_FILE_DATA_FAIL;
        }
    }

    //	写入校验码
    fflush(m_pFp);
    fHead.verifyCode[0] = HNC_FileVerifyCrc32File(m_pFp);
    fseek(m_pFp, 0, SEEK_SET);
    fwrite(&fHead, sizeof(FileHead), 1, m_pFp);
    fflush(m_pFp);

#ifdef _LINUX
    Bit16 fd = fileno(m_pFp); //获取文件描述符
    fsync(fd);	//	文件强制写
#endif

    CloseFile();
    Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("参数保存完成"));
    m_nParamNoList.clear();
    return 0;

}

Bit32 HmiParSet::ParamanGetAddr(Bit32 fileno, Bit32 nsubno, Bit32 nid)
{
    Bit32 nAddr = 0;
    switch (fileno)
    {
    case PARAMAN_FILE_NCU: //系统参数
        nAddr = PAR_NCU_BASE + nid;
        break;
    case PARAMAN_FILE_MAC: //机床参数
        nAddr = PAR_MAC_BASE + nid;
        break;
    case PARAMAN_FILE_CHAN: //通道参数
        nAddr = PAR_CHAN_BASE + nsubno * CHAN_PARAM_NUM + nid;
        break;
    case PARAMAN_FILE_AXIS: //坐标轴参数
        if (nid >= SERVO_PARM_START_IDX && nid < SERVO_PARM_START_IDX + SERVO_PARM_TOTAL_NUM)
        {
            nid = SERVO_PARM_START_IDX + (nid - SERVO_PARM_START_IDX);
        }
        nAddr = PAR_AXIS_BASE + nsubno * AXIS_PARAM_NUM + nid;
        break;
    case PARAMAN_FILE_ACMP: //轴补偿值参数
        nAddr = PAR_CMPE_BASE + nsubno * CPAX_PARAM_NUM + nid;
        break;
    case PARAMAN_FILE_CFG: //设备接口参数
        nAddr = PAR_PART_BASE + nsubno * NCOBJ_PARAM_NUM + nid;
        break;
    case PARAMAN_FILE_TABLE://数据表参数
        nAddr = PAR_TABLE_BASE + nid;
        break;
    default:
        break;
    }

    Bit32 totalParmNums = TOTAL_PARAM_NUM;
    if (nAddr > totalParmNums)
    {
        return -1;
    }
    return nAddr;
}

Bit32 HmiParSet::ParamanGetCount(Bit32 fileno)
{
    Bit32 count = 0;
    switch (fileno)
    {
    case PARAMAN_FILE_NCU: //NC参数
        count = NCU_PARAM_NUM;
        break;
    case PARAMAN_FILE_MAC: //机床参数
        count = MAC_PARAM_NUM;
        break;
    case PARAMAN_FILE_CHAN: //通道参数
        count = CHAN_PARAM_NUM;
        break;
    case PARAMAN_FILE_AXIS: //坐标轴参数
        count = AXIS_PARAM_NUM;
        break;
    case PARAMAN_FILE_ACMP: //轴补偿值参数
        count = CPAX_PARAM_NUM;
        break;
    case PARAMAN_FILE_CFG: //设备接口参数
        count = NCOBJ_PARAM_NUM;
        break;
    case PARAMAN_FILE_TABLE://数据表参数
        count = TABLE_PARAM_NUM;
        break;
    default:
        break;
    }

    return count;
}
