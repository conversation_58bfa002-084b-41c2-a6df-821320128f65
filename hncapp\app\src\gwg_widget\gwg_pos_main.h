#ifndef GWG_POS_MAIN_H
#define GWG_POS_MAIN_H

#include "containerwidget.h"

namespace Ui {
class GwgPosMain;
}

class GwgPosMain : public ContainerWidget
{
    Q_OBJECT

public:
    explicit GwgPosMain(QWidget *parent = 0);
    ~GwgPosMain();

private:
    Ui::GwgPosMain *ui;

    void refreshPos();
    void showAllPos();
    void showAbsolutePos();
    void showRelativePos();
public:
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
};

#endif // GWG_POS_MAIN_H
