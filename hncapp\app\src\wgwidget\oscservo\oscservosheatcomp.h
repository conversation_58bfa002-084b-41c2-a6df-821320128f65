﻿#ifndef OSCSERVOSHEATCOMP_H
#define OSCSERVOSHEATCOMP_H

#include <QModelIndex>
#include <QStyledItemDelegate>
#include <QWidget>

#include "containerwidget.h"
#include "hmioscservo.h"
#include "dlgoscservoselect.h"

namespace Ui {
class OscServoSHeatComp;
}

QT_BEGIN_NAMESPACE
class OscWave;
class OscList;
QT_END_NAMESPACE

class OscServoSHeatComp : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoSHeatComp(QWidget *parent = 0);
    ~OscServoSHeatComp();

    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    bool eventFilter(QObject *target, QEvent *event);
//#ifdef _NCUPPER_
//    void resizeEvent(QResizeEvent *);
//#endif
private slots:
    void OnTableFocuseReset();
private:
    Ui::OscServoSHeatComp *ui;
    QLabel *bmpLabel;
    OscWave *pOscSComp;
    OscList *sCompOscList;
    Bit8 focusIdx;
//#ifdef _NCUPPER_
//    bool firstFlag;
//#endif
    DlgOscservoSelect *pDlgFileSle; // 选择文件对话框
    int m_nLastRowCount;
    QDoubleValidator *doubleValidator;

    void SetColorStyle();
    void LoadWave();
    void Reset();
    void SetTableFoucs();
    void ClearTableFoucs();
    void LoadView();
    void LoadInfo();
    void Refresh();
    void DrawPlotManual(Bit32 num);
    QStringList GetParmList();
    void RedrawHistotyWave();
    void OpenHistoryDlg();
};

class SCompListDelegate : public QStyledItemDelegate
{
    Q_OBJECT

public:
        SCompListDelegate(QWidget *parent = 0) : QStyledItemDelegate(parent) {}
};

class SCompListModel:public QAbstractTableModel
{
    Q_OBJECT
public:
    SCompListModel(QObject *parent = 0):QAbstractTableModel(parent){
    }

    void refresh();
    int rowCount(const QModelIndex &parent) const;
    int columnCount(const QModelIndex &parent) const;
    Qt::ItemFlags flags(const QModelIndex &index) const;
    QVariant data(const QModelIndex &index, int role) const;
};

#endif // OSCSERVOHEATCOMP_H
