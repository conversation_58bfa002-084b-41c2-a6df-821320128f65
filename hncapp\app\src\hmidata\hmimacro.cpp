﻿#include "passwd.h"
#include "hncsysctrl.h"
#include "hncvar.h"
#include "hncdatadef.h"
#include "servicedata.h"

#include "hmimacro.h"

HmiNcMacroExInfo::HmiNcMacroExInfo(MacroData *macroData, Bit32 pathType, QString name, QString midpath)
{
    m_macroData.clear();
    m_pMacroData = macroData;

    LoadXml(pathType, name, midpath);
}

Bit32 HmiNcMacroExInfo::GetXmlDoc(QDomDocument &doc, Bit32 pathType, QString name, QString midpath)
{
    QString errorMsg("");
    int errorLine = 0;
    int errorColumn = 0;
    QString path = QString(midpath) + QString(name);

    Bit32 ret = GetXMLElement(pathType, path, doc,
                              &errorMsg, &errorLine, &errorColumn);
    if (ret == -2)
    {
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("%1文件打开失败").arg(name));
    }
    else if (ret == -3)
    {
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("%1文件出错(行:%2,列:%3,%4)").arg(name)
                         .arg(errorLine).arg(errorColumn).arg(errorMsg));
    }
    return ret;
}

void HmiNcMacroExInfo::LoadSubItem(QDomElement element, QList<MacroExInfoSubItem> &subItem)
{
    for(Bit32 i = 0; i < element.childNodes().count(); i++)
    {
        QDomNode node = element.childNodes().item(i);
        QDomElement e   = node.toElement();

        MacroExInfoSubItem sub;

        sub.m_sName  = e.attribute("name");
        sub.m_sValue = e.attribute("value");
        sub.m_sPicPath = e.attribute("picpath");

        subItem.append(sub);
    }
}

void HmiNcMacroExInfo::LoadXml(Bit32 pathType, QString name, QString midpath)
{
    QDomDocument doc;
    if (GetXmlDoc(doc, pathType, name, midpath) < 0)
    {
        return;
    }

    QDomElement element = doc.documentElement();
    for(Bit32 i = 0; i < element.childNodes().count(); i++)
    {
        QDomNode node = element.childNodes().item(i);
        QDomElement item = node.toElement();
        if (item.hasAttribute("no") == false)
        {
            continue;
        }
        bool ok = false;
        Bit32 macroNo   = item.attribute("no").toInt(&ok);
        if (ok == false || macroNo < NC_MACRO_ST || macroNo > NC_MACRO_ED)
        {
            continue;
        }

        MacroExInfo macro;
        macro.m_nflag = item.attribute("flag").toInt(&ok);
        macro.m_sComment = item.attribute("comment");
        macro.m_sPicPath = item.attribute("picpath");

        LoadSubItem(item, macro.m_subItem);
        m_macroData.insert(macroNo, macro);
    }
}

Bit32 HmiNcMacroExInfo::GetMacroNo(Bit32 idx)
{
    if (idx < 0 || idx >= m_macroData.count())
    {
        return -1;
    }
    return m_macroData.keys().at(idx);
}

Bit32 HmiNcMacroExInfo::GetMacroData(Bit32 macroNo, MacroExInfo &macro)
{
    if (m_macroData.contains(macroNo) == false)
    {
        return -1;
    }
    macro = m_macroData.value(macroNo);
    return 0;
}

Bit32 HmiNcMacroExInfo::GetMacroCount()
{
    return m_macroData.count();
}

QString HmiNcMacroExInfo::GetMacroComboxStr(MacroExInfo macro, Bit32 subIdx)
{
    if (subIdx < 0 || subIdx > macro.m_subItem.count())
    {
        return QString("");
    }
    return QObject::TR(macro.m_subItem.at(subIdx).m_sName);
}

void HmiNcMacroExInfo::NameToComboxIdx(Bit32 macroNo, MacroExInfo macro, QString &valStr)
{
    if (macro.m_subItem.count() != 0)
    {
        Bit32 idx = -1;
        for (int i = 0 ; i < macro.m_subItem.count(); i++)
        {
            if (macro.m_subItem.at(i).m_sName == valStr)
            {
                idx = i;
                break;
            }
        }
        if (idx < 0)
        {
            SetVal(macroNo, macro.m_subItem.at(0).m_sValue);
        }
        else
        {
            valStr = QString::number(idx);
        }
    }
}

void HmiNcMacroExInfo::ValToComboxIdx(Bit32 macroNo, MacroExInfo macro, QString &valStr)
{
    if (macro.m_subItem.count() != 0)
    {
        Bit32 idx = -1;
        for (int i = 0 ; i < macro.m_subItem.count(); i++)
        {
            if (macro.m_subItem.at(i).m_sValue == valStr)
            {
                idx = i;
                break;
            }
        }
        if (idx < 0)
        {
            SetVal(macroNo, macro.m_subItem.at(0).m_sValue);
        }
        else
        {
            valStr = QString::number(idx);
        }
    }
}

QString HmiNcMacroExInfo::GetMacroVal(Bit32 macroNo)
{
    MacroExInfo macro;
    if (GetMacroData(macroNo, macro) < 0)
    {
        return QString("");
    }

    QString valStr("");
    SDataUnion tmp;
    HNC_MacroVarGetValue(macroNo, &tmp);
    if(tmp.type == DTYPE_INT)
    {
        valStr = QString::number(tmp.v.i);
    }
    else if (tmp.type == DTYPE_FLOAT)
    {
        valStr =  QString::number(tmp.v.f, 'f', m_pMacroData->GetPrec(macroNo));
    }
    else if (tmp.type == DTYPE_STRING)
    {
        valStr = QString(tmp.v.s);
    }

    ValToComboxIdx(macroNo, macro, valStr);
    return valStr;
}

void HmiNcMacroExInfo::SetMacroType(Bit32 macroNo, Bit8 dataType)
{
    SDataUnion tmp;
    memset(&tmp, 0, sizeof(tmp));
    tmp.type = dataType;
    HNC_MacroVarSetValue(macroNo, &tmp);
}

QString HmiNcMacroExInfo::ComboxIdxToName(MacroExInfo macro, Bit32 ival)
{
    QString val("");
    if (macro.m_subItem.count() != 0)
    {
        if (ival >= 0 && ival < macro.m_subItem.count())
        {
            val = macro.m_subItem.at(ival).m_sName;
        }
    }
    return val;
}

void HmiNcMacroExInfo::ComboxIdxToVal(MacroExInfo macro, QString &val)
{
    bool ok = false;
    if (macro.m_subItem.count() != 0)
    {
        Bit32 ival = val.toInt(&ok);
        if (ival >= 0 && ival < macro.m_subItem.count())
        {
            val = macro.m_subItem.at(ival).m_sValue;
        }
    }
}

Bit32 HmiNcMacroExInfo::SetVal(Bit32 macroNo, QString val, bool flag, bool isTabVal)
{
    bool ok = false;
    bool ok2 = false;
    bool ok3 = false;
    MacroExInfo macro;
    if (GetMacroData(macroNo, macro) < 0)
    {
        return -1;
    }

    if (flag)
    {
        if (isTabVal)
        {
            NameToComboxIdx(macroNo, macro, val);
        }
        ComboxIdxToVal(macro, val);
    }
    SDataUnion tmp;
    HNC_MacroVarGetValue(macroNo, &tmp);
    if(tmp.type == DTYPE_INT)
    {
        Bit32 ival = val.toInt(&ok);
        Bit32 maxval = m_pMacroData->GetMax(macroNo).toInt(&ok2);
        Bit32 minval = m_pMacroData->GetMin(macroNo).toInt(&ok3);
        if (ok == false || (ok2 && ival > maxval) || (ok3 && ival < minval))
        {
            return -2;
        }
        tmp.v.i = ival;
    }
    else if (tmp.type == DTYPE_FLOAT)
    {
        fBit64 fval = val.toDouble(&ok);
        Bit32 maxval = m_pMacroData->GetMax(macroNo).toDouble(&ok2);
        Bit32 minval = m_pMacroData->GetMin(macroNo).toDouble(&ok3);
        if (ok == false || (ok2 && fval > maxval) || (ok3 && fval < minval))
        {
            return -2;
        }
        tmp.v.f = fval;
    }
    else if (tmp.type == DTYPE_STRING)
    {
        snprintf(tmp.v.s, sizeof(tmp.v.s), val.toStdString().c_str());
    }
    else
    {
        return -1;
    }
    return HNC_MacroVarSetValue(macroNo, &tmp);
}

/////////////////////////////////////////////////////////
MacroExInfo::MacroExInfo()
{
    m_nflag = 0;

    m_sComment = "";
    m_sPicPath = "";

    m_subItem.clear();
}

/////////////////////////////////////////////////////////
MacroExInfoSubItem::MacroExInfoSubItem()
{
    Clear();
}

MacroExInfoSubItem::MacroExInfoSubItem(QString name, QString val)
{
    m_sName = name;
    m_sValue = val;
}

void MacroExInfoSubItem::Clear()
{
    m_sName = "";
    m_sValue = "";
    m_sPicPath = "";
}

///////////////////////////////////////////////////////////////////////
HmiNcMacroShowBase::HmiNcMacroShowBase(HmiNcMacroExInfo *hmimacro, MacroData *macroData)
{
    m_pMacroData = macroData;
    m_pHmiNcMacro = hmimacro;
    m_nOldRight = -1;
    m_nMacroList.clear();
}

Bit32 HmiNcMacroShowBase::GetMacroNo(Bit32 idx)
{
    GetMacroList();
    if (idx < 0 || idx >= m_nMacroList.count())
    {
        return -1;
    }
    return m_nMacroList.at(idx);
}

Bit32 HmiNcMacroShowBase::GetMacroFlag(Bit32 idx)
{
    MacroExInfo macro;
    if (GetMacro(idx, macro) < 0)
    {
        return 0;
    }
    return macro.m_nflag;
}

Bit32 HmiNcMacroShowBase::GetMacro(Bit32 idx, MacroExInfo &macroData)
{
    GetMacroList();
    Bit32 macroNo = GetMacroNo(idx);
    return m_pHmiNcMacro->GetMacroData(macroNo, macroData);
}


int HmiNcMacroShowBase::GetMacroDataType(Bit32 idx)
{
    Bit32 macroNo = GetMacroNo(idx);
    if (macroNo < 0)
    {
        return -1;
    }
    SDataUnion tmp;
    HNC_MacroVarGetValue(macroNo, &tmp);
    return tmp.type;
}

QString HmiNcMacroShowBase::GetMacroMaxVal(Bit32 idx)
{    
    Bit32 macroNo = GetMacroNo(idx);
    return m_pMacroData->GetMax(macroNo);
}

QString HmiNcMacroShowBase::GetMacroMinVal(Bit32 idx)
{
    Bit32 macroNo = GetMacroNo(idx);
    return m_pMacroData->GetMin(macroNo);
}

QString HmiNcMacroShowBase::GetMacroName(Bit32 idx)
{
    Bit32 macroNo = GetMacroNo(idx);
    return m_pMacroData->GetName(macroNo);
}

QString HmiNcMacroShowBase::GetMacroComment(Bit32 idx)
{
    MacroExInfo macro;
    if (GetMacro(idx, macro) < 0)
    {
        return QString("");
    }
    QString str = macro.m_sComment;
    str.replace("\\n", "\n");
    return QObject::TR(str);
}

Bit32 HmiNcMacroShowBase::GetShowType(Bit32 idx)
{
    MacroExInfo macro;
    if (GetMacro(idx, macro) < 0)
    {
        return -1;
    }
    if (macro.m_subItem.isEmpty())
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

QString HmiNcMacroShowBase::GetMacroVal(Bit32 idx)
{
    Bit32 macroNo = GetMacroNo(idx);
    return m_pHmiNcMacro->GetMacroVal(macroNo);
}

QString HmiNcMacroShowBase::GetMacroTabVal(Bit32 idx)
{
    Bit32 macroNo = GetMacroNo(idx);
    MacroExInfo macro;
    if (m_pHmiNcMacro->GetMacroData(macroNo, macro) < 0)
    {
        return QString("");
    }
    QString valStr =  m_pHmiNcMacro->GetMacroVal(macroNo);
    if (macro.m_subItem.count() <= 0)
    {
        return valStr;
    }
    Bit32 val = valStr.toInt();
    return QObject::TR(m_pHmiNcMacro->ComboxIdxToName(macro, val));
}

QStringList HmiNcMacroShowBase::GetMacroComboxList(Bit32 idx)
{
    QStringList list;
    list.clear();

    MacroExInfo macro;
    if (GetMacro(idx, macro) < 0)
    {
        return list;
    }

    for (int i = 0 ; i < macro.m_subItem.count(); i++)
    {
        list.append(QObject::TR(macro.m_subItem.at(i).m_sName));
    }
    return list;
}

NcDataLimit HmiNcMacroShowBase::GetMacroInputDataLimit(Bit32 idx)
{
    Bit32 macroNo = GetMacroNo(idx);
    Bit32 dataType = m_pMacroData->GetType(macroNo);
    Bit32 prec = m_pMacroData->GetPrec(macroNo);
    if(dataType == DTYPE_INTEGER)
    {
        Bit32 min = m_pMacroData->GetMin(macroNo).toInt();
        Bit32 max = m_pMacroData->GetMax(macroNo).toInt();
        return DTYPE_INT_(min, max, 10);
    }
    else if(dataType == DTYPE_FLOAT)
    {
        fBit64 min = m_pMacroData->GetMin(macroNo).toDouble();
        fBit64 max = m_pMacroData->GetMax(macroNo).toDouble();
        return DTYPE_FLOAT_(min, max, 20, prec);
    }
    else if(dataType == DTYPE_STRING)
    {
        return DTYPE_STRING_(20);
    }
    return DataTypeLimit(dataType, 8, 0);
}

QString HmiNcMacroShowBase::GetMacroPicPath(Bit32 idx)
{
    MacroExInfo macro;
    if (GetMacro(idx, macro) < 0)
    {
        return QString("");
    }
    QString path("");
    if (macro.m_subItem.count() != 0)
    {
        QString valStr = GetMacroVal(idx);
        bool ok = false;
        Bit32 tmp = valStr.toInt(&ok);
        if (ok && tmp >= 0 && tmp < macro.m_subItem.count())
        {
            path = macro.m_subItem.at(tmp).m_sPicPath;
        }
    }
    if (path.isEmpty())
    {
        return macro.m_sPicPath;
    }
    else
    {
        return path;
    }
}

Bit32 HmiNcMacroShowBase::GetMacroNum()
{
    GetMacroList();
    return m_nMacroList.count();
}

QString HmiNcMacroShowBase::GetAllMacroName(Bit32 idx)
{
    Bit32 macroNo = m_pHmiNcMacro->GetMacroNo(idx);
    return m_pMacroData->GetName(macroNo);
}

Bit32 HmiNcMacroShowBase::GetAllMacroNum()
{
    return m_pHmiNcMacro->GetMacroCount();
}

Bit32 HmiNcMacroShowBase::SetVal(Bit32 idx, QString val)
{
    Bit32 macroNo = GetMacroNo(idx);
    return m_pHmiNcMacro->SetVal(macroNo, val, true);
}

Bit32 HmiNcMacroShowBase::SetTabVal(Bit32 idx, QString val)
{
    Bit32 macroNo = GetMacroNo(idx);
    return m_pHmiNcMacro->SetVal(macroNo, val, true, true);
}

void HmiNcMacroShowBase::GetMacroList()
{
    Bit32 right = passwd_get_rights();
    if (m_nOldRight == right || m_pHmiNcMacro == NULL || m_pMacroData == NULL)
    {
        return;
    }

    m_nMacroList.clear();
    for (int i = 0; i < m_pHmiNcMacro->GetMacroCount(); i++)
    {
        Bit32 macroNo = m_pHmiNcMacro->GetMacroNo(i);
        if (m_pMacroData->IsCurRightEditable(right, macroNo))
        {
            m_nMacroList.append(macroNo);
        }
    }
    m_nOldRight = right;
}

/////////////////////////////////////////////////////////
HmiNcMacroShow::HmiNcMacroShow(HmiNcMacroExInfo *hmimacro)
    : HmiNcMacroShowBase(hmimacro, hmimacro->GetMacroData())
{
    GetMacroList();
}


HmiNcMacroShow::HmiNcMacroShow(Bit32 pathType, QString name)
    :HmiNcMacroShowBase()
{
    m_pHmiNcMacro = new HmiNcMacroExInfo(&(ServiceData::Instance().ncMacro), pathType, name);
    m_pMacroData = m_pHmiNcMacro->GetMacroData();
    m_nOldRight = -1;
    m_nMacroList.clear();
}

/////////////////////////////////////////////////////////
HmiLLSUsualMacro::HmiLLSUsualMacro(HmiNcMacroExInfo *hmimacro)
    : HmiNcMacroShowBase(hmimacro, hmimacro->GetMacroData())
{
    GetMacroList();
}

void HmiLLSUsualMacro::GetMacroList()
{
    if (m_pHmiNcMacro == NULL || m_pMacroData == NULL)
    {
        return;
    }

    m_nMacroList.clear();
    for (int i = 0; i < m_pHmiNcMacro->GetMacroCount(); i++)
    {
        Bit32 macroNo = m_pHmiNcMacro->GetMacroNo(i);
        MacroExInfo macro;
        if (m_pHmiNcMacro->GetMacroData(macroNo, macro) < 0)
        {
            continue;
        }
        if (macro.m_nflag)
        {
            m_nMacroList.append(macroNo);
        }
    }
}
