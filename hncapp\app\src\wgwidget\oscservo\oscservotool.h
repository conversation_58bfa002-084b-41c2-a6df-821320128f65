﻿#ifndef OSCSERVOTOOL_H
#define OSCSERVOTOOL_H

#include "containerwidget.h"

namespace Ui {
class OscServoTool;
}

QT_BEGIN_NAMESPACE
class QWidget;
class OscWave;
QT_END_NAMESPACE

class OscServoTool : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoTool(QWidget *parent = 0);
    ~OscServoTool();
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
private:
    Ui::OscServoTool *ui;
    OscWave *pOscToolTime;
    Bit32 lastEndPos;
    void SetColorStyle();
    void Refresh();
    void Reset();
    void LoadInfo();
};

#endif // OSCSERVOTOOL_H
