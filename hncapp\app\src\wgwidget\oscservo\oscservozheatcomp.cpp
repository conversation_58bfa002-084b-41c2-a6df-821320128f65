﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservospindle.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-Z轴热误差界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年11月6日
* 说    明：
*/

#include <qmath.h>
#include <QKeyEvent>
#include <QScrollBar>

#include "hncchan.h"
#include "hncmath.h"
#include "hncsys.h"
#include "hncvar.h"
#include "hncreg.h"
#include "hncregdef.h"
#include "passwd.h"

#include "hmioscproc.h"
#include "hmioscservo.h"
#include "oscwave.h"
#include "osclist.h"

#include "oscservozheatcomp.h"
#include "ui_oscservozheatcomp.h"
#include    "datadef.h"
OscServoZHeatComp::OscServoZHeatComp(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoZHeatComp)
{
    ui->setupUi(this);
    m_nLastRowCount = 0;

    pOscZComp = new OscWave(this, HmiOscServo::OSC_SERVO_ZCOMP, "");
    bmpLabel = new QLabel(ui->frame);
    bmpLabel->setFont(QFont(FONT_TYPE, 12));
    bmpLabel->setText(TR("  适用范围\n 1、钻攻类型机床\n 2、丝杠固定方式：一端固定，一端游走\n\n  快捷键\n ALT+O打开历史误差数据"));
    ui->verticalLayout_2->addWidget(pOscZComp, 0);
    ui->verticalLayout_2->addWidget(bmpLabel, 1);
    ui->verticalLayout_2->setStretch(0, 1);
    ui->verticalLayout_2->setStretch(1, 1);
    ui->verticalLayout_2->setContentsMargins(0, 0, 0, 0);
    ui->verticalLayout_2->setSpacing(5);

    // 列表设置
    zCompOscList = new OscList(this);
    zCompOscList->installEventFilter(this);
    ui->gridLayout_2->addWidget(zCompOscList);
    zCompOscList->SetEditAgent(true);

    // 列表设置
    ui->tableView->setItemDelegate(new ZCompListDelegate);
    ui->tableView->setModel(new ZCompListModel);
    ui->tableView->setFont(QFont(FONT_TYPE, 12));
    ui->tableView->horizontalHeader()->setVisible(false);
    ui->tableView->verticalHeader()->setVisible(false);
    ui->tableView->horizontalHeader()->setStretchLastSection(true);
    ui->tableView->setColumnWidth(0, 80 * HOR_RATIO);
    ui->tableView->setAlternatingRowColors(true);
    ui->tableView->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableView->installEventFilter(this);
    focusIdx = -1;

    connect(this->zCompOscList, SIGNAL(SignalOscTableGetFocus()), this, SLOT(SlotFocusReset()));

    this->pDlgFileSle = new DlgOscservoSelect(this); // 选择文件对话框

    doubleValidator = new QDoubleValidator();
    doubleValidator->setBottom(-99999.9999);
    doubleValidator->setTop(999999.9999);
    doubleValidator->setNotation(QDoubleValidator::StandardNotation);
}

OscServoZHeatComp::~OscServoZHeatComp()
{
    delete ui;
}

QStringList OscServoZHeatComp::GetParmList()
{
    QStringList list;
    list.clear();

    Bit32 totalNo = 0;
    totalNo = HmiOscServo::ServoParmGetCount(HmiOscServo::OSC_SERVO_ZCOMP);

    for(Bit32 i = 0; i < totalNo; i++)
    {
        list.append(QString::number(HmiOscServo::ServoParRow2Id(i)));
    }
    return list;
}

void OscServoZHeatComp::SetTableFoucs()
{
    Bit32 ch = ActiveChan();
    Bit32 selRow = ui->tableView->currentIndex().row();
    if(selRow >= 0)
    {
        ui->tableView->selectRow(selRow);
    }
    else if(HmiOscServo::s_Conf[ch].stZCompConf.preHeat > 0)
    {
        ui->tableView->selectRow(0);
    }
    ui->tableView->setFocus();
}

void OscServoZHeatComp::ClearTableFoucs()
{
    ui->tableView->clearSelection();
    ui->tableView->clearFocus();
}

void OscServoZHeatComp::Reset()
{ // 清空图形
    pOscZComp->ClearPoint();
}

void OscServoZHeatComp::LoadWave()
{
    fBit64 rangLower = 0.0;
    fBit64 rangUpper = 0.0;
    fBit64 rangStep = 0.0;
    Bit32 ch = ActiveChan();

    rangLower = 0;
    rangUpper = HmiOscServo::s_Conf[ch].stZCompConf.preHeat * HmiOscServo::s_Conf[ch].stZCompConf.step;
    rangStep = (rangUpper - rangLower) / 10;
    pOscZComp->SetXRange(rangLower, rangUpper, rangStep);
    pOscZComp->SetYRange(-120, 120, 40);
}

void OscServoZHeatComp::LoadInfo()
{
    Bit32 ch = ActiveChan();
    fBit64 maxOffset = 0;
    Bit32 num = HmiOscServo::s_Conf[ch].stZCompConf.preHeat;
    fBit64 val = 0;

    for ( Bit32 i = 0; i < num; i++)
    {
        if (fabs(maxOffset) < fabs(HmiOscServo::CompGetOffsetValue(i)))
        {
            maxOffset = fabs(HmiOscServo::CompGetOffsetValue(i));
        }
    }
    val = fabs(maxOffset) / 1000.0;   // um转mm
    ui->labelMaxErr->setText(QString::number(val, 'f',3));
}

void OscServoZHeatComp::Refresh()
{
    ZCompListModel* model = static_cast<ZCompListModel*>(ui->tableView->model());
    if (model != NULL)
    {
        int rowCount = model->rowCount(QModelIndex());
        if (rowCount != m_nLastRowCount)            // 配置点数有变动时，刷新列表
        {
            LoadView();
            m_nLastRowCount = rowCount;
        }
    }

    if (HmiOscServo::CompGetRefreshEnable() == 1)
    {
        Bit32 runRow = 0;
        Bit32 ch = ActiveChan();
        const Bit32 MEASURE_ROW = 31;   // 用于在M00处提示用户是循环启动还是手动输入误差值
        const Bit32 MACRO_NO = 50;
        const Bit32 MACRO_VALUE_NO = 60;
        SDataUnion macrovar;
        SDataUnion tmpMacUnion;
        Bit32 var = 0;
        Bit32 i = 0;
        Bit16 regGValue = 0;
        Bit16 regFValue = 0;
        Bit32 bitValue = 0;
        Bit32 chRegBase = 0;

        HNC_RegGetFGBase(REG_FG_CHAN_BASE, &chRegBase);

        if (HmiOscServo::CompGetTestIndex() == 0)   // 没按操作键就进行采集
        {
            if (HmiOscServo::s_Conf[ch].stZCompConf.autoMeasure == 0)  // 没按操作键手动测量模式
            {
                HNC_ChannelGetValue(HNC_CHAN_RUN_ROW, ch, 0, &runRow);
                HNC_MacroVarGetValue(MACRO_NO, &macrovar);
                var = macrovar.v.i;
                if (runRow == MEASURE_ROW)
                {
                    if (var == 0)
                    {
                        MessageOut(QObject::TR("请按循环启动键继续!"));
                    }
                    else
                    {
                        MessageOut(QObject::TR("请手动输入误差值!"));
                    }
                }
                else
                {
                    MessageOut("");
                    HNC_RegGetValue(REG_TYPE_G, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_CTRL2, &regGValue);
                    bitValue = (regGValue >> 14) & 0x01;
                    if (bitValue == 1)
                    {
                        HNC_RegGetValue(REG_TYPE_F, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_STAT2, &regFValue);
                        bitValue = (regFValue >> 7) & 0x01;
                        if (bitValue == 1)
                        {
                            ;
                        }
                        else
                        {
                            HNC_RegSetBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                        }
                    }
                    else
                    {
                        HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                    }
                }
            }

            if (HmiOscServo::s_Conf[ch].stZCompConf.autoMeasure == 1)  // 没按操作键自动测量模式
            {
                HNC_MacroVarGetValue(MACRO_NO, &macrovar);
                var = macrovar.v.i;
                HNC_RegGetValue(REG_TYPE_G, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_CTRL2, &regGValue);
                bitValue = (regGValue >> 14) & 0x01;
                if (bitValue == 1)
                {
                    HNC_RegGetValue(REG_TYPE_F, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_STAT2, &regFValue);
                    bitValue = (regFValue >> 7) & 0x01;
                    if (bitValue == 1)
                    {
                        ;
                    }
                    else
                    {
                        if (var != 0)
                        {
                            HmiOscServo::CompRecordSysVar(-2);

                            pOscZComp->ClearPoint(0);
                            pOscZComp->SetYRange(-120, 120, 40);
                            QVector<double> x;
                            QVector<double> y0;

                            x.append(0);
                            y0.append(0);
                            for(i = 0; i < var; i++)
                            {
                                x.append(HmiOscServo::s_Conf[ch].stZCompConf.step * (i + 1));
                                y0.append(HmiOscServo::CompGetOffsetValue(i));
                            }
                            pOscZComp->LineZeroAddPoint(x, y0);
                            pOscZComp->WaveReplot();
                            this->LoadView();
                        }
                        else
                        {
                            HNC_MacroVarGetValue(MACRO_VALUE_NO, &tmpMacUnion);
                            HmiOscServo::CompSetCompOffsetBase(tmpMacUnion.v.f * 1000);
                        }
                        HNC_RegSetBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                    }
                }
                else
                {
                    HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                }
            }
        }
        else    // 按了操作键就进行采集
        {
            if (HmiOscServo::s_Conf[ch].stZCompConf.autoMeasure == 0)  // 按了操作键手动测量模式
            {
                HNC_ChannelGetValue(HNC_CHAN_RUN_ROW, ch, 0, &runRow);
                HNC_MacroVarGetValue(MACRO_NO, &macrovar);
                var = macrovar.v.i;
                if (runRow == MEASURE_ROW)
                {
                    if (var == 0)
                    {
                        MessageOut(QObject::TR("请按循环启动键继续!"));
                    }
                    else
                    {
                        MessageOut(QObject::TR("请手动输入误差值!"));
                    }
                }
                else
                {
                    MessageOut("");
                    HNC_RegGetValue(REG_TYPE_G, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_CTRL2, &regGValue);
                    bitValue = (regGValue >> 14) & 0x01;
                    if (bitValue == 1)
                    {
                        HNC_RegGetValue(REG_TYPE_F, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_STAT2, &regFValue);
                        bitValue = (regFValue >> 7) & 0x01;
                        if (bitValue == 1)
                        {
                            ;
                        }
                        else
                        {
                            if (var != 0)
                            {
                                HmiOscServo::CompRecordSysVar(var - 1);
                            }
                            HNC_RegSetBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                        }
                    }
                    else
                    {
                        HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                    }
                }
            }

            if (HmiOscServo::s_Conf[ch].stZCompConf.autoMeasure == 1)  // 按了操作键自动测量模式
            {
                HNC_MacroVarGetValue(MACRO_NO, &macrovar);
                var = macrovar.v.i;
                HNC_RegGetValue(REG_TYPE_G, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_CTRL2, &regGValue);
                bitValue = (regGValue >> 14) & 0x01;
                if (bitValue == 1)
                {
                    HNC_RegGetValue(REG_TYPE_F, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_STAT2, &regFValue);
                    bitValue = (regFValue >> 7) & 0x01;
                    if (bitValue == 1)
                    {
                        ;
                    }
                    else
                    {
                        if (var != 0)
                        {
                            HmiOscServo::CompRecordSysVar(-2);

                            pOscZComp->ClearPoint(0);
                            pOscZComp->SetYRange(-120, 120, 40);
                            QVector<double> x;
                            QVector<double> y0;

                            x.append(0);
                            y0.append(0);
                            for(i = 0; i < var; i++)
                            {
                                x.append(HmiOscServo::s_Conf[ch].stZCompConf.step * (i + 1));
                                y0.append(HmiOscServo::CompGetOffsetValue(i));
                            }
                            pOscZComp->LineZeroAddPoint(x, y0);
                            pOscZComp->WaveReplot();
                            this->LoadView();
                        }
                        else
                        {
                            HNC_MacroVarGetValue(MACRO_VALUE_NO, &tmpMacUnion);
                            HmiOscServo::CompSetCompOffsetBase(tmpMacUnion.v.f * 1000);
                        }
                        HNC_RegSetBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                    }
                }
                else
                {
                    HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                }
            }
        }
    }
}

void OscServoZHeatComp::DrawPlotManual(Bit32 num)
{
    pOscZComp->ClearPoint(0);
    pOscZComp->SetYRange(-120, 120, 40);
    QVector<double> x;
    QVector<double> y0;
    Bit32 ch = ActiveChan();

    x.append(0);
    y0.append(0);
    for(Bit32 i = 0; i < num; i++)
    {
        x.append(HmiOscServo::s_Conf[ch].stZCompConf.step * (i + 1));
        y0.append(HmiOscServo::CompGetOffsetValue(i));
    }
    pOscZComp->LineZeroAddPoint(x, y0);
    pOscZComp->WaveReplot();
}

void OscServoZHeatComp::RedrawHistotyWave()
{
    pOscZComp->ClearPoint(1);
    QVector<double> x;
    QVector<double> y0;
    Bit32 ch = ActiveChan();

    x.append(0);
    y0.append(0);
    for(Bit32 i = 0; i < HmiOscServo::s_Conf[ch].stZCompConf.preHeat; i++)
    {
        x.append(HmiOscServo::s_Conf[ch].stZCompConf.step * (i + 1));
        y0.append(HmiOscServo::CompGetHistoryOffsetValue(i));
    }
    pOscZComp->LineOneAddPoint(x, y0);
    pOscZComp->WaveReplot();
}

void OscServoZHeatComp::OpenHistoryDlg()
{
    this->pDlgFileSle->SetInit();
    if(this->pDlgFileSle->exec() == QDialog::Accepted)
    {
        Bit32 ret = HmiOscServo::CompParseHistoryFile();
        if (ret != 0)
        {
            MessageOut(QObject::TR("读取历史误差数据失败"));
        }
        else
        {
            RedrawHistotyWave();
        }
    }
}

void OscServoZHeatComp::SlotFocusReset()
{
    bool ret = zCompOscList->SetTableFocus();
    if (ret)
    {
        this->ClearTableFoucs();
        focusIdx = 1;
    }
}

bool OscServoZHeatComp::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        switch(keyEvent->key())
        {
        case Qt::Key_Left:
            if (focusIdx == 1)
            {
                zCompOscList->ClearTableFocus();
                this->SetTableFoucs();
                focusIdx = 0;
                MessageOut("");
                event->accept();
                return true;
            }
            break;
        case Qt::Key_Right:
            if (focusIdx == 0)
            {
                bool ret = zCompOscList->SetTableFocus();
                if (ret == false)
                {
                    return true;
                }
                this->ClearTableFoucs();
                focusIdx = 1;
                event->accept();
                return true;
            }
            break;
        case Qt::Key_Enter:
        case Qt::Key_Return:
            if (focusIdx == 0)
            {
                QString editStr = "";
                Bit32 row = ui->tableView->currentIndex().row();
                editStr = QString::number(HmiOscServo::CompGetOffsetValue(row));
                Bit32 ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[偏移量]:"), 6);
                if(ret == 0)
                {
                    bool ok = false;
                    fBit64 longTmp = editStr.toDouble(&ok);
                    if(ok == false)
                    {
                        break;
                    }
                    HmiOscServo::CompSetOffsetValue(row, longTmp);
                    //this->LoadView();
                    this->DrawPlotManual(row + 1);
                }
                event->accept();
                return true;
            }
            break;
        default:
            if(focusIdx == 0)
            {
                QString editStr = keyEvent->text();
                if(editStr.isEmpty() || editStr.length() > 1)
                {
                    return false;
                }
                int pos = 0;
                if(doubleValidator->validate(editStr, pos) != QDoubleValidator::Invalid)  // 按键成功匹配doubleValidator验证器
                {
                    Bit32 row = ui->tableView->currentIndex().row();
                    Bit32 ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[偏移量]:"), 6, -1, 0, doubleValidator, false);
                    if(ret == 0)
                    {
                        bool ok = false;
                        fBit64 longTmp = editStr.toDouble(&ok);
                        if(ok == false)
                        {
                            break;
                        }
                        HmiOscServo::CompSetOffsetValue(row, longTmp);
                        this->DrawPlotManual(row + 1);
                    }
                    event->accept();
                    return true;
                }
            }
            break;
        }
    }
    else if(event->type() == QEvent::FocusIn)
    {
        if(target == ui->tableView)
        {
            zCompOscList->ClearTableFocus();
            this->SetTableFoucs();
            focusIdx = 0;
        }
    }

    return QObject::eventFilter(target, event);
}

void OscServoZHeatComp::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::SETFOCUS)
    {
        if(this->focusIdx == 1)
        {
            bool ret = zCompOscList->SetTableFocus();
            if (ret == false)
            {
                zCompOscList->ClearTableFocus();
                this->SetTableFoucs();
                focusIdx = 0;
            }
            else
            {
                this->ClearTableFoucs();
                focusIdx = 1;
            }
        }
        else
        {
            zCompOscList->ClearTableFocus();
            this->SetTableFoucs();
            focusIdx = 0;
        }
        if(messageValue == "CLEARFOCUS")
        {
            zCompOscList->ClearTableFocus();
            this->ClearTableFoucs();
        }
    }
    else if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        QStringList strList;
        strList.clear();
        if(messageValue == "INIT") // 初始化，清除上次在该界面记住的当前行
        {
            strList = GetParmList();
            zCompOscList->RefresWidget(strList);
        }
        pOscZComp->ClearPoint();
        zCompOscList->LoadWidget();

        this->LoadWave();
        this->SetColorStyle();
        //focusIdx = 0;
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if (messageid == MsgData::REFRESH)
    {
        this->Refresh();
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_OSCSERVOSTART")
        {
            //this->Reset(); // 开始采样时才清除上一次的图形
        }
        else if (messageValue == "MSG_OSCSERVOSTOP")
        {
            this->LoadInfo();
        }
        else if (messageValue == "MSG_OSCSERVOAPPLY")
        {
            if (zCompOscList->RightCheck(MAC_RIGHTS) < 0)
            {
                return;
            }
            Bit32 ret = HmiOscServo::CompApplyData();
            if (ret == 0)
            {
                zCompOscList->LoadWidget();
            }
        }
        else if(messageValue == "MSG_OSCSERVOSAVE")
        {
            HmiOscServo::ParmSave();
            zCompOscList->LoadWidget();
        }
        else if (messageValue =="OPENHISTORY")
        {
            this->OpenHistoryDlg();
        }
        else if(messageValue =="OSCSERVOCOLOR")
        {
            this->SetColorStyle();
        }
    }
}

void OscServoZHeatComp::SetColorStyle()
{
    // 默认黑色风格
    QColor bk(0,0,0); // 背景
    QColor gd(0,0,0); // 网格
    QColor ft(0,0,0); // 字体颜色
    QColor c1(0,0,0); // 曲线1
    QColor c2(0,0,0); // 曲线2
    QColor c3(0,0,0); // 曲线3
    QColor c4(0,0,0); // 曲线4

    HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

    QPalette palette;
    palette.setColor(QPalette::Background, bk);
    ui->frame->setAutoFillBackground(true);
    ui->frame->setPalette(palette);

    bmpLabel->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    pOscZComp->SetColor(bk, gd, ft, c1, c2, c3, c4);
}

void ZCompListModel::refresh()
{
    this->reset();
}

void OscServoZHeatComp::LoadView()
{
    ZCompListModel* model = static_cast<ZCompListModel*>(ui->tableView->model());
    model->refresh();
    if(ui->tableView->hasFocus()) // 解决refresh后选中状态丢失
    {
        ui->tableView->selectRow(0);
    }
}

int ZCompListModel::rowCount(const QModelIndex &) const
{
    Bit32 ch = ActiveChan();
    Bit32 num = 0;
    num = HmiOscServo::s_Conf[ch].stZCompConf.preHeat;
    return num;
}

int ZCompListModel::columnCount(const QModelIndex &) const
{
    return 2;
}

Qt::ItemFlags ZCompListModel::flags(const QModelIndex &index) const
{
    Qt::ItemFlags flags = QAbstractItemModel::flags(index);

    flags |= Qt::ItemIsSelectable;

    return flags;
}

QVariant ZCompListModel::data(const QModelIndex &index, int role) const
{
    Bit32 period = 0;
    fBit64 offset = 0;
    Bit32 ch = ActiveChan();
    if (!index.isValid())
    {
        return QVariant();
    }

    if (role == Qt::DisplayRole && index.column() == 0)
    {
        period = (index.row() + 1) * HmiOscServo::s_Conf[ch].stZCompConf.step;
        return QString::number(period);
    }
    if (role == Qt::DisplayRole && index.column() == 1)
    {

        offset = HmiOscServo::CompGetOffsetValue(index.row());
        return QString::number(offset);
    }
    else if(role == Qt::TextAlignmentRole && index.column() == 1)
    {
        return int(Qt::AlignRight | Qt::AlignVCenter);
    }

    return QVariant();
}
