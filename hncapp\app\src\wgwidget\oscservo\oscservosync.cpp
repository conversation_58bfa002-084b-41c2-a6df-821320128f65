﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservosync.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-龙门同步界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年5月24日
* 说    明：
*/

#include "hncaxis.h"
#include "hncsys.h"
#include "hncsmpl.h"
#include "smplcalc.h"
#include "hncparaman.h"
#include "hncparamandef.h"

#include "common.h"
#include "hmioscproc.h"
#include "hmioscservo.h"
#include "oscwave.h"

#include "oscservosync.h"
#include "ui_oscservosync.h"

OscServoSync::OscServoSync(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoSync)
{
    ui->setupUi(this);

    pOscWaveCurrentFlow = new OscWave(this, HmiOscServo::OSC_SERVO_SYNC, "CurrentFlow");
    pOscWaveErr = new OscWave(this, HmiOscServo::OSC_SERVO_SYNC, "ERR");

    ui->verticalLayout_2->addWidget(pOscWaveCurrentFlow);
    ui->verticalLayout_2->addWidget(pOscWaveErr);
    ui->verticalLayout_2->setStretch(0, 1);
    ui->verticalLayout_2->setStretch(1, 1);
    ui->verticalLayout_2->setContentsMargins(0, 0, 0, 0);
    ui->verticalLayout_2->setSpacing(5);

    pOscWaveCurrentFlow->CreateGroup(pOscWaveErr->GetWavePlot());

    // 信息区设置
    ui->labelMaxPosErr->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    ui->labelMaxCurrentErr->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
}

OscServoSync::~OscServoSync()
{
    delete ui;
}

void OscServoSync::LoadInfo()
{
    Bit32 ch = ActiveChan();
    fBit64 maxPosErrVal = 0.000;
    fBit64 maxCurrentErrVal = 0.000;
    Bit32 posTmp = 0;
    Bit32 curTmp = 0;
    Bit32 movingUnit = 0;
    fBit64 ftmp = 0.0;
    Bit32 client = HmiOscServo::oscservo_get_sampl_client();
    Bit32 num = 0;
    QStringList axisNameList;
    axisNameList.clear();

    axisNameList << HmiOscServo::OscAxisToName(HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1);
    axisNameList << HmiOscServo::OscAxisToName(HmiOscServo::s_Conf[ch].stSyncConf.sync_axis2);
    pOscWaveCurrentFlow->SetAxisName(axisNameList);

    if(oscproc_get_total() > 0)
    {
        for (Bit32 i = 0; i < CHAN_AXES_NUM; ++i)
        {
            if (HmiOscServo::GetIndexAxesConf(i) > -1)
            {
                num++;
            }
        }
        if (num > MAX_REPORT_AXIS)
        {
            num = MAX_REPORT_AXIS;
        }

        HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &movingUnit);
        HmiOscServo::SmplCalcSyncMaxmin(&posTmp, &curTmp, 0, oscproc_get_total());
        ftmp = 1.0 / movingUnit;
        maxPosErrVal = posTmp * ftmp; // 最大位置偏差[mm]
        ftmp = HmiOscServo::SmplCalcLoadCoef(1, client) / 1000.0;
        maxCurrentErrVal = curTmp * ftmp; // 最大电流偏差[A]

        HmiOscServo::OscservoReportRecord(maxPosErrVal, maxCurrentErrVal);
    }
    ui->labelMaxPosErr->setText(QString::number(maxPosErrVal, 'f',3));
    ui->labelMaxCurrentErr->setText(QString::number(maxCurrentErrVal, 'f',3));
}

void OscServoSync::LoadWave()
{
    fBit64 rangLower = 0.0;
    fBit64 rangUpper = 0.0;
    fBit64 rangStep = 0.0;
    QString axisName = "";
    Bit32 ch = ActiveChan();

    rangLower = HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos;
    if ( abs(HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) < 10)
    {
		if (HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos >= HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos)
		{
			rangUpper = rangLower + 10;
		}
		else
		{
			rangUpper = rangLower - 10;
		}
    }
    else
    {
        if ( (HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) % 10 != 0)
        {
            if (HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos < 0)
            {
                rangUpper = ((HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) / 10 - 1) * 10 + HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos;
            }
            else
            {
                rangUpper = ((HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) / 10 + 1) * 10 + HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos;
            }
        }
        else
        {
            rangUpper = HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos;
        }
    }
    rangStep = Bit32((rangUpper - rangLower) / 10);
    pOscWaveCurrentFlow->SetXRange(rangLower, rangUpper, rangStep);
    pOscWaveErr->SetXRange(rangLower, rangUpper, rangStep);
    axisName = HmiOscServo::OscAxisToName(HmiOscServo::GetCurAxesConf());
    pOscWaveCurrentFlow->SetAxisName(QStringList() << axisName);
    pOscWaveErr->SetAxisName(QStringList() << axisName);
}

void OscServoSync::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        this->LoadWave();
        this->LoadInfo();
        this->SetColorStyle();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_OSCSERVOSTART")
        {
            this->Reset(); // 开始采样时才清除上一次的图形
        }
        else if(messageValue == "MSG_OSCSERVOSAVE")
        {
            MessageOut(TR("龙门同步无保存功能"));
        }
        else if (messageValue == "MSG_OSCSERVOSTOP")
        {
            this->LoadInfo();
        }
        else if(messageValue == "OSCSERVOCOLOR")
        {
            this->SetColorStyle();
        }
    }
    else if (messageid == MsgData::REFRESH)
    {
        this->Refresh();
    }
}

void OscServoSync::Reset()
{ // 清空图形
    pOscWaveCurrentFlow->ClearPoint();
    pOscWaveErr->ClearPoint();
    this->lastEndPos = 0;
}

void OscServoSync::Refresh()
{
    QVector<double> x;
    QVector<double> y0;
    QVector<double> y1;
    QVector<double> y2;
    QVector<double> y3;

    Bit32 i = 0;
    Bit32 stPos = 0;
    Bit32 edPos= 0;
    Bit64 *ch0_addr = NULL;
    Bit64 *ch1_addr = NULL;
    Bit64 *ch2_addr = NULL;
    Bit64 *ch3_addr = NULL;
    Bit64 *ch4_addr = NULL;

    Bit32 client = HmiOscServo::oscservo_get_sampl_client();
    Bit32 type = 0;
    Bit32 axisNo = 0;
    Bit32 offset = 0;
    Bit32 len = 0;
    Bit32 tmp = 0;

    fBit64 cof0 = 1;
    fBit64 off0 = 0;
    fBit64 cof1 = 1;
    fBit64 off1 = 0;

    if (oscproc_get_stat() != OSC_PROC_START)
    {
        this->lastEndPos = 0; // 停止后需要置零
        return;
    }

    stPos = this->lastEndPos;
    edPos = oscproc_get_pos();
    this->lastEndPos = edPos;

    ch0_addr = oscproc_get_smpldata(0);
    ch1_addr = oscproc_get_smpldata(1);
    ch2_addr = oscproc_get_smpldata(2);
    ch3_addr = oscproc_get_smpldata(3);
    ch4_addr = oscproc_get_smpldata(4);
    if (NULL == ch0_addr || NULL == ch1_addr
            || NULL == ch2_addr || NULL == ch3_addr || NULL == ch4_addr)
    {
        return;
    }

    HNC_SamplGetConfig(client, 0, type, axisNo, offset, len);
    for (i = stPos+1; i < edPos; ++i)
    {
        cof0 = smpl_calc_dist_coef(axisNo) * 1000.0;
        HNC_AxisGetValue(HNC_AXIS_WCS_ZERO, axisNo, &tmp);
//        off0 = -cof0 * tmp;
        off0 = 0;
        x.append(cof0 * ch0_addr[i] + off0);

        cof0 = HmiOscServo::SmplCalcLoadCoef(1, client) / 1000;
        off0 = 0;
        y0.append(cof0 * ch1_addr[i] + off0);

        cof0 = HmiOscServo::SmplCalcLoadCoef(2, client) / 1000;
        off0 = 0;
        y1.append(cof0 * ch2_addr[i] + off0);

        cof0 = smpl_calc_dist_coef(axisNo) * 1000.0;
        off0 = 0;
        cof1 = smpl_calc_dist_coef(axisNo) * 1000.0;
        off1 = 0;
        y2.append(cof0 * ch3_addr[i] + off0 - (cof1 * ch4_addr[i] + off1));

        cof0 = HmiOscServo::SmplCalcLoadCoef(1, client) / 1000;
        off0 = 0;
        cof1 = HmiOscServo::SmplCalcLoadCoef(2, client) / 1000;
        off1 = 0;
        y3.append(cof0 * ch1_addr[i] + off0 - (cof1 * ch2_addr[i] + off1));
    }

    pOscWaveCurrentFlow->LineZeroAddPoint(x, y0);
    pOscWaveCurrentFlow->LineOneAddPoint(x, y1);
    pOscWaveCurrentFlow->WaveReplot();
    pOscWaveErr->LineZeroAddPoint(x, y2);
    pOscWaveErr->LineOneAddPoint(x, y3);
    pOscWaveErr->WaveReplot();
}

void OscServoSync::SetColorStyle()
{
    // 默认黑色风格
    QColor bk(0,0,0); // 背景
    QColor gd(0,0,0); // 网格
    QColor ft(0,0,0); // 字体颜色
    QColor c1(0,0,0); // 曲线1
    QColor c2(0,0,0); // 曲线2
    QColor c3(0,0,0); // 曲线3
    QColor c4(0,0,0); // 曲线4

    HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

    QPalette palette;
    palette.setColor(QPalette::Background, bk);
    ui->frame->setAutoFillBackground(true);
    ui->frame->setPalette(palette);

    pOscWaveCurrentFlow->SetColor(bk, gd, ft, c1, c2, c3, c4);
    pOscWaveErr->SetColor(bk, gd, ft, c1, c2, c3, c4);
}
