﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservospindle.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-主轴热误差界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年11月6日
* 说    明：
*/

#include <qmath.h>
#include <QKeyEvent>

#include "hncchan.h"
#include "hncmath.h"
#include "hncsys.h"
#include "hncvar.h"
#include "hncreg.h"
#include "hncregdef.h"
#include "passwd.h"

#include "hmioscproc.h"
#include "hmioscservo.h"
#include "oscwave.h"
#include "osclist.h"

#include "oscservosheatcomp.h"
#include "ui_oscservosheatcomp.h"
#include    "datadef.h"

OscServoSHeatComp::OscServoSHeatComp(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoSHeatComp)
{
    ui->setupUi(this);

    m_nLastRowCount = 0;
    pOscSComp = new OscWave(this, HmiOscServo::OSC_SERVO_SCOMP, "");
    bmpLabel = new QLabel(ui->frame);
    bmpLabel->setFont(QFont(FONT_TYPE, 12));
    bmpLabel->setText(TR("  适用范围\n 1、钻攻类型机床\n 2、丝杠固定方式：一端固定，一端游走\n\n  快捷键\n ALT+O打开历史误差数据"));
//    bmpLabel->setPixmap(PixMapToSize(bmpLabel->size(), TransPicName("osc/blackTip.png")));
    ui->verticalLayout_2->addWidget(pOscSComp, 0);
    ui->verticalLayout_2->addWidget(bmpLabel, 1);
    ui->verticalLayout_2->setStretch(0, 1);
    ui->verticalLayout_2->setStretch(1, 1);
    ui->verticalLayout_2->setContentsMargins(0, 0, 0, 0);
    ui->verticalLayout_2->setSpacing(5);

    // 列表设置
    sCompOscList = new OscList(this);
    sCompOscList->installEventFilter(this);
    ui->gridLayout_2->addWidget(sCompOscList);
    sCompOscList->SetEditAgent(true);

    // 列表设置
    ui->tableView->setItemDelegate(new SCompListDelegate);
    ui->tableView->setModel(new SCompListModel);
    ui->tableView->setFont(QFont(FONT_TYPE, 12));
    ui->tableView->horizontalHeader()->setVisible(false);
    ui->tableView->verticalHeader()->setVisible(false);
    ui->tableView->horizontalHeader()->setStretchLastSection(true);
    ui->tableView->setColumnWidth(0, 80 * HOR_RATIO);
    ui->tableView->setAlternatingRowColors(true);
    ui->tableView->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableView->installEventFilter(this);
    focusIdx = -1;

    this->pDlgFileSle = new DlgOscservoSelect(this); // 选择文件对话框
    connect(sCompOscList, SIGNAL(SignalOscTableGetFocus()), this, SLOT(OnTableFocuseReset()));

    doubleValidator = new QDoubleValidator();
    doubleValidator->setBottom(-99999.9999);
    doubleValidator->setTop(999999.9999);
    doubleValidator->setNotation(QDoubleValidator::StandardNotation);
}

OscServoSHeatComp::~OscServoSHeatComp()
{
    delete ui;
}

QStringList OscServoSHeatComp::GetParmList()
{
    QStringList list;
    list.clear();

    Bit32 totalNo = 0;
    totalNo = HmiOscServo::ServoParmGetCount(HmiOscServo::OSC_SERVO_SCOMP);

    for(Bit32 i = 0; i < totalNo; i++)
    {
        list.append(QString::number(HmiOscServo::ServoParRow2Id(i)));
    }
    return list;
}

void OscServoSHeatComp::SetTableFoucs()
{
    Bit32 ch = ActiveChan();
    Bit32 selRow = ui->tableView->currentIndex().row();
    if(selRow >= 0)
    {
        ui->tableView->selectRow(selRow);
    }
    else if(HmiOscServo::s_Conf[ch].stZCompConf.preHeat > 0)
    {
        ui->tableView->selectRow(0);
    }
    ui->tableView->setFocus();
}

void OscServoSHeatComp::ClearTableFoucs()
{
    ui->tableView->clearSelection();
    ui->tableView->clearFocus();
}

void OscServoSHeatComp::Reset()
{ // 清空图形
    pOscSComp->ClearPoint();
}

void OscServoSHeatComp::LoadWave()
{
    fBit64 rangLower = 0.0;
    fBit64 rangUpper = 0.0;
    fBit64 rangStep = 0.0;
    Bit32 ch = ActiveChan();

    rangLower = 0;
    rangUpper = HmiOscServo::s_Conf[ch].stSCompConf.preHeat * HmiOscServo::s_Conf[ch].stSCompConf.step;
    rangStep = (rangUpper - rangLower) / 10;
    pOscSComp->SetXRange(rangLower, rangUpper, rangStep);
    pOscSComp->SetYRange(-120, 120, 40);
}

void OscServoSHeatComp::LoadInfo()
{
    fBit64 maxOffset = 0;
    Bit32 ch = ActiveChan();
    Bit32 num = HmiOscServo::s_Conf[ch].stSCompConf.preHeat;
    fBit64 val = 0;

    for ( Bit32 i = 0; i < num; i++)
    {
        if (fabs(maxOffset) < fabs(HmiOscServo::CompGetOffsetValue(i)))
        {
            maxOffset = fabs(HmiOscServo::CompGetOffsetValue(i));
        }
    }
    val = fabs(maxOffset) / 1000.0;   // um转mm
    ui->labelMaxErr->setText(QString::number(val, 'f',3));
}

void OscServoSHeatComp::Refresh()
{
    SCompListModel* model = static_cast<SCompListModel*>(ui->tableView->model());
    if (model != NULL)
    {
        int rowCount = model->rowCount(QModelIndex());
        if (rowCount != m_nLastRowCount)            // 配置点数有变动时，刷新列表
        {
            LoadView();
            m_nLastRowCount = rowCount;
        }
    }

    if (HmiOscServo::CompGetRefreshEnable() == 1)
    {
        Bit32 runRow = 0;
        Bit32 ch = ActiveChan();
        const Bit32 MEASURE_ROW = 15;    // 用于在M00处提示用户是循环启动还是手动输入误差值
        const Bit32 MACRO_NO = 50;
        const Bit32 MACRO_VALUE_NO = 60;
        SDataUnion macrovar;
        SDataUnion tmpMacUnion;
        Bit32 var = 0;
        Bit32 i = 0;
        Bit16 regGValue = 0;
        Bit16 regFValue = 0;
        Bit32 bitValue = 0;
        Bit32 chRegBase = 0;

        HNC_RegGetFGBase(REG_FG_CHAN_BASE, &chRegBase);

        if (HmiOscServo::CompGetTestIndex() == 0)   // 没按操作键就进行采集
        {
            if (HmiOscServo::s_Conf[ch].stSCompConf.autoMeasure == 0)  // 没按操作键手动测量模式
            {
                HNC_ChannelGetValue(HNC_CHAN_RUN_ROW, ch, 0, &runRow);
                HNC_MacroVarGetValue(MACRO_NO, &macrovar);
                var = macrovar.v.i;
                if (runRow == MEASURE_ROW)
                {
                    if (var == 0)
                    {
                        MessageOut(QObject::TR("请按循环启动键继续!"));
                    }
                    else
                    {
                        MessageOut(QObject::TR("请手动输入误差值!"));
                    }
                }
                else
                {
                    MessageOut("");
                    HNC_RegGetValue(REG_TYPE_G, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_CTRL2, &regGValue);// REG_CH_CTRL2        76 //通道控制字2
                    bitValue = (regGValue >> 14) & 0x01; // CH_CTRL_HEAT_COMP 0X4000
                    if (bitValue == 1)
                    {// REG_CH_STAT2        76 // 通道状态字2
                        HNC_RegGetValue(REG_TYPE_F, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_STAT2, &regFValue);
                        bitValue = (regFValue >> 7) & 0x01;// CH_STATE_HEAT_COMP 0X0080
                        if (bitValue == 1)
                        {
                            ;
                        }
                        else
                        {
                            HNC_RegSetBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);// CH_STATE_HEAT_COMP 0X0080
                        }
                    }
                    else
                    {
                        HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);// CH_STATE_HEAT_COMP 0X0080
                    }
                }
            }

            if (HmiOscServo::s_Conf[ch].stSCompConf.autoMeasure == 1)  // 没按操作键自动测量模式
            {
                HNC_MacroVarGetValue(MACRO_NO, &macrovar);
                var = macrovar.v.i;
                HNC_RegGetValue(REG_TYPE_G, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_CTRL2, &regGValue);
                bitValue = (regGValue >> 14) & 0x01;// CH_CTRL_HEAT_COMP 0X4000
                if (bitValue == 1)
                {
                    HNC_RegGetValue(REG_TYPE_F, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_STAT2, &regFValue);
                    bitValue = (regFValue >> 7) & 0x01;// CH_STATE_HEAT_COMP 0X0080
                    if (bitValue == 1)
                    {
                        ;
                    }
                    else
                    {
                        if (var != 0)
                        {
                            HmiOscServo::CompRecordSysVar(-2);

                            pOscSComp->ClearPoint(0);
                            pOscSComp->SetYRange(-120, 120, 40);
                            QVector<double> x;
                            QVector<double> y0;

                            x.append(0);
                            y0.append(0);
                            for(i = 0; i < var; i++)
                            {
                                x.append(HmiOscServo::s_Conf[ch].stSCompConf.step * (i + 1));
                                y0.append(HmiOscServo::CompGetOffsetValue(i));
                            }
                            pOscSComp->LineZeroAddPoint(x, y0);
                            pOscSComp->WaveReplot();
                            this->LoadView();
                        }
                        else
                        {
                            HNC_MacroVarGetValue(MACRO_VALUE_NO, &tmpMacUnion);
                            HmiOscServo::CompSetCompOffsetBase(tmpMacUnion.v.f * 1000);
                        }
                        HNC_RegSetBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);// CH_STATE_HEAT_COMP 0X0080
                    }
                }
                else
                {
                    HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);// CH_STATE_HEAT_COMP 0X0080
                }
            }
        }
        else    // 按了操作键就进行采集
        {
            if (HmiOscServo::s_Conf[ch].stSCompConf.autoMeasure == 0)  // 按了操作键手动测量模式
            {
                HNC_ChannelGetValue(HNC_CHAN_RUN_ROW, ch, 0, &runRow);
                HNC_MacroVarGetValue(MACRO_NO, &macrovar);
                var = macrovar.v.i;
                if (runRow == MEASURE_ROW)
                {
                    if (var == 0)
                    {
                        MessageOut(QObject::TR("请按循环启动键继续!"));
                    }
                    else
                    {
                        MessageOut(QObject::TR("请手动输入误差值!"));
                    }
                }
                else
                {
                    MessageOut("");
                    HNC_RegGetValue(REG_TYPE_G, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_CTRL2, &regGValue);
                    bitValue = (regGValue >> 14) & 0x01;
                    if (bitValue == 1)
                    {
                        HNC_RegGetValue(REG_TYPE_F, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_STAT2, &regFValue);
                        bitValue = (regFValue >> 7) & 0x01;
                        if (bitValue == 1)
                        {
                            ;
                        }
                        else
                        {
                            if (var != 0)
                            {
                                HmiOscServo::CompRecordSysVar(var - 1);
                            }
                            HNC_RegSetBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                        }
                    }
                    else
                    {
                        HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                    }
                }
            }

            if (HmiOscServo::s_Conf[ch].stSCompConf.autoMeasure == 1)  // 按了操作键自动测量模式
            {
                HNC_MacroVarGetValue(MACRO_NO, &macrovar);
                var = macrovar.v.i;
                HNC_RegGetValue(REG_TYPE_G, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_CTRL2, &regGValue);
                bitValue = (regGValue >> 14) & 0x01;
                if (bitValue == 1)
                {
                    HNC_RegGetValue(REG_TYPE_F, chRegBase * 2 + ch * CHAN_REG_NUM + REG_CH_STAT2, &regFValue);
                    bitValue = (regFValue >> 7) & 0x01;
                    if (bitValue == 1)
                    {
                        ;
                    }
                    else
                    {
                        if (var != 0)
                        {
                            HmiOscServo::CompRecordSysVar(-2);

                            pOscSComp->ClearPoint(0);
                            pOscSComp->SetYRange(-120, 120, 40);
                            QVector<double> x;
                            QVector<double> y0;

                            x.append(0);
                            y0.append(0);
                            for(i = 0; i < var; i++)
                            {
                                x.append(HmiOscServo::s_Conf[ch].stSCompConf.step * (i + 1));
                                y0.append(HmiOscServo::CompGetOffsetValue(i));
                            }
                            pOscSComp->LineZeroAddPoint(x, y0);
                            pOscSComp->WaveReplot();
                            this->LoadView();
                        }
                        else
                        {
                            HNC_MacroVarGetValue(MACRO_VALUE_NO, &tmpMacUnion);
                            HmiOscServo::CompSetCompOffsetBase(tmpMacUnion.v.f * 1000);
                        }
                        HNC_RegSetBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                    }
                }
                else
                {
                    HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + ch * 80 + 76, 7);
                }
            }
        }
    }
}

void OscServoSHeatComp::DrawPlotManual(Bit32 num)
{
    pOscSComp->ClearPoint(0);
    pOscSComp->SetYRange(-120, 120, 40);
    QVector<double> x;
    QVector<double> y0;
    Bit32 ch = ActiveChan();

    x.append(0);
    y0.append(0);
    for(Bit32 i = 0; i < num; i++)
    {
        x.append(HmiOscServo::s_Conf[ch].stSCompConf.step * (i + 1));
        y0.append(HmiOscServo::CompGetOffsetValue(i));
    }
    pOscSComp->LineZeroAddPoint(x, y0);
    pOscSComp->WaveReplot();
}

void OscServoSHeatComp::RedrawHistotyWave()
{
    pOscSComp->ClearPoint(1);
    QVector<double> x;
    QVector<double> y0;
    Bit32 ch = ActiveChan();

    x.append(0);
    y0.append(0);
    for(Bit32 i = 0; i < HmiOscServo::s_Conf[ch].stSCompConf.preHeat; i++)
    {
        x.append(HmiOscServo::s_Conf[ch].stSCompConf.step * (i + 1));
        y0.append(HmiOscServo::CompGetHistoryOffsetValue(i));
    }
    pOscSComp->LineOneAddPoint(x, y0);
    pOscSComp->WaveReplot();
}

void OscServoSHeatComp::OpenHistoryDlg()
{
    this->pDlgFileSle->SetInit();
    if(this->pDlgFileSle->exec() == QDialog::Accepted)
    {
        Bit32 ret = HmiOscServo::CompParseHistoryFile();
        if (ret != 0)
        {
            MessageOut(QObject::TR("读取历史误差数据失败"));
        }
        else
        {
            RedrawHistotyWave();
        }
    }
}

void OscServoSHeatComp::OnTableFocuseReset()
{
    bool ret = sCompOscList->SetTableFocus();
    if (ret == true)
    {
        this->ClearTableFoucs();
        focusIdx = 1;
    }
}

bool OscServoSHeatComp::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        switch(keyEvent->key())
        {
        case Qt::Key_Left:
            if (focusIdx == 1)
            {
                sCompOscList->ClearTableFocus();
                this->SetTableFoucs();
                focusIdx = 0;
                MessageOut("");
                event->accept();
                return true;
            }
            break;
        case Qt::Key_Right:
            if (focusIdx == 0)
            {
                bool ret = sCompOscList->SetTableFocus();
                if (ret == false)
                {
                    return true;
                }
                this->ClearTableFoucs();
                focusIdx = 1;
                event->accept();
                return true;
            }
            break;
        case Qt::Key_Enter:
        case Qt::Key_Return:
            if (focusIdx == 0)
            {
                QString editStr = "";
                Bit32 row = ui->tableView->currentIndex().row();
                editStr = QString::number(HmiOscServo::CompGetOffsetValue(row));
                Bit32 ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[偏移量]:"), 6);
                if(ret == 0)
                {
                    bool ok = false;
                    fBit64 longTmp = editStr.toDouble(&ok);
                    if(ok == false)
                    {
                        break;
                    }
                    HmiOscServo::CompSetOffsetValue(row, longTmp);
                    this->DrawPlotManual(row + 1);
                }
                event->accept();
                return true;
            }
            break;
        default:
            if(focusIdx == 0)
            {
                QString editStr = keyEvent->text();
                if(editStr.isEmpty() || editStr.length() > 1)
                {
                    return false;
                }
                int pos = 0;
                if(doubleValidator->validate(editStr, pos) != QDoubleValidator::Invalid)  // 按键成功匹配doubleValidator验证器
                {
                    Bit32 row = ui->tableView->currentIndex().row();
                    Bit32 ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[偏移量]:"), 6, -1, 0, doubleValidator, false);
                    if(ret == 0)
                    {
                        bool ok = false;
                        fBit64 longTmp = editStr.toDouble(&ok);
                        if(ok == false)
                        {
                            break;
                        }
                        HmiOscServo::CompSetOffsetValue(row, longTmp);
                        this->DrawPlotManual(row + 1);
                    }
                    event->accept();
                    return true;
                }
            }
            break;
        }
    }
    else if(event->type() == QEvent::FocusIn)
    {
        if(target == ui->tableView)
        {
            sCompOscList->ClearTableFocus();
            this->SetTableFoucs();
            focusIdx = 0;
        }
    }
#ifdef _NCUPEER_
    else if (event->type() == QEvent::Resize)
    {
        // 设置列表宽度,适应多分辨率
        Bit32 width = ui->tableView->width();
        Bit32 columnWidth = (width - ui->tableView->verticalScrollBar()->width())/ 2;
        ui->tableView->setColumnWidth(0, columnWidth);
        ui->tableView->setColumnWidth(1, columnWidth);
    }
#endif
    return QObject::eventFilter(target, event);
}

void OscServoSHeatComp::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::SETFOCUS)
    {
        if(this->focusIdx == 1)
        {
            bool ret = sCompOscList->SetTableFocus();
            if (ret == false)
            {
                sCompOscList->ClearTableFocus();
                this->SetTableFoucs();
                focusIdx = 0;
            }
            else
            {
                this->ClearTableFoucs();
                focusIdx = 1;
            }
        }
        else
        {
            sCompOscList->ClearTableFocus();
            this->SetTableFoucs();
            focusIdx = 0;
        }
        if(messageValue == "CLEARFOCUS")
        {
            sCompOscList->ClearTableFocus();
            this->ClearTableFoucs();
        }
    }
    else if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        QStringList strList;
        strList.clear();
        if(messageValue == "INIT") // 初始化，清除上次在该界面记住的当前行
        {
            strList = GetParmList();
            sCompOscList->RefresWidget(strList);
        }
        pOscSComp->ClearPoint();
        sCompOscList->LoadWidget();

        this->LoadWave();
        this->SetColorStyle();
        //focusIdx = 0;
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if (messageid == MsgData::REFRESH)
    {
        this->Refresh();
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_OSCSERVOSTART")
        {
            //this->Reset(); // 开始采样时才清除上一次的图形
        }
        else if (messageValue == "MSG_OSCSERVOSTOP")
        {
            this->LoadInfo();
        }
        else if (messageValue == "MSG_OSCSERVOAPPLY")
        {
            if (sCompOscList->RightCheck(MAC_RIGHTS) < 0)
            {
                return;
            }
            Bit32 ret = HmiOscServo::CompApplyData();
            if (ret == 0)
            {
                sCompOscList->LoadWidget();
            }
        }
        else if(messageValue == "MSG_OSCSERVOSAVE")
        {
            HmiOscServo::ParmSave();
            sCompOscList->LoadWidget();
        }
        else if (messageValue =="OPENHISTORY")
        {
            this->OpenHistoryDlg();
        }
        else if(messageValue =="OSCSERVOCOLOR")
        {
            this->SetColorStyle();
        }
    }
}

void OscServoSHeatComp::SetColorStyle()
{
    // 默认黑色风格
    QColor bk(0,0,0); // 背景
    QColor gd(0,0,0); // 网格
    QColor ft(0,0,0); // 字体颜色
    QColor c1(0,0,0); // 曲线1
    QColor c2(0,0,0); // 曲线2
    QColor c3(0,0,0); // 曲线3
    QColor c4(0,0,0); // 曲线4

    HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

    QPalette palette;
    palette.setColor(QPalette::Background, bk);
    ui->frame->setAutoFillBackground(true);
    ui->frame->setPalette(palette);

    bmpLabel->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));

    pOscSComp->SetColor(bk, gd, ft, c1, c2, c3, c4);
}

void SCompListModel::refresh()
{
    this->reset();
}

void OscServoSHeatComp::LoadView()
{
    SCompListModel* model = static_cast<SCompListModel*>(ui->tableView->model());
    model->refresh();
    if(ui->tableView->hasFocus()) // 解决refresh后选中状态丢失
    {
        ui->tableView->selectRow(0);
    }
}

int SCompListModel::rowCount(const QModelIndex &) const
{
    Bit32 ch = ActiveChan();
    Bit32 num = 0;
    num = HmiOscServo::s_Conf[ch].stSCompConf.preHeat;
    return num;
}

int SCompListModel::columnCount(const QModelIndex &) const
{
    return 2;
}

Qt::ItemFlags SCompListModel::flags(const QModelIndex &index) const
{
    Qt::ItemFlags flags = QAbstractItemModel::flags(index);

    flags |= Qt::ItemIsSelectable;

    return flags;
}

QVariant SCompListModel::data(const QModelIndex &index, int role) const
{
    Bit32 period = 0;
    fBit64 offset = 0;
    Bit32 ch = ActiveChan();
    if (!index.isValid())
    {
        return QVariant();
    }

    if (role == Qt::DisplayRole && index.column() == 0)
    {
        period = (index.row() + 1) * HmiOscServo::s_Conf[ch].stSCompConf.step;
        return QString::number(period);
    }
    if (role == Qt::DisplayRole && index.column() == 1)
    {

        offset = HmiOscServo::CompGetOffsetValue(index.row());
        return QString::number(offset);
    }
    else if(role == Qt::TextAlignmentRole && index.column() == 1)
    {
        return int(Qt::AlignRight | Qt::AlignVCenter);
    }

    return QVariant();
}
