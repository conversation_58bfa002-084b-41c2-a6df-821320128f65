﻿#ifndef HMIMACRO_H
#define HMIMACRO_H

#include "usermacrodata.h"

#define LNS_NCMACCFG_NAME ("LNS_NCMACCFG.XML")
class MacroExInfoSubItem // 宏下拉框内容
{
public:
    MacroExInfoSubItem();
    MacroExInfoSubItem(QString name, QString val);
    void Clear();

    QString m_sName;
    QString m_sPicPath;
    QString m_sValue;
};

class MacroExInfo
{
public:
    MacroExInfo();

    Bit32 m_nflag;

    QString m_sComment;
    QString m_sPicPath;

    QList<MacroExInfoSubItem> m_subItem;
};

class HmiNcMacroExInfo
{
public:
    HmiNcMacroExInfo(MacroData *macroData, Bit32 pathType, QString name, QString midpath = "");

    Bit32 SetVal(Bit32 macroNo, QString val, bool flag = false, bool isTabVal = false); // flag为ture:下拉框类型val为下拉框idx
    Bit32 SaveData();

    QString GetMacroVal(Bit32 macroNo);

    Bit32 GetMacroNo(Bit32 idx);

    Bit32 GetMacroCount();

    Bit32 GetMacroData(Bit32 macroNo, MacroExInfo &macro);

    MacroData *GetMacroData() {return m_pMacroData;}
    QString ComboxIdxToName(MacroExInfo macro, Bit32 ival);
private:
    QMap<Bit32,MacroExInfo> m_macroData;
    MacroData *m_pMacroData;

    void LoadXml(Bit32 pathType, QString name, QString midpath);
    Bit32 GetXmlDoc(QDomDocument &doc, Bit32 pathType, QString name, QString midpath);
    void LoadSubItem(QDomElement element, QList<MacroExInfoSubItem> &subItem);

    void LoadData();
    void SetMacroType(Bit32 macroNo, Bit8 dataType);

    void ComboxIdxToVal(MacroExInfo macro, QString &val);
    void ValToComboxIdx(Bit32 macroNo, MacroExInfo macro, QString &valStr);
    void NameToComboxIdx(Bit32 macroNo, MacroExInfo macro, QString &valStr);

    QString GetMacroComboxStr(MacroExInfo macro, Bit32 subIdx);
};

class HmiNcMacroShowBase
{
public:
    HmiNcMacroShowBase(HmiNcMacroExInfo *hmimacro = NULL, MacroData *macroData = NULL);

    Bit32 GetMacroNum();

    int GetMacroDataType(Bit32 idx);
    QString GetMacroMaxVal(Bit32 idx);
    QString GetMacroMinVal(Bit32 idx);
    QString GetMacroName(Bit32 idx);
    QString GetMacroComment(Bit32 idx);
    QString GetMacroPicPath(Bit32 idx);
    Bit32 GetShowType(Bit32 idx);
    QString GetMacroVal(Bit32 idx);
    Bit32 GetMacroNo(Bit32 idx);
    Bit32 GetMacroFlag(Bit32 idx);
    QStringList GetMacroComboxList(Bit32 idx);
    NcDataLimit GetMacroInputDataLimit(Bit32 idx);

    Bit32 SetVal(Bit32 idx, QString val);
    Bit32 GetAllMacroNum();
    QString GetAllMacroName(Bit32 idx);
    QString GetMacroTabVal(Bit32 idx);
    Bit32 SetTabVal(Bit32 idx, QString val);
protected:
    QList<Bit32> m_nMacroList;
    HmiNcMacroExInfo *m_pHmiNcMacro;
    MacroData *m_pMacroData;
    int m_nOldRight;

    virtual void GetMacroList();
    Bit32 GetMacro(Bit32 idx, MacroExInfo &macroData);
};

class HmiNcMacroShow:public HmiNcMacroShowBase
{
public:
    HmiNcMacroShow(HmiNcMacroExInfo *hmimacro);
    HmiNcMacroShow(Bit32 pathType, QString name);

};

class HmiLLSUsualMacro:public HmiNcMacroShowBase
{
public:
    HmiLLSUsualMacro(HmiNcMacroExInfo *hmimacro);
protected:
    void GetMacroList();
};

#endif // HMIMACRO_H
