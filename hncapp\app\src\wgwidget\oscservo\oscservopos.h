﻿#ifndef OSCSERVOPOS_H
#define OSCSERVOPOS_H

#include <QWidget>

#include "containerwidget.h"

namespace Ui {
class OscServoPos;
}

QT_BEGIN_NAMESPACE
class OscList;
class OscWave;
QT_END_NAMESPACE

class OscServoPos : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoPos(QWidget *parent = 0);
    ~OscServoPos();

    void SetColorStyle();
protected:
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);

    bool eventFilter(QObject *target, QEvent *event);
    void resizeEvent(QResizeEvent *);
private slots:

    void on_leftBtn_clicked();

    void on_rightBtn_clicked();

private:
    Ui::OscServoPos *ui;

    OscList *posOscList;
    OscWave *pOscWavePos;
    OscWave *pOscWaveTrackErr;
    Bit32 lastEndPos;
    bool m_bSampleStart;		// 采样开始
    Bit32 m_nWcsZero;
    bool m_bWcsFlag;

    void LoadInfo();

    bool firstFlag;
    void Refresh();
    void Reset();
    QStringList GetParmList();
    void LoadAxisVal(Bit32 type);
    void OnBtFlagChange();
    void ResetInfo();
};

#endif // OSCSERVOPOS_H
