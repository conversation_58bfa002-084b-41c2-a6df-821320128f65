﻿
#include <QIntValidator>
#include <QKeyEvent>
#include <QRegExpValidator>

#include "common.h"
#include "hmicommon.h"
#include "hmioscservo.h"
#include "hncparamandef.h"
#include "hncparaman.h"
#include "hncmath.h"
#include "msgchan.h"

#include "osczcompconf.h"
#include "ui_osczcompconf.h"

OscZCompConf::OscZCompConf(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscZCompConf)
{
    ui->setupUi(this);

    this->curFocusIndex = 0;

    wList.append(ui->labelDist);
    wList.append(ui->labelPreHeat);
    wList.append(ui->labelZeroX);
    wList.append(ui->labelStep);
    wList.append(ui->labelZeroY);
    wList.append(ui->labelZeroZ);
    wList.append(ui->labelPause);
    wList.append(ui->labelCoordX);
    wList.append(ui->labelHeight);
    wList.append(ui->labelCoordY);
    wList.append(ui->checkBoxAutoMeasure);
    wList.append(ui->labelCoordZ);
    wList.append(ui->checkBoxCoordOnOff);
    wList.append(ui->labelCoordOn);
    wList.append(ui->labelCoordOff);
    wList.append(ui->labelCoordBit);
    wList.append(ui->checkBoxBlowOnOff);
    wList.append(ui->labelBlowOn);
    wList.append(ui->labelBlowOff);
    for(int i = 0; i < wList.count(); i++)
    {
        wList.at(i)->setFocusPolicy(Qt::StrongFocus);
        wList.at(i)->installEventFilter(this);
    }

    this->dirLayout = new DirMoveLayout(this);
    this->dirLayoutList << ui->labelDist << ui->labelPreHeat
                        << ui->labelZeroX << ui->labelStep
                        << ui->labelZeroY << NULL
                        << ui->labelZeroZ << ui->labelPause
                        << ui->labelCoordX << ui->labelHeight
                        << ui->labelCoordY << ui->checkBoxAutoMeasure
                        << ui->labelCoordZ << ui->checkBoxCoordOnOff
                        << ui->labelCoordOn << ui->labelCoordOff
                        << ui->labelCoordBit << ui->checkBoxBlowOnOff
                        << ui->labelBlowOn << ui->labelBlowOff   ;
    dirLayout->SetDirMoveLayout(10, 2, this->dirLayoutList);

    intValidator = new QIntValidator(this);
    regValidator = new QRegExpValidator(this);

    Bit32 ch = ActiveChan();
    if (HmiOscServo::s_Conf[ch].stZCompConf.autoMeasure)
    {
        ui->checkBoxAutoMeasure->setChecked(true);
    }
    else
    {
        ui->checkBoxAutoMeasure->setChecked(false);
    }

    if (HmiOscServo::s_Conf[ch].stZCompExtraConf.coordOnOff)
    {
        ui->checkBoxCoordOnOff->setChecked(true);
    }
    else
    {
        ui->checkBoxCoordOnOff->setChecked(false);
    }

    if (HmiOscServo::s_Conf[ch].stZCompExtraConf.blowOnOff)
    {
        ui->checkBoxBlowOnOff->setChecked(true);
    }
    else
    {
        ui->checkBoxBlowOnOff->setChecked(false);
    }
}

OscZCompConf::~OscZCompConf()
{
    delete ui;
}

bool OscZCompConf::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        QWidget *tmp = dynamic_cast<QWidget *>(target);
        if(this->dirLayoutList.contains(tmp) && tmp != NULL)
        {
            if(keyEvent->key() == Qt::Key_Right || keyEvent->key() == Qt::Key_Left
              || keyEvent->key() == Qt::Key_Down || keyEvent->key() == Qt::Key_Up)
            {
                dirLayout->DirMoveOnKey(tmp, keyEvent->key());
                return true;
            }
            else if(keyEvent->key() == Qt::Key_Enter || keyEvent->key() == Qt::Key_Return)
            {
                this->DataSet(this->curFocusIndex);
                return true;
            }
        }
    }
    else if(event->type() == QEvent::FocusIn)
    {
        QWidget *tmp = dynamic_cast<QWidget *>(target);
        if(this->dirLayoutList.contains(tmp) && tmp != NULL)
        {
            this->curFocusIndex = this->dirLayoutList.indexOf(tmp);
        }
    }
    return QObject::eventFilter(target, event);
}

void OscZCompConf::FocusRedraw()
{
    for (Bit32 i = 0; i < wList.count(); i++)
    {
        if (i == this->curFocusIndex)
        {
            wList.at(i)->setProperty("selected", true);
            wList.at(i)->style()->polish(wList.at(i));
        }
        else
        {
            wList.at(i)->setProperty("selected", false);
            wList.at(i)->style()->polish(wList.at(i));
        }
    }
}

void OscZCompConf::LoadData()
{
    Bit32 parmAxisId = 102034;
    SDataProperty prop;
    fBit64 maxVel = 0;
    Bit32 ch = ActiveChan();

    disconnect(ui->checkBoxAutoMeasure, SIGNAL(clicked(bool)), this,SLOT(SlotCheckBoxAutoMeasureClicked(bool)));
    disconnect(ui->checkBoxBlowOnOff, SIGNAL(clicked(bool)), this,SLOT(SlotCheckBoxBlowOnOffClicked(bool)));
    disconnect(ui->checkBoxCoordOnOff, SIGNAL(clicked(bool)), this,SLOT(SlotCheckBoxCoordOnOffClicked(bool)));

    HNC_ParamanGetParaPropEx(parmAxisId, PARA_PROP_VALUE, &prop);
    maxVel = prop.value.val_real;
    ui->labelHighSpeed->setText(QString::number(maxVel, 'f', 3));

    ui->labelDist->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.dist));
    ui->labelPreHeat->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.preHeat));
    ui->labelZeroX->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.zeroX, 'f', 3));
    ui->labelStep->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.step));
    ui->labelZeroY->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.zeroY, 'f', 3));
    ui->labelZeroZ->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.zeroZ, 'f', 3));
    ui->labelPause->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.pause, 'f', 1));
    ui->labelCoordX->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.coordX, 'f', 3));
    ui->labelHeight->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.height));
    ui->labelCoordY->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.coordY, 'f', 3));
    ui->labelCoordZ->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompConf.coordZ, 'f', 3));
    ui->labelCoordOn->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompExtraConf.coordOnCmd));
    ui->labelCoordOff->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompExtraConf.coordOffCmd));
    ui->labelCoordBit->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompExtraConf.coordBitCmd));
    ui->labelBlowOn->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompExtraConf.blowOnCmd));
    ui->labelBlowOff->setText(QString::number(HmiOscServo::s_Conf[ch].stZCompExtraConf.blowOffCmd));

    if (HmiOscServo::s_Conf[ch].stZCompConf.autoMeasure)
    {
        ui->checkBoxAutoMeasure->setChecked(true);
    }
    else
    {
        ui->checkBoxAutoMeasure->setChecked(false);
    }

    if (HmiOscServo::s_Conf[ch].stZCompExtraConf.coordOnOff)
    {
        ui->checkBoxCoordOnOff->setChecked(true);
    }
    else
    {
        ui->checkBoxCoordOnOff->setChecked(false);
    }

    if (HmiOscServo::s_Conf[ch].stZCompExtraConf.blowOnOff)
    {
        ui->checkBoxBlowOnOff->setChecked(true);
    }
    else
    {
        ui->checkBoxBlowOnOff->setChecked(false);
    }

    connect(ui->checkBoxAutoMeasure, SIGNAL(clicked(bool)), this,SLOT(SlotCheckBoxAutoMeasureClicked(bool)));
    connect(ui->checkBoxBlowOnOff, SIGNAL(clicked(bool)), this,SLOT(SlotCheckBoxBlowOnOffClicked(bool)));
    connect(ui->checkBoxCoordOnOff, SIGNAL(clicked(bool)), this,SLOT(SlotCheckBoxCoordOnOffClicked(bool)));
}

void OscZCompConf::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    UNREFERENCED_PARAM(messageValue);
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        MessageOut(TR("请确认对刀仪坐标!"));
        ui->labelPic->setPixmap(PixMapToSize(ui->labelPic->size(), TransPicName(HmiOscServo::GetOscPicPath() + "zcomp.png")));
        this->LoadData();
        this->dirLayoutList.at(this->curFocusIndex)->setFocus();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::SETFOCUS)
    {
        this->LoadData();
        this->dirLayoutList.at(this->curFocusIndex)->setFocus();
    }
}

void OscZCompConf::DataSet(Bit32 idx)
{
    QString editStr = "";
    QString reg = "";
    Bit32 ret = 0;
    Bit32 ch = ActiveChan();
    bool ok = false;
    MessageOut("");

    if(idx >= this->dirLayoutList.count() || idx < 0)
    {
        return;
    }

    switch (idx) {
    case 0: // 测量行程
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.dist);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[测量行程]："), 4);
        if(ret == 0)
        {
            Bit32 longTmp = editStr.toLong(&ok);
            if (ok == false || longTmp < 100 || longTmp > 1000)
            {
                MessageOut(TR("输入数据无效！有效范围：%1~%2").arg(100).arg(1000));
            }
            else
            {
                HmiOscServo::s_Conf[ch].stZCompConf.dist = longTmp;
            }
        }
        break;
    case 1:// 最大采样点数(杨祥需求，最大采样点楼为4的倍数)
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.preHeat);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[最大采样点数(4的倍数)][范围:20~400]："),3);
        if(ret == 0)
        {
            Bit32 longTmp = editStr.toLong(&ok);
            if (ok == false || longTmp < 20 || longTmp > 400 || longTmp % 4 != 0)
            {
                MessageOut(TR("输入数据无效！有效范围：%1~%2，且是4的倍数").arg(20).arg(400));
            }
            else
            {
                HmiOscServo::s_Conf[ch].stZCompConf.preHeat = longTmp;
            }
        }
        break;
    case 2: // 测量起点X
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.zeroX, 'f', 3);
        reg = QString("^[0-9]{1,5}([.][0-9]{0,%1})?$").arg(3);
        regValidator->setRegExp(QRegExp(reg));

        ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[测量起点X）]："));
        if(ret == 0)
        {
            fBit64 dbTmp = editStr.toDouble(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompConf.zeroX = dbTmp;
        }
        break;
    case 3: // 采样间隔时间
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.step);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[采样间隔时间]："), 2);
        if(ret == 0)
        {
            Bit32 longTmp = editStr.toLong(&ok);
            if (ok == false || longTmp < 1 || longTmp > 10)
            {
                MessageOut(TR("输入数据无效！采样间隔时间范围为 %1 - %2").arg(1).arg(10));
            }
            else
            {
                HmiOscServo::s_Conf[ch].stZCompConf.step = longTmp;
            }
        }
        break;
    case 4:  // 测量起点Y
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.zeroY, 'f', 3);
        reg = QString("^[0-9]{1,5}([.][0-9]{0,%1})?$").arg(3);
        regValidator->setRegExp(QRegExp(reg));

        ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[测量起点Y）]："));
        if(ret == 0)
        {
            fBit64 dbTmp = editStr.toDouble(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompConf.zeroY = dbTmp;
        }
        break;
    case 6: // 测量起点Z
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.zeroZ, 'f', 3);
        reg = QString("^[0-9]{1,5}([.][0-9]{0,%1})?$").arg(3);
        regValidator->setRegExp(QRegExp(reg));

        ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[测量起点Z）]："));
        if(ret == 0)
        {
            fBit64 dbTmp = editStr.toDouble(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompConf.zeroZ = dbTmp;
        }
        break;
    case 7: // 往复暂停时间
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.pause, 'f', 1);
        reg = QString("^[0-9]{1,5}([.][0-9]{0,%1})?$").arg(1);
        regValidator->setRegExp(QRegExp(reg));

        ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[往复暂停时间）]："));
        if(ret == 0)
        {
            fBit64 dbTmp = editStr.toDouble(&ok);
            if (ok == false || HNC_DoubleCompare(dbTmp, 0.0) < 0 || HNC_DoubleCompare(dbTmp, 10.0) > 0)
            {
                MessageOut(TR("输入数据无效！有效范围： %1 或 %2").arg(0).arg(10));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompConf.pause = dbTmp;
        }
        break;
    case 8: // 对刀仪坐标X
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.coordX, 'f', 3);
        reg = QString("^[0-9]{1,5}([.][0-9]{0,%1})?$").arg(3);
        regValidator->setRegExp(QRegExp(reg));

        ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[对刀仪坐标X）]："));
        if(ret == 0)
        {
            fBit64 dbTmp = editStr.toDouble(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompConf.coordX = dbTmp;
        }
        break;
    case 9: // 测量安全高度
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.height);

        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[测量安全高度]："), 2);
        if(ret == 0)
        {
            Bit32 longTmp = editStr.toLong(&ok);
            if (ok == false || longTmp < 5 || longTmp > 50)
            {
                MessageOut(TR("输入数据无效！有效范围：%1~%2").arg(5).arg(50));
                break;
            }
            else
            {
                HmiOscServo::s_Conf[ch].stZCompConf.height = longTmp;
            }
        }
        break;
    case 10: // 对刀仪坐标Y
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.coordY, 'f', 3);
        reg = QString("^[0-9]{1,5}([.][0-9]{0,%1})?$").arg(3);
        regValidator->setRegExp(QRegExp(reg));

        ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[对刀仪坐标Y）]："));
        if(ret == 0)
        {
            fBit64 dbTmp = editStr.toDouble(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompConf.coordY = dbTmp;
        }
        break;
    case 11: // 自动测量
        ui->checkBoxAutoMeasure->click();
        break;
    case 12: // 对刀仪坐标Z
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompConf.coordZ, 'f', 3);
        reg = QString("^[0-9]{1,5}([.][0-9]{0,%1})?$").arg(3);
        regValidator->setRegExp(QRegExp(reg));

        ret = MessageInput(&editStr, DTYPE_FLOAT, TR("请输入[对刀仪坐标Z）]："));
        if(ret == 0)
        {
            fBit64 dbTmp = editStr.toDouble(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompConf.coordZ = dbTmp;
        }
        break;
    case 13: // 对刀仪开关
        ui->checkBoxCoordOnOff->click();
        break;
    case 14: // 对刀仪开指令
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompExtraConf.coordOnCmd);

        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[对刀仪开指令]："), 4);
        if(ret == 0)
        {
            Bit32 longTmp = editStr.toLong(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompExtraConf.coordOnCmd = longTmp;
        }
        break;
    case 15: // 对刀仪关指令
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompExtraConf.coordOffCmd);

        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[对刀仪关指令]："), 4);
        if(ret == 0)
        {
            Bit32 longTmp = editStr.toLong(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompExtraConf.coordOffCmd = longTmp;
        }
        break;
    case 16:// 对刀仪信号（十进制）
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompExtraConf.coordBitCmd);

        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[对刀仪信号]："), 4);
        if(ret == 0)
        {
            Bit32 longTmp = editStr.toLong(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompExtraConf.coordBitCmd = longTmp;
        }
        break;
    case 17: // 吹气开关
        ui->checkBoxBlowOnOff->click();
        break;
    case 18: // 吹气开指令
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompExtraConf.blowOnCmd);

        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[吹气开指令]："), 4);
        if(ret == 0)
        {
            Bit32 longTmp = editStr.toLong(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompExtraConf.blowOnCmd = longTmp;
        }
        break;
    case 19:// 吹气关指令
        editStr = QString::number(HmiOscServo::s_Conf[ch].stZCompExtraConf.blowOffCmd);

        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[吹气关指令]："), 4);
        if(ret == 0)
        {
            Bit32 longTmp = editStr.toLong(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效！"));
                break;
            }
            HmiOscServo::s_Conf[ch].stZCompExtraConf.blowOffCmd = longTmp;
        }
        break;
    default:
        break;
    }

    HmiOscServo::OscServoDataSave();
    this->LoadData();
    //this->dirLayoutList.at(idx)->setFocus();
    MsgChan::Instance().TranMsg(MsgData::SETFOCUS, ""); // 设置焦点
}

void OscZCompConf::SlotCheckBoxAutoMeasureClicked(bool checked)
{
    Bit32 ch = ActiveChan();
    if (true == checked)
    {
        HmiOscServo::s_Conf[ch].stZCompConf.autoMeasure = 1;
    }
    else
    {
        HmiOscServo::s_Conf[ch].stZCompConf.autoMeasure = 0;
    }
    HmiOscServo::OscServoDataSave();
    this->LoadData();
}

void OscZCompConf::SlotCheckBoxCoordOnOffClicked(bool checked)
{
    Bit32 ch = ActiveChan();
    if (true == checked)
    {
        HmiOscServo::s_Conf[ch].stZCompExtraConf.coordOnOff = 1;
    }
    else
    {
        HmiOscServo::s_Conf[ch].stZCompExtraConf.coordOnOff = 0;
    }
    HmiOscServo::OscServoDataSave();
    this->LoadData();
}

void OscZCompConf::SlotCheckBoxBlowOnOffClicked(bool checked)
{
    Bit32 ch = ActiveChan();
    if (true == checked)
    {
        HmiOscServo::s_Conf[ch].stZCompExtraConf.blowOnOff = 1;
    }
    else
    {
        HmiOscServo::s_Conf[ch].stZCompExtraConf.blowOnOff = 0;
    }
    HmiOscServo::OscServoDataSave();
    this->LoadData();
}
