﻿#include <qmath.h>

#include "hncmath.h"
#include "hncsmpl.h"
#include "hncaxis.h"
#include "smplcalc.h"
#include "hncsys.h"
#include "hncchan.h"

#include "hmioscproc.h"
#include "hmioscservo.h"
#include "hmipaintercolor.h"

#include "oscwave.h"
#include "ui_oscwave.h"

#define OSC_ZOOM_STEP (2.0)//(1.5) // 网格缩放系数（注：改变这个宏，下面的OSC_ZOOM_STEP也要随着变）
#define LOG_CALC_OSC_ZOOM (0.693) // (0.405) // OSC_ZOOM_STEP的自然对数（降低系统消耗，随OSC_ZOOM_STEP变）

OscWave::OscWave(QWidget *parent, HmiOscServo::OscServoType type, QString name, QString title) :
    QWidget(parent),
    ui(new Ui::OscWave)
{
    ui->setupUi(this);
    ui->labelWaveTitle->setText(title);

    oscType = type;
    waveName = name;
    m_nShowYAxis2 = false;
    m_vecYNameLabel.append(ui->yName2);
    m_vecYNameLabel.append(ui->yName3);
    m_vecYNameLabel.append(ui->yName4);
    m_vecYNameLabel.append(ui->yName5);
    m_vecYNameLabel.append(ui->yName6);
    m_vecYNameLabel.append(ui->yName7);
    m_vecYNameLabel.append(ui->yName8);
    m_vecYNameLabel.append(ui->yName9);
    m_vecYNameLabel.append(ui->yName10);
    m_vecYNameLabel.append(ui->yName11);
    m_vecYNameLabel.append(ui->yName12);
    m_vecYNameLabel.append(ui->yName13);
    m_vecYNameLabel.append(ui->yName14);
    m_vecYNameLabel.append(ui->yName15);
    m_vecYNameLabel.append(ui->yName16);
    m_vecYNameLabel.append(ui->yName17);
    m_vecYNameLabel.append(ui->yName18);
    m_vecYNameLabel.append(ui->yName19);
    m_vecYNameLabel.append(ui->yName20);
    m_vecYNameLabel.append(ui->yName21);
    m_vecYNameLabel.append(ui->yName22);
    m_vecYNameLabel.append(ui->yName23);
    m_vecYNameLabel.append(ui->yName24);
    m_vecYNameLabel.append(ui->yName25);
    m_vecYNameLabel.append(ui->yName26);
    m_vecYNameLabel.append(ui->yName27);
    m_vecYNameLabel.append(ui->yName28);
    m_vecYNameLabel.append(ui->yName29);
    m_vecYNameLabel.append(ui->yName30);
    m_vecYNameLabel.append(ui->yName31);
    m_vecYNameLabel.append(ui->yName32);

    m_mapCurveColor.clear();

    xStepNum = 5;
    yStepNum = 6;

    yTickPrec = 1;

    autoRangParmFlag = false;

    yMin = -6000;
    yMax = 6000;

    m_dbHisMaxX = 0;
    m_dbHisMinX = 0;
    m_dbHisMaxY = 0;
    m_dbHisMinY = 0;

    ui->wavePlot->xAxis->setTickLabelFont(QFont(FONT_TYPE, 9));
    ui->wavePlot->yAxis->setTickLabelFont(QFont(FONT_TYPE, 9));
    SelAreaDataRestore();

    this->SetupPlot();

    // 选择框部分
    fBit64 f64 = 0.0;
    for (Bit32 i = 0; i < 5; i++)
    {
        graphSelX.append(f64);
        graphSelY.append(f64);
    }
    QCPCurve *pCurve = new QCPCurve(ui->wavePlot->xAxis, ui->wavePlot->yAxis);
    m_vecpGraphSel.append(pCurve);

    QColor qc = HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_BLACK_CURVE1);
    // 将CURVE1到CURVE10放到容器中
    m_vecWaveCurve.clear();
    for (int type = OscWave::CURVE1; type < OscWave::TYPE_COUNT; type++)
    {
        m_vecWaveCurve.append((OscWave::ENType)type);
    }

    if (m_vecpGraphSel.count() > 0)
    {
        m_vecpGraphSel.at(0)->setPen(QPen(qc));
        ui->wavePlot->addPlottable(m_vecpGraphSel.at(0));
    }

    ui->wavePlot->installEventFilter(this);
    connect(ui->wavePlot, SIGNAL(mouseWheel(QWheelEvent*)), this, SLOT(WaveWigdetMouseWheel()));
}

OscWave::~OscWave()
{
    delete ui;
}

bool OscWave::eventFilter(QObject *wg, QEvent *event)
{
    if (event->type() == QEvent::MouseButtonPress)
    {
        if (wg == ui->wavePlot)
        {
            emit WaveWigdetFocusIn();
        }
    }

    return QObject::eventFilter(wg, event);
}

void OscWave::WaveWigdetMouseWheel()
{
    emit WaveWigdetZoomChangeByWheel();
}

void OscWave::SetYTickPrec(Bit32 prec)
{
    this->yTickPrec = prec;
}

void OscWave::SetupPlot()
{
    QColor qc = HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_BLACK_BACKGROUND);

    ui->wavePlot->setBackground(qc);    // 设置显示背景色
//    ui->wavePlot->axisRect()->setupFullAxesBox(true);   // 显示x、y、x2、y2轴

    ////////////////////////////X轴////////////////////////////
    // 重新设置X轴刻度中间有0个分刻度
    ui->wavePlot->xAxis->setAutoSubTicks(false);
    ui->wavePlot->xAxis->setSubTickCount(0);

    ui->wavePlot->xAxis2->setAutoSubTicks(false);
    ui->wavePlot->xAxis2->setSubTickCount(0);

    ////////////////////////////X轴去掉自动标尺////////////////////////////
    ui->wavePlot->xAxis->setAutoTickStep(false);
    ui->wavePlot->xAxis2->setAutoTickStep(false);

    ////////////////////////////Y轴////////////////////////////
    // 重新设置Y轴刻度中间有0个分刻度
    ui->wavePlot->yAxis->setAutoSubTicks(false);
    ui->wavePlot->yAxis->setSubTickCount(0);

    ui->wavePlot->yAxis2->setAutoSubTicks(false);
    ui->wavePlot->yAxis2->setSubTickCount(0);

    ////////////////////////////Y轴去掉自动标尺////////////////////////////
    ui->wavePlot->yAxis->setAutoTickStep(false);
    ui->wavePlot->yAxis2->setAutoTickStep(false);


    switch(oscType)
    {
    case HmiOscServo::OSC_SERVO_SPE:    // 速度环
        if (waveName == "SPD")      // 速度波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(ms)");

            // 设置Y轴名
            ui->yName1->setText("X(mm/min:");
            ui->yName2->setText(TR("指令"));
            ui->yName3->setText("/");
            ui->yName4->setText(TR("实际"));
            ui->yName5->setText(")");


            ////////////////////////////速度环---指令////////////////////////////
            ui->wavePlot->addGraph();    // 指令

            ////////////////////////////速度环---实际////////////////////////////
            ui->wavePlot->addGraph();    // 实际

            ////////////////////////////速度环---缩放限制////////////////////////////
            plotXStepMix = 5;
            plotXStepMax = 20500;
            plotYStepMix = 15;
            plotYStepMax = 491520;

            ////////////////////////////速度环---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10000;
            baseXStep = 1000;
            baseYRangLower = -6000;
            baseYRangUpper = 6000;
            baseYStep = 2000;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        else if (waveName == "ACC")     // 加速度波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(ms)");

            ////////////////////////////加速度---Y轴////////////////////////////
            // 重新设置Y轴刻度中间有0个分刻度
            ui->wavePlot->yAxis->setAutoSubTicks(false);
            ui->wavePlot->yAxis->setSubTickCount(0);

            ui->wavePlot->yAxis2->setAutoSubTicks(false);
            ui->wavePlot->yAxis2->setSubTickCount(0);

            // 设置Y轴名
            ui->yName1->setText(TR("加速度(m/s2)"));


            ////////////////////////////加速度---曲线////////////////////////////
            ui->wavePlot->addGraph();    // 指令

            ////////////////////////////加速度---缩放限制////////////////////////////
            plotXStepMix = 5;
            plotXStepMax = 20500;
            plotYStepMix = 0.02;
            plotYStepMax = 20500;

            ////////////////////////////加速度---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10000;
            baseXStep = 1000;
            baseYRangLower = -6;
            baseYRangUpper = 6;
            baseYStep = 2;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        break;
    case HmiOscServo::OSC_SERVO_POS:
        if (waveName == "POS")      // 位置波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(ms)");

            // 设置Y轴名
            ui->yName1->setText("X(mm");
            ui->yName2->setText(TR("指令"));
            ui->yName3->setText("/");
            ui->yName4->setText(TR("实际"));
            ui->yName5->setText(")");


            ////////////////////////////位置环---指令////////////////////////////
            ui->wavePlot->addGraph();    // 指令

            ////////////////////////////位置环---实际////////////////////////////
            ui->wavePlot->addGraph();    // 实际

            ////////////////////////////位置环---缩放限制////////////////////////////
            plotXStepMix = 5;
            plotXStepMax = 20500;
            plotYStepMix = 2;
            plotYStepMax = 7680;

            ////////////////////////////位置环---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10000;
            baseXStep = 1000;
            baseYRangLower = -300;
            baseYRangUpper = 300;
            baseYStep = 100;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        else if (waveName == "TrackErr")     // 跟随误差波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(ms)");

            // 设置Y轴名
            ui->yName1->setText(TR("跟随误差(um)"));

            ////////////////////////////跟随误差---曲线////////////////////////////
            ui->wavePlot->addGraph();    // 指令

            ////////////////////////////跟随误差---缩放限制////////////////////////////
            plotXStepMix = 5;
            plotXStepMax = 20500;
            plotYStepMix = 30;
            plotYStepMax = 122880;

            ////////////////////////////跟随误差---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10000;
            baseXStep = 1000;
            baseYRangLower = -3000;
            baseYRangUpper = 3000;
            baseYStep = 1000;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        break;
    case HmiOscServo::OSC_SERVO_TOOL:
        SetYTickPrec(1);

        // 设置X轴名
        ui->xName->setText("T(ms)");

        // 设置Y轴名
        ui->yName1->setText(TR("换刀信号点:"));
        ui->yName2->setText(HmiOscServo::ToolChangeReg());

        ////////////////////////////换刀时间---指令////////////////////////////
        ui->wavePlot->addGraph();    // 指令

        ////////////////////////////换刀时间---缩放限制////////////////////////////
        plotXStepMix = 1;
        plotXStepMax = 16000;
        plotYStepMix = 15;
        plotYStepMax = 256000;

        ////////////////////////////换刀时间---标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = -3;
        baseYRangUpper = 3;
        baseYStep = 1;

        optXRangLower = baseXRangLower;
        optXRangUpper = baseXRangUpper;
        optXStep = baseXStep;
        optYRangLower = baseYRangLower;
        optYRangUpper = baseYRangUpper;
        optYStep = baseYStep;
        break;
    case HmiOscServo::OSC_SERVO_SPINDLE:
        SetYTickPrec(1);

        // 设置X轴名
        ui->xName->setText("T(ms)");

        // 设置Y轴名
        ui->yName1->setText(TR("主轴转速(rpm)"));

        ////////////////////////////主轴升降速---指令////////////////////////////
        ui->wavePlot->addGraph();    // 指令

        ////////////////////////////主轴升降速---缩放限制////////////////////////////
        plotXStepMix = 2;
        plotXStepMax = 20500;// Fixed bug#4913, value: 16->20500,限制值与速度环保持一致
        plotYStepMix = 125;
        plotYStepMax = 32000;

        ////////////////////////////主轴升降速---标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = -6000;
        baseYRangUpper = 6000;
        baseYStep = 2000;

        optXRangLower = baseXRangLower;
        optXRangUpper = baseXRangUpper;
        optXStep = baseXStep;
        optYRangLower = baseYRangLower;
        optYRangUpper = baseYRangUpper;
        optYStep = baseYStep;
        break;
    case HmiOscServo::OSC_SERVO_FREQTAP:
        SetYTickPrec(1);

        // 设置X轴名
        ui->xName->setText("T(ms)");

        // 设置Y轴名
        ui->yName1->setText(TR("同步误差(um)"));
        ////////////////////////////变频器刚攻---指令////////////////////////////
        ui->wavePlot->addGraph();    // 指令

        ////////////////////////////变频器刚攻---缩放限制////////////////////////////
        plotXStepMix = 1;
        plotXStepMax = 8000;
        plotYStepMix = 5;
        plotYStepMax = 10240;

        ////////////////////////////变频器刚攻---标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = -30;
        baseYRangUpper = 30;
        baseYStep = 10;

        optXRangLower = baseXRangLower;
        optXRangUpper = baseXRangUpper;
        optXStep = baseXStep;
        optYRangLower = baseYRangLower;
        optYRangUpper = baseYRangUpper;
        optYStep = baseYStep;
        break;
	case HmiOscServo::OSC_SERVO_BEARTAP:
		SetYTickPrec(1);

		// 设置X轴名
		ui->xName->setText("T(ms)");

		// 设置Y轴名
		ui->yName1->setText(TR("同步误差(um)"));
		////////////////////////////乘载攻丝---指令////////////////////////////
		ui->wavePlot->addGraph();    // 指令

									 ////////////////////////////变频器刚攻---缩放限制////////////////////////////
		plotXStepMix = 1;
		plotXStepMax = 8000;
		plotYStepMix = 5;
		plotYStepMax = 10240;

		////////////////////////////变频器刚攻---标尺////////////////////////////
		baseXRangLower = 0;
		baseXRangUpper = 10000;
		baseXStep = 1000;
		baseYRangLower = -30;
        baseYRangUpper = 30;
		baseYStep = 10;

		optXRangLower = baseXRangLower;
		optXRangUpper = baseXRangUpper;
		optXStep = baseXStep;
		optYRangLower = baseYRangLower;
        optYRangUpper = baseYRangUpper;
		optYStep = baseYStep;
		break;
    case HmiOscServo::OSC_SERVO_TAP:
        if (waveName == "SyncErr")      // 刚性攻丝-同步误差主界面波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(ms)");

            // 设置Y轴名
            ui->yName1->setText("E(Z-C)um");

            ////////////////////////////刚性攻丝-同步误差主界面////////////////////////////
            ui->wavePlot->addGraph();    //

            ////////////////////////////刚性攻丝-同步误差主界面---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 7700;
            plotYStepMix = 2;
            plotYStepMax = 7680;

            ////////////////////////////刚性攻丝-同步误差主界面波形图---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10000;
            baseXStep = 1000;
            baseYRangLower = -120;
            baseYRangUpper = 120;
            baseYStep = 40;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;

        }
        else if (waveName == "SPE")     // 刚性攻丝-主界面-速度波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(ms)");

            // 设置Y轴名
            ui->yName1->setText("Z");
            ui->yName2->setText(TR("(mm/min)"));
            ui->yName3->setText("/");
            ui->yName4->setText(TR("C"));
            ui->yName5->setText("(r/min)");

            ////////////////////////////刚性攻丝-Z轴---曲线////////////////////////////
            ui->wavePlot->addGraph();    // Z轴

            ////////////////////////////刚性攻丝-C轴---曲线////////////////////////////
            ui->wavePlot->addGraph();    // C轴

            ////////////////////////////跟随误差---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 7700;
            plotYStepMix = 15;
            plotYStepMax = 491520;

            ////////////////////////////刚性攻丝-主界面-速度波形图---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10000;
            baseXStep = 1000;
            baseYRangLower = -6000;
            baseYRangUpper = 6000;
            baseYStep = 2000;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
    case HmiOscServo::OSC_SERVO_FREQ:
        if (waveName == "CurrentFlow")      // 陷波器-电机指令电流波形图
        {
            ////////////////////////////基本设置////////////////////////////
            ui->wavePlot->axisRect()->setupFullAxesBox(true);   // 显示网格

            SetYTickPrec(1);

            ////////////////////////////X轴////////////////////////////
            // 重新设置X轴刻度中间有0个分刻度
            ui->wavePlot->xAxis->setAutoSubTicks(false);
            ui->wavePlot->xAxis->setSubTickCount(0);

            ui->wavePlot->xAxis2->setAutoSubTicks(false);
            ui->wavePlot->xAxis2->setSubTickCount(0);

            // 设置X轴名
            ui->xName->setText("T(ms)");


            ////////////////////////////Y轴////////////////////////////
            // 重新设置Y轴刻度中间有0个分刻度
            ui->wavePlot->yAxis->setAutoSubTicks(false);
            ui->wavePlot->yAxis->setSubTickCount(0);

            ui->wavePlot->yAxis2->setAutoSubTicks(false);
            ui->wavePlot->yAxis2->setSubTickCount(0);

            // 设置Y轴名
            ui->yName1->setText("X");
            ui->yName2->setText(TR(":电机指令电流[0.01A]"));
//            ui->yName3->setText("/");
//            ui->yName4->setText(TR("实际"));
//            ui->yName5->setText(")");


            ////////////////////////////陷波器-电机指令电流////////////////////////////
            ui->wavePlot->addGraph();    // 陷波器-电机指令电流


            ////////////////////////////陷波器-电机指令电流---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 7700;
            plotYStepMix = 10;
            plotYStepMax = 327680;

            ////////////////////////////陷波器-电机指令电流---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10000;
            baseXStep = 1000;
            baseYRangLower = -1200;
            baseYRangUpper = 1200;
            baseYStep = 400;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;

        }
        else if (waveName == "FreqDomain")     // 陷波器-频域波形图
        {
            ////////////////////////////陷波器-频域////////////////////////////
            ui->wavePlot->setBackground(qc);    // 设置显示背景色
            ui->wavePlot->axisRect()->setupFullAxesBox(true);   // 显示网格
            SetYTickPrec(1);
            ////////////////////////////陷波器-频域////////////////////////////
            // 重新设置X轴刻度中间有0个分刻度
            ui->wavePlot->xAxis->setAutoSubTicks(false);
            ui->wavePlot->xAxis->setSubTickCount(0);

            ui->wavePlot->xAxis2->setAutoSubTicks(false);
            ui->wavePlot->xAxis2->setSubTickCount(0);

            // 设置X轴名
            ui->xName->setText("F(Hz)");

            ////////////////////////////陷波器-频域////////////////////////////
            // 重新设置Y轴刻度中间有0个分刻度
            ui->wavePlot->yAxis->setAutoSubTicks(false);
            ui->wavePlot->yAxis->setSubTickCount(0);

            ui->wavePlot->yAxis2->setAutoSubTicks(false);
            ui->wavePlot->yAxis2->setSubTickCount(0);

            // 设置Y轴名
            ui->yName3->setText(TR("频域图"));

            // 增加y轴label间距，保证和spdPlot的图标对齐



            ////////////////////////////陷波器-频域曲线////////////////////////////
            ui->wavePlot->addGraph();    // 指令

            ////////////////////////////陷波器-频域---缩放限制////////////////////////////
            plotXStepMix = 25;
            plotXStepMax = 200;
            plotYStepMix = 10;
            plotYStepMax = 81920;

            ////////////////////////////陷波器-频域---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 1000;
            baseXStep = 100;
            baseYRangLower = 0;
            baseYRangUpper = 120;
            baseYStep = 20;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        break;
    case HmiOscServo::OSC_SERVO_CUSTOM:    // 自定义
        if (waveName == "ORG")      // 原始值波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(ms)");

            // 设置Y轴名
            ui->yName1->setText(TR("原始值"));

            ////////////////////////////自定义---原始值////////////////////////////
            ui->wavePlot->addGraph();    // 原始值


            ////////////////////////////原始值---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 7700;
            plotYStepMix = 1;
            plotYStepMax = 800000000;

            ////////////////////////////原始值---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10000;
            baseXStep = 1000;
            baseYRangLower = -270;
            baseYRangUpper = 270;
            baseYStep = 90;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        else if (waveName == "SHIFT")     // 换算值波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(ms)");

            ////////////////////////////换算值---Y轴////////////////////////////
            // 重新设置Y轴刻度中间有0个分刻度
            ui->wavePlot->yAxis->setAutoSubTicks(false);
            ui->wavePlot->yAxis->setSubTickCount(0);

            ui->wavePlot->yAxis2->setAutoSubTicks(false);
            ui->wavePlot->yAxis2->setSubTickCount(0);

            // 设置Y轴名
            ui->yName2->setText(TR("换算值"));


            ////////////////////////////换算值---曲线////////////////////////////
            ui->wavePlot->addGraph();    // 换算值

            ////////////////////////////换算值---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 7700;
            plotYStepMix = 1;
            plotYStepMax = 800000000;

            ////////////////////////////换算值---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10000;
            baseXStep = 1000;
            baseYRangLower = -9;
            baseYRangUpper = 9;
            baseYStep = 3;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        break;
    case HmiOscServo::OSC_SERVO_SYNC:    // 龙门同步
        if (waveName == "CurrentFlow")      // 负载电流波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("P(mm)");

            // 设置Y轴名
            ui->yName1->setText(TR("负载电流(A)"));
            ui->yName2->setText("X");
            ui->yName3->setText("/");
            ui->yName4->setText("Y");


            ////////////////////////////龙门同步---负载电流X////////////////////////////
            ui->wavePlot->addGraph();    // 负载电流X

            ////////////////////////////龙门同步---负载电流Y////////////////////////////
            ui->wavePlot->addGraph();    // 负载电流Y

            ////////////////////////////负载电流---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 200;
            plotYStepMix = 2;
            plotYStepMax = 960;

            ////////////////////////////负载电流---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 2000;
            baseXStep = 200;
            baseYRangLower = -30;
            baseYRangUpper = 30;
            baseYStep = 10;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        else if (waveName == "ERR")     // 同步偏差波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("P(mm)");

            ////////////////////////////同步偏差---Y轴////////////////////////////
            // 重新设置Y轴刻度中间有0个分刻度
            ui->wavePlot->yAxis->setAutoSubTicks(false);
            ui->wavePlot->yAxis->setSubTickCount(0);

            ui->wavePlot->yAxis2->setAutoSubTicks(false);
            ui->wavePlot->yAxis2->setSubTickCount(0);

            // 设置Y轴名
            ui->yName2->setText(TR("同步位置偏差(mm)"));
            ui->yName3->setText("/");
            ui->yName4->setText(TR("同步电流偏差(A)"));


            ////////////////////////////同步位置偏差---曲线////////////////////////////
            ui->wavePlot->addGraph();    // 同步位置偏差

            ////////////////////////////同步电流偏差---曲线////////////////////////////
            ui->wavePlot->addGraph();    // 同步电流偏差

            ////////////////////////////同步偏差---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 200;
            plotYStepMix = 1;
            plotYStepMax = 960;

            ////////////////////////////同步偏差---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 2000;
            baseXStep = 200;
            baseYRangLower = -300;
            baseYRangUpper = 300;
            baseYStep = 100;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        break;
    case HmiOscServo::OSC_SERVO_LOOP: // 全闭环诊断
        SetYTickPrec(1);

        // 设置X轴名
        ui->xName->setText("X(mm)");

        // 设置Y轴名
        ui->yName1->setText(TR("E(um:"));
        ui->yName2->setText(TR("半闭环与全闭环误差值"));
        ui->yName3->setText(TR(")"));

        ////////////////////////////全闭环诊断---误差值////////////////////////////
        ui->wavePlot->addGraph();    // 误差值

        ////////////////////////////全闭环诊断---缩放限制////////////////////////////
        plotXStepMix = 20;
        plotXStepMax = 20;
        plotYStepMix = 100;
        plotYStepMax = 100;

        ////////////////////////////全闭环诊断---标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 100;
        baseXStep = 10;
        baseYRangLower = -60;
        baseYRangUpper = 60;
        baseYStep = 20;

        optXRangLower = baseXRangLower;
        optXRangUpper = baseXRangUpper;
        optXStep = baseXStep;
        optYRangLower = baseYRangLower;
        optYRangUpper = baseYRangUpper;
        optYStep = baseYStep;
        break;
    case HmiOscServo::OSC_SERVO_ZCOMP:
        SetYTickPrec(1);

        // 设置X轴名
        ui->xName->setText("T(min)");

        // 设置Y轴名
        ui->yName1->setText(TR("Z轴丝杠变形量（um） （"));
        ui->yName2->setText(TR("在线"));
        ui->yName3->setText("/");
        ui->yName4->setText(TR("离线"));
        ui->yName5->setText(")");

        ////////////////////////////Z轴丝杠变形量---误差值////////////////////////////
        ui->wavePlot->addGraph();    // 误差值
        ui->wavePlot->addGraph();   // 历史误差值

        ////////////////////////////Z轴丝杠变形量---缩放限制////////////////////////////
        plotXStepMix = 1;
        plotXStepMax = 36;
        plotYStepMix = 40;
        plotYStepMax = 1000;

        ////////////////////////////Z轴丝杠变形量---标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 1000;
        baseXStep = 10;
        baseYRangLower = -120;
        baseYRangUpper = 120;
        baseYStep = 40;

        optXRangLower = baseXRangLower;
        optXRangUpper = baseXRangUpper;
        optXStep = baseXStep;
        optYRangLower = baseYRangLower;
        optYRangUpper = baseYRangUpper;
        optYStep = baseYStep;
        break;
    case HmiOscServo::OSC_SERVO_SCOMP:
        SetYTickPrec(1);

        // 设置X轴名
        ui->xName->setText("T(min)");

        // 设置Y轴名
        ui->yName1->setText(TR("主轴Z向变形量（um） （"));
        ui->yName2->setText(TR("在线"));
        ui->yName3->setText("/");
        ui->yName4->setText(TR("离线"));
        ui->yName5->setText(")");

        ////////////////////////////主轴Z向变形量---误差值////////////////////////////
        ui->wavePlot->addGraph();    // 误差值
        ui->wavePlot->addGraph();   // 历史误差值

        ////////////////////////////主轴Z向变形量---缩放限制////////////////////////////
        plotXStepMix = 1;
        plotXStepMax = 36;
        plotYStepMix = 40;
        plotYStepMax = 1000;

        ////////////////////////////主轴Z向变形量---标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 1000;
        baseXStep = 10;
        baseYRangLower = -120;
        baseYRangUpper = 120;
        baseYStep = 40;

        optXRangLower = baseXRangLower;
        optXRangUpper = baseXRangUpper;
        optXStep = baseXStep;
        optYRangLower = baseYRangLower;
        optYRangUpper = baseYRangUpper;
        optYStep = baseYStep;
        break;
    case HmiOscServo::OSC_SERVO_COMP_SENSOR:
        SetYTickPrec(1);

        if (waveName == "TempAndThermalCurve" || waveName == "TempAndThermalCurve1"
                || waveName == "TempAndThermalCurve2" || waveName == "TempAndThermalCurve3")
        {
            if(waveName == "TempAndThermalCurve")
            {
                // 设置X轴名
                ui->xName->setText(TR("温度"));
                // 设置Y轴名
                ui->yName1->setText(TR("伸长量K（um）"));
            }
            else if(waveName == "TempAndThermalCurve1")
            {
                // 设置X轴名
                ui->xName->setText(TR("温度"));
                // 设置Y轴名
                ui->yName1->setText(TR("升温段(0.5h)伸长量K（um）"));
            }
            else if(waveName == "TempAndThermalCurve2")
            {
                // 设置X轴名
                ui->xName->setText(TR("温度"));
                // 设置Y轴名
                ui->yName1->setText(TR("恒温段伸长量K（um）"));
            }
            else if(waveName == "TempAndThermalCurve3")
            {
                // 设置X轴名
                ui->xName->setText(TR("温度"));
                // 设置Y轴名
                ui->yName1->setText(TR("降温段伸长量K（um）"));
            }

            ////////////////////////////热变形量---缩放限制////////////////////////////
            plotXStepMix = 0.1;
            plotXStepMax = 36;
            plotYStepMix = 2;
            plotYStepMax = 20500;

            ////////////////////////////热变形量---标尺////////////////////////////
            baseXRangLower = -20;
            baseXRangUpper = 20;
            baseXStep = 5;
            baseYRangLower = -50;
            baseYRangUpper = 50;
            baseYStep = 10;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        else if (waveName == "RTLCurve" || waveName == "RTLCurve1" || waveName == "RTLCurve2" || waveName == "RTLCurve3")
        {
            if(waveName == "RTLCurve")
            {
                // 设置X轴名
                ui->xName->setText(TR("个数"));
                // 设置Y轴名
                ui->yName1->setText(TR("飘移量B（mm）"));
            }
            else if(waveName == "RTLCurve1")
            {
                // 设置X轴名
                ui->xName->setText(TR("个数"));
                // 设置Y轴名
                ui->yName1->setText(TR("升温段(0.5h)飘移量B（mm）"));
            }
            else if(waveName == "RTLCurve2")
            {
                // 设置X轴名
                ui->xName->setText(TR("个数"));
                // 设置Y轴名
                ui->yName1->setText(TR("恒温段飘移量B（mm）"));
            }
            else if(waveName == "RTLCurve3")
            {
                // 设置X轴名
                ui->xName->setText(TR("个数"));
                // 设置Y轴名
                ui->yName1->setText(TR("降温段飘移量B（mm）"));
            }

            ////////////////////////////热变形量---缩放限制////////////////////////////
            plotXStepMix = 0.1;
            plotXStepMax = 10;
            plotYStepMix = 2;
            plotYStepMax = 20500;

            ////////////////////////////热变形量---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 100;
            baseXStep = 10;
            baseYRangLower = -10;
            baseYRangUpper = 10;
            baseYStep = 2;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        else if (waveName == "ThermalCurve" || waveName == "ThermalCurve1" || waveName == "ThermalCurve2" || waveName == "ThermalCurve3")
        {
            // 设置X轴名
            ui->xName->setText(TR("次数"));
            // 设置Y轴名
            if (waveName == "ThermalCurve")
            {
                ui->yName1->setText(TR("变形量（um/m）"));
            }
            else if (waveName == "ThermalCurve1")
            {
                ui->yName1->setText(TR("升温段(0.5h)变形量（um/m）"));
            }
            else if (waveName == "ThermalCurve2")
            {
                ui->yName1->setText(TR("恒温段变形量（um/m）"));
            }
            else if (waveName == "ThermalCurve3")
            {
                ui->yName1->setText(TR("降温段变形量（um/m）"));
            }

            ////////////////////////////热变形量---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 36;
            plotYStepMix = 2;
            plotYStepMax = 20500;

            ////////////////////////////热变形量---标尺////////////////////////////
            baseXRangLower = 1;
            baseXRangUpper = 10;
            baseXStep = 1;
            baseYRangLower = -50;
            baseYRangUpper = 50;
            baseYStep = 10;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        else if (waveName == "SamplCurve" || waveName == "SamplCurve1" || waveName == "SamplCurve2" || waveName == "SamplCurve3")
        {
            // 设置X轴名
            ui->xName->setText(TR("次数"));
            // 设置Y轴名
            if (waveName == "SamplCurve")
            {
                ui->yName1->setText(TR("目标点温度采样（℃）"));
            }
            else if (waveName == "SamplCurve1")
            {
                ui->yName1->setText(TR("升温段(0.5h)目标点温度采样（℃）"));
            }
            else if (waveName == "SamplCurve2")
            {
                ui->yName1->setText(TR("恒温段目标点温度采样（℃）"));
            }
            else if (waveName == "SamplCurve3")
            {
                ui->yName1->setText(TR("降温段目标点温度采样（℃）"));
            }

            ////////////////////////////目标点温度采样---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 36;
            plotYStepMix = 40;
            plotYStepMax = 500;

            ////////////////////////////目标点温度采样---标尺////////////////////////////
            baseXRangLower = 1;
            baseXRangUpper = 10;
            baseXStep = 1;
            baseYRangLower = -20;
            baseYRangUpper = 20;
            baseYStep = 5;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        else if (waveName == "TimeAndTempCurve")      // 时间温度曲线
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(s)");

            // 设置Y轴名
            ui->yName1->setText(TR("温度(℃)"));
//            ui->yName2->setText(TR("指令"));
//            ui->yName3->setText("/");
//            ui->yName4->setText(TR("实际"));
//            ui->yName2->setText(")");


            ////////////////////////////速度环---指令////////////////////////////
            ui->wavePlot->addGraph();    // 指令

            ////////////////////////////速度环---实际////////////////////////////
//            ui->wavePlot->addGraph();    // 实际

            ////////////////////////////速度环---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 100;
            plotYStepMix = 0.1;
            plotYStepMax = 10;

            ////////////////////////////速度环---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10;
            baseXStep = 1;
            baseYRangLower = -3;
            baseYRangUpper = 3;
            baseYStep = 1;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        else if (waveName == "TimeAndPosCurve")      // 时间位移曲线
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(s)");

            // 设置Y轴名
            ui->yName1->setText(TR("位移(um)"));
//            ui->yName2->setText(TR("指令"));
//            ui->yName3->setText("/");
//            ui->yName4->setText(TR("实际"));
//            ui->yName2->setText(")");


            ////////////////////////////速度环---指令////////////////////////////
            ui->wavePlot->addGraph();    // 指令

            ////////////////////////////速度环---实际////////////////////////////
//            ui->wavePlot->addGraph();    // 实际

            ////////////////////////////速度环---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 100;
            plotYStepMix = 0.1;
            plotYStepMax = 10;

            ////////////////////////////速度环---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 10;
            baseXStep = 1;
            baseYRangLower = -3;
            baseYRangUpper = 3;
            baseYStep = 1;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        else if (waveName == "PosAndThermalCurve")      // 位置热误差曲线
        {
            // 设置X轴名
            ui->xName->setText(TR("位置(mm)"));

            // 设置Y轴名
            ui->yName1->setText(TR("热误差(um)"));

            ////////////////////////////位置热误差---缩放限制////////////////////////////
            plotXStepMix = 1;
            plotXStepMax = 1000;
            plotYStepMix = 0.2;
            plotYStepMax = 1000;

            ////////////////////////////位置热误差---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 200;
            baseXStep = 20;
            baseYRangLower = -50;
            baseYRangUpper = 50;
            baseYStep = 10;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        break;
    case HmiOscServo::OSC_SERVO_RASTER:
        if (waveName == "POS")      // 光栅信号波形图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("T(ms)");

            // 设置Y轴名
            ui->yName1->setText(TR("X光栅信号波形图(单位：V)"));


            ////////////////////////////位置环---指令////////////////////////////
            ui->wavePlot->addGraph();    // 正弦信号
            ui->wavePlot->addGraph();   // 余弦信号

            ////////////////////////////位置环---缩放限制////////////////////////////
            plotXStepMix = 5;
            plotXStepMax = 20500;
            plotYStepMix = 0.5;
            plotYStepMax = 1;

            ////////////////////////////位置环---标尺////////////////////////////
            baseXRangLower = 0;
            baseXRangUpper = 1000;
            baseXStep = 100;

            baseYRangLower = -1.5;
            baseYRangUpper = 1.5;
            baseYStep = 0.5;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        if (waveName == "sinAmpl")      // 正弦信号幅值变化图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("P(mm)");

            // 设置Y轴名
            ui->yName1->setText(TR("正弦信号幅值变化图(单位：V)"));

            ui->wavePlot->addGraph();    // 正弦信号

            plotXStepMix = 1;
            plotXStepMax = 500;
            plotYStepMix = 0.001;
            plotYStepMax = 1.0;

            baseXRangLower = 0;
            baseXRangUpper = 2000;
            baseXStep = 200;
            baseYRangLower = 0.7;
            baseYRangUpper = 1.3;
            baseYStep = 0.1;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        if (waveName == "sinZero")      // 正弦信号零点变化图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("P(mm)");

            // 设置Y轴名
            ui->yName1->setText(TR("正弦信号零点变化图(单位：V)"));

            ////////////////////////////位置环---指令////////////////////////////
            ui->wavePlot->addGraph();    // 正弦信号

            plotXStepMix = 1;
            plotXStepMax = 500;
            plotYStepMix = 0.001;
            plotYStepMax = 1.0;

            baseXRangLower = 0;
            baseXRangUpper = 2000;
            baseXStep = 200;
            baseYRangLower = -0.3;
            baseYRangUpper = 0.3;
            baseYStep = 0.1;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        if (waveName == "cosAmpl")      // 余弦信号幅值变化图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("P(mm)");

            // 设置Y轴名
            ui->yName1->setText(TR("余弦信号幅值变化图(单位：V)"));

            ui->wavePlot->addGraph();   // 余弦信号

            plotXStepMix = 1;
            plotXStepMax = 500;
            plotYStepMix = 0.001;
            plotYStepMax = 1;

            baseXRangLower = 0;
            baseXRangUpper = 2000;
            baseXStep = 200;
            baseYRangLower = 0.7;
            baseYRangUpper = 1.3;
            baseYStep = 0.1;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        if (waveName == "cosZero")      // 余弦信号零点变化图
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("P(mm)");

            // 设置Y轴名
            ui->yName1->setText(TR("余弦信号零点变化图(单位：V)"));

            ui->wavePlot->addGraph();   // 余弦信号

            plotXStepMix = 1;
            plotXStepMax = 500;
            plotYStepMix = 0.001;
            plotYStepMax = 1.0;

            baseXRangLower = 0;
            baseXRangUpper = 2000;
            baseXStep = 200;
            baseYRangLower = -0.3;
            baseYRangUpper = 0.3;
            baseYStep = 0.1;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        if (waveName == "phaseDiff")      // 光栅信号相位差
        {
            SetYTickPrec(1);

            // 设置X轴名
            ui->xName->setText("P(mm)");

            // 设置Y轴名
            ui->yName1->setText(TR("正余弦信号相位差(单位：度)"));

            ui->wavePlot->addGraph();    // 相位差

            plotXStepMix = 1;
            plotXStepMax = 500;
            plotYStepMix = 0.1;
            plotYStepMax = 30.0;

            baseXRangLower = 0;
            baseXRangUpper = 2000;
            baseXStep = 200;
            baseYRangLower = 93.0;
            baseYRangUpper = 87.0;
            baseYStep = 1.0;

            optXRangLower = baseXRangLower;
            optXRangUpper = baseXRangUpper;
            optXStep = baseXStep;
            optYRangLower = baseYRangLower;
            optYRangUpper = baseYRangUpper;
            optYStep = baseYStep;
        }
        break;
		case HmiOscServo::OSC_SERVO_ACCE:
			if (waveName == "Speed")    // 速度波形图
			{
				 SetYTickPrec(1);

				 // 设置X轴名
				 ui->xName->setText("T(ms)");

				 // 设置Y轴名
                 ui->yName1->setText(TR("速度(mm/min:"));
				 ui->yName2->setText(TR("指令"));
				 ui->yName3->setText("/");
				 ui->yName4->setText(TR("实际"));
				 ui->yName5->setText(")");


				 ////////////////////////////速度---指令////////////////////////////
				 ui->wavePlot->addGraph();    // 指令

				////////////////////////////速度---实际////////////////////////////
				 ui->wavePlot->addGraph();    // 实际

				////////////////////////////速度---缩放限制////////////////////////////
				 plotXStepMix = 5;
				 plotXStepMax = 20500;
				 plotYStepMix = 15;
				 plotYStepMax = 491520;

				 ////////////////////////////速度---标尺////////////////////////////
				 baseXRangLower = 0;
				 baseXRangUpper = 10000;
				 baseXStep = 1000;
				 baseYRangLower = -6000;
				 baseYRangUpper = 6000;
				 baseYStep = 2000;

				 optXRangLower = baseXRangLower;
				 optXRangUpper = baseXRangUpper;
				 optXStep = baseXStep;
				 optYRangLower = baseYRangLower;
				 optYRangUpper = baseYRangUpper;
				 optYStep = baseYStep;
			 }
			 if (waveName == "Electric") // 电流波形图
			 {
				 SetYTickPrec(1);

				 // 设置X轴名
				 ui->xName->setText("T(ms)");

				 ////////////////////////////加速度---Y轴////////////////////////////
				 // 重新设置Y轴刻度中间有0个分刻度
				 ui->wavePlot->yAxis->setAutoSubTicks(false);
				 ui->wavePlot->yAxis->setSubTickCount(0);

				 ui->wavePlot->yAxis2->setAutoSubTicks(false);
				 ui->wavePlot->yAxis2->setSubTickCount(0);

				 // 设置Y轴名
                 ui->yName1->setText(TR("电流(A)"));


				 ////////////////////////////电流---曲线////////////////////////////
				 ui->wavePlot->addGraph();    // 指令

				 ////////////////////////////电流---缩放限制////////////////////////////
				 plotXStepMix = 5;
				 plotXStepMax = 20500;
				 plotYStepMix = 0.02;
				 plotYStepMax = 20500;

				 ////////////////////////////电流---标尺////////////////////////////
				 baseXRangLower = 0;
				 baseXRangUpper = 10000;
				 baseXStep = 1000;
				 baseYRangLower = -6;
				 baseYRangUpper = 6;
				 baseYStep = 2;

				 optXRangLower = baseXRangLower;
				 optXRangUpper = baseXRangUpper;
				 optXStep = baseXStep;
				 optYRangLower = baseYRangLower;
				 optYRangUpper = baseYRangUpper;
				 optYStep = baseYStep;
			 }
			 break;
		case HmiOscServo::OSC_SERVO_KCOMPEN:
			if (waveName == "Speed")    // 速度波形图
			{
				SetYTickPrec(1);

				// 设置X轴名
				ui->xName->setText("T(ms)");

				// 设置Y轴名
				ui->yName1->setText(TR("速度(mm/min:"));
				ui->yName2->setText(TR("指令"));
				ui->yName3->setText("/");
				ui->yName4->setText(TR("实际"));
				ui->yName5->setText(")");


				////////////////////////////速度---指令////////////////////////////
				ui->wavePlot->addGraph();    // 指令

				////////////////////////////速度---实际////////////////////////////
				ui->wavePlot->addGraph();    // 实际

				////////////////////////////速度---缩放限制////////////////////////////
				plotXStepMix = 5;
				plotXStepMax = 20500;
				plotYStepMix = 15;
				plotYStepMax = 491520;

				////////////////////////////速度---标尺////////////////////////////
				baseXRangLower = 0;
				baseXRangUpper = 10000;
				baseXStep = 1000;
				baseYRangLower = -6000;
				baseYRangUpper = 6000;
				baseYStep = 2000;

				optXRangLower = baseXRangLower;
				optXRangUpper = baseXRangUpper;
				optXStep = baseXStep;
				optYRangLower = baseYRangLower;
				optYRangUpper = baseYRangUpper;
				optYStep = baseYStep;
			}
			if (waveName == "TrackErr") // 跟随误差波形图
			{
				SetYTickPrec(1);

				// 设置X轴名
				ui->xName->setText("T(ms)");

				////////////////////////////跟随误差---Y轴////////////////////////////
				// 重新设置Y轴刻度中间有0个分刻度
				ui->wavePlot->yAxis->setAutoSubTicks(false);
				ui->wavePlot->yAxis->setSubTickCount(0);

				ui->wavePlot->yAxis2->setAutoSubTicks(false);
				ui->wavePlot->yAxis2->setSubTickCount(0);

				// 设置Y轴名
				ui->yName1->setText(TR("跟随误差(um)"));

				////////////////////////////跟随误差---曲线////////////////////////////
				ui->wavePlot->addGraph();    // 指令

				////////////////////////////跟随误差---缩放限制////////////////////////////
				plotXStepMix = 5;
				plotXStepMax = 20500;
				plotYStepMix = 0.3;// 30;
				plotYStepMax = 122880;

				////////////////////////////跟随误差---标尺////////////////////////////
				baseXRangLower = 0;
				baseXRangUpper = 10000;
				baseXStep = 1000;
				baseYRangLower = -3000;
				baseYRangUpper = 3000;
				baseYStep = 1000;

				optXRangLower = baseXRangLower;
				optXRangUpper = baseXRangUpper;
				optXStep = baseXStep;
				optYRangLower = baseYRangLower;
				optYRangUpper = baseYRangUpper;
				optYStep = baseYStep;
			}
			break;
		case HmiOscServo::OSC_SERVO_INERTIA:      //惯量辨识
			if (waveName == "Speed")    // 速度波形图
			{
				SetYTickPrec(1);//设置Y轴精度，1为整数，一位小数为10；两位小数为100，依此类推

				// 设置X轴名
				ui->xName->setText("T(ms)");

				// 设置Y轴名
				ui->yName1->setText(TR("速度(mm/min:"));
				ui->yName2->setText(TR("指令"));
				ui->yName3->setText("/");
				ui->yName4->setText(TR("实际"));
				ui->yName5->setText(")");


				////////////////////////////速度---指令////////////////////////////
				ui->wavePlot->addGraph();    // 指令

			   ////////////////////////////速度---实际////////////////////////////
				ui->wavePlot->addGraph();    // 实际

			   ////////////////////////////速度---缩放限制////////////////////////////
				plotXStepMix = 1;//5
				plotXStepMax = 20500;
				plotYStepMix = 1;//15
				plotYStepMax = 991520;//491520

				////////////////////////////速度---标尺////////////////////////////
				baseXRangLower = 0;
				baseXRangUpper = 2000;
				baseXStep = 200;
				baseYRangLower = -6000;
				baseYRangUpper = 6000;
				baseYStep = 2000;

				optXRangLower = baseXRangLower;
				optXRangUpper = baseXRangUpper;
				optXStep = baseXStep;
				optYRangLower = baseYRangLower;
				optYRangUpper = baseYRangUpper;
				optYStep = baseYStep;
			}
			if (waveName == "Electric") // 电流波形图
			{
				SetYTickPrec(1);

				// 设置X轴名
				ui->xName->setText("T(ms)");
				

				////////////////////////////加速度---Y轴////////////////////////////
				// 重新设置Y轴刻度中间有0个分刻度
				ui->wavePlot->yAxis->setAutoSubTicks(false);
				ui->wavePlot->yAxis->setSubTickCount(0);

				ui->wavePlot->yAxis2->setAutoSubTicks(false);
				ui->wavePlot->yAxis2->setSubTickCount(0);

				// 设置Y轴名
				ui->yName1->setText(TR("电流(A)"));


				////////////////////////////电流---曲线////////////////////////////
				ui->wavePlot->addGraph();    // 指令

				////////////////////////////电流---缩放限制////////////////////////////
				plotXStepMix = 5;
				plotXStepMax = 20500;
				plotYStepMix = 0.02;
				plotYStepMax = 20500;

				////////////////////////////电流---标尺////////////////////////////
				baseXRangLower = 0;
				baseXRangUpper = 2000;
				baseXStep = 200;
				baseYRangLower = -6;
				baseYRangUpper = 6;
				baseYStep = 2;

				optXRangLower = baseXRangLower;
				optXRangUpper = baseXRangUpper;
				optXStep = baseXStep;
				optYRangLower = baseYRangLower;
				optYRangUpper = baseYRangUpper;
				optYStep = baseYStep;
			}
			break;
    default:
        break;
    } 

    XAxisSetTickParm(baseXRangLower, baseXRangUpper, baseXStep);
    YAxisSetTickParm(baseYRangLower, baseYRangUpper, baseYStep);

    ui->wavePlot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
}

void OscWave::SetXRange(fBit64 rangLower, fBit64 rangUpper, fBit64 rangStep)
{
    QVector<double> xrange;
    for(Bit32 i = 0; i <= 10; i++)
    {
        if(i%2 == 0)
        {
            xrange.append(rangLower + i * rangStep);
        }
    }

    baseXRangLower = rangLower;
    baseXRangUpper = rangUpper;
    baseXStep = rangStep;

    optXRangLower = rangLower;
    optXRangUpper = rangUpper;
    optXStep = rangStep;

    if(rangLower > rangUpper) // x轴顺序排列
    {
        ui->wavePlot->xAxis->setRangeReversed(true);
        ui->wavePlot->xAxis2->setRangeReversed(true);
    }
    else
    {
        ui->wavePlot->xAxis->setRangeReversed(false);
        ui->wavePlot->xAxis2->setRangeReversed(false);
    }

    XAxisSetTickParm(optXRangLower, optXRangUpper, optXStep);
    ui->wavePlot->xAxis->setAutoTicks(false);
    ui->wavePlot->xAxis2->setAutoTicks(false);
    ui->wavePlot->xAxis->setTickVector(xrange);
    ui->wavePlot->xAxis2->setTickVector(xrange);
    ui->wavePlot->xAxis->setTickLabels(true);
    ui->wavePlot->replot();
}

void OscWave::SetYRange(fBit64 rangLower, fBit64 rangUpper, fBit64 rangStep)
{
    baseYRangLower = rangLower;
    baseYRangUpper = rangUpper;
    baseYStep = rangStep;

    optYRangLower = rangLower;
    optYRangUpper = rangUpper;
    optYStep = rangStep;
    YAxisSetTickParm(optYRangLower, optYRangUpper, optYStep);
    ui->wavePlot->yAxis2->setTickLength(0);
    ui->wavePlot->replot();
}

void OscWave::SetXMaxMin(fBit64 max, fBit64 min)
{
    plotXStepMix = min;
    plotXStepMax = max;
}

void OscWave::SetYMaxMin(fBit64 max, fBit64 min)
{
    plotYStepMix = min;
    plotYStepMax = max;
}

void OscWave::SetZoomXUp()
{
    fBit64 lower = ui->wavePlot->xAxis->rangeLower();
    fBit64 upper = ui->wavePlot->xAxis->rangeUpper();
    fBit64 tickStep = ui->wavePlot->xAxis->tickStep();

    if (tickStep > plotXStepMix)
    {
        if (Bit32(tickStep / 2.0 * 100 + 0.001) > Bit32(plotXStepMix * 100))
        {
            tickStep = Bit32(tickStep / 2.0 + 0.001);
        }
        else
        {
            tickStep = plotXStepMix;
        }

        upper = lower + tickStep * xStepNum;

        ui->wavePlot->xAxis->setRange(lower, upper);
        ui->wavePlot->xAxis->setTickStep(tickStep);

        ui->wavePlot->xAxis2->setRange(lower, upper);
        ui->wavePlot->xAxis2->setTickStep(tickStep);

        ui->wavePlot->replot();
    }
}

void OscWave::SetZoomXDn()
{
    fBit64 lower = ui->wavePlot->xAxis->rangeLower();
    fBit64 upper = ui->wavePlot->xAxis->rangeUpper();
    fBit64 tickStep = ui->wavePlot->xAxis->tickStep();

    if (tickStep < plotXStepMax)
    {
        if (Bit32(tickStep * 2.0 * 100 + 0.001) < Bit32(plotXStepMax * 100))
        {
            tickStep = Bit32(tickStep * 2.0 + 0.001);
        }
        else
        {
            tickStep = plotXStepMax;
        }

        upper = lower + tickStep * xStepNum;

        ui->wavePlot->xAxis->setRange(lower, upper);
        ui->wavePlot->xAxis->setTickStep(tickStep);

        ui->wavePlot->xAxis2->setRange(lower, upper);
        ui->wavePlot->xAxis2->setTickStep(tickStep);

        ui->wavePlot->replot();
    }
}

void OscWave::SetZoomYUp()
{
    fBit64 lower = ui->wavePlot->yAxis->rangeLower();
    fBit64 upper = ui->wavePlot->yAxis->rangeUpper();
    fBit64 tickStep = ui->wavePlot->yAxis->tickStep();

    if (tickStep > plotYStepMix)
    {
        if (uBit32(tickStep / 2.0 * 100 + 0.001) > uBit32(plotYStepMix * 100))
        {
            tickStep = uBit32(tickStep / 2.0 * 100 + 0.001) / 100.0;
        }
        else
        {
            tickStep = plotYStepMix;
        }


        if(oscType == HmiOscServo::OSC_SERVO_FREQ && waveName == "FreqDomain")
        {
            upper = tickStep * yStepNum;
            lower = 0;
        }
        else
        {
            upper = tickStep * yStepNum / 2;
            lower = -upper;
        }

        ui->wavePlot->yAxis->setRange(lower, upper);
        ui->wavePlot->yAxis->setTickStep(tickStep);

        ui->wavePlot->yAxis2->setRange(lower, upper);
        ui->wavePlot->yAxis2->setTickStep(tickStep);

        ui->wavePlot->replot();
    }
}

void OscWave::SetZoomYDn()
{
    fBit64 lower = ui->wavePlot->yAxis->rangeLower();
    fBit64 upper = ui->wavePlot->yAxis->rangeUpper();
    fBit64 tickStep = ui->wavePlot->yAxis->tickStep();

    if (tickStep < plotYStepMax)
    {
        if (uBit32(tickStep * 2.0 * 100 + 0.001) < uBit32(plotYStepMax * 100))
        {
            tickStep = uBit32(tickStep * 2.0 * 100 + 0.001) / 100.0;
        }
        else
        {
            tickStep = plotYStepMax;
        }

        if(oscType == HmiOscServo::OSC_SERVO_FREQ && waveName == "FreqDomain")
        {
            upper = tickStep * yStepNum;
            lower = 0;
        }
        else
        {
            upper = tickStep * yStepNum / 2;
            lower = -upper;
        }

        ui->wavePlot->yAxis->setRange(lower, upper);
        ui->wavePlot->yAxis->setTickStep(tickStep);

        ui->wavePlot->yAxis2->setRange(lower, upper);
        ui->wavePlot->yAxis2->setTickStep(tickStep);

        ui->wavePlot->replot();
    }
}

void OscWave::SetZoomXLeft()
{
    fBit64 lower = ui->wavePlot->xAxis->rangeLower();
    fBit64 upper = ui->wavePlot->xAxis->rangeUpper();
    fBit64 tickStep = ui->wavePlot->xAxis->tickStep();

    if (lower - tickStep < plotXStepMix)
    {
        fBit64 space = upper - lower;
        lower = plotXStepMix;
        upper = lower + space;
    }
    else
    {
        lower = lower - tickStep;
        upper = upper - tickStep;
    }

    ui->wavePlot->xAxis->setRange(lower, upper);
    ui->wavePlot->xAxis2->setRange(lower, upper);

    ui->wavePlot->replot();
}

void OscWave::SetZoomXRight()
{
    fBit64 lower = ui->wavePlot->xAxis->rangeLower();
    fBit64 upper = ui->wavePlot->xAxis->rangeUpper();
    fBit64 tickStep = ui->wavePlot->xAxis->tickStep();

    if (upper + tickStep > plotXStepMax)
    {
        fBit64 space = upper - lower;
        upper = plotXStepMax;
        lower = upper - space;
    }
    else
    {
        lower = lower + tickStep;
        upper = upper + tickStep;
    }

    ui->wavePlot->xAxis->setRange(lower, upper);
    ui->wavePlot->xAxis2->setRange(lower, upper);

    ui->wavePlot->replot();
}

void OscWave::CalSelArea()
{
    fBit64 lowerX = ui->wavePlot->xAxis->rangeLower();
    fBit64 lowerY = ui->wavePlot->yAxis->rangeLower();
    fBit64 stepX = ui->wavePlot->xAxis->tickStep() / 2;
    fBit64 stepY = ui->wavePlot->yAxis->tickStep() / 2;

    /////////////////////////////////////////////////
    //      0_______________1
    //     4|               |
    //      |               |
    //      |_______________|
    //     3                 2
    /////////////////////////////////////////////////

    graphSelX[0] = lowerX + stepX * selAreaPosX;
    graphSelY[0] = lowerY + stepY * selAreaPosY;

    graphSelX[1] = graphSelX[0] + stepX * selAreaLength;
    graphSelY[1] = graphSelY[0];

    graphSelX[2] = graphSelX[1];
    graphSelY[2] = graphSelY[1] - stepY * selAreaHeight;

    graphSelX[3] = graphSelX[2] - stepX * selAreaLength;
    graphSelY[3] = graphSelY[2];

    graphSelX[4] = graphSelX[0];
    graphSelY[4] = graphSelY[0];

    if (m_vecpGraphSel.count() > 0 && m_vecpGraphSel.at(0) != NULL)
    {
        m_vecpGraphSel.at(0)->setData(graphSelX, graphSelY);
    }
}

void OscWave::ShowSelArea()
{
    CalSelArea();
}

void OscWave::HideSelArea()
{
    if (m_vecpGraphSel.count() > 0 && m_vecpGraphSel.at(0) != NULL)
    {
        m_vecpGraphSel.at(0)->clearData();
    }
}

void OscWave::XAxisGetTickParm(fBit64 *lower, fBit64 *upper, fBit64 *step)
{
    *lower = ui->wavePlot->xAxis->rangeLower();
    *upper = ui->wavePlot->xAxis->rangeUpper();
    *step = ui->wavePlot->xAxis->tickStep();
}

void OscWave::XAxisSetTickParm(fBit64 lower, fBit64 upper, fBit64 step)
{
    ui->wavePlot->xAxis->setRange(lower, upper);
//    ui->wavePlot->xAxis->setTickStep(step * 2);
    ui->wavePlot->xAxis2->setRange(lower, upper);
    ui->wavePlot->xAxis2->setTickStep(step);

    if (oscType == HmiOscServo::OSC_SERVO_COMP_SENSOR)
    {
        ui->wavePlot->xAxis->setTickStep(step);
        ui->wavePlot->xAxis->setSubTickCount(0);
    }
    else
    {
        ui->wavePlot->xAxis->setTickStep(step * 2);
        ui->wavePlot->xAxis->setSubTickCount(1);
    }

    ui->wavePlot->xAxis2->setSubTickCount(0);
}

void OscWave::YAxisGetTickParm(fBit64 *lower, fBit64 *upper, fBit64 *step)
{
    *lower = ui->wavePlot->yAxis->rangeLower();
    *upper = ui->wavePlot->yAxis->rangeUpper();
    *step = ui->wavePlot->yAxis->tickStep();
}

void OscWave::YAxisSetTickParm(fBit64 lower, fBit64 upper, fBit64 step)
{
    ui->wavePlot->yAxis->setRange(lower, upper);
    ui->wavePlot->yAxis->setTickStep(step);
    if (oscType == HmiOscServo::OSC_SERVO_RASTER)
    {
        ui->wavePlot->yAxis2->setRange(lower * 1800, upper * 1800);
        ui->wavePlot->yAxis2->setTickStep(step * 1800);
    }
}

void OscWave::SetYAxis2Visible(bool visible)
{
    m_nShowYAxis2 = visible;
    fBit64 lower = 0.0;
    fBit64 upper = 0.0;
    fBit64 step = 0.0;
    YAxisGetTickParm(&lower, &upper, &step);
    YAxisSetTickParm(lower, upper, step);

    ui->wavePlot->yAxis2->setVisible(visible);
    ui->wavePlot->replot();
}

void OscWave::WaveReplot()
{
//    ui->wavePlot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectPlottables);
//    ui->wavePlot->axisRect()->setupFullAxesBox();
//    ui->wavePlot->rescaleAxes();
    ui->wavePlot->replot();
}

QCustomPlot *OscWave::GetWavePlot()
{
    return ui->wavePlot;
}

Bit32 OscWave::MoveSelArea(OscSelMoveDir dir)
{
    fBit64 lowerX = ui->wavePlot->xAxis->rangeLower();
    fBit64 stepX = ui->wavePlot->xAxis->tickStep() / 2;
    fBit64 xValSelLeft = lowerX + stepX * selAreaPosX; // 选框左边对应的X坐标
    fBit64 xValSelRight = xValSelLeft + stepX * selAreaLength; // 选框右边对应的X坐标
    Bit32 xValCount = ui->wavePlot->graph(0)->data()->keys().count();

    fBit64 lowerY = ui->wavePlot->yAxis->rangeLower();
    fBit64 stepY = ui->wavePlot->yAxis->tickStep() / 2;
    fBit64 yValSelUp = lowerY + stepY * selAreaPosY; // 选框上边对应的Y坐标
    fBit64 yValSelDown = yValSelUp - stepY * selAreaHeight; // 选框下边对应的Y坐标

    fBit64 xValMax = 0;
    fBit64 xValMin = 0;

    xValCount = ui->wavePlot->graph(0)->data()->keys().count();

    if (xValCount - 1 >= 0)
    {
        xValMin = 0;
        xValMax =(fBit64)(ui->wavePlot->graph(0)->data()->keys().at(xValCount - 1)); // 取最后一个X值
    }

    if (oscType == HmiOscServo::OSC_SERVO_RASTER)
    {
        xValMin = baseXRangLower;
        xValMax = baseXRangUpper;

        yMax = baseYRangUpper;
        yMin = baseYRangLower;
    }

    if (dir == OSC_SEL_MOVE_LEFT)  // left
    {
        if (HNC_DoubleCompare(lowerX, xValSelLeft) < 0)
		{
			selAreaPosX--;
		}
        else if (HNC_DoubleCompare(xValSelLeft, xValMin) > 0)
        {
            ui->wavePlot->xAxis->moveRange(-stepX);
        }
		else
		{
			return -1;
		}
    }
    else if (dir == OSC_SEL_MOVE_RIGHT)  // right
    {
        if ((selAreaPosX + selAreaLength) < allLength)
        {
            selAreaPosX++;
        }
        else if (xValSelRight < xValMax)
        { // 选框不动，图像左移
            ui->wavePlot->xAxis->moveRange(stepX);
        }
        else
        {
            return -1;
        }
    }
    else if (dir == OSC_SEL_MOVE_UP)   // up
    {
        if (selAreaPosY < allHeight)
        {
            selAreaPosY++;
        }
        else if (yValSelUp < yMax)
        {
            ui->wavePlot->yAxis->moveRange(stepY);
        }
        else
        {
            return -1;
        }
    }
    else if (dir == OSC_SEL_MOVE_DOWN)  // down
    {
        if ((selAreaPosY - selAreaHeight) > 0)
        {
            selAreaPosY--;
        }
        else if (yValSelDown > yMin)
        {
            ui->wavePlot->yAxis->moveRange(-stepY);
        }
        else
        {
            return -1;
        }
    }
    ShowSelArea();
    ui->wavePlot->replot();
    return 0;
}

void OscWave::SelAreaDataRestore()
{
    allLength = xStepNum * 2;    // 显示区域总长度
    allHeight = yStepNum * 2;    // 显示区域总宽度
    selAreaLength = 2;// 选择框长度
    selAreaHeight = 4;// 选择框宽度
    selAreaPosX = (allLength - selAreaLength) / 2;  // 选择框左上角X坐标
    selAreaPosY = allHeight - (allHeight - selAreaHeight) / 2;  // 选择框右上角Y坐标
}

void OscWave::PlotRestore()
{
    ui->wavePlot->xAxis->rescale(false);
    ui->wavePlot->xAxis->setAutoTickStep(false);

    RasterSpecialSetWave();

    XAxisSetTickParm(optXRangLower, optXRangUpper, optXStep);
    YAxisSetTickParm(optYRangLower, optYRangUpper, optYStep);

    ui->wavePlot->replot();
}

// 函数原型来源于标准8型的oscservo_calc_maxmin
void OscWave::AutoRangParm()
{
    Bit32 startPos = 0;
    Bit32 endPos = oscproc_get_total();
    Bit32 client = HmiOscServo::oscservo_get_sampl_client();
    Bit32 chn = 0;
    Bit32 type = 0;
    Bit32 axNo = 0;
    Bit32 offset = 0;
    Bit32 len = 0;
    fBit64 cof = 0, off = 0;
    Bit64 min  = 0, max = 0;
    fBit64 fMax0 = 0.0, fMin0 = 0.0;
    fBit64 fMax1 = 0.0, fMin1 = 0.0;
    fBit64 fMax = 0.0, fMin = 0.0;
    fBit64 fTmp = 0.0;
    Bit32 tmp = 0;

    fBit64 baseStepGrid = baseYStep;

    fBit64 newUpperY = 0.0, newLowerY = 0.0;
    fBit64 newStepY = 0.0;

    if (autoRangParmFlag == false)
    {
        autoRangParmFlag = true;
    }

    if(endPos >= SMPL_DATA_NUM)
    {
        endPos = SMPL_DATA_NUM - 1;
    }

    switch(oscType)
    {
    case HmiOscServo::OSC_SERVO_SPE:    // 速度环
        if (waveName == "SPD")      // 速度波形图
        {
            chn = 0;
            HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
            cof = smpl_calc_spd_coef(axNo, 4)*60000.0; // 指令速度：mm/min
            off = 0;
            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax0 = cof * max + off;
            fMin0 = cof * min + off;

            chn = 1;
            HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
            cof = smpl_calc_spd_coef(axNo, 5)*60000.0; // 实际速度：mm/min
            off = 0;
            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax1 = cof * max + off;
            fMin1 = cof * min + off;

            fMax = fMax0;
            fMin = fMin0;
            if ((Bit32)fMax0 < (Bit32)fMax1)
            {
                fMax = fMax1;
            }
            if ((Bit32)fMin0 > (Bit32)fMin1)
            {
                fMin = fMin1;
            }
        }
        else if (waveName == "ACC")     // 加速度波形图
        {
            chn = 1;
            HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
            cof = HmiOscServo::SmplCalcSpddiffCoef(axNo, 5); // * ACCVEL_MUTI2INT;
			off = 0;
            HmiOscServo::SmplCalcDiffMaxmin(chn, &max, &min, startPos, endPos);

            fMax = cof * max + off;
            fMin = cof * min + off;
        }
        break;
    case HmiOscServo::OSC_SERVO_POS:
        if (waveName == "POS")      // 位置波形图
        {
            chn = 0;
            HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
            cof = smpl_calc_dist_coef(axNo) * 1000.0;
            HNC_AxisGetValue(HNC_AXIS_WCS_ZERO, axNo, &tmp);
            off = -cof * tmp;
            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax0 = cof * max + off;
            fMin0 = cof * min + off;

            chn = 1;
            HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
            cof = smpl_calc_dist_coef(axNo) * 1000.0;
            HNC_AxisGetValue(HNC_AXIS_WCS_ZERO, axNo, &tmp);
            off = -cof * tmp;
            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax1 = cof * max + off;
            fMin1 = cof * min + off;

            fMax = fMax0;
            fMin = fMin0;
            if ((Bit32)fMax0 < (Bit32)fMax1)
            {
                fMax = fMax1;
            }
            if ((Bit32)fMin0 > (Bit32)fMin1)
            {
                fMin = fMin1;
            }
        }
        else if (waveName == "TrackErr")     // 跟随误差波形图
        {
            chn = 2;
            cof = fabs(smpl_calc_follow_err_coef(axNo))*1000.0; // um
            off = 0;

            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax = cof * max + off;
            fMin = cof * min + off;
        }
        break;
    case HmiOscServo::OSC_SERVO_TOOL:
        fMax = 1.0;
        fMin = 0.0;
        break;
    case HmiOscServo::OSC_SERVO_SPINDLE:
        chn = 1;
        HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
        cof = HmiOscServo::smpl_calc_tapvel_coef(chn, client) * 60000.0;
        off = 0;
        HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
        fMax = cof * max + off;
        fMin = cof * min + off;
        if (HNC_DoubleCompare(fMax, fMin) < 0)  // 解决bug#6177,当最小值大于最大值时，则交替最大值与最小值
        {
            qSwap(fMax, fMin);
        }
        break;
    case HmiOscServo::OSC_SERVO_FREQTAP:
        chn = 0;
        cof = smpl_calc_dist_coef(0) * 1000.0 * 1000.0;
        off = 0;
        HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
        fMax = cof * max + off;
        fMin = cof * min + off;
        break;
	case HmiOscServo::OSC_SERVO_BEARTAP:
		chn = 0;
		cof = smpl_calc_dist_coef(0) * 1000.0 * 1000.0;
		off = 0;
		HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
		fMax = cof * max + off;
		fMin = cof * min + off;
		break;
    case HmiOscServo::OSC_SERVO_TAP:
        if (waveName == "SyncErr")      // 刚性攻丝-同步误差主界面波形图
        {
            Bit64 *chn_addr1 = NULL, *chn_addr2 = NULL;
            Bit32 i = 0, smpl_period = oscproc_get_smpl_period(), tmp = 0;
            fBit64 fdelta = 0.0;
            fBit64 tmpThread = 0.0;
            Bit32 ch = ActiveChan();
            Bit32 macType = GetOscprocCurCh(ch);

            if (macType == 1 && HmiOscServo::s_Conf[ch].stTapConf.tapDir == 1)
            {
                tmpThread = -(HmiOscServo::s_Conf[ch].stTapConf.thread_lead);
            }
            else
            {
                tmpThread = HmiOscServo::s_Conf[ch].stTapConf.thread_lead;
            }
            HNC_SamplGetConfig(client, 0, type, axNo, offset, len);
            chn_addr1 = oscproc_get_smpldata(0);
            chn_addr2 = oscproc_get_smpldata(1);
            //nc_assert(chn_addr1 != NULL && chn_addr2 != NULL);
            for (fTmp = 0, fdelta = 0, i = 0; i < startPos; i++)
            {
                fTmp = HmiOscServo::SmplCalcTapvelCoef(1, tmpThread, client)*chn_addr2[i] - smpl_calc_spd_coef(axNo,5)*chn_addr1[i]; // 速度差
                fdelta = fTmp * smpl_period * 1000 + fdelta; // um
            }
            tmp = i;
            fTmp = HmiOscServo::SmplCalcTapvelCoef(1, tmpThread, client)*chn_addr2[tmp] - smpl_calc_spd_coef(axNo,5)*chn_addr1[tmp]; // 速度差
            fdelta = fTmp * smpl_period * 1000 + fTmp; // um
            fMax = fMin = fdelta;
            for (tmp = i+1; tmp < endPos; tmp++)
            {
                fTmp = HmiOscServo::SmplCalcTapvelCoef(1, tmpThread, client)*chn_addr2[tmp] - smpl_calc_spd_coef(axNo,5)*chn_addr1[tmp];
                fdelta = fTmp * smpl_period * 1000 + fdelta;
                if (fdelta > fMax)
                {
                    fMax = fdelta;
                }
                else if (fdelta < fMin)
                {
                    fMin = fdelta;
                }
            }
        }
        else if (waveName == "SPE")     // 刚性攻丝-主界面-速度波形图
        {
            chn = 0;
            HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
            cof = smpl_calc_spd_coef(axNo, 5) * 60000.0; // 实际速度：mm/min
            off = 0;
            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax0 = cof * max + off;
            fMin0 = cof * min + off;

            chn = 1;
            HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
            cof = HmiOscServo::smpl_calc_tapvel_coef(1, client) * 60000.0;
            off = 0;
            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax1 = cof * max + off;
            fMin1 = cof * min + off;
            if (HNC_DoubleCompare(fMax1, fMin1) < 0)
            {
                fBit64 swValue = fMax1;
                fMax1 = fMin1;
                fMin1 = swValue;
            }

            fMax = fMax0;
            fMin = fMin0;
            if ((Bit32)fMax0 < (Bit32)fMax1)
            {
                fMax = fMax1;
            }
            if ((Bit32)fMin0 > (Bit32)fMin1)
            {
                fMin = fMin1;
            }
        }
    case HmiOscServo::OSC_SERVO_CUSTOM:
        if(waveName == "ORG")
        {
            chn = 0;
            cof = 1; // 原始值
            off = 0;
            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax = cof * max + off;
            fMin = cof * min + off;
        }
        else if(waveName == "SHIFT")
        {
            chn = 0;
            cof = HmiOscServo::s_Conf[chn].stCustomConf.convCof / HmiOscServo::s_Conf[chn].stCustomConf.convBase; // 换算值
            off = HmiOscServo::s_Conf[chn].stCustomConf.convOff;
            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax = cof * max + off;
            fMin = cof * min + off;
        }
        break;
    case HmiOscServo::OSC_SERVO_SYNC:
        if(waveName == "CurrentFlow")
        {
            HNC_SamplGetConfig(client, 1, type, axNo, offset, len);
            cof = HmiOscServo::SmplCalcLoadCoef(1, client) / 1000.0;
            off = 0;
            HmiOscServo::SmplCalcMaxmin(1, &max, &min, 0, oscproc_get_total());
            fMax0 = cof * max + off;
            fMin0 = cof * min + off;

            HNC_SamplGetConfig(client, 2, type, axNo, offset, len);
            cof = HmiOscServo::SmplCalcLoadCoef(2, client) / 1000.0;
            off = 0;
            HmiOscServo::SmplCalcMaxmin(2, &max, &min, 0, oscproc_get_total());
            fMax1 = cof * max + off;
            fMin1 = cof * min + off;

            fMax = fMax0;
            fMin = fMin0;

            if ((Bit32)fMax0 < (Bit32)fMax1)
            {
                fMax = fMax1;
            }
            if ((Bit32)fMin0 > (Bit32)fMin1)
            {
                fMin = fMin1;
            }
        }
        else if(waveName == "ERR")
        {
            HNC_SamplGetConfig(client, 3, type, axNo, offset, len);
            cof = smpl_calc_dist_coef(axNo) * 1000.0;
            off = 0;
            HmiOscServo::SmplCalcSyncErrMaxmin(3, 4, &max, &min, 0, oscproc_get_total());
            fMax0 = cof * max + off;
            fMin0 = cof * min + off;

            HNC_SamplGetConfig(client, 1, type, axNo, offset, len);
            cof = smpl_calc_dist_coef(axNo) * 1000.0;
            off = 0;
            HmiOscServo::SmplCalcSyncErrMaxmin(1, 2, &max, &min, 0, oscproc_get_total());
            fMax1 = cof * max + off;
            fMin1 = cof * min + off;

            fMax = fMax0;
            fMin = fMin0;

            if ((Bit32)fMax0 < (Bit32)fMax1)
            {
                fMax = fMax1;
            }
            if ((Bit32)fMin0 > (Bit32)fMin1)
            {
                fMin = fMin1;
            }
        }
        break;
    case HmiOscServo::OSC_SERVO_FREQ:
        if(waveName == "CurrentFlow")
        {
            cof = 1.0;
            off = 0.0;
            HmiOscServo::SmplCalcMaxmin(0, &max, &min, 0, oscproc_get_total());
            fMax = cof * max + off;
            fMin = cof * min + off;
        }
        else if(waveName == "FreqDomain")
        {
            cof = 1.0;
            off = 0.0;
            HmiOscServo::SmplCalcFreqMaxmin(0, &max, &min, 0, oscproc_get_total());
            fMax = cof * max + off;
            fMin = cof * min + off;
        }
		break;
	case HmiOscServo::OSC_SERVO_ACCE:
		if (waveName == "Speed")      // 速度波形图
		{
			chn = 0;
			HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
			cof = smpl_calc_spd_coef(axNo, 4)*60000.0; // 指令速度：mm/min
			off = 0;
			HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
			fMax0 = cof * max + off;
			fMin0 = cof * min + off;

			chn = 1;
			HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
			cof = smpl_calc_spd_coef(axNo, 5)*60000.0; // 实际速度：mm/min
			off = 0;
			HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
			fMax1 = cof * max + off;
			fMin1 = cof * min + off;

			fMax = fMax0;
			fMin = fMin0;
			if ((Bit32)fMax0 < (Bit32)fMax1)
			{
				fMax = fMax1;
			}
			if ((Bit32)fMin0 > (Bit32)fMin1)
			{
				fMin = fMin1;
			}
		}
		else if (waveName == "Electric")     // 电流波形图
		{
			HNC_SamplGetConfig(client, 2, type, axNo, offset, len);
			cof = HmiOscServo::SmplCalcLoadCoef(2, client) / 1000.0;
			off = 0;
			HmiOscServo::SmplCalcMaxmin(2, &max, &min, 0, oscproc_get_total());
			fMax = cof * max + off;
			fMin = cof * min + off;
		}
		break;
	case HmiOscServo::OSC_SERVO_KCOMPEN:
		if (waveName == "Speed")      // 速度波形图
		{
			chn = 0;
			HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
			cof = smpl_calc_spd_coef(axNo, 4)*60000.0; // 指令速度：mm/min
			off = 0;
			HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
			fMax0 = cof * max + off;
			fMin0 = cof * min + off;

			chn = 1;
			HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
			cof = smpl_calc_spd_coef(axNo, 5)*60000.0; // 实际速度：mm/min
			off = 0;
			HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
			fMax1 = cof * max + off;
			fMin1 = cof * min + off;

			fMax = fMax0;
			fMin = fMin0;
			if ((Bit32)fMax0 < (Bit32)fMax1)
			{
				fMax = fMax1;
			}
			if ((Bit32)fMin0 > (Bit32)fMin1)
			{
				fMin = fMin1;
			}
		}
		else if (waveName == "TrackErr")     // 跟随误差波形图
		{
			cof = fabs(smpl_calc_follow_err_coef(axNo))*1000.0; // um
			off = 0;

			HmiOscServo::SmplCalcMaxmin(2, &max, &min, startPos, endPos);
			fMax = cof * max + off;
			fMin = cof * min + off;
		}
		break;
	case HmiOscServo::OSC_SERVO_INERTIA:
		if (waveName == "Speed")      // 速度波形图
		{
			chn = 0;
			HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
			cof = smpl_calc_spd_coef(axNo, 4)*60000.0; // 指令速度：mm/min
			off = 0;
			HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
			fMax0 = cof * max + off;
			fMin0 = cof * min + off;

			chn = 1;
			HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
			cof = smpl_calc_spd_coef(axNo, 5)*60000.0; // 实际速度：mm/min
			off = 0; 
			HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
			fMax1 = cof * max + off;
			fMin1 = cof * min + off;

			fMax = fMax0;
			fMin = fMin0;
			if ((Bit32)fMax0 < (Bit32)fMax1)
			{
				fMax = fMax1;
			}
			if ((Bit32)fMin0 > (Bit32)fMin1)
			{
				fMin = fMin1;
			}
		}
		else if (waveName == "Electric")     // 电流波形图
		{
			HNC_SamplGetConfig(client, 2, type, axNo, offset, len);
			cof = HmiOscServo::SmplCalcLoadCoef(2, client) / 1000.0;
			off = 0;
			HmiOscServo::SmplCalcMaxmin(2, &max, &min, 0, oscproc_get_total());
			fMax = cof * max + off;
			fMin = cof * min + off;
		}
		break;
    default:
        break;
    }

    fBit64 tmp1 = 0;
    fBit64 tmp2 = 0;

    if (HNC_DoubleCompare(fMax, fMin) < 0)
    {
        fBit64 swValue = fMax;
        fMax = fMin;
        fMin = swValue;
    }

    if (HNC_DoubleCompare(fMax, 0) >= 0 && HNC_DoubleCompare(fMin, 0) >= 0)
    {
        fTmp = fMax * 2 / yStepNum;
    }
    else if (HNC_DoubleCompare(fMax, 0) < 0 && HNC_DoubleCompare(fMin, 0) < 0)
    {
        fTmp = (0 - fMin) * 2 / yStepNum;
    }
    else
    {
        tmp1 = fMax * 2 / yStepNum;
        tmp2 = (0 - fMin) * 2 / yStepNum;

        if (HNC_DoubleCompare(tmp1, tmp2) >= 0)
        {
            fTmp = tmp1;
        }
        else
        {
            fTmp = tmp2;
        }
    }

    yMax = fMax + fTmp; // 上下保留一个间距，防止上下数据过小被限制
    yMin = fMin - fTmp;

	if (HNC_DoubleCompare(0, fTmp / baseStepGrid) >= 0)
	{
		if (HNC_DoubleCompare(0, qAbs(yMax)) == 0)
		{
			fTmp = 1;
		}
		else
		{
			fTmp = yMax;
		}
	}
	
	fTmp = log(fTmp / baseStepGrid) / LOG_CALC_OSC_ZOOM; // log(ftmp/base_value)/log(OSC_ZOOM_STEP)

    if (fTmp >= 0)
    {
        tmp = (Bit32)(fTmp + 1); // 加1相当于向上圆整
    }
    else
    {
        tmp = -(Bit32)(-fTmp - 1);
    }

    tmp = (Bit32)(baseStepGrid * pow(OSC_ZOOM_STEP, tmp));

    if (tmp > plotYStepMax)
    {
        fTmp = OscAdaptStep(plotYStepMax);
    }
    else if (tmp < plotYStepMix)
    {
        fTmp = OscAdaptStep(plotYStepMix);
    }
    else
    {
        fTmp = OscAdaptStep(tmp);
    }

    // 保护,防止后续坐标运算中除0
    if (fTmp < 0.000001)
    {
        newStepY = baseStepGrid;
    }
    else
    {
        newStepY = fTmp;
    }

    if(oscType == HmiOscServo::OSC_SERVO_FREQ && waveName == "FreqDomain")
    {
        newUpperY = newStepY * yStepNum;
        newLowerY = 0;
    }
    else
    {
        newUpperY = newStepY * yStepNum / 2;
        newLowerY = -newUpperY;
    }


    optYRangLower = newLowerY;
    optYRangUpper = newUpperY;
    optYStep = newStepY;

    Bit32 xValCount = ui->wavePlot->graph(0)->data()->keys().count();
    Bit32 xValMax = 0;
    if (xValCount - 1 >= 0)
    {
        xValMax =(fBit64)(ui->wavePlot->graph(0)->data()->keys().at(xValCount - 1)); // 取最后一个X值
    }
    optXRangUpper = xValMax;
    optXStep = (Bit32)(optXRangUpper / allLength)+1;
    optXRangUpper = optXStep*allLength;

    if (oscType == HmiOscServo::OSC_SERVO_SYNC)
    {
        Bit32 ch = ActiveChan();
        optXRangLower = HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos;
        if ( abs(HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) < 10)
        {
            optXRangUpper = optXRangLower + 10;
        }
        else
        {
            if ( (HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) % 10 != 0)
            {
                if (HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos < 0)
                {
                    optXRangUpper = ((HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) / 10 - 1) * 10 + HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos;
                }
                else
                {
                    optXRangUpper = ((HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) / 10 + 1) * 10 + HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos;
                }
            }
            else
            {
                optXRangUpper = HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos;
            }
        }
        optXStep = Bit32((optXRangUpper - optXRangLower) / 10);
    }
    else if (oscType == HmiOscServo::OSC_SERVO_RASTER)
    {
        xValCount = ui->wavePlot->graph(0)->data()->keys().count();
        if(xValCount <= 0)
        {
            return;
        }
        fBit64 xValSt = fBit64(ui->wavePlot->graph(0)->data()->keys().at(0));
        fBit64 xValEd = fBit64(ui->wavePlot->graph(0)->data()->keys().at(xValCount - 1));
        if (xValCount - 1 >= 0)
        {
            if (HNC_DoubleCompare(xValSt, xValEd) > 0)
            {
                optXRangUpper = ceil(xValSt);
                optXRangLower = floor(xValEd);
            }
            else
            {
                optXRangUpper = ceil(xValEd);
                optXRangLower = floor(xValSt);
            }
            optXStep = (optXRangUpper - optXRangLower) / 10;
        }
        baseXRangLower = optXRangLower;
        baseXRangUpper = optXRangUpper;
        baseXStep = optXStep;
    }

    XAxisSetTickParm(optXRangLower, optXRangUpper, optXStep);
    YAxisSetTickParm(optYRangLower, optYRangUpper, optYStep);

//    CalSelArea();

    ui->wavePlot->replot();
}

void OscWave::AutoRangParm(bool xRang, bool yRang)
{
    Bit32 startPos = 0;
    Bit32 endPos = oscproc_get_total();
    Bit32 client = HmiOscServo::oscservo_get_sampl_client();
    Bit32 chn = 0;
    Bit32 type = 0;
    Bit32 axNo = 0;
    Bit32 offset = 0;
    Bit32 len = 0;
    fBit64 cof = 0, off = 0;
    Bit64 min  = 0, max = 0;
    fBit64 fMax0 = 0.0, fMin0 = 0.0;
    fBit64 fMax1 = 0.0, fMin1 = 0.0;
    fBit64 fMax = 0.0, fMin = 0.0;
    fBit64 fTmp = 0.0;
    Bit32 tmp = 0;
    fBit64 fStepTmp = 0.0;

    fBit64 baseStepGrid = baseYStep;

    fBit64 newUpperY = 0.0, newLowerY = 0.0;
    fBit64 newStepY = 0.0;

    if(endPos >= SMPL_DATA_NUM)
    {
        endPos = SMPL_DATA_NUM - 1;
    }

    if (!xRang && !yRang)
    {
        return;
    }

    if (yRang)
    {
        switch(oscType)
        {
        case HmiOscServo::OSC_SERVO_SPE:    // 速度环
            if (waveName == "SPD")      // 速度波形图
            {
                chn = 0;
                HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
                cof = smpl_calc_spd_coef(axNo, 4)*60000.0; // 指令速度：mm/min
                off = 0;
                HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
                fMax0 = cof * max + off;
                fMin0 = cof * min + off;

                chn = 1;
                HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
                cof = smpl_calc_spd_coef(axNo, 5)*60000.0; // 实际速度：mm/min
                off = 0;
                HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
                fMax1 = cof * max + off;
                fMin1 = cof * min + off;

                fMax = fMax0;
                fMin = fMin0;
                if ((Bit32)fMax0 < (Bit32)fMax1)
                {
                    fMax = fMax1;
                }
                if ((Bit32)fMin0 > (Bit32)fMin1)
                {
                    fMin = fMin1;
                }
            }
            else if (waveName == "ACC")     // 加速度波形图
            {
                chn = 1;
                HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
				cof = HmiOscServo::SmplCalcSpddiffCoef(axNo, 5);// * ACCVEL_MUTI2INT;       // *ACCVEL_MUTI2INT后会造成波形计算值与实际值相差ACCVEL_MUTI2INT倍造成图形显示不对
                off = 0;
                HmiOscServo::SmplCalcDiffMaxmin(chn, &max, &min, startPos, endPos);

                fMax = cof * max + off;
                fMin = cof * min + off;
            }
            break;
        case HmiOscServo::OSC_SERVO_POS:
            if (waveName == "POS")      // 位置波形图
            {
                chn = 0;
                HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
                cof = smpl_calc_dist_coef(axNo) * 1000.0;
                HNC_AxisGetValue(HNC_AXIS_WCS_ZERO, axNo, &tmp);
                off = -cof * tmp;
                HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
                fMax0 = cof * max + off;
                fMin0 = cof * min + off;

                chn = 1;
                HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
                cof = smpl_calc_dist_coef(axNo) * 1000.0;
                HNC_AxisGetValue(HNC_AXIS_WCS_ZERO, axNo, &tmp);
                off = -cof * tmp;
                HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
                fMax1 = cof * max + off;
                fMin1 = cof * min + off;

                fMax = fMax0;
                fMin = fMin0;
                if ((Bit32)fMax0 < (Bit32)fMax1)
                {
                    fMax = fMax1;
                }
                if ((Bit32)fMin0 > (Bit32)fMin1)
                {
                    fMin = fMin1;
                }
            }
            else if (waveName == "TrackErr")     // 跟随误差波形图
            {
                chn = 2;
                HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
                cof = fabs(smpl_calc_follow_err_coef(axNo))*1000.0; // um
                off = 0;

                HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
                fMax = cof * max + off;
                fMin = cof * min + off;
            }
            break;
        case HmiOscServo::OSC_SERVO_TOOL:
            fMax = 1.0;
            fMin = 0.0;
            break;
        case HmiOscServo::OSC_SERVO_SPINDLE:
            chn = 1;
            HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
            cof = HmiOscServo::smpl_calc_tapvel_coef(chn, client) * 60000.0;
            off = 0;
            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax = cof * max + off;
            fMin = cof * min + off;
            if (HNC_DoubleCompare(fMax, fMin) < 0)  // 解决bug#6177,当最小值大于最大值时，则交替最大值与最小值
            {
                qSwap(fMax, fMin);
            }
            break;
        case HmiOscServo::OSC_SERVO_FREQTAP:
            chn = 0;
            cof = smpl_calc_dist_coef(0) * 1000.0 * 1000.0;
            off = 0;
            HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
            fMax = cof * max + off;
            fMin = cof * min + off;
            break;
		case HmiOscServo::OSC_SERVO_BEARTAP:
			chn = 0;
			cof = smpl_calc_dist_coef(0) * 1000.0 * 1000.0;
			off = 0;
			HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
			fMax = cof * max + off;
			fMin = cof * min + off;
			break;
        case HmiOscServo::OSC_SERVO_TAP:
            if (waveName == "SyncErr")      // 刚性攻丝-同步误差主界面波形图
            {
                Bit64 *chn_addr1 = NULL, *chn_addr2 = NULL;
                Bit32 i = 0, smpl_period = oscproc_get_smpl_period(), tmp = 0;
                fBit64 fdelta = 0.0;
                fBit64 tmpThread = 0.0;
                Bit32 ch = ActiveChan();
                Bit32 macType = GetOscprocCurCh(ch);

    //            HNC_SystemGetValue(HNC_SYS_ACTIVE_CHAN, &ch);

                if (macType == 1 && HmiOscServo::s_Conf[ch].stTapConf.tapDir == 1)
                {
                    tmpThread = -(HmiOscServo::s_Conf[ch].stTapConf.thread_lead);
                }
                else
                {
                    tmpThread = HmiOscServo::s_Conf[ch].stTapConf.thread_lead;
                }
                HNC_SamplGetConfig(client, 0, type, axNo, offset, len);
                chn_addr1 = oscproc_get_smpldata(0);
                chn_addr2 = oscproc_get_smpldata(1);
                //nc_assert(chn_addr1 != NULL && chn_addr2 != NULL);
                for (fTmp = 0, fdelta = 0, i = 0; i < startPos; i++)
                {
                    fTmp = HmiOscServo::SmplCalcTapvelCoef(1, tmpThread, client)*chn_addr2[i] - smpl_calc_spd_coef(axNo,5)*chn_addr1[i]; // 速度差
                    fdelta = fTmp * smpl_period * 1000 + fdelta; // um
                }
                tmp = i;
                fTmp = HmiOscServo::SmplCalcTapvelCoef(1, tmpThread, client)*chn_addr2[tmp] - smpl_calc_spd_coef(axNo,5)*chn_addr1[tmp]; // 速度差
                fdelta = fTmp * smpl_period * 1000 + fTmp; // um
                fMax = fMin = fdelta;
                for (tmp = i+1; tmp < endPos; tmp++)
                {
                    fTmp = HmiOscServo::SmplCalcTapvelCoef(1, tmpThread, client)*chn_addr2[tmp] - smpl_calc_spd_coef(axNo,5)*chn_addr1[tmp];
                    fdelta = fTmp * smpl_period * 1000 + fdelta;
                    if (fdelta > fMax)
                    {
                        fMax = fdelta;
                    }
                    else if (fdelta < fMin)
                    {
                        fMin = fdelta;
                    }
                }
            }
            else if (waveName == "SPE")     // 刚性攻丝-主界面-速度波形图
            {
                chn = 0;
                HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
                cof = smpl_calc_spd_coef(axNo, 5) * 60000.0; // 实际速度：mm/min
                off = 0;
                HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
                fMax0 = cof * max + off;
                fMin0 = cof * min + off;

                chn = 1;
                HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
                cof = HmiOscServo::smpl_calc_tapvel_coef(1, client)*60000.0;
                off = 0;
                HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
                fMax1 = cof * max + off;
                fMin1 = cof * min + off;
                if (HNC_DoubleCompare(fMax1, fMin1) < 0)
                {
                    fBit64 swValue = fMax1;
                    fMax1 = fMin1;
                    fMin1 = swValue;
                }

                fMax = fMax0;
                fMin = fMin0;
                if ((Bit32)fMax0 < (Bit32)fMax1)
                {
                    fMax = fMax1;
                }
                if ((Bit32)fMin0 > (Bit32)fMin1)
                {
                    fMin = fMin1;
                }
            }
        case HmiOscServo::OSC_SERVO_CUSTOM:
            if(waveName == "ORG")
            {
                chn = 0;
                cof = 1; // 原始值
                off = 0;
                HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
                fMax = cof * max + off;
                fMin = cof * min + off;
            }
            else if(waveName == "SHIFT")
            {
                chn = 0;
                cof = HmiOscServo::s_Conf[chn].stCustomConf.convCof / HmiOscServo::s_Conf[chn].stCustomConf.convBase; // 换算值
                off = HmiOscServo::s_Conf[chn].stCustomConf.convOff;
                HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
                fMax = cof * max + off;
                fMin = cof * min + off;
            }
            break;
        case HmiOscServo::OSC_SERVO_SYNC:
            if(waveName == "CurrentFlow")
            {
                HNC_SamplGetConfig(client, 1, type, axNo, offset, len);
                cof = HmiOscServo::SmplCalcLoadCoef(1, client) / 1000.0;
                off = 0;
                HmiOscServo::SmplCalcMaxmin(1, &max, &min, 0, oscproc_get_total());
                fMax0 = cof * max + off;
                fMin0 = cof * min + off;

                HNC_SamplGetConfig(client, 2, type, axNo, offset, len);
                cof = HmiOscServo::SmplCalcLoadCoef(2, client) / 1000.0;
                off = 0;
                HmiOscServo::SmplCalcMaxmin(2, &max, &min, 0, oscproc_get_total());
                fMax1 = cof * max + off;
                fMin1 = cof * min + off;

                fMax = fMax0;
                fMin = fMin0;

                if ((Bit32)fMax0 < (Bit32)fMax1)
                {
                    fMax = fMax1;
                }
                if ((Bit32)fMin0 > (Bit32)fMin1)
                {
                    fMin = fMin1;
                }
            }
            else if(waveName == "ERR")
            {
                HNC_SamplGetConfig(client, 3, type, axNo, offset, len);
                cof = smpl_calc_dist_coef(axNo) * 1000.0;
                off = 0;
                HmiOscServo::SmplCalcSyncErrMaxmin(3, 4, &max, &min, 0, oscproc_get_total());
                fMax0 = cof * max + off;
                fMin0 = cof * min + off;

                HNC_SamplGetConfig(client, 1, type, axNo, offset, len);
                cof = smpl_calc_dist_coef(axNo) * 1000.0;
                off = 0;
                HmiOscServo::SmplCalcSyncErrMaxmin(1, 2, &max, &min, 0, oscproc_get_total());
                fMax1 = cof * max + off;
                fMin1 = cof * min + off;

                fMax = fMax0;
                fMin = fMin0;

                if ((Bit32)fMax0 < (Bit32)fMax1)
                {
                    fMax = fMax1;
                }
                if ((Bit32)fMin0 > (Bit32)fMin1)
                {
                    fMin = fMin1;
                }
            }
            break;
        case HmiOscServo::OSC_SERVO_FREQ:
            if(waveName == "CurrentFlow")
            {
                cof = 1.0;
                off = 0.0;
                HmiOscServo::SmplCalcMaxmin(0, &max, &min, 0, oscproc_get_total());
                fMax = cof * max + off;
                fMin = cof * min + off;
            }
            else if(waveName == "FreqDomain")
            {
                cof = 1.0;
                off = 0.0;
                HmiOscServo::SmplCalcFreqMaxmin(0, &max, &min, 0, oscproc_get_total());
                fMax = cof * max + off;
                fMin = cof * min + off;
            }
            break;
        case HmiOscServo::OSC_SERVO_RASTER:
            if(waveName == "sinAmpl" || waveName == "cosAmpl")
            {
                fMax = 1.3;
                fMin = 0.7;
            }
            else if(waveName == "sinZero" || waveName == "cosZero")
            {
                fMax = 0.3;
                fMin = -0.3;
            }
            else if(waveName == "phaseDiff")
            {
                fMax = 0.75;
                fMin = -0.75;
            }
			break;
		case HmiOscServo::OSC_SERVO_ACCE:
			if (waveName == "Speed")      // 速度波形图
			{
				chn = 0;
				HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
				cof = smpl_calc_spd_coef(axNo, 4)*60000.0; // 指令速度：mm/min
				off = 0;
				HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
				fMax0 = cof * max + off;
				fMin0 = cof * min + off;

				chn = 1;
				HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
				cof = smpl_calc_spd_coef(axNo, 5)*60000.0; // 实际速度：mm/min
				off = 0;
				HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
				fMax1 = cof * max + off;
				fMin1 = cof * min + off;

				fMax = fMax0;
				fMin = fMin0;
				if ((Bit32)fMax0 < (Bit32)fMax1)
				{
					fMax = fMax1;
				}
				if ((Bit32)fMin0 > (Bit32)fMin1)
				{
					fMin = fMin1;
				}
			}
			else if (waveName == "Electric")     // 电流波形图
			{
				HNC_SamplGetConfig(client, 2, type, axNo, offset, len);
				cof = HmiOscServo::SmplCalcLoadCoef(2, client) / 1000.0;
				off = 0;
				HmiOscServo::SmplCalcMaxmin(2, &max, &min, 0, oscproc_get_total());
				fMax = cof * max + off;
				fMin = cof * min + off;
			}
			break;
		case HmiOscServo::OSC_SERVO_KCOMPEN:
			if (waveName == "Speed")      // 速度波形图
			{
				chn = 0;
				HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
				cof = smpl_calc_spd_coef(axNo, 4)*60000.0; // 指令速度：mm/min
				off = 0;
				HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
				fMax0 = cof * max + off;
				fMin0 = cof * min + off;

				chn = 1;
				HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
				cof = smpl_calc_spd_coef(axNo, 5)*60000.0; // 实际速度：mm/min
				off = 0;
				HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
				fMax1 = cof * max + off;
				fMin1 = cof * min + off;

				fMax = fMax0;
				fMin = fMin0;
				if ((Bit32)fMax0 < (Bit32)fMax1)
				{
					fMax = fMax1;
				}
				if ((Bit32)fMin0 > (Bit32)fMin1)
				{
					fMin = fMin1;
				}
			}
			else if (waveName == "TrackErr")     // 跟随误差波形图
			{
                chn = 2;
                HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
                cof = fabs(smpl_calc_follow_err_coef(axNo)) * 1000.0; // um
				off = 0;

				HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
				fMax = cof * max + off;
				fMin = cof * min + off;
			}
			break;
		case HmiOscServo::OSC_SERVO_INERTIA://惯量辨识
			if (waveName == "Speed")      // 速度波形图
			{
				chn = 0;
				HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
				cof = smpl_calc_spd_coef(axNo, 4)*60000.0; // 指令速度：mm/min
				off = 0;
				HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
				fMax0 = cof * max + off;
				fMin0 = cof * min + off;

				chn = 1;
				HNC_SamplGetConfig(client, chn, type, axNo, offset, len);
				cof = smpl_calc_spd_coef(axNo, 5)*60000.0; // 实际速度：mm/min
				off = 0;
				HmiOscServo::SmplCalcMaxmin(chn, &max, &min, startPos, endPos);
				fMax1 = cof * max + off;
				fMin1 = cof * min + off;

				fMax = fMax0;
				fMin = fMin0;
				if ((Bit32)fMax0 < (Bit32)fMax1)
				{
					fMax = fMax1;
				}
				if ((Bit32)fMin0 > (Bit32)fMin1)
				{
					fMin = fMin1;
				}
			}
			else if (waveName == "Electric")     // 电流波形图
			{
				HNC_SamplGetConfig(client, 2, type, axNo, offset, len);
				cof = HmiOscServo::SmplCalcLoadCoef(2, client) / 1000.0;
				off = 0;
				HmiOscServo::SmplCalcMaxmin(2, &max, &min, 0, oscproc_get_total());
				fMax = cof * max + off;
				fMin = cof * min + off;
			}
			break;
        default:
            break;
        }

        fBit64 tmp1 = 0;
        fBit64 tmp2 = 0;

        if (HNC_DoubleCompare(fMax, fMin) < 0)
        {
            fBit64 swValue = fMax;
            fMax = fMin;
            fMin = swValue;
        }

        if (HNC_DoubleCompare(fMax, 0) >= 0 && HNC_DoubleCompare(fMin, 0) >= 0)
        {
            fTmp = fMax * 2 / yStepNum;
        }
        else if (HNC_DoubleCompare(fMax, 0) < 0 && HNC_DoubleCompare(fMin, 0) < 0)
        {
            fTmp = (0 - fMin) * 2 / yStepNum;
        }
        else
        {
            tmp1 = fMax * 2 / yStepNum;
            tmp2 = (0 - fMin) * 2 / yStepNum;

            if (HNC_DoubleCompare(tmp1, tmp2) >= 0)
            {
                fTmp = tmp1;
            }
            else
            {
                fTmp = tmp2;
            }
        }

        yMax = fMax + fTmp; // 上下保留一个间距，防止上下数据过小被限制
        yMin = fMin - fTmp;

		if (HNC_DoubleCompare(0, fTmp / baseStepGrid) >= 0)
		{
			if (HNC_DoubleCompare(0, qAbs(yMax)) == 0)
			{
				fTmp = 1;
			}
			else
			{
				fTmp = yMax;
			}
		}

		fTmp = log(fTmp / baseStepGrid) / LOG_CALC_OSC_ZOOM; // log(ftmp/base_value)/log(OSC_ZOOM_STEP)
        if (fTmp >= 0)
        {
            tmp = (Bit32)(fTmp + 1); // 加1相当于向上圆整
        }
        else
        {
            tmp = -(Bit32)(-fTmp - 1);
        }

        fStepTmp = baseStepGrid * pow(OSC_ZOOM_STEP, tmp);

        if (fStepTmp > plotYStepMax)
        {
			fTmp = OscAdaptStep(plotYStepMax);
        }
        else if (fStepTmp < plotYStepMix)
        {
            fTmp = OscAdaptStep(plotYStepMix);
        }
        else
        {
            fTmp = OscAdaptStep(fStepTmp);
        }

        // 保护,防止后续坐标运算中除0
        if (fTmp < 0.000001)
        {
            newStepY = baseStepGrid;
        }
        else
        {
            newStepY = fTmp;
        }

        if(oscType == HmiOscServo::OSC_SERVO_FREQ && waveName == "FreqDomain")
        {
            newUpperY = newStepY * yStepNum;
            newLowerY = 0;
        }
        else if (oscType == HmiOscServo::OSC_SERVO_RASTER)
        {
            if(waveName == "POS")
            {
                newUpperY = 1.5;
                newLowerY = -1.5;
                newStepY = 0.5;
            }
        }
        else
        {
            newUpperY = newStepY * yStepNum / 2;
            newLowerY = -newUpperY;
        }

        optYRangLower = newLowerY;
        optYRangUpper = newUpperY;
        optYStep = newStepY;

        YAxisSetTickParm(optYRangLower, optYRangUpper, optYStep);
    }

    if (xRang)
    {
        Bit32 xValCount = ui->wavePlot->graph(0)->data()->keys().count();
        Bit32 xValMax = 0;
        if (xValCount - 1 >= 0)
        {
            xValMax =(fBit64)(ui->wavePlot->graph(0)->data()->keys().at(xValCount - 1)); // 取最后一个X值
        }
        optXRangUpper = xValMax;
        optXStep = (Bit32)(optXRangUpper / allLength)+1;
        optXRangUpper = optXStep*allLength;
        /*if (oscType == HmiOscServo::OSC_SERVO_COMP_SENSOR && waveName == "RTLCurve")
        {
            optXStep = 1;
            optXRangUpper = 2;
        }
        else */if (oscType == HmiOscServo::OSC_SERVO_SYNC)
        {
            Bit32 ch = ActiveChan();
            optXRangLower = HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos;
            if ( abs(HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) < 10)
            {
                optXRangUpper = optXRangLower + 10;
            }
            else
            {
                if ( (HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) % 10 != 0)
                {
                    if (HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos < 0)
                    {
                        optXRangUpper = ((HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) / 10 - 1) * 10 + HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos;
                    }
                    else
                    {
                        optXRangUpper = ((HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos) / 10 + 1) * 10 + HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos;
                    }
                }
                else
                {
                    optXRangUpper = HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos;
                }
            }
            optXStep = Bit32((optXRangUpper - optXRangLower) / 10);
        }

        XAxisSetTickParm(optXRangLower, optXRangUpper, optXStep);
    }


//    CalSelArea();

    ui->wavePlot->replot();
}

///////////////////////////////////////////////////////////////////////////////
//
//    fBit64 OscAdaptStep(fBit64 step)
//
//    功能：
//			  对坐标网格网格系数自适应调整
//
//    参数：
//			  in_value: 每格值
//
//    描述：
//			  小于10以下不做调整，其他变为5的倍数
//
//    返回：
//			  输出值
//
/////////////////////////////////////////////////////////////////////////////
fBit64 OscWave::OscAdaptStep(fBit64 step)
{
    Bit32 scale = 0, unit = 0;
    fBit64 mulParm = 1.0;
    Bit32 tmp = 0;
    fBit64 newStep = 0.0;

    if (step < 0.01)
    {
        mulParm = 100000;
    }
    else if (step < 0.1)
    {
        mulParm = 10000;
    }
    else if (step < 1.0)
    {
        mulParm = 1000;
    }
    else
    {
        mulParm = 1;
    }

    tmp = (Bit32)(step * mulParm);

    if (tmp < 10) // 小于10不处理
    {
        newStep = tmp / mulParm;
    }
    else
    {
        scale = tmp;
        unit = tmp % 10;
        if ((unit > 0) && (unit < 4)) // 个位小于4，将个位变为0
        {
            scale = tmp/10*10;
        }
        else if((unit == 4) || (unit == 6)) // 个位是4~6， 变成5
        {
            scale = tmp / 10 * 10 + 5;
        }
        else if(unit > 6) // 个位值大于6，增加至10
        {
            scale = tmp / 10 * 10 + 10;
        }
        newStep = scale / mulParm;
    }

    return newStep;
}

void OscWave::GetCurSelAreaInfo(Bit32 *xPos, Bit32 *yPos, Bit32 *xLength, Bit32 *yHeight)
{
    *xPos = selAreaPosX;
    *yPos = selAreaPosY;
    *xLength = selAreaLength;
    *yHeight = selAreaHeight;
}

void OscWave::SetCurSelAreaInfo(Bit32 xPos, Bit32 yPos, Bit32 xLength, Bit32 yHeight)
{
    selAreaPosX = xPos;
    selAreaPosY = yPos;
    selAreaLength = xLength;
    selAreaHeight = yHeight;
}

Bit32 OscWave::SelAreaZoomUp()
{
    if ((selAreaLength + 2) <= allLength && (selAreaHeight + 2) <= allHeight)
    {
        selAreaLength += 2;
        selAreaHeight += 2;

        selAreaPosX--;
        selAreaPosY++;

        if (selAreaPosX < 0)
        {
            selAreaPosX = 0;
        }

        if (selAreaPosY > allHeight)
        {
            selAreaPosY = allHeight;
        }

        ShowSelArea();
        return 0;
    }
    else
    {
        return -1;
    }
}

Bit32 OscWave::SelAreaZoomDown()
{
    if (selAreaLength > 2 && selAreaHeight > 2)
    {
        selAreaLength -= 2;
        selAreaHeight -= 2;

        selAreaPosX++;
        selAreaPosY--;

        ShowSelArea();

        return 0;
    }
    else
    {
        return -1;
    }
}

Bit32 OscWave::SelAreaZoomFullSrceen()
{
    Bit32 xValCount = 0;
    fBit64 xValMax = 0.0;

    fBit64 lowerX = ui->wavePlot->xAxis->rangeLower();
    fBit64 lowerY = ui->wavePlot->yAxis->rangeLower();
    fBit64 stepX = ui->wavePlot->xAxis->tickStep() / 2;
    fBit64 stepY = ui->wavePlot->yAxis->tickStep() / 2;

    fBit64 newLowerX = lowerX + stepX * selAreaPosX;
    fBit64 newStepX = (Bit32)(stepX * selAreaLength / xStepNum);
    fBit64 newUpperX = newLowerX + stepX * selAreaLength;

    xValCount = ui->wavePlot->graph(0)->data()->keys().count();

    if (xValCount - 1 >= 0)
    {
        xValMax =(fBit64)(ui->wavePlot->graph(0)->data()->keys().at(xValCount - 1)); // 取最后一个X值
    }

//    fBit64 vCenter = (lowerY + upperY) / 2;
//    vCenter = vCenter - ((selAreaPosY + (selAreaPosY - selAreaHeight) - yStepNum * 2) / 2) * stepY;
//    fBit64 newStepY = stepY * selAreaHeight / yStepNum;

//    fBit64 newLowerY = vCenter - stepY * selAreaHeight / 2;
//    fBit64 newUpperY = vCenter + stepY * selAreaHeight / 2;

    fBit64 newUpperY = lowerY + stepY * selAreaPosY;
    fBit64 newLowerY = newUpperY - stepY * selAreaHeight;
    fBit64 newStepY = (newUpperY - newLowerY) / yStepNum;

    newStepY = ((Bit32)(newStepY * this->yTickPrec)) / (this->yTickPrec*1.0);

    if (xValMax < newLowerX)
    {
        return -1;
    }

    if (newStepX < plotXStepMix && newStepY < plotYStepMix)
    {
        return -1;
    }

    if (newStepX >= plotXStepMix)
    {
        ui->wavePlot->xAxis->setRange(newLowerX, newUpperX);
        ui->wavePlot->xAxis->setTickStep(newStepX);

        ui->wavePlot->xAxis2->setRange(newLowerX, newUpperX);
        ui->wavePlot->xAxis2->setTickStep(newStepX);
    }

    if (newStepY >= plotYStepMix)
    {
        ui->wavePlot->yAxis->setRange(newLowerY, newUpperY);
        ui->wavePlot->yAxis->setTickStep(newStepY);

        ui->wavePlot->yAxis2->setRange(newLowerY, newUpperY);
        ui->wavePlot->yAxis2->setTickStep(newStepY);
    }

    ui->wavePlot->replot();
    return 0;
}

/*!
 * \brief OscWave::CircleAddPoint
 * \param [in] index 图形索引
 * \param [in] x 横坐标值
 * \param [in] y 纵坐标值
 */
void OscWave::CircleAddPoint(Bit32 index, QVector<double> x, QVector<double> y, bool showPoint, bool showCurveName, bool xInvert)
{
//     m_pGraphSel->setLineStyle(QCPCurve::lsLine);					// 加上此语句后，在关闭时崩溃，原因未知

    if (index >= m_vecpGraphSel.count())
    {
        while ((index - m_vecpGraphSel.count()) >= 0)
        {
            QCPCurve* pCurve = new QCPCurve(ui->wavePlot->xAxis, ui->wavePlot->yAxis);
            m_vecpGraphSel.append(pCurve);

            // 设置波形颜色
            QColor qc;
            if (index >= 0 && index  < m_vecWaveCurve.count())
            {
                qc = m_mapCurveColor.value(m_vecWaveCurve.at(index));
            }
            else
            {
                qc = HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_BLACK_CURVE1);
            }

            m_vecpGraphSel.at(index)->setPen(QPen(qc));
            ui->wavePlot->addPlottable(m_vecpGraphSel.at(index));
        }
    }

    if (m_vecpGraphSel.at(index) == NULL)
    {
        return;
    }

    if (showPoint)
    {
        m_vecpGraphSel.at(index)->setScatterStyle(QCPScatterStyle::ssCircle);
    }
    else
    {
        m_vecpGraphSel.at(index)->setScatterStyle(QCPScatterStyle::ssNone);
    }

    if (showCurveName)
    {
        SetCurveName(index + 1);
    }

    if (xInvert)
    {
        ui->wavePlot->xAxis->setAutoTickLabels(true);
        QVector<double>::iterator xitor = x.begin();
        for (; xitor != x.end(); ++xitor)
        {
            (*xitor) = 0 - (*xitor);
        }
    }

    m_vecpGraphSel.at(index)->setData(x, y);
    ui->wavePlot->axisRect()->setupFullAxesBox();

    double xMax = 0;
    double xMin = 0;
    double yMax = 0;
    double yMin = 0;

    GetDataLimit(x, xMin, xMax);
    GetDataLimit(y, yMin, yMax);

    if (index == 0)
    {
        m_dbHisMaxX = xMax;
        m_dbHisMinX = xMin;
        m_dbHisMaxY = yMax;
        m_dbHisMinY = yMin;
    }

    if (HNC_DoubleCompare(m_dbHisMaxX, m_dbHisMinX) != 0)
    {
        if (HNC_DoubleCompare(xMin, m_dbHisMinX) > 0)
        {
            xMin = m_dbHisMinX;
        }

        if (HNC_DoubleCompare(xMax, m_dbHisMaxX) < 0)
        {
            xMax = m_dbHisMaxX;
        }
    }

    if (HNC_DoubleCompare(m_dbHisMaxY, m_dbHisMinY) != 0)
    {
        if (HNC_DoubleCompare(yMin, m_dbHisMinY) > 0)
        {
            yMin = m_dbHisMinY;
        }

        if (HNC_DoubleCompare(yMax, m_dbHisMaxY) < 0)
        {
            yMax = m_dbHisMaxY;
        }
    }

    if (HNC_DoubleCompare(xMin, xMax) == 0)
    {
        if (HNC_DoubleCompare(xMin, baseXRangLower) > 0)
        {
            xMin = baseXRangLower;
        }

        if (HNC_DoubleCompare(xMax, baseXRangUpper) < 0)
        {
            xMax = baseXRangUpper;
        }
    }
    else
    {
        if (HNC_DoubleCompare(xMin, m_dbHisMinX) < 0)
        {
            m_dbHisMinX = xMin;
        }

        if (HNC_DoubleCompare(xMax, m_dbHisMaxX) > 0)
        {
            m_dbHisMaxX = xMax;
        }
    }

    if (HNC_DoubleCompare(yMin, yMax) == 0)
    {
        if (HNC_DoubleCompare(yMin, baseYRangLower) > 0)
        {
            yMin = baseYRangLower;
        }

        if (HNC_DoubleCompare(yMax, baseYRangUpper) < 0)
        {
            yMax = baseYRangUpper;
        }
    }
    else
    {
        if (HNC_DoubleCompare(yMin, m_dbHisMinY) < 0)
        {
            m_dbHisMinY = yMin;
        }

        if (HNC_DoubleCompare(yMax, m_dbHisMaxY) > 0)
        {
            m_dbHisMaxY = yMax;
        }
    }

    GetScaleParameter(xMax, xMin, 10, optXRangLower, optXRangUpper, optXStep);
    XAxisSetTickParm(optXRangLower, optXRangUpper, optXStep);

    GetScaleParameter(yMax, yMin, 8, optYRangLower, optYRangUpper, optYStep);
    YAxisSetTickParm(optYRangLower, optYRangUpper, optYStep);
	if (xInvert)
    {
        ui->wavePlot->xAxis->setAutoTickLabels(false);
        QVector<QString> labels;

        fBit64 lower = 0 - optXRangLower;

        for (int i = 0; i <= 10; i++)
        {
            labels.append(QString("%1").arg(lower - optXStep * i));
        }

        ui->wavePlot->xAxis->setTickVectorLabels(labels);
    }
}

Bit32 OscWave::GetSelCount()
{
    return m_vecpGraphSel.count();
}

/*!
 * \brief OscWave::GetScaleParameter 获取缩放参数
 * \param [in] max 最大值
 * \param [in] min 最小值
 * \param [in] rfStepNum 参数步数
 * \param [out] lower 刻度最小值
 * \param [out] upper 刻度最大值
 * \param [out] stepValue 最优刻度值
 */
void OscWave::GetScaleParameter(fBit64 max, fBit64 min, Bit32 rfStepNum, fBit64& lower, fBit64& upper, fBit64& stepValue)
{
    fBit64 tmp = (max - min) / rfStepNum;
    if (HNC_DoubleCompare(tmp, 1) > 0)
    {
        // ceil取大于等于的最小整数
        Bit32 tmpStepValue = (Bit32)ceil(tmp);

        if (tmpStepValue == 0)
        {
            return;
        }

        // floor取小于等于的最大整数
        Bit32 tmpMin = (Bit32)floor(min);

        if (tmpMin % tmpStepValue != 0)
        {
            lower = tmpMin - (tmpStepValue - abs(tmpMin % tmpStepValue));
        }
        else
        {
            lower = tmpMin;
        }

        // 最小值大于0，但刻度最小于值小于0时，刻度最小值为0
        if (HNC_DoubleCompare(0, min) < 0 && HNC_DoubleCompare(0, lower) > 0)
        {
            lower = 0;
        }

        Bit32 tmpStepNum = (Bit32)ceil((max - lower) / tmpStepValue);

        if (tmpStepNum % 2 != 0)
        {
            tmpStepNum++;
        }

        stepValue = tmpStepValue;
        upper = lower + tmpStepNum * stepValue;
    }
    else
    {
        // ceil取大于等于的最小整数
        fBit64 tmpStepValue = ceil(tmp * 10) / 10;

        if (HNC_DoubleCompare(0, tmpStepValue) == 0)
        {
            return;
        }

        // floor取小于等于的最大整数
        fBit64 tmpMin = floor(min * 10) / 10;

        if ((Bit32)(tmpMin * 10) % (Bit32)(tmpStepValue * 10) != 0)
        {
            lower = tmpMin - (tmpStepValue - (fBit64)abs((Bit32)(tmpMin * 10) % (Bit32)(tmpStepValue * 10)) / 10.0);
        }
        else
        {
            lower = tmpMin;
        }

        // 最小值大于0，但刻度最小于值小于0时，刻度最小值为0
        if (HNC_DoubleCompare(0, min) < 0 && HNC_DoubleCompare(0, lower) > 0)
        {
            lower = 0;
        }

        Bit32 tmpStepNum = (Bit32)ceil((max - lower) / tmpStepValue);

        if (tmpStepNum % 2 != 0)
        {
            tmpStepNum++;
        }

        stepValue = tmpStepValue;
        upper = lower + tmpStepNum * stepValue;
    }
}

//void OscWave::GetScaleParameter(fBit64 max, fBit64 min, Bit32 rfStepNum, fBit64& lower, fBit64& upper, fBit64& stepValue)
//{
//    // ceil取大于等于的最小整数
//    Bit32 tmpStepValue = (Bit32)ceil((max - min) / rfStepNum);

//    if (tmpStepValue == 0)
//	{
//		return;
//	}

//    // floor取小于等于的最大整数
//    Bit32 tmpMin = (Bit32)floor(min);

//    if (tmpMin % tmpStepValue != 0)
//    {
//        lower = tmpMin - (tmpStepValue - abs(tmpMin % tmpStepValue));
//    }
//    else
//    {
//        lower = tmpMin;
//    }

//    // 最小值大于0，但刻度最小于值小于0时，刻度最小值为0
//    if (HNC_DoubleCompare(0, min) < 0 && HNC_DoubleCompare(0, lower) > 0)
//    {
//        lower = 0;
//    }

//    Bit32 tmpStepNum = (Bit32)ceil((max - lower) / tmpStepValue);

//    if (tmpStepNum % 2 != 0)
//    {
//        tmpStepNum++;
//    }

//    stepValue = tmpStepValue;
//    upper = lower + tmpStepNum * stepValue;
//}

/**
 * @brief OscWave::LineAddPoint 添加曲线数据
 * @param [in] index            曲线索引值
 * @param [in] x                x轴坐标值
 * @param [in] y                y轴坐标值
 * @param [in] flag             是否调整y轴
 */
void OscWave::LineAddPoint(Bit32 index, QVector<double>x, QVector<double>y, Bit32 flag, bool displayName)
{
    if (index < 0)
    {
        return;
    }

    // 索引号超过范围时则增加相应的曲线
    if (index >= ui->wavePlot->graphCount())
    {
        Bit32 count = index - ui->wavePlot->graphCount() + 1;
        for (int i = 0; i < count; i++)
        {
            ui->wavePlot->addGraph();
            if (displayName)
            {
                SetCurveName(ui->wavePlot->graphCount());
            }

            SetGraphColor(ui->wavePlot->graphCount() - 1);
        }
    }
    else if (displayName)
    {
        SetCurveName(ui->wavePlot->graphCount());
    }

    // 再次判断不在有效范围时则返回
    if (index >= ui->wavePlot->graphCount())
    {
        return;
    }

    ui->wavePlot->graph(index)->addData(x, y);
    if (!x.isEmpty() && x.last() > ui->wavePlot->xAxis->rangeUpper() && flag == 0)
    {
        Bit32 xValCount = ui->wavePlot->graph(index)->data()->keys().count();
        Bit32 xValMax = 0;
        if (xValCount - 1 >= 0)
        {
            xValMax =(fBit64)(ui->wavePlot->graph(index)->data()->keys().at(xValCount - 1)); // 取最后一个X值
        }
        optXRangUpper = xValMax;
        optXStep = (Bit32)(optXRangUpper / allLength) + 1;
        optXRangUpper = optXStep * allLength;
        XAxisSetTickParm(optXRangLower, optXRangUpper, optXStep);
        ui->wavePlot->xAxis2->setTickLength(0);
    }

    if (flag == 0)
    {
        double yValMin = ui->wavePlot->yAxis->rangeLower();
        double yValMax = ui->wavePlot->yAxis->rangeUpper();
        for (Bit32 i =0; i < y.count(); i++)
        { // 计算最大值、最小值
            if (y.at(i) > yValMax)
            {
                yValMax = y.at(i);
            }
            if (y.at(i) < yValMin)
            {
                yValMin = y.at(i);
            }
        }
        if (yValMin < ui->wavePlot->yAxis->rangeLower()
                || yValMax > ui->wavePlot->yAxis->rangeUpper())
        {
            optYRangUpper = yValMax;
            optYRangLower = yValMin;
            if (ui->wavePlot->yAxis->rangeLower() == 0)
            { // y轴只有正向
                optYRangLower = 0;
                optYStep = (Bit32)((optYRangUpper - optYRangLower) / (allHeight / 2)) + 1;
                optYRangUpper = optYStep * (allHeight / 2);
            }
            else if (ui->wavePlot->yAxis->rangeLower() < 0)
            { // Y轴正负对称
                if (HNC_DoubleCompare(ui->wavePlot->yAxis->rangeUpper(), ui->wavePlot->yAxis->rangeLower() * (-1)) == 0)
                {
                    if (optYRangUpper > -optYRangLower)
                    {
                        optYRangLower = -optYRangUpper;
                    }
                    else
                    {
                        optYRangUpper = -optYRangLower;
                    }
                }

//                optYStep = (Bit32)((optYRangUpper - optYRangLower) / (allHeight / 2)) + 1;
                optYStep = (Bit32)((optYRangUpper * 2) / (allHeight / 2)) + 1;
                optYRangUpper = optYStep * (allHeight / 4);
                optYRangLower = -optYRangUpper;
            }
            YAxisSetTickParm(optYRangLower, optYRangUpper, optYStep);
            ui->wavePlot->yAxis2->setTickLength(0);
        }

        AutoRangParm(true, false);
    }
}

/**
 * @brief OscWave::LineZeroAddPoint 添加曲线0数据
 * @param [in]x     x轴数据
 * @param [in]y     y轴数据
 * @param [in]flag  是否调整Y轴
 */
void OscWave::LineZeroAddPoint(const QVector<double>x, const QVector<double>y, Bit32 flag)
{
    if(oscType == HmiOscServo::OSC_SERVO_SYNC) // 临时解决龙门同步图形绘制方式错误
    {
        ui->wavePlot->graph(0)->setLineStyle(QCPGraph::lsNone);
        ui->wavePlot->graph(0)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssSquare, 0.5));
    }
    else if(oscType == HmiOscServo::OSC_SERVO_LOOP)
    {
        ui->wavePlot->graph(0)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssCircle, 5));
    }

    ui->wavePlot->graph(0)->addData(x, y);
    if (!x.isEmpty() && x.last() > ui->wavePlot->xAxis->rangeUpper() && flag == 0)
    {
        Bit32 xValCount = ui->wavePlot->graph(0)->data()->keys().count();
        Bit32 xValMax = 0;
        if (xValCount - 1 >= 0)
        {
            xValMax =(fBit64)(ui->wavePlot->graph(0)->data()->keys().at(xValCount - 1)); // 取最后一个X值
        }
        if(oscType == HmiOscServo::OSC_SERVO_RASTER)
        {
            optXRangLower += 1000;
            optXRangUpper += 1000;
             XAxisSetTickParm(optXRangLower, optXRangUpper, optXStep);
        }
        else
        {
            optXRangUpper = xValMax;
            optXStep = (Bit32)(optXRangUpper / allLength) + 1;
            optXRangUpper = optXStep * allLength;
            XAxisSetTickParm(optXRangLower, optXRangUpper, optXStep);
            ui->wavePlot->xAxis2->setTickLength(0);
        }

    }

    if (flag == 0)
    {
        double yValMin = ui->wavePlot->yAxis->rangeLower();
        double yValMax = ui->wavePlot->yAxis->rangeUpper();
        for (Bit32 i =0; i < y.count(); i++)
        { // 计算最大值、最小值
            if (y.at(i) > yValMax)
            {
                yValMax = y.at(i);
            }
            if (y.at(i) < yValMin)
            {
                yValMin = y.at(i);
            }
        }
        if (yValMin < ui->wavePlot->yAxis->rangeLower()
                || yValMax > ui->wavePlot->yAxis->rangeUpper())
        {
            optYRangUpper = yValMax;
            optYRangLower = yValMin;
            if (ui->wavePlot->yAxis->rangeLower() == 0)
            { // y轴只有正向
                optYRangLower = 0;
                optYStep = (Bit32)((optYRangUpper - optYRangLower) / (allHeight / 2)) + 1;
                optYRangUpper = optYStep * (allHeight / 2);
            }
            else if (ui->wavePlot->yAxis->rangeLower() < 0)
            { // Y轴正负对称
                if (HNC_DoubleCompare(ui->wavePlot->yAxis->rangeUpper(), ui->wavePlot->yAxis->rangeLower() * (-1)))
                {
                    if (optYRangUpper > -optYRangLower)
                    {
                        optYRangLower = -optYRangUpper;
                    }
                    else
                    {
                        optYRangUpper = -optYRangLower;
                    }
                }

                optYStep = (Bit32)((optYRangUpper - optYRangLower) / (allHeight / 2)) + 1;
                optYRangUpper = optYStep * (allHeight / 4);
                optYRangLower = -optYRangUpper;
            }
            YAxisSetTickParm(optYRangLower, optYRangUpper, optYStep);
            ui->wavePlot->yAxis2->setTickLength(0);
        }

        AutoRangParm(false, true);
    }
}

/**
 * @brief OscWave::LineOneAddPoint
 * @param [in]x     X轴数据
 * @param [in]y     Y轴数据
 * @param [in]flag  是否调整Y轴
 */
void OscWave::LineOneAddPoint(const QVector<double>x, const QVector<double>y, Bit32 flag)
{
    if(oscType == HmiOscServo::OSC_SERVO_SYNC)
    {
        ui->wavePlot->graph(1)->setLineStyle(QCPGraph::lsNone);
        ui->wavePlot->graph(1)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssSquare, 0.5));
    }

    if (!x.isEmpty() && x.last() > ui->wavePlot->xAxis->rangeUpper() && flag == 0)
    {
        Bit32 xValCount = ui->wavePlot->graph(1)->data()->keys().count();
        Bit32 xValMax = 0;
        if (xValCount - 1 >= 0)
        {
            xValMax =(fBit64)(ui->wavePlot->graph(1)->data()->keys().at(xValCount - 1)); // 取最后一个X值
        }
        if(oscType != HmiOscServo::OSC_SERVO_RASTER)
        {
            optXRangUpper = xValMax;
            optXStep = (Bit32)(optXRangUpper / allLength) + 1;
            optXRangUpper = optXStep * allLength;
            XAxisSetTickParm(optXRangLower, optXRangUpper, optXStep);
            ui->wavePlot->xAxis2->setTickLength(0);
        }
    }

    if (flag == 0)
    {
        double yValMin = ui->wavePlot->yAxis->rangeLower();
        double yValMax = ui->wavePlot->yAxis->rangeUpper();
        for (Bit32 i =0; i < y.count(); i++)
        { // 计算最大值、最小值
            if (y.at(i) > yValMax)
            {
                yValMax = y.at(i);
            }
            if (y.at(i) < yValMin)
            {
                yValMin = y.at(i);
            }
        }
        if (yValMin < ui->wavePlot->yAxis->rangeLower()
                || yValMax > ui->wavePlot->yAxis->rangeUpper())
        {
            optYRangUpper = yValMax;
            optYRangLower = yValMin;
            if (ui->wavePlot->yAxis->rangeLower() == 0)
            { // y轴只有正向
                optYRangLower = 0;
                optYStep = (Bit32)((optYRangUpper - optYRangLower) / (allHeight / 2)) + 1;
                optYRangUpper = optYStep * (allHeight / 2);
            }
            else if (ui->wavePlot->yAxis->rangeLower() < 0)
            { // Y轴正负对称
                if (HNC_DoubleCompare(ui->wavePlot->yAxis->rangeUpper(), ui->wavePlot->yAxis->rangeLower() * (-1)))
                {
                    if (optYRangUpper > -optYRangLower)
                    {
                        optYRangLower = -optYRangUpper;
                    }
                    else
                    {
                        optYRangUpper = -optYRangLower;
                    }
                }

                optYStep = (Bit32)((optYRangUpper - optYRangLower) / (allHeight / 2)) + 1;
                optYRangUpper = optYStep * (allHeight / 4);
                optYRangLower = -optYRangUpper;
            }
            YAxisSetTickParm(optYRangLower, optYRangUpper, optYStep);
            ui->wavePlot->yAxis2->setTickLength(0);
        }

        AutoRangParm(false, true);
    }

    ui->wavePlot->graph(1)->addData(x, y);
//    ui->wavePlot->replot();
}

/**
 * @brief OscWave::ClearPoint 清除所有曲线数据
 */
void OscWave::ClearPoint()
{
    for (Bit32 i = ui->wavePlot->graphCount() - 1; i >= 0; i--)
    {
        ui->wavePlot->graph(i)->clearData();
        if (oscType == HmiOscServo::OSC_SERVO_COMP_SENSOR)
        {
            ui->wavePlot->removeGraph(ui->wavePlot->graph(i));
        }
    }
//    ui->wavePlot->replot();
    optXRangLower = baseXRangLower;
    optXRangUpper = baseXRangUpper;
    optXStep = baseXStep;
    optYRangLower = baseYRangLower;
    optYRangUpper = baseYRangUpper;

    if (oscType == HmiOscServo::OSC_SERVO_COMP_SENSOR)// && waveName == "TempAndThermalCurve")
    {
        for (int i = 0; i < m_vecpGraphSel.count(); ++i)
        {
            if (m_vecpGraphSel.at(i) != NULL)
            {
                m_vecpGraphSel.at(i)->clearData();
            }
        }
        for (int i = 0; i < m_vecpGraphSelOp.count(); ++i)
        {
            if (m_vecpGraphSelOp.at(i) != NULL)
            {
                m_vecpGraphSelOp.at(i)->clearData();
            }
        }

		m_dbHisMaxX = 0;
		m_dbHisMinX = 0;
		m_dbHisMaxY = 0;
		m_dbHisMinY = 0;
//        autoRangParmFlag = false;
//        return;
    }
    optYStep = baseYStep;

    this->PlotRestore();

    autoRangParmFlag = false;
}

/**
 * @brief OscWave::ClearPoint 清除某曲线数据
 * @param plotIdx 曲线索引
 */
void OscWave::ClearPoint(Bit32 plotIdx)
{
    for (Bit32 i = 0; i < ui->wavePlot->graphCount(); i++)
    {
        if (i == plotIdx)
        {
            ui->wavePlot->graph(i)->clearData();
            this->PlotRestore();
        }
    }
}

/**
 * @brief OscWave::ClearCurveName 清除曲线名称
 */
void OscWave::ClearCurveName()
{
    for (int i = 0; i < m_vecYNameLabel.count(); i++)
    {
        if (m_vecYNameLabel.at(i) != NULL)
        {
            m_vecYNameLabel.at(i)->setText("");
        }
    }
}

void OscWave::SetColor(QColor &bk, QColor &gd, QColor &ft, QColor &c1, QColor &c2, QColor &c3, QColor &c4)
{
    //
    ui->wavePlot->setBackground(bk);
    ui->wavePlot->xAxis->setTickLabelColor(ft);
    ui->wavePlot->xAxis->setLabelColor(ft);
    ui->wavePlot->xAxis->setBasePen(QPen(gd));
    ui->wavePlot->xAxis->setTickPen(QPen(ft));
    ui->wavePlot->xAxis->setSubTickPen(ft);
    ui->wavePlot->xAxis2->setTickLabelColor(ft);
    ui->wavePlot->xAxis2->setLabelColor(ft);
    ui->wavePlot->xAxis2->setBasePen(QPen(gd));
    ui->wavePlot->xAxis2->setTickPen(QPen(ft));
    ui->wavePlot->xAxis2->setSubTickPen(ft);

    ui->wavePlot->yAxis->setTickLabelColor(ft);
    ui->wavePlot->yAxis->setLabelColor(ft);
    ui->wavePlot->yAxis->setBasePen(QPen(gd));
    ui->wavePlot->yAxis->setTickPen(QPen(ft));
    ui->wavePlot->yAxis->setSubTickPen(ft);
    ui->wavePlot->yAxis2->setTickLabelColor(ft);
    ui->wavePlot->yAxis2->setLabelColor(ft);
    ui->wavePlot->yAxis2->setBasePen(QPen(gd));
    ui->wavePlot->yAxis2->setTickPen(QPen(ft));
    ui->wavePlot->yAxis2->setSubTickPen(ft);

    for (Bit32 i = 0; i < ui->wavePlot->graphCount(); i++)
    {
        if (0 == i)
        {
            ui->wavePlot->graph(i)->setPen(QPen(c1));
        }
        else if (1 == i)
        {
            ui->wavePlot->graph(i)->setPen(QPen(c2));
        }
        else if (2 == i)
        {
            ui->wavePlot->graph(i)->setPen(QPen(c3));
        }
        else
        {
            ui->wavePlot->graph(i)->setPen(QPen(c4));
        }
    }

   /* QPalette pa;
    pa.setColor(QPalette::WindowText, ft);
    ui->xName->setPalette(pa);
    ui->yName1->setPalette(pa);
    ui->yName2->setPalette(pa);
    ui->yName3->setPalette(pa);
    ui->yName4->setPalette(pa);
    ui->yName5->setPalette(pa);*/
    ui->xName->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName1->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName3->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName5->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));

    switch (oscType)
    {
    case HmiOscServo::OSC_SERVO_SPE:
    case HmiOscServo::OSC_SERVO_POS:
    case HmiOscServo::OSC_SERVO_TOOL:
       /* pa.setColor(QPalette::WindowText, c1);
        ui->yName2->setPalette(pa);
        pa.setColor(QPalette::WindowText, c2);
        ui->yName4->setPalette(pa);*/
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
        break;
    case HmiOscServo::OSC_SERVO_TAP:
        if (waveName == "SPE")
        {
            /*pa.setColor(QPalette::WindowText, c1);
            ui->yName1->setPalette(pa);
            pa.setColor(QPalette::WindowText, c2);
            ui->yName4->setPalette(pa);*/
            ui->yName1->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
            ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
        }
        break;
    case HmiOscServo::OSC_SERVO_FREQ:
       /* pa.setColor(QPalette::WindowText, c1);
        ui->yName2->setPalette(pa);
        pa.setColor(QPalette::WindowText, c2);
        ui->yName3->setPalette(pa);*/
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName3->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
        break;
    case HmiOscServo::OSC_SERVO_CUSTOM:
        /*pa.setColor(QPalette::WindowText, c1);
        ui->yName1->setPalette(pa);
        pa.setColor(QPalette::WindowText, c2);
        ui->yName2->setPalette(pa);*/
        ui->yName1->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
        break;
    case HmiOscServo::OSC_SERVO_SYNC:
        /*pa.setColor(QPalette::WindowText, c1);
        ui->yName2->setPalette(pa);
        pa.setColor(QPalette::WindowText, c2);
        ui->yName4->setPalette(pa);*/
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
        break;
    case HmiOscServo::OSC_SERVO_LOOP:
       /* pa.setColor(QPalette::WindowText, c1);
        ui->yName2->setPalette(pa);*/
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        break;
    case HmiOscServo::OSC_SERVO_ZCOMP:
    /*    pa.setColor(QPalette::WindowText, c1);
        ui->yName2->setPalette(pa);
        pa.setColor(QPalette::WindowText, c2);
        ui->yName4->setPalette(pa);*/
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
        break;
    case HmiOscServo::OSC_SERVO_SCOMP:
     /*   pa.setColor(QPalette::WindowText, c1);
        ui->yName2->setPalette(pa);
        pa.setColor(QPalette::WindowText, c2);
        ui->yName4->setPalette(pa);*/
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
		break;
	case HmiOscServo::OSC_SERVO_ACCE:
		ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
		ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
		break;
	case HmiOscServo::OSC_SERVO_KCOMPEN:
		ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
		ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
		break;
	case HmiOscServo::OSC_SERVO_INERTIA:
		ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
		ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
    	break;
    default:
        break;
    }

    ui->wavePlot->replot();
}

/**
 * @brief OscWave::SetGraphColor 设置图形颜色
 * @param [in] curveNo 图形编号
 */
void OscWave::SetGraphColor(Bit32 curveNo)
{
    if (curveNo >= 0 && curveNo < m_vecWaveCurve.count() && curveNo < ui->wavePlot->graphCount())
    {
        if (m_mapCurveColor.keys().contains(m_vecWaveCurve.at(curveNo)) == true)
        {
            QColor color = m_mapCurveColor.value(m_vecWaveCurve.at(curveNo));
            ui->wavePlot->graph(curveNo)->setPen(QPen(color));
        }
    }
}

/**
 * @brief OscWave::SetColor 设置颜色
 * @param [in] type         类型
 * @param [in] color        颜色
 */
void OscWave::SetColor(OscWave::ENType type, QColor &color)
{
    switch (type)
    {
    case OscWave::BACKGROUND:
        ui->wavePlot->setBackground(color);
        break;
    case OscWave::GRID:
        ui->wavePlot->xAxis->setBasePen(QPen(color));
        ui->wavePlot->xAxis2->setBasePen(QPen(color));

        ui->wavePlot->yAxis->setBasePen(QPen(color));
        ui->wavePlot->yAxis2->setBasePen(QPen(color));
        break;
    case OscWave::FOREGROUND:
        ui->wavePlot->xAxis->setTickLabelColor(color);
        ui->wavePlot->xAxis->setLabelColor(color);
        ui->wavePlot->xAxis->setTickPen(QPen(color));
        ui->wavePlot->xAxis->setSubTickPen(color);
        ui->wavePlot->xAxis2->setTickLabelColor(color);
        ui->wavePlot->xAxis2->setLabelColor(color);
        ui->wavePlot->xAxis2->setTickPen(QPen(color));
        ui->wavePlot->xAxis2->setSubTickPen(color);

        ui->wavePlot->yAxis->setTickLabelColor(color);
        ui->wavePlot->yAxis->setLabelColor(color);
        ui->wavePlot->yAxis->setTickPen(QPen(color));
        ui->wavePlot->yAxis->setSubTickPen(color);
        ui->wavePlot->yAxis2->setTickLabelColor(color);
        ui->wavePlot->yAxis2->setLabelColor(color);
        ui->wavePlot->yAxis2->setTickPen(QPen(color));
        ui->wavePlot->yAxis2->setSubTickPen(color);

        ui->labelWaveTitle->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
        ui->xName->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
        ui->yName1->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
        if (oscType != HmiOscServo::OSC_SERVO_COMP_SENSOR)
        {
            ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
            ui->yName3->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
            ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
            ui->yName5->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
        }
        break;
    default:
        break;
    }

    // 设置波形颜色
    if (m_vecWaveCurve.contains(type) == true)
    {
        Bit32 graphIndex = m_vecWaveCurve.indexOf(type);
        m_mapCurveColor.insert(type, color);
        if (graphIndex >= 0 && graphIndex < ui->wavePlot->graphCount())
        {
            ui->wavePlot->graph(graphIndex)->setPen(QPen(color));
        }
    }

    ui->wavePlot->replot();
}

void OscWave::SetAxisName(QStringList strList)
{
    if(strList.count() <= 0)
    {
        return;
    }
    switch(oscType)
    {
    case HmiOscServo::OSC_SERVO_SPE:
        if(strList.count() >= 2)
        {
            ui->yName1->setText(TR("%1(%2/min:").arg(strList[0]).arg(strList[1]));
        }
        break;
    case HmiOscServo::OSC_SERVO_POS:
        if(waveName == "POS")
        {
            if(strList.count() >= 2)
            {
                ui->yName1->setText(TR("%1(%2:").arg(strList[0]).arg(strList[1]));
            }
        }
        else if(waveName == "TrackErr")
        {
            ui->yName1->setText(TR("跟随误差(%1)").arg(strList[0]));
        }
        break;
    case HmiOscServo::OSC_SERVO_TOOL:
        ui->yName2->setText(HmiOscServo::ToolChangeReg());
        break;
    case HmiOscServo::OSC_SERVO_TAP:
        ui->yName2->setText(strList[0]);
        break;
    case HmiOscServo::OSC_SERVO_SYNC:
        if(strList.count() >= 2)
        {
            ui->yName2->setText(strList[0]);
            ui->yName4->setText(strList[1]);
        }
        break;
    case HmiOscServo::OSC_SERVO_FREQ:
        if(strList.count() >= 2)
        {
            ui->yName1->setText(strList[0]);
            ui->yName2->setText(strList[1]);
        }
        break;
    case HmiOscServo::OSC_SERVO_LOOP:
        if(strList.count() >= 2)
        {
            ui->yName1->setText(TR("E(%1:").arg(strList[0]));
            ui->xName->setText(TR("%1(mm)").arg(strList[1]));
        }
        break;
    case HmiOscServo::OSC_SERVO_RASTER:
        if(waveName == "POS")
        {
            //if(strList.count() >= 2)
            {
                ui->yName1->setText(TR("%1光栅信号波形图(单位：V)").arg(strList[0]));
            }
        }
        break;
    case HmiOscServo::OSC_SERVO_KCOMPEN:
        if (waveName == "Speed")
        {
            if (strList.count() >= 2)
            {
                ui->yName1->setText(TR("%1(%2/min:").arg(strList[0]).arg(strList[1]));
            }
        }
        else if (waveName == "TrackErr")
        {
            ui->yName1->setText(TR("%1跟随误差(%2)").arg(strList[0]).arg(strList[1]));
        }
        break;
	case HmiOscServo::OSC_SERVO_INERTIA:
		if (waveName == "Speed")//速度波形图
		{
			if (strList.count() >= 2)
				ui->yName1->setText(TR("%1速度(%2/min:").arg(strList[0]).arg(strList[1]));
		}
		if (waveName == "Electric") // 电流波形图
		{
			ui->yName1->setText(TR("%1电流(A)").arg(strList[0]));
		}
		break;
    default:
        break;
    }
}

void OscWave::UpdateYAxisName(QString strName, QString waveName)
{
    if(strName.isEmpty())
    {
        return;
    }
    bool ok = false;
    fBit64 hourTime = strName.toFloat(&ok) / 60;
    if(!ok)
    {
        MessageOut(TR("数据转换错误!"));
        return;
    }
    if (oscType == HmiOscServo::OSC_SERVO_COMP_SENSOR)
    {
        if(waveName == "TempAndThermalCurve1")
        {
            ui->yName1->setText(TR("升温段(%1h)伸长量K（um）").arg(QString::number(hourTime, 'f', 1)));
        }
        else if(waveName == "RTLCurve1")
        {
            ui->yName1->setText(TR("升温段(%1h)飘移量B（mm）").arg(QString::number(hourTime, 'f', 1)));
        }
        else if (waveName == "ThermalCurve1")
        {
            ui->yName1->setText(TR("升温段(%1h)变形量（um/m）").arg(QString::number(hourTime, 'f', 1)));
        }
        else if (waveName == "SamplCurve1")
        {
            ui->yName1->setText(TR("升温段(%1h)目标点温度采样（℃）").arg(QString::number(hourTime, 'f', 1)));
        }
    }
}

void OscWave::CreateGroup(QCustomPlot *plot) // 关联两个波形图，使其布局一致
{
    if (NULL == plot)
    {
        return;
    }
    QCPMarginGroup *group = new QCPMarginGroup(ui->wavePlot);
    ui->wavePlot->axisRect()->setMarginGroup(QCP::msLeft|QCP::msRight, group);
    plot->axisRect()->setMarginGroup(QCP::msLeft|QCP::msRight, group);
}

/**
 * @brief OscWave::SetCurveName 设置曲线名称及颜色
 * @param [in] no 曲线编号(从1开始)
 */
void OscWave::SetCurveName(Bit32 no)
{
    // 例:no = 2,显示曲线名称为(C1/C2),m_vecYNameLabel的个数至少需要5个，所以需要判断m_vecYNameLabel的个数是否为大于等于2*no+1
    if (m_vecYNameLabel.count() < (no * 2 + 1))
    {
        return;
    }

    if (m_vecYNameLabel.at(0) != NULL)
    {
        m_vecYNameLabel.at(0)->setText("(");
    }

    QColor color(255, 255, 255);
    for (int i = 0; i < no; i++)
    {
        if (m_mapCurveColor.keys().contains(m_vecWaveCurve.at(i)) == true)
        {
            color = m_mapCurveColor.value(m_vecWaveCurve.at(i));
        }

        Bit32 index = i * 2 + 1;
        if (m_vecYNameLabel.at(index) != NULL)
        {
            m_vecYNameLabel.at(index)->setText(QString("C%1").arg(i+1));
            if (i >= 0 && i < m_vecWaveCurve.count())// && i < ui->wavePlot->graphCount())
            {
                if (index == 1)
                {
                    m_vecYNameLabel.at(0)->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
                }
                m_vecYNameLabel.at(index)->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
            }
        }

        if (m_vecYNameLabel.at((i + 1) * 2) != NULL)
        {
            m_vecYNameLabel.at((i + 1) * 2)->setText("/");
            m_vecYNameLabel.at((i + 1) * 2)->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
        }
    }

    if (m_vecYNameLabel.at(no * 2) != NULL)
    {
        m_vecYNameLabel.at(no * 2)->setText(")");
        m_vecYNameLabel.at(no * 2)->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(color.red()).arg(color.green()).arg(color.blue()));
    }
}

void OscWave::GetDataLimit(const QVector<double>& vecValue, fBit64& min, fBit64& max)
{
    if (vecValue.count() != 0)
    {
        min = vecValue.at(0);
        max = vecValue.at(0);

        for (int i = 0; i < vecValue.count(); i++)
        {
            if (HNC_DoubleCompare(min, vecValue.at(i)) > 0)
            {
                min = vecValue.at(i);
            }

            if (HNC_DoubleCompare(max, vecValue.at(i)) < 0)
            {
                max = vecValue.at(i);
            }
        }
    }
}

void OscWave::YAxisGetDataMaxAndMin(Bit32 index, fBit64& yMin, fBit64& yMax)
{
    if (index >= ui->wavePlot->graphCount())
    {
        return;
    }

    QList<QCPData> list = ui->wavePlot->graph(0)->data()->values();

    if (list.count() == 0)
    {
        return;
    }

    fBit64 min = list.at(0).value;
    fBit64 max = list.at(0).value;

    for (int i = 0; i < list.count(); i++)
    {
        if (HNC_DoubleCompare(min, list.at(i).value) > 0)
        {
            min = list.at(i).value;
        }

        if (HNC_DoubleCompare(max, list.at(i).value) < 0)
        {
            max = list.at(i).value;
        }
    }

    yMin = min;
    yMax = max;
}

void OscWave::RasterSpecialSetWave()
{
    if(oscType == HmiOscServo::OSC_SERVO_RASTER)
    {
        optYRangLower = baseYRangLower;
        optYRangUpper = baseYRangUpper;
        optYStep = baseYStep;
    }
}

void OscWave::SetRasterZoomYUp()
{
    fBit64 lower = ui->wavePlot->yAxis->rangeLower();
    fBit64 upper = ui->wavePlot->yAxis->rangeUpper();
    fBit64 tickStep = ui->wavePlot->yAxis->tickStep();

    fBit64 midVal = lower + tickStep * yStepNum / 2;

    if (tickStep > plotYStepMix)
    {
        if (uBit32(tickStep / 2.0 * 1000 + 0.0001) > uBit32(plotYStepMix * 1000))
        {
            tickStep = uBit32(tickStep / 2.0 * 1000 + 0.0001) / 1000.0;
        }
        else
        {
            tickStep = plotYStepMix;
        }

        upper = midVal + tickStep * yStepNum / 2;
        lower = midVal - tickStep * yStepNum / 2;

        ui->wavePlot->yAxis->setRange(lower, upper);
        ui->wavePlot->yAxis->setTickStep(tickStep);

        ui->wavePlot->yAxis2->setRange(lower, upper);
        ui->wavePlot->yAxis2->setTickStep(tickStep);

        ui->wavePlot->replot();
    }
}

void OscWave::SetRasterZoomYDn()
{
    fBit64 lower = ui->wavePlot->yAxis->rangeLower();
    fBit64 upper = ui->wavePlot->yAxis->rangeUpper();
    fBit64 tickStep = ui->wavePlot->yAxis->tickStep();

    fBit64 midVal = lower + tickStep * yStepNum / 2;

    if (tickStep < plotYStepMax)
    {
        if (uBit32(tickStep * 2.0 * 1000 + 0.0001) < uBit32(plotYStepMax * 1000))
        {
            tickStep = uBit32(tickStep * 2.0 * 1000 + 0.0001) / 1000.0;
        }
        else
        {
            tickStep = plotYStepMax;
        }

        upper = midVal + tickStep * yStepNum / 2;
        lower = midVal - tickStep * yStepNum / 2;

        ui->wavePlot->yAxis->setRange(lower, upper);
        ui->wavePlot->yAxis->setTickStep(tickStep);

        ui->wavePlot->yAxis2->setRange(lower, upper);
        ui->wavePlot->yAxis2->setTickStep(tickStep);

        ui->wavePlot->replot();
    }
}

void OscWave::SetRasterZoomXUp()
{
    fBit64 lower = ui->wavePlot->xAxis->rangeLower();
    fBit64 upper = ui->wavePlot->xAxis->rangeUpper();
    fBit64 tickStep = ui->wavePlot->xAxis->tickStep();

    if (tickStep > plotXStepMix)
    {
        if (Bit32(tickStep / 2.0 * 100 + 0.001) > Bit32(plotXStepMix * 100))
        {
            tickStep = Bit32(tickStep / 2.0 + 0.001);
        }
        else
        {
            tickStep = plotXStepMix;
        }

        upper = lower + tickStep * xStepNum;

        ui->wavePlot->xAxis->setRange(lower, upper);
        ui->wavePlot->xAxis->setTickStep(tickStep);

        ui->wavePlot->xAxis2->setRange(lower, upper);
        ui->wavePlot->xAxis2->setTickStep(tickStep);

        ui->wavePlot->replot();
    }
}

void OscWave::SetRasterZoomXDn()
{
    fBit64 lower = ui->wavePlot->xAxis->rangeLower();
    fBit64 upper = ui->wavePlot->xAxis->rangeUpper();
    fBit64 tickStep = ui->wavePlot->xAxis->tickStep();

    if (tickStep < plotXStepMax)
    {
        if (Bit32(tickStep * 2.0 * 100 + 0.001) < Bit32(plotXStepMax * 100))
        {
            tickStep = Bit32(tickStep * 2.0 + 0.001);
        }
        else
        {
            tickStep = plotXStepMax;
        }

        upper = lower + tickStep * xStepNum;

        ui->wavePlot->xAxis->setRange(lower, upper);
        ui->wavePlot->xAxis->setTickStep(tickStep);

        ui->wavePlot->xAxis2->setRange(lower, upper);
        ui->wavePlot->xAxis2->setTickStep(tickStep);

        ui->wavePlot->replot();
    }
}

void OscWave::RasterAddPoint(QVector<fBit64> x, QVector<fBit64> y)
{
    ui->wavePlot->graph(0)->addData(x, y);

    fBit64 yMax = 0.0;
    fBit64 yMin = 0.0;

    fBit64 yAve = 0.0;
    fBit64 yTotal = 0.0;

    Bit32 yMaxIt = 0;
    Bit32 yMinIt = 0;

    // y轴自适应
    yMaxIt = std::max_element(y.begin(), y.end()) - y.begin();
    yMinIt = std::min_element(y.begin(), y.end()) - y.begin();
    if (yMaxIt >=0 && yMaxIt < y.count())
    {
        yMax = y.at(yMaxIt);
    }
    if (yMinIt >=0 && yMinIt < y.count())
    {
        yMin = y.at(yMinIt);
    }
    for(Bit32 i = 0; i < y.count(); i++)
    {
        yTotal += y.at(i);
    }
    yAve = yTotal / y.count();

    fBit64 yTmp = (yMax - yMin) / yStepNum;
    fBit64 yLower = 0.0;
    fBit64 yUpper = 0.0;
    fBit64 yStepValue = 0.0;

    if (HNC_DoubleCompare(yTmp, 1) > 0)
    {
        // ceil取大于等于的最小整数
        Bit32 tmpStepValue = (Bit32)ceil(yTmp);

        if (tmpStepValue == 0)
        {
            return;
        }

        yStepValue = tmpStepValue;
        yUpper = yAve + yStepValue * yStepNum / 2;
        yLower = yAve - yStepValue * yStepNum / 2;
    }
    else
    {
        // ceil取大于等于的最小整数
        fBit64 tmpStepValue = ceil(yTmp * 1000) / 1000;

        if (HNC_DoubleCompare(0, tmpStepValue) == 0)
        {
            return;
        }

        yStepValue = tmpStepValue;
        yUpper = yAve + yStepValue * yStepNum / 2;
        yLower = yAve - yStepValue * yStepNum / 2;
    }

    YAxisSetTickParm(yLower, yUpper, yStepValue);
    baseYRangLower = yLower;
    baseYRangUpper = yUpper;
    baseYStep = yStepValue;
}

void OscWave::SetWaveTitle(const QString& title)
{
    ui->labelWaveTitle->setText(title);
}
