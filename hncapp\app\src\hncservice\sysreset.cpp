﻿/*!
 * @file sysreset.cpp
 * @brief 用于启动复位和获取相应通道复位状态
 * @note  ResetStart(ch[]):启动复位
 *        ResetOnMsg(ch,code):处理定时器刷新或获取复位过程中的状态变化
 *        ResetFinish():检测所有通道是否复位完成
 *        GetResetState(ch):获取某个通道的状态
 *        GetResetError(ch):获取某个通道产生的异常
 *
 * @version V1.00
 * @date 2022/12/20
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */
#include "hncsys.h"
#include "hncreg.h"
#include "hncchan.h"
#include "hncalarm.h"
#include "hncsysctrl.h"

#include "common.h"
#include "hmicommon.h"
#include "mdimanage.h"
#include "prog.h"
#include "hmialarmdef.h"
#include "hmiselfadjusting.h"

#include "sysreset.h"

#define CLEAR_PROGMDI_STEP 0

const Bit32 RESET_TIME = 5; // 复位时间不能超过5秒

time_t SysReset::m_resetTime = 0;
SysReset::ENState SysReset::m_nResetState[SYS_CHAN_NUM] = {SysReset::FREE, SysReset::FREE, SysReset::FREE, SysReset::FREE};
SysReset::ENError SysReset::m_nResetError[SYS_CHAN_NUM] = {SysReset::ERR_NORMAL};

SysReset::SysReset()
{
    for (Bit32 ch = 0; ch < SYS_CHAN_NUM; ch++)
    {
        m_nResetState[ch] = FREE;
        m_nResetError[ch] = ERR_NORMAL;
    }
}

SysReset::~SysReset()
{
}

/**
 * @brief SysReset::ResetStart 多通道启动复位
 * @param ch 通道号
 * @return 0:全部启动失败 ret:启动成功的通道
 */
Bit32 SysReset::ResetStart(const Bit32 ch[SYS_CHAN_NUM])
{
    Bit32 chNo = ActiveChan();
    Bit32 ret = 0;
    if (IsEStop(chNo) == true)
    {
        return ret;
    }
    ENError err = ERR_NORMAL;
    for (Bit32 i = 0; i < SYS_CHAN_NUM; i++)
    {
        if (ch[i] == 1)
        {
            err = ResetStart(i);
            if (err == ERR_NORMAL)
            {
                ret += (1 << i);
            }
        }
    }

    return ret;
}

/**
 * @brief SysReset::ResetStart 单通道启动复位
 * @param ch 通道号
 * @return
 */
SysReset::ENError SysReset::ResetStart(Bit32 ch)
{
    if (IsEStop(ch) == true)
    {
        return  ERR_ESTOP;
    }
    ENError ret = CheckSysReset(ch);
    if (ret != ERR_NORMAL)
    {
        if (ret == ERR_UNDONE)
        {
            return  ret;
        }
        m_nResetState[ch] = FAILED;
        m_nResetError[ch] = ret;
        return ret;
    }

    /* sjy 2020.8.8
    * 1、非运行中，才清除复位标记，防止校验进行中时按复位，内核的插补缓冲区数据处理流程异常。
    * 2、校验进行中时复位，由内核发送ncEvtVerifyFinish来清除校验标记
    * */
    if (IsRunning(ch) == 0)
    {
        Bit32 i32 = 0;
        HNC_ChannelSetValue(HNC_CHAN_VERIFY, ch, 0, &i32);
    }
    ChangeChStateReset(ch, true);
    SysResetOnChan(ch);

    return ret;

}

/**
 * @brief SysReset::SysResetFinish 复位完成清除标记
 */
void SysReset::ResetFinish()
{
    if (IsAllSysResetFinish() == false)
    {
        return;
    }

    ResetFinishClearFlag();
}

/**
 * @brief SysReset::GetResetState 获取某通道的状态
 * @param ch 通道号
 * @return
 */
SysReset::ENState SysReset::GetResetState(Bit32 ch)
{
    return m_nResetState[ch];
}

/**
 * @brief SysReset::GetResetError 获取某通道产生的异常
 * @param ch
 * @return
 */
SysReset::ENError SysReset::GetResetError(Bit32 ch)
{
    return m_nResetError[ch];
}

/**
 * @brief SysReset::SysResetRefresh 定时刷新
 * @return
 */
SysReset::ENState SysReset::SysResetRefresh()
{
    ENState result = FREE;
    if (IsAllSysResetFinish() == true)
    {
        return result;
    }

    time_t td = 0;
    time(&td);

    if (((td - m_resetTime) >= RESET_TIME))
    {
        for (Bit32 ch = 0; ch < SYS_CHAN_NUM; ch++)
        {
            if (m_nResetState[ch] == PROGRESS)
            {
                result = FAILED;
                m_nResetState[ch] = FAILED;

                ClrProgMdiResetStep(ch);
            }
        }

    }

    return result;
}


/**
 * @brief SysReset::SysResetOnChan
 * @param ch
 * @return
 */
SysReset::ENState SysReset::SysResetOnChan(Bit32 ch)
{
    ENState ret = FREE;
    uBit16 code = kbReset;

    time(&m_resetTime);
//    HmiSelfAdjusting::SelfAdjustStopFunc(); // 2.42屏蔽此处

    ret = ResetOnMsg(ch, code);

    return ret;
}

/**
 * @brief SysReset::ResetOnMsg prog、mdi复位
 * @param ch 通道号
 * @param code 事件
 * @return
 */
SysReset::ENState SysReset::ResetOnMsg(Bit32 ch, uBit16 code)
{
    // 定时器消息
    if (ch == -1 && code == 0xFFFF)
    {
        return SysResetRefresh();
    }
    Bit32 ret = -1;
    ENState state = FREE;
    // MDI复位：运行中先停止后清除；非运行中直接清除；
    if (GetMdiState(ch) == MDI_STATE)
    {
        Prog::ProgClearResetStep(ch);
        ret = MdiManage::MdiOnReset(ch, code);
    }
    else
    {
        ret = Prog::ProgOnReset(ch, code);
    }
    state = Bit32ToENState(ret);
    // 解决mdi复位完毕后触发行解释会改变标记问题
    if (state != FREE)
    {
        m_nResetState[ch] = state;
    }
    if (state == SUCCESS)
    {
        ChangeChStateReset(ch, false);
    }

    return state;

}

/**
 * @brief SysReset::CheckSysReset 是否能复位
 * @param ch 通道号
 * @return   ERR_FORBID:禁止复位
 *           ERR_UNDONE:上一次复位未完成,不能进行复位!
 *           ERR_ESTOP :处于急停无法复位
 *           ERR_HOMING:回零第二阶段不能进行复位!
 *           ERR_THREAD:正在螺纹加工,不能进行复位!
 *           ERR_RIDCYC:正在刚性攻丝,不能进行复位!
 *           ERR_NORMAL:可进行复位
 */
SysReset::ENError SysReset::CheckSysReset(Bit32 ch)
{
    Bit32 homingState = 0;
    Bit32 threadState = 0;
    Bit32 rigid = 0;
    Bit32 isCycle = 0;
    Bit32 val = 0;
    Bit32 chRegIndex = 0;

    if (m_nResetState[ch] == PROGRESS)
    {
        return ERR_UNDONE;
    }

    HNC_RegGetFGBase(REG_FG_CHAN_BASE, &chRegIndex);
    chRegIndex = chRegIndex * 2 + 80 * ch + 76;
    // #define REG_CH_CTRL2        76 //通道控制字2
    // #define CH_CTRL_RESET_FORBID      0X0010 // 通道禁止复位【Reset按键无效】
    HNC_RegGetValue(REG_TYPE_G, chRegIndex, &val);
    if ((val & 0x0010) != 0)
    {
        return ERR_FORBID;
    }

    if (IsEStop(ch) == true)
    {
        return ERR_ESTOP;
    }

    // 回零第二阶段
    HNC_ChannelGetValue(HNC_CHAN_IS_HOMING, ch, 0, &homingState);
    if (homingState == 2)
    {
        return ERR_HOMING;
    }

    // 螺纹加工
    HNC_ChannelGetValue(HNC_CHAN_IS_THREADING, ch, 0, &threadState);
    if (threadState != 0)
    {
        return ERR_THREAD;
    }

    // 刚性攻丝
    HNC_ChannelGetValue(HNC_CHAN_IS_RIGID, ch, 0, &rigid);

    HNC_ChannelGetValue(HNC_CHAN_CYCLE, ch, 0, &isCycle); // bug4787
    if (rigid && isCycle)
    {
        return ERR_RIDCYC;
    }

    return ERR_NORMAL;
}

/**
 * @brief SysReset::IsAllSysResetFinish 所有通道是否复位完成
 * @return
 */
bool SysReset::IsAllSysResetFinish()
{
    for (Bit32 ch = 0; ch < SYS_CHAN_NUM; ch++)
    {
        if (m_nResetState[ch] == PROGRESS)
        {
            return false;
        }
    }

    return true;
}

/**
 * @brief SysReset::ResetFinishClearFlag 清除标记
 */
void SysReset::ResetFinishClearFlag()
{
    Bit32 i32 = 0;
    for (Bit32 ch = 0; ch < SYS_CHAN_NUM; ch++)
    {
        if (m_nResetState[ch] == FAILED)
        {
            ClrMulChanResetFlag();
        }
        else if (m_nResetState[ch] == SUCCESS)
        {
            ClrMulChanResetFlag();
            // 清除自检间隔提示
            HNC_AlarmClr(ALARM_TYPE_ALL, ALARM_LEVEL_ALL);
            HNC_AlarmDelAlarm(ALARM_HM, ALARM_MSG, 0, HMI_MSG_SELFTEST);
//            HNC_AlarmDelAlarm(ALARM_HM, ALARM_ERR, 0, HMI_ERR_RANDOM_LINE_OVER_TIME); // 复位清除超时报警(2.42无任意行流程超时报警)

//            // 清除校验标记
//            HNC_ChannelSetValue(HNC_CHAN_VERIFY, ch, 0, &i32);
        }

        m_nResetState[ch] = FREE;
    }
}
/**
 * @brief SysReset::ClrProgMdiResetStep 超时后重置状态机
 * @param ch
 */
void SysReset::ClrProgMdiResetStep(Bit32 ch)
{
    if (GetMdiState(ch) == MDI_STATE)
    {
        MdiManage::SetMdiResetStep(ch, CLEAR_PROGMDI_STEP);
    }
    else
    {
        Prog::SetProgResetStep(ch, CLEAR_PROGMDI_STEP);
    }
}

/**
 * @brief SysReset::ClrMulChanResetFlag 多通道清除复位标记
 */
void SysReset::ClrMulChanResetFlag()
{
    Bit32 i32 = 0;
    if (GetChannelNum() > 1)
    {
        // 多通道需要清除复位标记
        HNC_SystemSetValue(HNC_SYS_MUL_CHAN_RESET, &i32);
    }
}

void SysReset::ChangeChStateReset(Bit32 ch, bool flag)
{
    if (GetChannelNum() <= 1)
    {
        return;
    }
    Bit32 chRegBase = 0;
    HNC_RegGetFGBase(REG_FG_CHAN_BASE, &chRegBase);
    if (flag == true)
    {
        // #define REG_CH_STAT2        76 // 通道状态字2
        // #define CH_STATE_RESET	0x0001 //通道复位
        HNC_RegSetBit(REG_TYPE_F, chRegBase * 2 + 80 * ch + 76, 0);
    }
    else
    {
        // #define REG_CH_STAT2        76 // 通道状态字2
        // #define CH_STATE_RESET	0x0001 //通道复位
        HNC_RegClrBit(REG_TYPE_F, chRegBase * 2 + 80 * ch + 76, 0);
    }
}

SysReset::ENState SysReset::Bit32ToENState(Bit32 ret)
{
    if (ret == -1)
    {
         return FREE;
    }
    else if (ret == 0)
    {
        return PROGRESS;
    }
    else if (ret == -2)
    {
        return FAILED;
    }
    else if (ret == 1)
    {
        return SUCCESS;
    }

    return FREE;
}
