﻿
#include <QIntValidator>
#include <QKeyEvent>
#include <QValidator>

#include "hncreg.h"
#include "hncregdef.h"

#include "common.h"
#include "hmioscservo.h"
#include "msgchan.h"

#include "osctoolconf.h"
#include "ui_osctoolconf.h"

OscToolConf::OscToolConf(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscToolConf)
{
    ui->setupUi(this);
    this->curFocusIndex = 0;

    intValidatorToolNo = new QIntValidator(this);
    intValidatorPeriod = new QIntValidator(this);
    QRegExp expReg("^([R|r]\\d+\\.\\d)$"); // 范围0~100000
    validatorReg = new QRegExpValidator(expReg);

    intValidatorToolNo->setBottom(1);
    intValidatorToolNo->setTop(1000);
    intValidatorPeriod->setBottom(1);
    intValidatorPeriod->setTop(1000);

    wList.clear();
    wList.append(ui->labelToolNo);
    wList.append(ui->labelToolSignalReg);
    wList.append(ui->labelPeriodMulti);
    for(int i = 0; i < wList.count(); i++)
    {
        wList.at(i)->setFocusPolicy(Qt::StrongFocus);
        wList.at(i)->installEventFilter(this);
    }
    this->dirLayout = new DirMoveLayout(this);
    dirLayout->SetDirMoveLayout(3, 1, this->wList);
}

OscToolConf::~OscToolConf()
{
    delete ui;
}

void OscToolConf::LoadData()
{
    Bit32 ch = ActiveChan();
    ui->labelToolNo->setText(QString::number(HmiOscServo::s_Conf[ch].stToolConf.toolNo));
    ui->labelToolSignalReg->setText(QString("R%1.%2").arg(HmiOscServo::s_Conf[ch].stToolConf.toolIdx).arg(HmiOscServo::s_Conf[ch].stToolConf.toolBit));
//    ui->labelOscPeriod->setText(QString::number(HmiOscServo::s_Conf[ch].stToolConf.toolPeriod));

    Bit32 ncuCycle = 0;
    Bit32 multi = 0;
    Bit32 ret = HmiOscServo::GetNcuCycleMulti(HmiOscServo::s_Conf[ch].stToolConf.toolPeriod, ncuCycle, multi);
    ncuCycle = ncuCycle / 1000;

    if(ret == -1)
    {
        ui->labelPeriodMulti->setText("");
        ui->labelNcuCycle->setText(QString::number(ncuCycle));
        ui->labelPeriodResult->setText(QString::number(HmiOscServo::s_Conf[ch].stToolConf.toolPeriod));

        MessageOut(QObject::TR("采样周期不是插补周期整数倍，请手动设置采样周期"));
    }
    else
    {
        ui->labelPeriodMulti->setText(QString::number(multi));
        ui->labelNcuCycle->setText(QString::number(ncuCycle));
        ui->labelPeriodResult->setText(QString::number(ncuCycle * multi));
    }
}

void OscToolConf::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    UNREFERENCED_PARAM(messageValue);
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        this->LoadData();
        //this->FocusRedraw();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::SETFOCUS)
    {
        this->LoadData();
        this->wList.at(this->curFocusIndex)->setFocus();
    }
}

bool OscToolConf::eventFilter(QObject *wg, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        QWidget *tmp = dynamic_cast<QWidget *>(wg);
        if(this->wList.contains(tmp) && tmp != NULL)
        {
            this->curFocusIndex = this->wList.indexOf(tmp);
            if(keyEvent->key() == Qt::Key_Right || keyEvent->key() == Qt::Key_Left
              || keyEvent->key() == Qt::Key_Down || keyEvent->key() == Qt::Key_Up)
            {
                dirLayout->DirMoveOnKey(tmp, keyEvent->key());
                this->curFocusIndex = dirLayout->GetCurFocusIdx();
                return true;
            }
            else if(keyEvent->key() == Qt::Key_Enter || keyEvent->key() == Qt::Key_Return)
            {
                this->CurValSet(this->curFocusIndex);
                return true;
            }

        }
    }
    else if(event->type() == QEvent::FocusIn)
    {
        QWidget *tmp = dynamic_cast<QWidget *>(wg);
        if(this->wList.contains(tmp) && tmp != NULL)
        {
            this->curFocusIndex = this->wList.indexOf(tmp);
        }
    }
    return QObject::eventFilter(wg, event);
}


void OscToolConf::CurValSet(Bit32 row)
{
    QString editStr = "";
    Bit32 ret = 0;
    Bit32 ch = ActiveChan();
    bool ok = false;
    Bit32 cycle = HmiOscServo::GetNcuCycle() / 1000;
    if(cycle == 0)
    {
        cycle = 1;
    }

    MessageOut("");

    if(row < 0 || row >= this->wList.count())
    {
        return;
    }

    if (this->curFocusIndex == 0)
    {
        editStr = QString::number(HmiOscServo::s_Conf[ch].stToolConf.toolNo);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[换刀刀号]:"), -1, -1, 0, intValidatorToolNo);
        if(ret == 0)
        {
            Bit32 toolNo = editStr.toLong(&ok);
            if (ok == false || toolNo < 1 || toolNo > 1000)
            {
                MessageOut(TR("输入数据无效！范围：%1~%2").arg(1).arg(1000));
                this->wList.at(row)->setFocus();
                return;
            }

            HmiOscServo::s_Conf[ch].stToolConf.toolNo = toolNo;
            this->LoadData();
        }
        //this->wList.at(row)->setFocus();
        MsgChan::Instance().TranMsg(MsgData::SETFOCUS, ""); // 设置焦点
    }
    else if (this->curFocusIndex == 1)
    {
        editStr = QString("R%1.%2").arg(HmiOscServo::s_Conf[ch].stToolConf.toolIdx).arg(HmiOscServo::s_Conf[ch].stToolConf.toolBit);
        ret = MessageInput(&editStr, DTYPE_STRING, TR("请输入[换刀信号点]:"), -1, -1, 0, validatorReg);
        if(ret == 0)
        {
            Bit8 buf[128] = {0};
            Bit32 type = 0;
            Bit32 toolIdx = 0;
            Bit32 i = 0;
            Bit32 flag = 0;
            Bit32 min = 0;
            Bit32 max = 0;
            Bit32 toolBit = 0;
            strlcpy(buf, editStr.toStdString().data(), 128);

            if ( buf[0] == 'R' || buf[0] == 'r' )
            {
                type = SAMPL_R_REG;
            }
            else
            {
                MessageOut(TR("寄存器名错误，请输入R寄存器！"));
                this->wList.at(row)->setFocus();
                return;
            }

            if (buf[1] < '0' || buf[1] > '9')
            {
                MessageOut(TR("输入数据无效！"));
                this->wList.at(row)->setFocus();
                return;
            }

            for( i = 1; buf[i] != '\0'; i++)
            {
                if ( buf[i] >= '0' && buf[i] <= '9')
                {
                    toolIdx = toolIdx*10 + (buf[i] - '0');
                }
                else if (buf[i] == '.')
                {
                    flag = i;
                    if (buf[i+1] >= '0' && buf[i+1] <= '7')
                    {
                        toolBit = (Bit32)(buf[i+1] - '0');
                    }
                    else
                    {
                        MessageOut(TR("输入数据无效！"));
                        this->wList.at(row)->setFocus();
                        return;
                    }

                    if (buf[i+2] != '\0')
                    {
                        MessageOut(TR("输入数据无效！"));
                        this->wList.at(row)->setFocus();
                        return;
                    }
                    else
                    {
                        break;
                    }
                }
                else
                {
                    MessageOut(TR("输入数据无效！"));
                    this->wList.at(row)->setFocus();
                    return;
                }
            }
            min = 0;
            HNC_RegGetNum(REG_TYPE_R, &max);
            if ( toolIdx > max-1 || toolIdx < min || flag == -1)
            {
                MessageOut(TR("输入数据无效！"));
                this->wList.at(row)->setFocus();
                return;
            }
            else
            {
                HmiOscServo::s_Conf[ch].stToolConf.toolIdx = toolIdx;
                HmiOscServo::s_Conf[ch].stToolConf.toolBit  = toolBit;
                HmiOscServo::s_Conf[ch].stToolConf.toolReg = type;
                this->LoadData();
            }
        }
        //this->wList.at(row)->setFocus();
        MsgChan::Instance().TranMsg(MsgData::SETFOCUS, ""); // 设置焦点
    }
    else
    {
        editStr = QString::number(HmiOscServo::s_Conf[ch].stToolConf.toolPeriod / cycle);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[采样周期/插补周期 倍率]:"), -1, -1, 0, intValidatorPeriod);
        if(ret == 0)
        {
            Bit32 tmpPeriod = editStr.toLong(&ok);
            Bit32 min = 1;
            Bit32 max = 1000 / cycle;
            if (ok == false || tmpPeriod < min || tmpPeriod > max)
            {
                MessageOut(TR("输入数据无效！有效范围为：%1~%2").arg(min).arg(max));
                this->wList.at(row)->setFocus();
                return;
            }
            HmiOscServo::s_Conf[ch].stToolConf.toolPeriod = tmpPeriod * cycle;
            this->LoadData();
        }
        //this->wList.at(row)->setFocus();
        MsgChan::Instance().TranMsg(MsgData::SETFOCUS, ""); // 设置焦点
    }
    HmiOscServo::OscServoDataSave();
}

void OscToolConf::FocusRedraw()
{
    if (curFocusIndex == 0)
    {
        ui->labelToolNo->setProperty("selected", true);
        ui->labelToolNo->style()->polish(ui->labelToolNo);
        ui->labelToolSignalReg->setProperty("selected", false);
        ui->labelToolSignalReg->style()->polish(ui->labelToolSignalReg);
        ui->labelPeriodMulti->setProperty("selected", false);
        ui->labelPeriodMulti->style()->polish(ui->labelPeriodMulti);
    }
    else if (curFocusIndex == 1)
    {
        ui->labelToolNo->setProperty("selected", false);
        ui->labelToolNo->style()->polish(ui->labelToolNo);
        ui->labelToolSignalReg->setProperty("selected", true);
        ui->labelToolSignalReg->style()->polish(ui->labelToolSignalReg);
        ui->labelPeriodMulti->setProperty("selected", false);
        ui->labelPeriodMulti->style()->polish(ui->labelPeriodMulti);
    }
    else
    {
        ui->labelToolNo->setProperty("selected", false);
        ui->labelToolNo->style()->polish(ui->labelToolNo);
        ui->labelToolSignalReg->setProperty("selected", false);
        ui->labelToolSignalReg->style()->polish(ui->labelToolSignalReg);
        ui->labelPeriodMulti->setProperty("selected", true);
        ui->labelPeriodMulti->style()->polish(ui->labelPeriodMulti);
    }
}

