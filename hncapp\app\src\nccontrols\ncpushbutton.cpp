﻿#include <QKeyEvent>

#include "ncpushbutton.h"

NcPushButton::Nc<PERSON><PERSON><PERSON>utton(QWidget *parent) :
    QPushButton(parent)
{
}

NcPushButton::NcPushButton(const QString &text, QWidget *parent):
    QPushButton(parent)
{
    setText(text);
}

void NcPushButton::keyPressEvent(QKeyEvent *e)
{
    if(e == NULL)
    {
        return;
    }
    if(e->key() == Qt::Key_Space)
    {
        e->accept();
    }
    else
    {
        //QPushButton::keyPressEvent(e);
    }
}
