﻿#ifndef ROUNDWAVE_H
#define ROUNDWAVE_H

#include "containerwidget.h"

namespace Ui {
class RoundWave;
}

class RoundwaveGraph:public QObject
{
public:
    enum
    {
        NoOperation = 0x0000,
        AddPoint = 0x0001,
        ClearPoint = 0x0002,
    };
    explicit RoundwaveGraph(QObject *parent = 0):QObject(parent){}
    ~RoundwaveGraph(){}
    QVector<double>x;
    QVector<double>y;
    QColor curColor;
    QVector<double>addx;
    QVector<double>addy;

};

class RoundWave : public ContainerWidget
{
    Q_OBJECT

public:
    explicit RoundWave(QWidget *parent = 0);
    ~RoundWave();

    void paintEvent(QPaintEvent *);
    void SetColor(const QColor &bg, const QColor &ft, const QColor &c1, const QColor &c2);
    void replot();
    void resizeEvent(QResizeEvent *ev);
    void SetParm(const QString &str1, const QString &str2);
    void LineZeroAddPoint(const QVector<double> x, const QVector<double> y);
    void LineOneAddPoint(const QVector<double> x, const QVector<double> y);
    void LineSetRadius(double r);
    void ClearData();
    void SetScale(double val);
    void SetAxiNameX(QString str); // 设置水平轴名
    void SetAxiNameY(QString str); // 设置竖直轴名
    void SetDotRoundShowFlag(bool flag); // 设置是否显示标准圆
    void SetZoomFlag(bool flag); // 设置是否显示放大缩小提示
    void SetStdCircleRadius(fBit64 r);
    void ResetStdCircleRadius();
    void ShowScaleArea(bool flag);
    void SetScaleAreaRadius(double radius); // 设置圆形区域显示半径
    void SetScaleAreaText(QString str); // 设置圆形区域文本
    void UpdateScaleArea(Bit32 x, Bit32 y);
    bool GetScaleData(fBit64 xValue, fBit64 yValue, fBit64 &xOut, fBit64 &yOut);
    bool CrdTran(double x, double y, double &outx, double &outy);
    bool IsValid(fBit64 xMin, fBit64 xMax, fBit64 yMin, fBit64 yMax);
    void GetCenterCross(Bit32 type, QVector<double> &x, QVector<double> &y);
private:

    void CordRedraw(QPainter &painter, double rate = 1.0);
    void RadAndSpeRedraw(QPainter &painter);
    void GraphRedraw(QPainter &painter, double rate = 1.0);
    Ui::RoundWave *ui;
    QColor crdcolor; // 背景色
    QColor fontcolor; // 文本色
    QColor curveAct; // 实际位置曲线颜色
    QColor curveCmd; // 指令位置曲线颜色

    QPoint centrePoint;
    Bit32 resizeCof; // 比例系数，所有图上的点根据
    double radius;
    double m_dbStdCirRadius;    // 标圆半径（自定义单位mc）
    double m_dbScale;    // 刻度宽度（自定义画图单位mc）
    double m_dbScaleDist; // 单位刻度代表距离（单位um）

    QString scaleAreaText;

    QString parm1st; // 第一个参数
    QString parm2nd; // 第二个参数

    double mc(double val){return val * resizeCof;} // 乘以比例系数,适应不同分辨率
    double mcX(double val){return (val * resizeCof + centrePoint.x());}
    double mcY(double val){return (val * resizeCof + centrePoint.y());}

    QString axisNameX; // 水平轴名
    QString axisNameY; // 竖直轴名
    bool m_bDotRoundShow; // 是否显示标准圆标记
    bool m_bZoomFlag; // 是否显示放大缩小提示标记
    bool m_bShowScaleAreaFlag;      // 显示
    double m_nShowScaleAreaRadius; // 圆形区域半径
    Bit32 m_nScaleX;
    Bit32 m_nScaleY;

    RoundwaveGraph *graph0;
    RoundwaveGraph *graph1;
    QList<RoundwaveGraph *>gList;
    void ScaleRedraw(QPainter &painter);
    void RasterScaleRedraw(QPainter &painter);
	void DrawScaleArea(QPainter &painter);

    void DrawScaleAreaText(QPainter &painter); // 绘制圆形区域显示文本
};

#endif // ROUNDWAVE_H
