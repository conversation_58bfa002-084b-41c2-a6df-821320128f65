﻿#include "hncalarm.h"
#include "hncsysctrl.h"
#include "hncfileverify.h"
#include "hncfprogman.h"

#include "common.h"
#include "alarmdata.h"

#include "randomrecorddata.h"

#define RANDOM_RECORD_FILE_NAME "RandomRecordData.DAT"
#define RANDOM_RECORD_FILE_FLAG "RAN"
#define RANDOM_RECORD_FILE_VER (10000)

RandomRecordData::RandomRecordData()
{
    for (int i = 0; i < SYS_CHAN_NUM; i++)
    {
        ClrRandomRecordRow(i);
    }
}

void RandomRecordData::SetRandomRecordRow(Bit32 ch,
                RandomRecordData::RECORD_TYPE type, Bit32 row)
{
    if(ch >= 0 && ch < SYS_CHAN_NUM)
    {
        m_type[ch] = type;
        m_nRunRow[ch] = row;
    }
}

void RandomRecordData::SetRandomRecordRow(Bit32 ch,
            RandomRecordData::RECORD_TYPE type)
{
    if(ch >= 0 && ch < SYS_CHAN_NUM)
    {
        Bit32 row = HNC_FprogViewGetRunRow(ch);
        if  (row <= 0)
        {
            return;
        }

        if (type == RESET_RECORD)
        {
            if (IsRunning(ch))
            {
                if (IsVerifyState(ch))
                {
                    return;
                }
                m_type[ch] = type;
                m_nRunRow[ch] = row;
            }
            return;
        }
        else
        {
            m_type[ch] = type;
            m_nRunRow[ch] = row;
        }
    }
}

void RandomRecordData::ClrRandomRecordRow(Bit32 ch)
{
    if(ch >= 0 && ch < SYS_CHAN_NUM)
    {
        m_type[ch] = NULL_RECORD;
        m_nRunRow[ch] = -1;
    }
}

Bit32 RandomRecordData::GetRandomRecordRow(Bit32 ch)
{
    if(ch >= 0 && ch < SYS_CHAN_NUM && m_type[ch] != NULL_RECORD)
    {
        return m_nRunRow[ch];
    }
    return -1;
}

void RandomRecordData::Save()
{
    for (int ch = 0; ch < SYS_CHAN_NUM; ch++)
    {
        if (IsRunning(ch))
        {
            SetRandomRecordRow(ch, ESTOP_RECORD);
        }
    }

    FILE *fp = NULL;
    FileHead fHead;
    Bit8 filePath[PATH_NAME_LEN] = {'\0'};
    Bit8 dataPath[PATH_NAME_LEN] = {'\0'};

    HNC_SysCtrlGetConfig(HNC_SYS_CFG_DATA_PATH, dataPath);
    snprintf(filePath, PATH_NAME_LEN, "%s%c%s", dataPath, DIR_SEPARATOR, RANDOM_RECORD_FILE_NAME);

    Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("任意行跳转--保存%1文件读取开始").arg(filePath));
    fp = fopen(filePath, "wb+");
    if(fp == NULL)
    {
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("任意行跳转--保存文件打开失败"));
        return;
    }

    memset(&fHead, 0, sizeof(FileHead));
    memcpy(fHead.fileFlag, RANDOM_RECORD_FILE_FLAG, sizeof(fHead.fileFlag));
    fHead.version = RANDOM_RECORD_FILE_VER;
    fHead.verifyType = CRC_VER;
    fHead.fileInfoAddr = sizeof(FileHead);
    fHead.fileDataAddr = sizeof(FileHead);
    fwrite(&fHead, sizeof(FileHead), 1, fp);

    Bit32 count = SYS_CHAN_NUM;
    fwrite(&count, 1, sizeof(Bit32), fp);
    for(Bit32 i = 0; i < count && i < SYS_CHAN_NUM; i++)
    {
        fwrite(&m_type[i], 1, sizeof(RECORD_TYPE), fp);
        fwrite(&m_nRunRow[i], 1, sizeof(Bit32), fp);
    }

    //	写入校验码
    fflush(fp);
    fHead.verifyCode[0] = HNC_FileVerifyCrc32File(fp);
    fseek(fp, 0, SEEK_SET);
    fwrite(&fHead, sizeof(FileHead), 1, fp);

    NcFSync(fp);

    fclose(fp);
    fp = NULL;

    Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("任意行跳转--保存文件完成"));
    return;
}

void RandomRecordData::Load()
{
    FILE *fp = NULL;
    FileHead fHead;
    Bit8 filePath[PATH_NAME_LEN] = {'\0'};
    Bit8 dataPath[PATH_NAME_LEN] = {'\0'};

    HNC_SysCtrlGetConfig(HNC_SYS_CFG_DATA_PATH, dataPath);
    snprintf(filePath, PATH_NAME_LEN, "%s%c%s", dataPath, DIR_SEPARATOR, RANDOM_RECORD_FILE_NAME);

    Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("任意行跳转--载入%1文件开始").arg(filePath));

    if (0 != access(filePath, 0))
    {
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("任意行跳转--载入文件不存在"));
        return;
    }

    fp = fopen(filePath, "rb");
    if (NULL == fp)
    {
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("任意行跳转--载入文件打开失败"));
        return;
    }

    fread(&fHead, sizeof(FileHead), 1, fp);
    if (strncmp(fHead.fileFlag, RANDOM_RECORD_FILE_FLAG, 4) != 0
     || fHead.version != RANDOM_RECORD_FILE_VER
     || fHead.verifyCode[0] != HNC_FileVerifyCrc32File(fp))
    {
        Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("任意行跳转--载入文件版本号/校验码不匹配"));
        fclose(fp);
        return;
    }

    Bit32 count = 0;
    fread(&count, 1, sizeof(Bit32), fp);
    for(Bit32 i = 0; i < count && i < SYS_CHAN_NUM; i++)
    {
        fread(&m_type[i], 1, sizeof(RECORD_TYPE), fp);
        fread(&m_nRunRow[i], 1, sizeof(Bit32), fp);
    }

    for(Bit32 i = count; i < SYS_CHAN_NUM; i++)
    {
        ClrRandomRecordRow(i);
    }

    fclose(fp);
    fp = NULL;
    Logdt::LogdtInput(LOG_FILECHANGE, QObject::TR("任意行跳转--载入文件读取完成"));

    return;
}

void RandomRecordData::RecordAlarmRowInfoByAlarmIdx(Bit32 idx)
{
    Bit32 ch =0;
    Bit32 type = 0;
    Bit32 err_no = 0;
    Bit8 error_info[STR_BUF_LEN] = {0};

    if(GetChannelNum() == 1)
    {
        ch = ActiveChan();
    }
    else
    {
        HNC_AlarmGetData(ALARM_TYPE_ALL, ALARM_ERR, idx, &err_no, error_info);

        type = err_no / TYPE_RADIX;
        if(type == ALARM_CH || type == ALARM_PS || type == ALARM_CH)
        {
            ch = err_no % LEVEL_RADIX / SUB_RADIX;
        }
    }

    if(ChannalState(ch) != 1)
    {
        return;
    }

    SetRandomRecordRow(ch, RandomRecordData::ALARM_RECORD); // 记录报警时的运行行号
}
