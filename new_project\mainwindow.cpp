#include "mainwindow.h"
#include <QApplication>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QScrollBar>
#include <QLabel>
#include <QAction>
#include <QTimer>
#include <QMessageBox>
#include <QInputDialog>
#include <QLineEdit>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_contentLayout(nullptr)
    , m_splitter(nullptr)
    , m_ladderView(nullptr)
    , m_verticalScrollBar(nullptr)
    , m_horizontalScrollBar(nullptr)
    , m_toolBar(nullptr)
    , m_debugAction(nullptr)
    , m_editAction(nullptr)
    , m_refreshAction(nullptr)
    , m_findAction(nullptr)
    , m_statusLabel(nullptr)
    , m_positionLabel(nullptr)
    , m_modeLabel(nullptr)
    , m_ladderData(nullptr)
    , m_refreshTimer(nullptr)
    , m_currentRow(0)
    , m_currentCol(0)
    , m_selectedRow(-1)
    , m_selectedCol(-1)
    , m_debugMode(false)
    , m_editMode(false)
    , m_forceRefresh(false)
{
    setupUI();
    setupToolBar();
    setupStatusBar();
    setupConnections();
    
    // 设置窗口属性
    setWindowTitle("PLC梯形图监控");
    setMinimumSize(800, 600);
    resize(1200, 800);
    
    // 创建刷新定时器
    m_refreshTimer = new QTimer(this);
    m_refreshTimer->setInterval(100); // 100ms刷新间隔
    connect(m_refreshTimer, &QTimer::timeout, this, &MainWindow::onTimerRefresh);
}

MainWindow::~MainWindow()
{
    if (m_refreshTimer) {
        m_refreshTimer->stop();
    }
}

void MainWindow::setupUI()
{
    // 创建中央窗口部件
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // 创建主布局
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setContentsMargins(2, 2, 2, 2);
    m_mainLayout->setSpacing(2);
    
    // 创建内容布局
    m_contentLayout = new QHBoxLayout();
    m_contentLayout->setContentsMargins(0, 0, 0, 0);
    m_contentLayout->setSpacing(2);
    
    // 创建梯形图视图
    m_ladderView = new LadderView(this);
    
    // 创建滚动条
    m_verticalScrollBar = new QScrollBar(Qt::Vertical, this);
    m_horizontalScrollBar = new QScrollBar(Qt::Horizontal, this);
    
    // 设置滚动条范围
    m_verticalScrollBar->setRange(0, 100);
    m_verticalScrollBar->setPageStep(CELL_PER_COL);
    m_verticalScrollBar->setSingleStep(1);
    
    m_horizontalScrollBar->setRange(0, 0);
    m_horizontalScrollBar->setPageStep(CELL_PER_ROW);
    m_horizontalScrollBar->setSingleStep(1);
    
    // 布局组装
    m_contentLayout->addWidget(m_ladderView, 1);
    m_contentLayout->addWidget(m_verticalScrollBar, 0);
    
    m_mainLayout->addLayout(m_contentLayout, 1);
    m_mainLayout->addWidget(m_horizontalScrollBar, 0);
}

void MainWindow::setupToolBar()
{
    m_toolBar = addToolBar("主工具栏");
    
    // 调试模式切换
    m_debugAction = new QAction("调试", this);
    m_debugAction->setCheckable(true);
    m_debugAction->setToolTip("切换调试模式");
    m_toolBar->addAction(m_debugAction);
    
    // 编辑模式切换
    m_editAction = new QAction("编辑", this);
    m_editAction->setCheckable(true);
    m_editAction->setToolTip("切换编辑模式");
    m_toolBar->addAction(m_editAction);
    
    m_toolBar->addSeparator();
    
    // 刷新
    m_refreshAction = new QAction("刷新", this);
    m_refreshAction->setToolTip("强制刷新显示");
    m_toolBar->addAction(m_refreshAction);
    
    // 查找
    m_findAction = new QAction("查找", this);
    m_findAction->setToolTip("查找元件");
    m_toolBar->addAction(m_findAction);
}

void MainWindow::setupStatusBar()
{
    // 状态标签
    m_statusLabel = new QLabel("就绪", this);
    statusBar()->addWidget(m_statusLabel, 1);
    
    // 位置标签
    m_positionLabel = new QLabel("行: 0, 列: 0", this);
    statusBar()->addPermanentWidget(m_positionLabel);
    
    // 模式标签
    m_modeLabel = new QLabel("监控模式", this);
    statusBar()->addPermanentWidget(m_modeLabel);
}

void MainWindow::setupConnections()
{
    // 滚动条信号连接
    connect(m_verticalScrollBar, &QScrollBar::valueChanged,
            this, &MainWindow::onVerticalScrollChanged);
    connect(m_horizontalScrollBar, &QScrollBar::valueChanged,
            this, &MainWindow::onHorizontalScrollChanged);
    
    // 工具栏动作连接
    connect(m_debugAction, &QAction::toggled,
            this, &MainWindow::onDebugToggled);
    connect(m_editAction, &QAction::toggled,
            this, &MainWindow::onEditToggled);
    connect(m_refreshAction, &QAction::triggered,
            this, &MainWindow::onRefreshTriggered);
    connect(m_findAction, &QAction::triggered,
            this, &MainWindow::onFindTriggered);
    
    // 梯形图视图信号连接
    connect(m_ladderView, &LadderView::cellSelected,
            this, &MainWindow::onCellSelected);
    connect(m_ladderView, &LadderView::cellDoubleClicked,
            this, &MainWindow::onCellDoubleClicked);
}

void MainWindow::setLadderData(LadderData *data)
{
    m_ladderData = data;
    if (m_ladderView) {
        m_ladderView->setLadderData(data);
    }
    updateScrollBars();
}

void MainWindow::refreshDisplay()
{
    if (m_ladderView) {
        m_ladderView->refreshDisplay();
    }
}

void MainWindow::setDebugMode(bool enabled)
{
    m_debugMode = enabled;
    m_debugAction->setChecked(enabled);
    
    if (m_ladderView) {
        m_ladderView->setDebugMode(enabled);
    }
    
    // 启动或停止定时器
    if (enabled) {
        m_refreshTimer->start();
    } else {
        m_refreshTimer->stop();
    }
    
    updateStatusBar();
}

void MainWindow::setEditMode(bool enabled)
{
    m_editMode = enabled;
    m_editAction->setChecked(enabled);
    
    if (m_ladderView) {
        m_ladderView->setEditMode(enabled);
    }
    
    updateStatusBar();
}

// 槽函数实现
void MainWindow::onVerticalScrollChanged(int value)
{
    m_currentRow = value;
    if (m_ladderView) {
        m_ladderView->setStartRow(value);
    }
}

void MainWindow::onHorizontalScrollChanged(int value)
{
    m_currentCol = value;
    if (m_ladderView) {
        m_ladderView->setStartCol(value);
    }
}

void MainWindow::onDebugToggled(bool checked)
{
    setDebugMode(checked);
}

void MainWindow::onEditToggled(bool checked)
{
    setEditMode(checked);
}

void MainWindow::onRefreshTriggered()
{
    m_forceRefresh = true;
    refreshDisplay();
    m_forceRefresh = false;
}

void MainWindow::onFindTriggered()
{
    bool ok;
    QString text = QInputDialog::getText(this, "查找元件",
                                        "请输入要查找的元件参数:", 
                                        QLineEdit::Normal, "", &ok);
    if (ok && !text.isEmpty()) {
        // TODO: 实现查找功能
        QMessageBox::information(this, "查找", "查找功能待实现");
    }
}

void MainWindow::onTimerRefresh()
{
    if (m_debugMode && m_ladderView) {
        m_ladderView->refreshDisplay();
    }
}

void MainWindow::onCellSelected(int row, int col)
{
    m_selectedRow = row;
    m_selectedCol = col;
    updateStatusBar();
}

void MainWindow::onCellDoubleClicked(int row, int col)
{
    if (m_editMode) {
        // TODO: 实现元件编辑功能
        QMessageBox::information(this, "编辑", 
                                QString("编辑元件 [%1, %2]").arg(row).arg(col));
    }
}

void MainWindow::updateScrollBars()
{
    if (!m_ladderData) return;
    
    int totalRows = m_ladderData->getTotalRows();
    m_verticalScrollBar->setRange(0, qMax(0, totalRows - CELL_PER_COL));
    m_verticalScrollBar->setPageStep(CELL_PER_COL);
}

void MainWindow::updateStatusBar()
{
    // 更新位置信息
    if (m_selectedRow >= 0 && m_selectedCol >= 0) {
        m_positionLabel->setText(QString("行: %1, 列: %2")
                                .arg(m_selectedRow).arg(m_selectedCol));
    } else {
        m_positionLabel->setText(QString("显示: 行%1-行%2")
                                .arg(m_currentRow)
                                .arg(m_currentRow + CELL_PER_COL - 1));
    }
    
    // 更新模式信息
    QString mode = "监控模式";
    if (m_editMode) {
        mode = "编辑模式";
    } else if (m_debugMode) {
        mode = "调试模式";
    }
    m_modeLabel->setText(mode);
}
