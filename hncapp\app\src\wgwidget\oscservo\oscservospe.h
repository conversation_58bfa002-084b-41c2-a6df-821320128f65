﻿#ifndef OSCSERVOSPE_H
#define OSCSERVOSPE_H

#include <QWidget>

#include "containerwidget.h"

namespace Ui {
class OscServoSpe;
}

QT_BEGIN_NAMESPACE
class OscWave;
class OscList;
QT_END_NAMESPACE

class OscServoSpe : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OscServoSpe(QWidget *parent = 0);
    ~OscServoSpe();

    void SetColorStyle();
protected:
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);

    bool eventFilter(QObject *target, QEvent *event);
    void resizeEvent(QResizeEvent *);
private slots:
    void on_leftBtn_clicked();
    void on_rightBtn_clicked();

private:
    Ui::OscServoSpe *ui;

    OscWave *pOscWaveSpd;
    OscWave *pOscWaveAcc;
    OscList *speOscList;

    Bit32 lastEndPos;
	bool m_bSampleStart;		// 采样开始

    void LoadInfo();

    bool firstFlag;
    void Refresh();
    void Reset();
    QStringList GetParmList();
    void LoadAxisVal(Bit32 type);
    void OnBtFlagChange();
    void ResetInfo();
};

#endif // OSCSERVOSPE_H
