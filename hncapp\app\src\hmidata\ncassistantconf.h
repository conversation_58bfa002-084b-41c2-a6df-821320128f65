﻿#ifndef NCASSISTANTCONF_H
#define NCASSISTANTCONF_H

#include "common.h"

class NcAssistantConf
{
public:
    NcAssistantConf();
    static void SetExecFlag(Bit32 val);
    static void InitAss();

private:
    static void WriteAssFile();
    static QString GetFilPath();
    static void GetAssConf();
    static void CreateAssFile();

    static QString m_sPath;
    static Bit32 m_nExecFlag;
    static void GetExecFlag();
};

#endif // NCASSISTANTCONF_H
