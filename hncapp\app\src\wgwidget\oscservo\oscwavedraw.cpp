﻿/*!
 * @file oscwavedraw.cpp
 * @brief 示波器界面用示波器控件
 * @note 仅供oscapp用
 *
 * @version V1.00
 * @date 2017/12/15
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */
#include <qmath.h>
#include <algorithm>

#include "hncmath.h"
#include "hncsmpl.h"
#include "hncaxis.h"
#include "smplcalc.h"
#include "hncsys.h"
#include "hncchan.h"

#include "hmioscproc.h"
#include "hmioscservo.h"
#include "hmiscrewwear.h"
#include "hmipaintercolor.h"

#include "oscwavedraw.h"
#include "ui_oscwavedraw.h"

OscWaveDraw::OscWaveDraw(QWidget *parent, QString name) :
    QWidget(parent),
    ui(new Ui::OscWaveDraw)
{
    ui->setupUi(this);
    waveName = name;
    this->yMin = -6000;
    this->yMax = 6000;
    this->yMin2 = -6000;
    this->yMax2 = 6000;

    ui->wavePlot->xAxis->setTickLabelFont(QFont(FONT_TYPE, 9));
    ui->wavePlot->yAxis->setTickLabelFont(QFont(FONT_TYPE, 9));
    ui->wavePlot->yAxis2->setTickLabelFont(QFont(FONT_TYPE, 9));

    this->SelAreaDataRestore();

    this->SetupPlot();

    graphSel = new QCPCurve(ui->wavePlot->xAxis, ui->wavePlot->yAxis);
    graphSel->setPen(QPen(QColor(0, 255, 255)));
    ui->wavePlot->addPlottable(graphSel);
}

OscWaveDraw::~OscWaveDraw()
{
    delete ui;
}

void OscWaveDraw::SetupPlot()
{
    ui->wavePlot->setBackground(HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_BLACK_BACKGROUND)); // 设置显示背景色
    if(waveName == "Other")
    {
        ui->wavePlot->axisRect()->setupFullAxesBox(false); // 显示x、y、x2、y2轴
    }
    else
    {
        ui->wavePlot->axisRect()->setupFullAxesBox(true); // 显示x、y、x2、y2轴并关联x/x2,y/y2轴
    }

    ////////////////////////////X轴////////////////////////////
    // 重新设置X轴刻度中间有0个分刻度
    ui->wavePlot->xAxis->setAutoSubTicks(false);
    ui->wavePlot->xAxis->setSubTickCount(0);

    ui->wavePlot->xAxis2->setAutoSubTicks(false);
    ui->wavePlot->xAxis2->setSubTickCount(0);

    ////////////////////////////X轴去掉自动标尺////////////////////////////
    ui->wavePlot->xAxis->setAutoTickStep(false);
    ui->wavePlot->xAxis2->setAutoTickStep(false);

    ////////////////////////////Y轴////////////////////////////
    // 重新设置Y轴刻度中间有0个分刻度
    ui->wavePlot->yAxis->setAutoSubTicks(false);
    ui->wavePlot->yAxis->setSubTickCount(0);

    ui->wavePlot->yAxis2->setAutoSubTicks(false);
    ui->wavePlot->yAxis2->setSubTickCount(0);

    if (waveName == "Pos") // 故障录像-位置波形图
    {
        // 设置X轴名
        ui->xName->setText("T(ms)");

        // 设置Y轴名
        ui->yName1->setText("(mm:");
        ui->yName2->setText(TR("指令"));
        ui->yName3->setText("/");
        ui->yName4->setText(TR("实际"));
        ui->yName5->setText(")");

        ////////////////////////////位置环---指令////////////////////////////
        ui->wavePlot->addGraph();    // 指令
        ui->wavePlot->addGraph();    // 实际

        ////////////////////////////位置环---缩放限制////////////////////////////
        plotXStepMix = 5;
        plotXStepMax = 2000;
        plotYStepMix = 2;
        plotYStepMax = 7680;

        ////////////////////////////位置环---标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = -300;
        baseYRnagUpper = 300;
        baseYStep = 100;
    }
    else if (waveName == "Speed") // 故障录像-速度波形图
    {
        // 设置X轴名
        ui->xName->setText("T(ms)");

        // 设置Y轴名
        ui->yName1->setText("(mm/min:");
        ui->yName2->setText(TR("指令"));
        ui->yName3->setText("/");
        ui->yName4->setText(TR("实际"));
        ui->yName5->setText(")");

        ////////////////////////////速度环////////////////////////////
        ui->wavePlot->addGraph();    // 指令
        ui->wavePlot->addGraph();    // 实际

        ////////////////////////////速度环---缩放限制////////////////////////////
        plotXStepMix = 5;
        plotXStepMax = 2000;
        plotYStepMix = 15;
        plotYStepMax = 491520;

        ////////////////////////////速度环---标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = -6000;
        baseYRnagUpper = 6000;
        baseYStep = 2000;
    }
    else if (waveName == "CurrentFlow") // 故障录像-负载电流形图
    {
        // 设置X轴名
        ui->xName->setText("T(ms)");
        // 设置Y轴名
        ui->yName1->setText("");
        ui->yName2->setText(TR(":负载电流(A)"));

        ////////////////////////////电机实际电流////////////////////////////
        ui->wavePlot->addGraph();

        ////////////////////////////电机实际电流---缩放限制////////////////////////////
        plotXStepMix = 1;
        plotXStepMax = 2000;
        plotYStepMix = 2;
        plotYStepMax = 960;

        ////////////////////////////电机实际电流---标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = -30;
        baseYRnagUpper = 30;
        baseYStep = 10;
    }
    else if (waveName == "Other") // 故障录像-其他类型波形图
    {
        // 设置X轴名
        ui->xName->setText("T(ms)");

        // 设置Y轴名
        ui->yName1->setText("");
        ui->yName2->setText("");

        // 设置曲线个数
        ui->wavePlot->addGraph(); // 曲线1
        ui->wavePlot->addGraph(ui->wavePlot->xAxis, ui->wavePlot->yAxis2); // 曲线2

        // 缩放限制
        plotXStepMix = 1;
        plotXStepMax = 2000;
        plotYStepMix = 2;
        plotYStepMax = 960;

        // 标尺
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = -30;
        baseYRnagUpper = 30;
        baseYStep = 10;
    }
    else if(waveName == "SCREWWEAR_NUM") // 丝杠磨损-正向、反向次数
    {
        // 设置X轴名
        ui->xName->setText(TR("(mm/度)"));

        // 设置Y轴名
        ui->yName1->setText(TR("X轴(次数:"));
        ui->yName2->setText(TR("正向"));
        ui->yName3->setText("/");
        ui->yName4->setText(TR("反向"));
        ui->yName5->setText(")");

        ////////////////////////////正向个数////////////////////////////
        ui->wavePlot->addGraph();

        ////////////////////////////反向个数////////////////////////////
        ui->wavePlot->addGraph();

        ////////////////////////////缩放限制////////////////////////////
        plotXStepMix = 5;
        plotXStepMax = 20500;
        plotYStepMix = 15;
        plotYStepMax = 491520;

        ////////////////////////////标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 450;
        baseXStep = 50;
        baseYRangLower = 0;
        baseYRnagUpper = 6;
        baseYStep = 1;
    }
    else if(waveName == "SCREWWEAR_BASE") // 丝杠负荷
    {
        // 设置X轴名
        ui->xName->setText(TR("(mm/度)"));

        // 设置Y轴名
        ui->yName1->setText(TR("X轴"));
        ui->yName2->setText(TR("(负荷:kJ)"));

        ////////////////////////////基数////////////////////////////
        ui->wavePlot->addGraph();

        ///////////////////////////缩放限制////////////////////////////
        plotXStepMix = 1;
        plotXStepMax = 7700;
        plotYStepMix = 2;
        plotYStepMax = 960;

        ////////////////////////////标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 450;
        baseXStep = 50;
        baseYRangLower = 0;
        baseYRnagUpper = 10;
        baseYStep = 5;
    }
    else if (waveName == "SELF_ADJUST_SPEED")     // 自整定-速度波形图
    {
        // 设置X轴名
        ui->xName->setText("T(ms)");

        // 设置Y轴名
        ui->yName1->setText("F(mm/min)");

        ////////////////////////////速度环////////////////////////////
        ui->wavePlot->addGraph();    // 实际

        ////////////////////////////速度环---缩放限制////////////////////////////
        plotXStepMix = 5;
        plotXStepMax = 2000;
        plotYStepMix = 15;
        plotYStepMax = 491520;

        ////////////////////////////速度环---标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = -6000;
        baseYRnagUpper = 6000;
        baseYStep = 2000;
    }
    else if(waveName == "SPINDLETIME") // 主轴转速-累计时间曲线
    {
        // 设置X轴名
        ui->xName->setText(TR("(rpm)"));

        // 设置Y轴名
        ui->yName1->setText(TR("累计时间"));
        ui->yName2->setText(TR("(h)"));

        ////////////////////////////基数////////////////////////////
        ui->wavePlot->addGraph();

        ///////////////////////////缩放限制////////////////////////////
        plotXStepMix = 100;
        plotXStepMax = 1000;
        plotYStepMix = 0.001;
        plotYStepMax = 100000;

        ////////////////////////////标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 20000;
        baseXStep = 1000;
        baseYRangLower = 0;
        baseYRnagUpper = 260;
        baseYStep = 20;
    }
    else if(waveName == "SPINDLEROTATE") // 主轴转速-累计转数曲线
    {
        // 设置X轴名
        ui->xName->setText(TR("(rpm)"));

        // 设置Y轴名
        ui->yName1->setText(TR("累计转数"));
        ui->yName2->setText(TR("(转)"));

        ////////////////////////////基数////////////////////////////
        ui->wavePlot->addGraph();

        ///////////////////////////缩放限制////////////////////////////
        plotXStepMix = 100;
        plotXStepMax = 1000;
        plotYStepMix = 1;
        plotYStepMax = 100000000;

        ////////////////////////////标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 20000;
        baseXStep = 1000;
        baseYRangLower = 0;
        baseYRnagUpper = 260;
        baseYStep = 20;
    }
    else if(waveName == "SPINDLERUNPOWER") // 主轴转速-累计运行能耗曲线
    {
        // 设置X轴名
        ui->xName->setText(TR("(rpm)"));

        // 设置Y轴名
        ui->yName1->setText(TR("累计运行能耗"));
        ui->yName2->setText(TR("(kw·h)"));

        ////////////////////////////基数////////////////////////////
        ui->wavePlot->addGraph();

        ///////////////////////////缩放限制////////////////////////////
        plotXStepMix = 100;
        plotXStepMax = 1000;
        plotYStepMix = 0.001;
        plotYStepMax = 100000;

        ////////////////////////////标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 20000;
        baseXStep = 1000;
        baseYRangLower = 0;
        baseYRnagUpper = 260;
        baseYStep = 20;
    }
    else if(waveName == "ULTRASOUND_ELECTRIC") // 超声波-电流曲线
    {
        // 设置X轴名
        ui->xName->setText(TR("(点)"));

        // 设置Y轴名
        ui->yName1->setText(TR("电流"));
        ui->yName2->setText(TR("(mA)"));

        ////////////////////////////基数////////////////////////////
        ui->wavePlot->addGraph();

        ///////////////////////////缩放限制////////////////////////////
        plotXStepMix = 100;
        plotXStepMax = 1000;
        plotYStepMix = 1;
        plotYStepMax = 1000;

        ////////////////////////////标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = 0;
        baseYRnagUpper = 5000;
        baseYStep = 200;
    }
    else if(waveName == "ULTRASOUND_POWER") // 超声波-功率曲线
    {
        // 设置X轴名
        ui->xName->setText(TR("(点)"));

        // 设置Y轴名
        ui->yName1->setText(TR("功率"));
        ui->yName2->setText(TR("(w)"));

        ////////////////////////////基数////////////////////////////
        ui->wavePlot->addGraph();

        ///////////////////////////缩放限制////////////////////////////
        plotXStepMix = 100;
        plotXStepMax = 1000;
        plotYStepMix = 1;
        plotYStepMax = 1000;

        ////////////////////////////标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = 0;
        baseYRnagUpper = 5000;
        baseYStep = 200;
    }
    else if(waveName == "ULTRASOUND_FREQ") // 超声波-频率曲线
    {
        // 设置X轴名
        ui->xName->setText(TR("(点)"));

        // 设置Y轴名
        ui->yName1->setText(TR("频率"));
        ui->yName2->setText(TR("(Hz)"));

        ////////////////////////////基数////////////////////////////
        ui->wavePlot->addGraph();

        ///////////////////////////缩放限制////////////////////////////
        plotXStepMix = 100;
        plotXStepMax = 1000;
        plotYStepMix = 1;
        plotYStepMax = 10000;

        ////////////////////////////标尺////////////////////////////
        baseXRangLower = 0;
        baseXRangUpper = 10000;
        baseXStep = 1000;
        baseYRangLower = 0;
        baseYRnagUpper = 65000;
        baseYStep = 2000;
    }
    else if (waveName == "CNC")
    {
        // 设置X轴名
        ui->xName->setText("");
        ui->horizontalLayout_3->removeWidget(ui->xName);
        // 设置Y轴名
        ui->yName1->setText(TR("HMI进程cpu占用(%)"));

        baseXRangLower = 0;
        baseXRangUpper = 60000;
        baseXStep = 20000;
        baseYRangLower = 0;
        baseYRnagUpper = 100;
        baseYStep = 20;

        ui->wavePlot->addGraph();    // 占用率
    }
    else if (waveName == "NCU")
    {
        // 设置X轴名
        ui->xName->setText("");
        ui->horizontalLayout_3->removeWidget(ui->xName);
        // 设置Y轴名
        ui->yName1->setText(TR("内核任务耗时(μs)"));

        baseXRangLower = 0;
        baseXRangUpper = 60000;
        baseXStep = 20000;
        baseYRangLower = 0;
        baseYRnagUpper = 50;
        baseYStep = 10;

        ui->wavePlot->addGraph();       // 总线读写实时值
        ui->wavePlot->addGraph();       // 轴控制实时值
        ui->wavePlot->addGraph();       // PLC1扫描实时值
        ui->wavePlot->addGraph();       // PLC2扫描实时值
        ui->wavePlot->addGraph();       // 通道控制实时值
        ui->wavePlot->addGraph();       // 从轴控制实时值
        ui->wavePlot->addGraph();       // 解释器线程耗时
        ui->wavePlot->addGraph();       // 插补线程总耗时
    }

    XAxisSetTickParm(baseXRangLower, baseXRangUpper, baseXStep);
    YAxisSetTickParm(baseYRangLower, baseYRnagUpper, baseYStep);

    ui->wavePlot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
}

/**
 * @brief OscWaveDraw::SetYName 设置Y轴名
 * @param str 左侧轴名
 * @param str2 右侧轴名
 */
void OscWaveDraw::SetYName(QString str, QString str2)
{
    ui->yName11->setText(str2); // 右侧轴名

    if(waveName == "Pos") //故障录像-位置
    {
        ui->yName1->setText(str + "(mm:");
    }
    else if(waveName == "Speed") //故障录像-速度
    {
        ui->yName1->setText(str + "(mm/min:");
    }
    else if(waveName == "CurrentFlow") //故障录像-电流
    {
        ui->yName1->setText(str);
    }
    else if(waveName == "Other") //故障录像-其他
    {
        ui->yName1->setText(str);
        if(str2 != "")
        {
            ui->wavePlot->yAxis2->setTickLabels(true);
        }
        else
        {
            ui->wavePlot->yAxis2->setTickLabels(false);
        }
    }
    else if(waveName == "SCREWWEAR_NUM" ) //丝杠磨损-正反向数据
    {
        ui->yName1->setText(str + TR("轴(次数:"));
    }
    else if(waveName == "SCREWWEAR_BASE") //丝杠负荷值
    {
        ui->yName1->setText(str + TR("轴"));
    }
    else if (waveName == "SELF_ADJUST_SPEED") // 自整定-速度波形图
    {
        ui->yName1->setText(str + TR("轴") + "(mm/min");
    }
}

/**
 * @brief OscWaveDraw::SetXBaseValue 设置X轴标尺
 * @param startCoord 起点
 * @param spaceNum 个数
 * @param spaceValue 间距
 */
void OscWaveDraw::SetXBaseValue(fBit64 startCoord, Bit32 spaceNum, fBit64 spaceValue)
{
    if(waveName == "SCREWWEAR_NUM")
    {
        baseXRangLower = startCoord;
        baseXRangUpper = startCoord + (spaceNum - 1) * spaceValue;
        baseXStep = spaceValue;
        baseYRangLower = 0;
        baseYRnagUpper = 6;
        baseYStep = 1;
    }
    else if(waveName == "SCREWWEAR_BASE")
    {
        baseXRangLower = startCoord;
        baseXRangUpper = startCoord + (spaceNum - 1) * spaceValue;
        baseXStep = spaceValue;
        baseYRangLower = 0;
        baseYRnagUpper = 10;
        baseYStep = 5;
    }
    else if(waveName == "SELF_ADJUST_SPEED")
    {
        baseXRangLower = startCoord;
        baseXRangUpper = startCoord + spaceNum * spaceValue;
        baseXStep = spaceValue;
    }


    XAxisSetTickParm(baseXRangLower, baseXRangUpper, baseXStep);
    YAxisSetTickParm(baseYRangLower, baseYRnagUpper, baseYStep);

    QVector<double> xrange;
    for(Bit32 i = 0; i < spaceNum; i++)
    {
        if(i % 2 == 0)
        {
            xrange.append(baseXRangLower + i * baseXStep);
        }
    }

    ui->wavePlot->xAxis->setAutoTicks(false);
    ui->wavePlot->xAxis2->setAutoTicks(false);
    ui->wavePlot->xAxis->setTickVector(xrange);
    ui->wavePlot->xAxis2->setTickVector(xrange);

    ui->wavePlot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
    ui->wavePlot->replot();
}

/**
 * @brief OscWaveDraw::SetSpindleWearXBaseValue 设置主轴负荷X轴标尺
 * @param spaceValue 间距
 * @param endSpeed   终点
 */
void OscWaveDraw::SetSpindleWearXBaseValue(Bit32 spaceValue, Bit32 endSpeed, Bit32 spaceNum)
{
    if(waveName == "SPINDLETIME")
    {
        baseXRangLower = 0;
        baseXRangUpper = endSpeed + spaceValue;
        baseXStep = spaceValue;
    }
    else if(waveName == "SPINDLEROTATE")
    {
        baseXRangLower = 0;
        baseXRangUpper = endSpeed + spaceValue;
        baseXStep = spaceValue;
    }
    else if(waveName == "SPINDLERUNPOWER")
    {
        baseXRangLower = 0;
        baseXRangUpper = endSpeed + spaceValue;
        baseXStep = spaceValue;
    }


    XAxisSetTickParm(baseXRangLower, baseXRangUpper, baseXStep);

    QVector<double> xrange;
    for(Bit32 i = 0; i < spaceNum+2; i++)
    {
        if(i % 2 == 0)
        {
            xrange.append(baseXRangLower + i * baseXStep);
        }
    }

    ui->wavePlot->xAxis->setAutoTicks(false);
    ui->wavePlot->xAxis2->setAutoTicks(false);
    ui->wavePlot->xAxis->setTickVector(xrange);
    ui->wavePlot->xAxis2->setTickVector(xrange);

    ui->wavePlot->setInteractions(QCP::iRangeDrag | QCP::iRangeZoom);
    ui->wavePlot->replot();
}

/**
 * @brief OscWaveDraw::SetZoomXUp 横轴放大
 * @note 横轴是时间，需采用整数
 */
void OscWaveDraw::SetZoomXUp()
{
    Bit32 lower = (Bit32)ui->wavePlot->xAxis->rangeLower();
    Bit32 upper = (Bit32)ui->wavePlot->xAxis->rangeUpper();
    Bit32 tickStep = (Bit32)ui->wavePlot->xAxis->tickStep();
    Bit32 newStep = (Bit32)plotXStepMix; // 放大后间隔

    if (tickStep <= plotXStepMix)
    {
        return;
    }

    if (Bit32(tickStep / 2.0 + 0.001) > (Bit32)plotXStepMix)
    {
        newStep = Bit32(tickStep / 2.0 + 0.001);
    }

	lower = lower / newStep * newStep;
	upper = (lower / newStep + ui->wavePlot->xAxis->tickVector().size() - 1) * newStep;

//    upper = lower + Bit32((upper - lower) * newStep / tickStep);

    XAxisSetTickParm(lower, upper, newStep);
    ui->wavePlot->replot();

    this->allLength = ui->wavePlot->xAxis->tickVector().size() - 1; // 横轴格子数
    if(this->allLength < 1)
    {
        this->allLength = 1;
    }
}

/**
 * @brief OscWaveDraw::SetZoomXDn 横轴缩小
 * @note 横轴是时间，需采用整数
 */
void OscWaveDraw::SetZoomXDn()
{
    Bit32 lower = (Bit32)ui->wavePlot->xAxis->rangeLower();
    Bit32 upper = (Bit32)ui->wavePlot->xAxis->rangeUpper();
    Bit32 tickStep = (Bit32)ui->wavePlot->xAxis->tickStep();
    Bit32 newStep = (Bit32)plotXStepMax; // 放大后间隔

    if (tickStep >= plotXStepMax)
    {
        return;
    }

    if (Bit32(tickStep * 2.0 + 0.001) < (Bit32)plotXStepMax)
    {
        newStep = Bit32(tickStep * 2.0 + 0.001);
    }
	
	lower = lower / newStep * newStep;
	upper = (lower / newStep + ui->wavePlot->xAxis->tickVector().size() - 1) * newStep;

//    upper = lower + Bit32((upper - lower) * newStep / tickStep);

    XAxisSetTickParm(lower, upper, newStep);
    ui->wavePlot->replot();

    this->allLength = ui->wavePlot->xAxis->tickVector().size() - 1; // 横轴格子数
    if(this->allLength < 1)
    {
        this->allLength = 1;
    }

}

/**
 * @brief OscWaveDraw::SetZoomYUp 纵轴放大
 */
void OscWaveDraw::SetZoomYUp()
{
    fBit64 lower = ui->wavePlot->yAxis->rangeLower();
    fBit64 upper = ui->wavePlot->yAxis->rangeUpper();
    fBit64 newupper = (3 * upper + lower) / 4.0;
    fBit64 newlower = (upper + 3 * lower) / 4.0;
    fBit64 lower2 = ui->wavePlot->yAxis2->rangeLower();
    fBit64 upper2 = ui->wavePlot->yAxis2->rangeUpper();
    fBit64 newupper2 = (3 * upper2 + lower2) / 4.0;
    fBit64 newlower2 = (upper2 + 3 * lower2) / 4.0;

    ui->wavePlot->yAxis->setRange(newlower, newupper);
    ui->wavePlot->yAxis2->setRange(newlower2, newupper2);

    ui->wavePlot->replot();

    this->allHeight = ui->wavePlot->yAxis->tickVector().size() - 1; // 纵轴格子数
    if(this->allHeight < 1)
    {
        this->allHeight = 1;
    }
}

/**
 * @brief OscWaveDraw::SetZoomYDn 纵轴缩小
 */
void OscWaveDraw::SetZoomYDn()
{
    fBit64 lower = ui->wavePlot->yAxis->rangeLower();
    fBit64 upper = ui->wavePlot->yAxis->rangeUpper();
    fBit64 newupper = (3 * upper - lower) / 2.0;
    fBit64 newlower = (3 * lower - upper) / 2.0;
    fBit64 lower2 = ui->wavePlot->yAxis2->rangeLower();
    fBit64 upper2 = ui->wavePlot->yAxis2->rangeUpper();
    fBit64 newupper2 = (3 * upper2 - lower2) / 2.0;
    fBit64 newlower2 = (3 * lower2 - upper2) / 2.0;

    ui->wavePlot->yAxis->setRange(newlower, newupper);
    ui->wavePlot->yAxis2->setRange(newlower2, newupper2);

    ui->wavePlot->replot();

    this->allHeight = ui->wavePlot->yAxis->tickVector().size() - 1; // 纵轴格子数
    if(this->allHeight < 1)
    {
        this->allHeight = 1;
    }
}

/**
 * @brief OscWaveDraw::CalSelArea 设置选择框部分
 */
void OscWaveDraw::CalSelArea()
{
    QVector<double> graphSelX, graphSelY;
    fBit64 lowerX = ui->wavePlot->xAxis->tickVector().first();
    fBit64 lowerY = ui->wavePlot->yAxis->tickVector().first();
    fBit64 stepX = ui->wavePlot->xAxis->tickStep();
    fBit64 stepY = ui->wavePlot->yAxis->tickStep();

    for (Bit32 i = 0; i < 5; i++)
    {
        graphSelX.append(0.0);
        graphSelY.append(0.0);
    }

    /////////////////////////////////////////////////
    //      0_________1
    //     4|         |
    //      |         |
    //      |_________|
    //     3          2
    /////////////////////////////////////////////////
    graphSelX[0] = lowerX + stepX * selAreaPosX;
    graphSelY[0] = lowerY + stepY * selAreaPosY;

    graphSelX[1] = graphSelX[0] + stepX * selAreaLength;
    graphSelY[1] = graphSelY[0];

    graphSelX[2] = graphSelX[1];
    graphSelY[2] = graphSelY[1] - stepY * selAreaHeight;

    graphSelX[3] = graphSelX[2] - stepX * selAreaLength;
    graphSelY[3] = graphSelY[2];

    graphSelX[4] = graphSelX[0];
    graphSelY[4] = graphSelY[0];

    this->graphSel->setData(graphSelX, graphSelY);
}

void OscWaveDraw::ShowSelArea()
{
    this->CalSelArea();

    ui->wavePlot->replot();
}

void OscWaveDraw::HideSelArea()
{
    graphSel->clearData();
}

void OscWaveDraw::XAxisGetTickParm(fBit64 *lower, fBit64 *upper, fBit64 *step)
{
    *lower = ui->wavePlot->xAxis->rangeLower();
    *upper = ui->wavePlot->xAxis->rangeUpper();
    *step = ui->wavePlot->xAxis->tickStep();
}

void OscWaveDraw::XAxisSetTickParm(fBit64 lower, fBit64 upper, fBit64 step)
{
    ui->wavePlot->xAxis->setRange(lower, upper);
    ui->wavePlot->xAxis2->setRange(lower, upper);

    if(this->waveName == "SCREWWEAR_NUM"
       || this->waveName == "SCREWWEAR_BASE"
       || this->waveName == "SPINDLETIME"
       || this->waveName == "SPINDLEROTATE"
       || this->waveName == "SPINDLERUNPOWER")
    {
        ui->wavePlot->xAxis->setTickStep(step * 2);
        ui->wavePlot->xAxis2->setTickStep(step);

        ui->wavePlot->xAxis->setSubTickCount(1);
        ui->wavePlot->xAxis2->setSubTickCount(0);
    }
    else
    {
        ui->wavePlot->xAxis->setTickStep(step);
        ui->wavePlot->xAxis2->setTickStep(step);
    }
}

void OscWaveDraw::YAxisGetTickParm(fBit64 *lower, fBit64 *upper, fBit64 *step)
{
    *lower = ui->wavePlot->yAxis->rangeLower();
    *upper = ui->wavePlot->yAxis->rangeUpper();
    *step = ui->wavePlot->yAxis->tickStep();
}

void OscWaveDraw::YAxisSetTickParm(fBit64 lower, fBit64 upper, fBit64 step)
{
    ui->wavePlot->yAxis->setRange(lower, upper);
    ui->wavePlot->yAxis2->setRange(lower, upper);

    if(this->waveName == "SPINDLETIME"
       || this->waveName == "SPINDLEROTATE"
       || this->waveName == "SPINDLERUNPOWER")
    {
        ui->wavePlot->yAxis->setTickStep(step * 2);
        ui->wavePlot->yAxis2->setTickStep(step);

        ui->wavePlot->yAxis->setSubTickCount(1);
        ui->wavePlot->yAxis2->setSubTickCount(0);
    }
    else
    {
        ui->wavePlot->yAxis->setTickStep(step);
        ui->wavePlot->yAxis2->setTickStep(step);
    }

}

void OscWaveDraw::WaveReplot()
{
    ui->wavePlot->replot();
}

QCustomPlot *OscWaveDraw::GetWavePlot()
{
    return ui->wavePlot;
}

/**
 * @brief OscWaveDraw::MoveSelArea 移动选择框
 * @param dir 方向
 * @return 0：成功 -1：已经到边界
 */
Bit32 OscWaveDraw::MoveSelArea(OscSelMoveDir dir)
{
    fBit64 lowerX = ui->wavePlot->xAxis->tickVector().first();
    fBit64 stepX = ui->wavePlot->xAxis->tickStep();
    fBit64 xValSelLeft = lowerX + stepX * selAreaPosX; // 选框左边对应的X坐标
    fBit64 xValSelRight =xValSelLeft + stepX * selAreaLength; // 选框右边对应的X坐标
    fBit64 lowerY = ui->wavePlot->yAxis->tickVector().first();
    fBit64 stepY = ui->wavePlot->yAxis->tickStep();
    fBit64 yValSelUp = lowerY + stepY * selAreaPosY; // 选框上边对应的Y坐标
    fBit64 yValSelDown = yValSelUp - stepY * selAreaHeight; // 选框下边对应的Y坐标
    fBit64 xValMax = 0;
    fBit64 stepY2 = ui->wavePlot->yAxis2->tickStep();

    if (ui->wavePlot->graph(0)->data()->keys().count() > 0)
    {
        xValMax = ui->wavePlot->graph(0)->data()->keys().last(); // 取最后一个X值
    }
    else if (ui->wavePlot->graphCount() > 1)
    {
		if (ui->wavePlot->graph(1) != NULL &&
			ui->wavePlot->graph(1)->data()->keys().count() != 0)
		{
			xValMax = ui->wavePlot->graph(1)->data()->keys().last(); // 取最后一个X值
		}
		else
		{
			return -1;
		}
    }
	else
	{
		return -1;
	}

    if (dir == OSC_SEL_MOVE_LEFT) // left
    {
        if (xValSelLeft > 0 && selAreaPosX > 0)
        {
            selAreaPosX--;
        }
        else if (xValSelLeft > 0)
        {
            if (lowerX > stepX)
            {
                ui->wavePlot->xAxis->moveRange(-stepX);
                ui->wavePlot->replot();
            }
            else
            {
                ui->wavePlot->xAxis->moveRange(-lowerX);
                ui->wavePlot->replot();
            }
        }
        else
        {
            return -1;
        }
    }
    else if (dir == OSC_SEL_MOVE_RIGHT) // right
    {
        if ((selAreaPosX + selAreaLength) < allLength)
        {
            selAreaPosX++;
        }
        else if (xValSelRight < xValMax)
        {
            ui->wavePlot->xAxis->moveRange(stepX);
            ui->wavePlot->replot();
        }
        else
        {
            return -1;
        }
    }
    else if (dir == OSC_SEL_MOVE_UP) // up
    {
        if (selAreaPosY < allHeight)
        {
            selAreaPosY++;
        }
        else if (yValSelUp < this->yMax)
        {
            ui->wavePlot->yAxis->moveRange(stepY);
            ui->wavePlot->yAxis2->moveRange(stepY2);
            ui->wavePlot->replot();
        }
        else
        {
            return -1;
        }
    }
    else if (dir == OSC_SEL_MOVE_DOWN) // down
    {
        if ((selAreaPosY - selAreaHeight) > 0)
        {
            selAreaPosY--;
        }
        else if (yValSelDown > this->yMin)
        {
            ui->wavePlot->yAxis->moveRange(-stepY);
            ui->wavePlot->yAxis2->moveRange(-stepY2);
            ui->wavePlot->replot();
        }
        else
        {
            return -1;
        }
    }

    this->ShowSelArea();

    return 0;
}

/**
 * @brief OscWaveDraw::SelAreaDataRestore 还原选择框
 */
void OscWaveDraw::SelAreaDataRestore()
{
    this->allLength = ui->wavePlot->xAxis->tickVector().size() - 1; // 横轴格子数
    this->allHeight = ui->wavePlot->yAxis->tickVector().size() - 1; // 纵轴格子数
    if(this->allLength < 1)
    {
        this->allLength = 1;
    }

    if(this->allHeight < 1)
    {
        this->allHeight = 1;
    }

    this->selAreaLength = 1; // 选择框长度
    this->selAreaHeight = 1; // 选择框宽度
    this->selAreaPosX = (allLength - 1) / 2; // 选择框左上角X坐标
    this->selAreaPosY = (allHeight + 1) / 2; // 选择框左上角Y坐标
}

/**
 * @brief OscWaveDraw::SelAreaZoomUp 区块放大
 */
Bit32 OscWaveDraw::SelAreaZoomUp()
{
    if ((this->selAreaLength + 1) > this->allLength || (this->selAreaHeight + 1) > this->allHeight)
    {
        return -1;
    }

    this->selAreaLength += 1;
    this->selAreaHeight += 1;

    if ((this->selAreaPosX + this->selAreaLength) > this->allLength)
    {
        this->selAreaPosX--;
    }

    if ((this->selAreaPosY - this->selAreaHeight) < 0)
    {
        this->selAreaPosY++;
    }

    this->ShowSelArea();

    return 0;
}

/**
 * @brief OscWaveDraw::SelAreaZoomUp 区块缩小
 */
Bit32 OscWaveDraw::SelAreaZoomDown()
{
    if (this->selAreaLength <= 1 || this->selAreaHeight <= 1)
    {
        return -1;
    }

    this->selAreaLength--;
    this->selAreaHeight--;

    this->ShowSelArea();

    return 0;
}

/**
 * @brief OscWaveDraw::SelAreaZoomFullSrceen 选择框区域全屏
 * @return 0：成功 -1：已经最大
 */
Bit32 OscWaveDraw::SelAreaZoomFullSrceen()
{
    Bit32 lowerX = (Bit32)ui->wavePlot->xAxis->tickVector().first();
    fBit64 lowerY = ui->wavePlot->yAxis->tickVector().first();
    fBit64 lowerY2 = ui->wavePlot->yAxis2->tickVector().first();
    Bit32 stepX = (Bit32)ui->wavePlot->xAxis->tickStep();
    fBit64 stepY = ui->wavePlot->yAxis->tickStep();
    fBit64 stepY2 = ui->wavePlot->yAxis2->tickStep();
    Bit32 newLowerX = lowerX + stepX * this->selAreaPosX;
    Bit32 newUpperX = newLowerX + stepX * this->selAreaLength;
    Bit32 newStepX = (Bit32)this->plotXStepMix;
    fBit64 newUpperY = lowerY + stepY * this->selAreaPosY;
    fBit64 newLowerY = newUpperY - stepY * this->selAreaHeight;
    fBit64 newUpperY2 = lowerY2 + stepY2 * this->selAreaPosY;
    fBit64 newLowerY2 = newUpperY2 - stepY2 * this->selAreaHeight;
    Bit32 xStepNum = ui->wavePlot->xAxis->tickVector().size() - 1;

    if(stepX <= (Bit32)this->plotXStepMix || xStepNum <= 0)
    {
        return -1;
    }

    newStepX = (Bit32)(stepX * this->selAreaLength / xStepNum);

    if (newStepX < (Bit32)this->plotXStepMix)
    {
        return -1;
    }

	newLowerX = newLowerX / newStepX * newStepX;
	newUpperX = (newLowerX / newStepX + xStepNum) * newStepX;

    XAxisSetTickParm(newLowerX, newUpperX, newStepX);

    ui->wavePlot->yAxis->setRange(newLowerY, newUpperY);
    ui->wavePlot->yAxis2->setRange(newLowerY2, newUpperY2);

    ui->wavePlot->replot();

    this->allLength = ui->wavePlot->xAxis->tickVector().size() - 1; // 横轴格子数
    if(this->allLength < 1)
    {
        this->allLength = 1;
    }

    this->allHeight = ui->wavePlot->yAxis->tickVector().size() - 1; // 纵轴格子数
    if(this->allHeight < 1)
    {
        this->allHeight = 1;
    }

    return 0;
}

void OscWaveDraw::LineZeroAddPoint(const QVector<double>x, const QVector<double>y)
{
    ui->wavePlot->graph(0)->addData(x, y);

    if (this->waveName == "SELF_ADJUST_SPEED")
    {
        if (!x.isEmpty() && x.last() > ui->wavePlot->xAxis->rangeUpper())
        {
            Bit32 xValCount = ui->wavePlot->graph(0)->data()->keys().count();
            Bit32 xValMax = 0;
            if (xValCount - 1 >= 0)
            {
                xValMax =(fBit64)(ui->wavePlot->graph(0)->data()->keys().at(xValCount - 1)); // 取最后一个X值
            }
            this->SetXBaseValue(0, 10, (Bit32)(xValMax / 10));
            ui->wavePlot->xAxis2->setTickLength(0);
        }
    }

    if (waveName == "ULTRASOUND_ELECTRIC"
                || waveName == "ULTRASOUND_POWER"
                || waveName == "ULTRASOUND_FREQ") // 超声波
    {
        if(ui->wavePlot->graph(0)->data()->keys().count() == x.count())
        {
            // 第一次添加数据时重置最大最小值，后面只进行比较
            this->yMin = y.at(0);
            this->yMax = y.at(0);
        }
    }
    else
    {
        this->yMin = y.at(0);
        this->yMax = y.at(0);
    }

    for(Bit32 ii = 0; ii < x.count(); ii++)
    {
        if(this->yMin > (fBit64)(y.at(ii)))
        {
            this->yMin = (fBit64)(y.at(ii));
        }

        if(this->yMax < (fBit64)(y.at(ii)))
        {
            this->yMax = (fBit64)(y.at(ii));
        }
    }
}

void OscWaveDraw::SpindleLineZeroAddPoint(const QVector<double>x, const QVector<double>y)
{
    ui->wavePlot->graph(0)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssCircle, 4));
    ui->wavePlot->graph(0)->addData(x, y);

    if(y.count() > 0)
    {
        Bit32 maxIdx = 0;
        fBit64 yMaxVal = 0.0;

        maxIdx = std::max_element(y.begin(), y.end()) - y.begin();
        yMaxVal = y.at(maxIdx);

        SpindleYAxisAutoSet(yMaxVal);
    }

    ui->wavePlot->replot();
}

void OscWaveDraw::LineOneAddPoint(const QVector<double>x, const QVector<double>y)
{
    ui->wavePlot->graph(1)->addData(x, y);

    this->yMin2 = y.at(0);
    this->yMax2 = y.at(0);

    for(Bit32 ii = 0; ii < x.count(); ii++)
    {
        if(this->yMin2 > (fBit64)(y.at(ii)))
        {
            this->yMin2 = (fBit64)(y.at(ii));
        }

        if(this->yMax2 < (fBit64)(y.at(ii)))
        {
            this->yMax2 = (fBit64)(y.at(ii));
        }
    }

    if(ui->wavePlot->graph(0) == NULL ||
       ui->wavePlot->graph(0)->data()->keys().count() == 0)
    {
        this->yMin = this->yMin2;
        this->yMax = this->yMax2;
    }
}

/**
 * @brief OscWaveDraw::ResetRange 自动调整纵轴显示范围
 */
void OscWaveDraw::ResetRange()
{
    ui->wavePlot->xAxis->setTickStep(this->baseXStep);
    ui->wavePlot->xAxis2->setTickStep(this->baseXStep);
    ui->wavePlot->rescaleAxes(true); // 自动设置显示

    // 平直线范围调整
    if(this->yMin == this->yMax && this->yMin != 0.0)
    {
        if(this->yMin > 0.0)
        {
            ui->wavePlot->yAxis->setRange(this->yMin * 0.9, this->yMin * 1.1);
        }
        else
        {
            ui->wavePlot->yAxis->setRange(this->yMin * 1.1, this->yMin * 0.9);
        }
    }

    if(this->yMin2 == this->yMax2 && this->yMin2 != 0.0)
    {
        if(this->yMin2 > 0.0)
        {
            ui->wavePlot->yAxis2->setRange(this->yMin2 * 0.9, this->yMin2 * 1.1);
        }
        else
        {
            ui->wavePlot->yAxis2->setRange(this->yMin2 * 1.1, this->yMin2 * 0.9);
        }
    }
    ui->wavePlot->replot(); // 刷新图形

    this->ResetRangeTick();
}

/**
 * @brief OscWaveDraw::ResetRange 调整纵轴上下边界为间隔的整数倍
 */
void OscWaveDraw::ResetRangeTick()
{
    QVector<double> graphTick = ui->wavePlot->yAxis->tickVector();
    fBit64 lower = graphTick.first();
    fBit64 upper = graphTick.last();
    fBit64 lower2 = lower;
    fBit64 upper2 = upper;

    if(ui->wavePlot->graphCount() > 1)
    {
        if(ui->wavePlot->graph(1) != NULL &&
           ui->wavePlot->graph(1)->data()->keys().count() != 0)
        {
            QVector<double> graphTick2 = ui->wavePlot->yAxis2->tickVector();

            lower2 = graphTick2.first();
            upper2 = graphTick2.last();
        }
    }

    ui->wavePlot->yAxis->setRange(lower, upper);
    ui->wavePlot->yAxis2->setRange(lower2, upper2);

    lower2 = ui->wavePlot->xAxis->rangeLower();
    upper2 = ui->wavePlot->xAxis->rangeUpper();
    ui->wavePlot->xAxis2->setRange(lower2, upper2);

    ui->wavePlot->replot(); // 刷新图形
}

void OscWaveDraw::UltrasoundResetRange()
{
    ui->wavePlot->yAxis->setRange(this->yMin - this->baseYStep, this->yMax + this->baseYStep);
    ui->wavePlot->replot(); // 刷新图形
}

void OscWaveDraw::ClearPoint()
{
    for (Bit32 i = 0; i < ui->wavePlot->graphCount(); i++)
    {
        ui->wavePlot->graph(i)->clearData();
    }

    this->graphSel->clearData();

    this->yMin = 0.0;
    this->yMax = 0.0;
    this->yMin2 = 0.0;
    this->yMax2 = 0.0;

    ui->wavePlot->replot();
}

/**
 * @brief OscWave::LineAddPoint 添加曲线数据
 * @param [in] index            曲线索引值
 * @param [in] x                x轴坐标值
 * @param [in] y                y轴坐标值
 */
void OscWaveDraw::LineAddPoint(Bit32 index, QVector<double> x, QVector<double> y)
{
    if (index < 0)
    {
        return;
    }

    // 索引号超过范围时则增加相应的曲线
    if (index >= ui->wavePlot->graphCount())
    {
        Bit32 count = index - ui->wavePlot->graphCount() + 1;
        for (int i = 0; i < count; i++)
        {
            ui->wavePlot->addGraph();
        }
    }

    // 再次判断不在有效范围时则返回
    if (index >= ui->wavePlot->graphCount())
    {
        return;
    }

    ui->wavePlot->graph(index)->addData(x, y);
}

void OscWaveDraw::SetColor(QColor &bk, QColor &gd, QColor &ft, QColor &c1, QColor &c2, QColor &c3, QColor &c4)
{
    ui->wavePlot->setBackground(bk);
    ui->wavePlot->xAxis->setTickLabelColor(ft);
    ui->wavePlot->xAxis->setLabelColor(ft);
    ui->wavePlot->xAxis->setBasePen(QPen(gd));
    ui->wavePlot->xAxis->setTickPen(QPen(ft));
    ui->wavePlot->xAxis->setSubTickPen(ft);
    ui->wavePlot->xAxis2->setTickLabelColor(ft);
    ui->wavePlot->xAxis2->setLabelColor(ft);
    ui->wavePlot->xAxis2->setBasePen(QPen(gd));
    ui->wavePlot->xAxis2->setTickPen(QPen(ft));
    ui->wavePlot->xAxis2->setSubTickPen(ft);

    ui->wavePlot->yAxis->setTickLabelColor(ft);
    ui->wavePlot->yAxis->setLabelColor(ft);
    ui->wavePlot->yAxis->setBasePen(QPen(gd));
    ui->wavePlot->yAxis->setTickPen(QPen(ft));
    ui->wavePlot->yAxis->setSubTickPen(ft);
    ui->wavePlot->yAxis2->setTickLabelColor(c2);
    ui->wavePlot->yAxis2->setLabelColor(c2);
    ui->wavePlot->yAxis2->setBasePen(QPen(gd));
    ui->wavePlot->yAxis2->setTickPen(QPen(c2));
    ui->wavePlot->yAxis2->setSubTickPen(c2);

    for (Bit32 i = 0; i < ui->wavePlot->graphCount(); i++)
    {
        if (0 == i)
        {
            ui->wavePlot->graph(i)->setPen(QPen(c1));
        }
        else if (1 == i)
        {
            ui->wavePlot->graph(i)->setPen(QPen(c2));
        }
        else if (2 == i)
        {
            ui->wavePlot->graph(i)->setPen(QPen(c3));
        }
        else
        {
            ui->wavePlot->graph(i)->setPen(QPen(c4));
        }

        if(this->waveName == "SCREWWEAR_NUM" || this->waveName == "SCREWWEAR_BASE")
        {
            // 将每个数据点用原点绘制
            ui->wavePlot->graph(i)->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssCircle, 4));
        }
    }

    ui->xName->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName1->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName3->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName5->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));
    ui->yName11->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(ft.red()).arg(ft.green()).arg(ft.blue()));

    if(waveName == "Pos")
    {
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
    }
    else if(waveName == "Speed")
    {
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
    }
    else if(waveName == "CurrentFlow")
    {
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
    }
    else if(waveName == "Other")
    {
        ui->yName1->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName11->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
    }
    else if(waveName == "SCREWWEAR_NUM")
    {
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
    }
    else if(waveName == "SCREWWEAR_BASE") //丝杠磨损-基数
    {
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
    }
    else if(waveName == "SELF_ADJUST_SPEED")
    {
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
        ui->yName4->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c2.red()).arg(c2.green()).arg(c2.blue()));
    }
    else if(waveName == "SPINDLETIME"
            || waveName == "SPINDLEROTATE"
            || waveName == "SPINDLERUNPOWER") //主轴负荷
    {
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
    }
    else if(waveName == "ULTRASOUND_ELECTRIC"
            || waveName == "ULTRASOUND_POWER"
            || waveName == "ULTRASOUND_FREQ") // 超声波
    {
        ui->yName2->setStyleSheet(QString("color: rgb(%1, %2, %3)").arg(c1.red()).arg(c1.green()).arg(c1.blue()));
    }

    ui->wavePlot->replot();
}

void OscWaveDraw::SetLineColor(int lineNo, QColor &color)
{
    if (ui->wavePlot->graphCount() <= lineNo)
    {
        return;
    }
    ui->wavePlot->graph(lineNo)->setPen(QPen(color));
}

void OscWaveDraw::CreateGroup(QCustomPlot *plot) // 关联两个波形图，使其布局一致
{
    if (NULL == plot)
    {
        return;
    }
    QCPMarginGroup *group = new QCPMarginGroup(ui->wavePlot);
    ui->wavePlot->axisRect()->setMarginGroup(QCP::msLeft|QCP::msRight, group);
    plot->axisRect()->setMarginGroup(QCP::msLeft|QCP::msRight, group);
}

void OscWaveDraw::SpindleYAxisAutoSet(fBit64 yMaxVal)
{
    fBit64 yStepMax = plotYStepMax;
    fBit64 yStepMin = plotYStepMix;

    QVector<fBit64> stepV;
    stepV.clear();

    for(Bit32 i = 0; HNC_DoubleCompare(yStepMin * pow(10.0, i), yStepMax) <= 0; i++)
    {
        stepV.append(yStepMin * pow(10.0, i));
    }

    if(HNC_DoubleCompare(yMaxVal / 13.0, stepV.at(0)) <= 0)
    {
        baseYStep = stepV.at(0);
        baseYRangLower = 0;
        baseYRnagUpper = 0 + baseYStep * 13;
    }
    else if(HNC_DoubleCompare(yMaxVal / 13.0, stepV.at(stepV.count()-1)) > 0)
    {
        baseYStep = stepV.at(stepV.count()-1);
        baseYRangLower = 0;
        baseYRnagUpper = 0 + baseYStep * 13;
    }
    else
    {
        for(Bit32 i = 1; i < stepV.count(); i++)
        {
            if(HNC_DoubleCompare(yMaxVal / 13.0, stepV.at(i-1)) > 0 && HNC_DoubleCompare(yMaxVal / 13.0, stepV.at(i)) <= 0)
            {
                baseYStep = stepV.at(i);
                baseYRangLower = 0;
                baseYRnagUpper = 0 + baseYStep * 13;
                break;
            }
        }
    }

    YAxisSetTickParm(baseYRangLower, baseYRnagUpper, baseYStep);
}
