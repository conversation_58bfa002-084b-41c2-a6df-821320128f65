﻿/*!
 * @file overlimitloglist.cpp
 * @brief 主轴全生命周期负荷超限日志列表控件
 * @note
 *
 * @version V1.00
 * @date 2021/3/6
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */

#include <QHeaderView>
#include <QKeyEvent>
#include <QScrollBar>

#include "hncsysctrl.h"
#include "hncsysctrldef.h"

#include "common.h"
#include "filemanage.h"

#include "overlimitloglist.h"
#include "ui_overlimitloglist.h"

const Bit32 LOG_ROW_NUM = 15;

OverLimitLogList::OverLimitLogList(Bit32 type, QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OverLimitLogList)
{
    ui->setupUi(this);

    m_nListType = type;
    this->firstFlag = false;

    curFirstRow = 0;

    ui->tableWidget->setEditTriggers(QTableWidget::NoEditTriggers); // 不能编辑
    ui->tableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);    // 隐藏竖直滚动条
    ui->tableWidget->setAlternatingRowColors(true); // 设置交替行颜色
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection); // 只能单选
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows); // 单击选中整行
    ui->tableWidget->verticalHeader()->setVisible(false); // 隐藏水平header
    ui->tableWidget->horizontalHeader()->setHighlightSections(false); // 点击表时不对表头行光亮（获取焦点）
    ui->tableWidget->horizontalHeader()->setResizeMode(QHeaderView::Fixed);
    ui->tableWidget->setColumnCount(4);     // 设置列宽
    ui->tableWidget->setColumnWidth(0, 50); // 设置列宽度
    ui->tableWidget->setColumnWidth(1, 150);
    ui->tableWidget->setColumnWidth(2, 100);
    ui->tableWidget->setColumnWidth(3, 120);

    ui->tableWidget->setHorizontalHeaderLabels(QStringList()<<TR("序号")<<TR("时间")<<TR("峰值")<<TR("持续时间"));
    ui->tableWidget->horizontalHeader()->setStyleSheet("QHeaderView::section{}"); // 改变header背景颜色
    ui->tableWidget->horizontalHeader()->setFont(QFont(FONT_TYPE, 12));

    RATION_SCALE scale = Resize::GetWidHeiRation();
    if(scale == RATION_5_3)
    {
        ui->tableWidget->setFont(QFont(FONT_TYPE, 12));
    }
    else
    {
        ui->tableWidget->setFont(QFont(FONT_TYPE, 10));
    }
    ui->tableWidget->setRowCount(LOG_ROW_NUM); // 设置行数

    for(Bit32 i = 0; i < LOG_ROW_NUM; i++)
    {
        QTableWidgetItem *pItem = NULL;
        pItem = new QTableWidgetItem();
        ui->tableWidget->setItem(i, 0, pItem);

        pItem = new QTableWidgetItem();
        ui->tableWidget->setItem(i, 1, pItem);

        pItem = new QTableWidgetItem();
        ui->tableWidget->setItem(i, 2, pItem);

        pItem = new QTableWidgetItem();
        ui->tableWidget->setItem(i, 3, pItem);
    }

    ui->verticalScrollBar->setMinimum(0);

    ui->tableWidget->installEventFilter(this);
    oldFirstRow = -1;
}

OverLimitLogList::~OverLimitLogList()
{
    delete ui;
}

void OverLimitLogList::ClearSelection()
{
    ui->tableWidget->clearSelection();
    ui->tableWidget->clearFocus();
}

void OverLimitLogList::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    UNREFERENCED_PARAM(messageValue);
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        ImportLogFileData();
        OnWidgetRefresh(curFirstRow, 1);
        OnVScrollBarRefresh(logData.count());
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::SETFOCUS)
    {
        ui->tableWidget->selectRow(0);
        ui->tableWidget->setFocus();
    }
}

void OverLimitLogList::ImportLogFileData()
{
    Bit8 tmpText[256] = {'\0'};
    Bit8 filename[PATH_NAME_LEN] = {'\0'};
    Bit8 tmpPath[PATH_NAME_LEN] = {'\0'};

    QString tmpString = "";
    QStringList tmpStringList;
    const QString logFileNameStr = "OVERLIMIT";

    Bit32 ret = HNC_SysCtrlGetConfig(HNC_SYS_CFG_LOG_PATH, tmpPath);

    FILE *fp = NULL;

    QDir dir(tmpPath);

    if(!dir.exists())
    {
        FileManage::DirNew(FileManage::DRIVE_SYS, FileManage::LOG_TYPE, QString(tmpPath));
    }

    if (ret != 0)
    {
        return;
    }

    snprintf(filename, PATH_NAME_LEN, "%s%c%s.LOG", tmpPath, DIR_SEPARATOR, logFileNameStr.toStdString().data());

    fp = fopen(filename, "r");
    if (fp == NULL)
    {
        return;
    }

    logData.clear();

    while(!feof(fp))
    {
        tmpStringList.clear();

        memset(tmpText, 0, sizeof(tmpText));

        fgets(tmpText, 256, fp);
        tmpString = QString(tmpText);
        tmpString.simplified();
        tmpString.replace(" ", "");
        tmpStringList = tmpString.split("@");

        if(m_nListType == tmpStringList.at(0).toInt() && false == tmpString.isEmpty())
        {
            logData.append(tmpString);
        }
    }

    fclose(fp);
    fp = NULL;
}

void OverLimitLogList::OnWidgetRefresh(Bit32 firstRow, Bit32 flag)
{
    QString stTimeStr[LOG_ROW_NUM];
    QString peakStr[LOG_ROW_NUM];
    QString durationStr[LOG_ROW_NUM];
    Bit32 rowNum = LOG_ROW_NUM;
    Bit32 logIndex1 = 0;
    Bit32 logIndex2 = 0;
    Bit32 row = 0, col = 0;
    Bit32 logTypeNum = logData.count();

    if (oldFirstRow == firstRow && flag == 0)
    {
        return;
    }
    if(firstRow < 0 || (firstRow < LOG_ROW_NUM && logTypeNum < LOG_ROW_NUM))
    {
        firstRow = 0;
    }
    else if(firstRow > logTypeNum - LOG_ROW_NUM)
    {
        firstRow = logTypeNum - LOG_ROW_NUM;
    }
    oldFirstRow = firstRow;
    curFirstRow = firstRow;

    if (logTypeNum <= 0)
    {
        firstRow = 0;

        logIndex1 = 0;
        logIndex2 = 0;
        rowNum = 0;
    }
    else if (logTypeNum <= LOG_ROW_NUM)
    {
        firstRow = logTypeNum - 1;

        logIndex1 = logTypeNum - 1;
        logIndex2 = 0;
        rowNum = logTypeNum;
    }
    else
    {
        logIndex1 = logTypeNum - 1 - firstRow;
        logIndex2 = logIndex1 - LOG_ROW_NUM + 1;
        if (logIndex2 < 0)
        {
            logIndex2 = 0;
        }
        rowNum = logIndex1 - logIndex2 + 1;
    }
    GetLogListContent(logIndex2, &rowNum, stTimeStr, peakStr, durationStr);
    for (row = 0; row < LOG_ROW_NUM; row++)
    {
        if (row < rowNum)
        {
            ui->tableWidget->item(row, 0)->setText(QString::number(logIndex2 + rowNum - row));
            ui->tableWidget->item(row, 1)->setText(stTimeStr[rowNum - 1 - row]);
            ui->tableWidget->item(row, 2)->setText(peakStr[rowNum - 1 - row]);
            ui->tableWidget->item(row, 3)->setText(durationStr[rowNum - 1 - row]);
        }
        else
        {
            for (col = 0; col < 4; col++)
            {
                ui->tableWidget->item(row, col)->setText("");
            }
        }
    }
}

void OverLimitLogList::OnVScrollBarRefresh(Bit32 maxNum)
{
    if(maxNum <= LOG_ROW_NUM)
    {
        ui->verticalScrollBar->setMaximum(0);
    }
    else
    {
        ui->verticalScrollBar->setMaximum(maxNum - LOG_ROW_NUM);
    }
}

void OverLimitLogList::resizeEvent(QResizeEvent *)
{
    firstFlag = false;
}

bool OverLimitLogList::eventFilter(QObject *wg, QEvent *event)
{
    if(event->type() == QEvent::Paint && !firstFlag)
    {
        Bit32 tabH = ui->tableWidget->height();
        Bit32 headH = ui->tableWidget->horizontalHeader()->height();
        for (Bit32 i = 0; i < LOG_ROW_NUM; i++)
        {
            ui->tableWidget->setRowHeight(i, (tabH-headH) / LOG_ROW_NUM);
        }
        firstFlag = true;
        return true;
    }

    return QObject::eventFilter(wg, event);
}

void OverLimitLogList::keyPressEvent(QKeyEvent *event)
{
    if(!this->isVisible())
    {
        event->ignore();
        return;
    }

    // 防止内存泄漏
    if(curFirstRow < 0)
    {
        curFirstRow = 0;
        return;
    }
    else if((curFirstRow + LOG_ROW_NUM) > logData.count()) // 解决当前第1行在0行时不刷新
    {
        OnWidgetRefresh(curFirstRow, 1);
        return;
    }
    OnVScrollBarRefresh(logData.count());
    switch (event->key())
    {
    case Qt::Key_Up:
        //如果还在显示当中，则不响应Up键消息
        if (curFirstRow > 0)
        {
            curFirstRow--;
            OnWidgetRefresh(curFirstRow);
            ui->verticalScrollBar->setValue(curFirstRow);
        }
        event->accept();
        break;
    case Qt::Key_Down:
        //如果还在显示当中，则不响应Down键消息
        if ((curFirstRow + LOG_ROW_NUM) < logData.count())
        {
            curFirstRow++;
            OnWidgetRefresh(curFirstRow);
            ui->verticalScrollBar->setValue(curFirstRow);
        }
        event->accept();
        break;
    case Qt::Key_PageUp:
        //如果还在显示当中，则不响应PageUp键消息
        if (curFirstRow > 0)
        {
            if(curFirstRow > LOG_ROW_NUM)
            {
                curFirstRow = curFirstRow - LOG_ROW_NUM;
            }
            else
            {
                curFirstRow = 0;
            }
            OnWidgetRefresh(curFirstRow);
            ui->verticalScrollBar->setValue(curFirstRow);
        }
        event->accept();
        break;
    case Qt::Key_PageDown:
        //如果还在显示当中，则不响应PageDown键消息
        if ((curFirstRow + LOG_ROW_NUM) < logData.count())
        {
            if((curFirstRow + LOG_ROW_NUM) < logData.count() - LOG_ROW_NUM)
            {
                curFirstRow = curFirstRow + LOG_ROW_NUM;
            }
            else
            {
                curFirstRow = logData.count() - LOG_ROW_NUM;
            }
            OnWidgetRefresh(curFirstRow);
            ui->verticalScrollBar->setValue(curFirstRow);
        }
        event->accept();
        break;
    default:
        event->ignore();
    }
}

void OverLimitLogList::wheelEvent(QWheelEvent *wheelEvt)
{
    Bit32 numDegrees = wheelEvt->delta() / 8; // 滚动的角度，*8就是鼠标滚动的距离
    Bit32 numSteps = numDegrees / 15;         // 滚动的步数，*15就是鼠标滚动的角度

    if (wheelEvt->orientation() == Qt::Vertical)
    {
        OnVScrollBarRefresh(logData.count());
        Bit32 row = ui->verticalScrollBar->value();
        row -= numSteps;
        Bit32 maxNum = logData.count() - LOG_ROW_NUM;
        if (row > maxNum)
        {
            row = maxNum;
        }
        if(row < 0)
        {
           row = 0;
        }
        if(curFirstRow == row && row == 0) // 解决当前第1行在0行时不刷新
        {
            OnWidgetRefresh(curFirstRow, 1);
        }
        curFirstRow = row;

        ui->verticalScrollBar->setValue(row);

        wheelEvt->accept();      //接收该事件
    }
}

void OverLimitLogList::GetLogListContent(Bit32 index, Bit32 *pLogNum, QString *stTimeStr, QString *peakStr, QString *durationStr)
{
    QString strData = "";
    QStringList strDataList;

    Bit32 i32 = 0;
    Bit32 ii = 0;

    if(pLogNum == NULL || stTimeStr == NULL || peakStr == NULL || durationStr == NULL)
    {
        return;
    }

    if (index >= logData.count())
    {
        return;
    }

    i32 = 0;

    for (ii = 0; ii < *pLogNum; ii++)
    {
        i32 = index + ii;
        if (i32 < logData.count())
        {
            strData = logData.at(i32);
            strData.simplified();
            strData.replace(" ", "");
            strDataList = strData.split("@");

            stTimeStr[ii] = strDataList.at(1);
            peakStr[ii] = strDataList.at(2);
            durationStr[ii] = strDataList.at(3);
        }
        else
        {
            break;
        }
    }
    *pLogNum = ii;
}

void OverLimitLogList::on_verticalScrollBar_valueChanged(int value)
{
    if(curFirstRow == value && value == 0) // 解决当前第1行在0行时不刷新
    {
        OnWidgetRefresh(curFirstRow, 1);
    }
    curFirstRow = value;
    OnWidgetRefresh(curFirstRow, 1);
}
