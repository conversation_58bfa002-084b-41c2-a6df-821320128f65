﻿#ifndef SELFADJUSTSTEP1_H
#define SELFADJUSTSTEP1_H

#include "containerwidget.h"
#include <QDateTime>
#include <QMovie>

namespace Ui {
class SelfAdjustStep1;
}

QT_BEGIN_NAMESPACE
class QMovie;
QT_END_NAMESPACE

class SelfAdjustStep1 : public ContainerWidget
{
    Q_OBJECT

public:
    enum LISTCONTENT
    {
        COL_AXISNO,
        COL_AXISNAME,
        COL_AXISTYPE,
        COL_AXISSPIN,
        COL_AXISSWING,
        COL_AXISFLAG,
        COL_AXISTIME,

        COL_TOTAL,
    };
    explicit SelfAdjustStep1(QWidget *parent = 0);
    ~SelfAdjustStep1();

    void WidgetIn();
    void WidgetExit();

protected:
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    bool eventFilter(QObject *target, QEvent *event);

private:
    Ui::SelfAdjustStep1 *ui;
    QString picPath;
    QMovie *pMovie;
    bool firstFlag; // 首次绘制标记
    Bit32 axisLastStatus;   // 轴的上次位置 -1：安全区域以左 0：安全区域 1：安全区域以右
    Bit32 m_nSaveChannel;
    Bit32 m_listCount;      // 列表行数（可变）

    void LoadWidget();
    void LoadAxisInfo();
    void GetTableCount();

private slots:
    void SlotTableWidgetCellChanged(int currentRow, int currentColumn, int previousRow, int previousColumn);
};

#endif // SELFADJUSTSTEP1_H
