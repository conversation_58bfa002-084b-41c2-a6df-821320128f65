﻿#ifndef SELFADJUSTSTEP3_H
#define SELFADJUSTSTEP3_H

#include "containerwidget.h"
#include <QDateTime>
#include <QMovie>

namespace Ui {
class SelfAdjustStep3;
}

//QT_BEGIN_NAMESPACE
//class QMovie;
//QT_END_NAMESPACE

class SelfAdjustStep3 : public ContainerWidget
{
    Q_OBJECT

public:
    explicit SelfAdjustStep3(QWidget *parent = 0);
    ~SelfAdjustStep3();

    void WidgetIn();
    Bit32 WidgetExitChk();

protected:
    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    bool eventFilter(QObject *target, QEvent *event);

private:
    Ui::SelfAdjustStep3 *ui;    
    //QString picPath;
    //QMovie *pMovie;
    bool firstFlag; // 首次绘制标记
    bool stopFlag;  // 强制停止标记
    QDateTime curTime;
    Bit32 curStatus;    // 自整定当前状态，防止相同状态重复刷新文本

    void RefreshMotorPos();
    void RefreshAdjustStep();
    void RefreshAdjustStepNCUC();
    void RefreshAdjustStepECAT();
    void ResetAdjustStep();
    void ClearAdjustStep();
    void InsertAjustStep(bool enter, QString info);
    void SetButtonState();
};

#endif // SELFADJUSTSTEP3_H
