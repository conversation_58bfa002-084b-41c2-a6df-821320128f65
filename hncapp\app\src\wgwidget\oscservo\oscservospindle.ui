<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OscServoSpindle</class>
 <widget class="QWidget" name="OscServoSpindle">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>710</width>
    <height>442</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,1">
   <property name="spacing">
    <number>2</number>
   </property>
   <property name="leftMargin">
    <number>2</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2"/>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="backgrd3" native="true">
     <property name="selected" stdset="0">
      <bool>false</bool>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout" stretch="1,8,1,4">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="labelTitle">
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>12</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string> 信息</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout" rowstretch="1,1,1,1,1,1,1,1,1,1">
        <property name="leftMargin">
         <number>6</number>
        </property>
        <item row="7" column="1">
         <widget class="QLabel" name="labelDownCof">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <spacer name="verticalSpacer_3">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="8" column="2">
         <widget class="QLabel" name="label_17">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>（s）</string>
          </property>
         </widget>
        </item>
        <item row="8" column="0">
         <widget class="QLabel" name="label_15">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>主轴延时时间</string>
          </property>
         </widget>
        </item>
        <item row="9" column="1">
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="7" column="0">
         <widget class="QLabel" name="label_13">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>主轴减速系数</string>
          </property>
         </widget>
        </item>
        <item row="2" column="2">
         <widget class="QLabel" name="label_6">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>（s）</string>
          </property>
         </widget>
        </item>
        <item row="8" column="1">
         <widget class="QLabel" name="labelDelayTime">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="3" column="2">
         <widget class="QLabel" name="label_9">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>（s）</string>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="label_4">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>升速时间</string>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLabel" name="labelUpTime">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="QLabel" name="label_7">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>降速时间</string>
          </property>
         </widget>
        </item>
        <item row="6" column="0">
         <widget class="QLabel" name="label_11">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>主轴加速系数</string>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="QLabel" name="labelDownTime">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="6" column="1">
         <widget class="QLabel" name="labelUpCof">
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string/>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
         </widget>
        </item>
        <item row="4" column="1">
         <spacer name="verticalSpacer_2">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
        <item row="1" column="0" colspan="2">
         <widget class="QLabel" name="label_3">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>主轴升降速时间</string>
          </property>
         </widget>
        </item>
        <item row="5" column="0" colspan="3">
         <widget class="QLabel" name="label_10">
          <property name="minimumSize">
           <size>
            <width>10</width>
            <height>0</height>
           </size>
          </property>
          <property name="font">
           <font>
            <family>微软雅黑</family>
            <pointsize>12</pointsize>
            <weight>50</weight>
            <bold>false</bold>
           </font>
          </property>
          <property name="text">
           <string>跟随式刚攻主轴参数推荐值</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QLabel" name="labelTitle1">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>0</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>微软雅黑</family>
          <pointsize>12</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string> 主轴调整参数</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout_2"/>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
