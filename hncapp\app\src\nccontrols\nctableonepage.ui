<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>NcTableOnePage</class>
 <widget class="QWidget" name="NcTableOnePage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>300</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_2">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MyTable" name="ncTable"/>
   </item>
   <item>
    <widget class="QScrollBar" name="ncScrollBar">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MyTable</class>
   <extends>QTableWidget</extends>
   <header location="global">mytable.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
