﻿#include <QEvent>
#include <QWheelEvent>
#include <QHeaderView>

#include "nccombobox.h"

#include "nctableonepage.h"
#include "ui_nctableonepage.h"

const int MaxRowHeight = 50;
NcTableOnePage::NcTableOnePage(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::NcTableOnePage)
{
    ui->setupUi(this);

    this->pageRowNum = 0;
    this->selIdx = 0;
    this->startIdx = 0;
    this->totalRowNum = 0;
    ui->ncTable->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    ui->ncTable->horizontalHeader()->setResizeMode(QHeaderView::Fixed);
    this->installEventFilter(this);
}

NcTableOnePage::~NcTableOnePage()
{
    delete ui;
}

void NcTableOnePage::TableConnectSlot()
{
    connect(ui->ncScrollBar, SIGNAL(valueChanged(int)), this, SLOT(ScrollValChgResponse(int)));
    connect(ui->ncTable, SIGNAL(currentCellChanged(int,int,int,int)), this, SLOT(TableSelRowSet(int,int,int,int)));
}

bool NcTableOnePage::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        bool ret = true;
        int curCol = ui->ncTable->currentColumn();
        int curRow = ui->ncTable->currentRow();

        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        if(curRow >= 0 && curCol >= 0)
        {
            if(ui->ncTable->item(curRow, curCol)->data(ComboBoxStyle).toBool()
                    && keyEvent->key() >= Qt::Key_0 && keyEvent->key() <= Qt::Key_9)
            {
                return true;
            }
        }

        switch(keyEvent->key())
        {
        case Qt::Key_Up:
        case Qt::Key_Down:
        case Qt::Key_PageUp:
        case Qt::Key_PageDown:
            ret = this->KeyPressResponce(keyEvent->key());
            return ret;
            break;
        case Qt::Key_F4: // 因下拉框控件会响应该按键，因此屏蔽
            return ret;
            break;
        default:
            break;
        }
    }
    else if(event->type() == QEvent::Resize)
    {
        resizeTableRowHeight();
    }
    else if(event->type() == QEvent::Wheel)
    {
        QWheelEvent *wheelEvt = static_cast<QWheelEvent *>(event);
        int numDegrees = wheelEvt->delta() / 8;//滚动的角度，*8就是鼠标滚动的距离
        int numSteps = numDegrees / 15;//滚动的步数，*15就是鼠标滚动的角度

        if (wheelEvt->orientation() == Qt::Vertical) //
        {
            this->selIdx -= numSteps;
            this->CheckTableData();
            TableItemSetFocus();

            return true;      //接收该事件
        }
    }

    return QObject::eventFilter(target, event);
}

bool NcTableOnePage::KeyPressResponce(int key)
{
    int curCol = ui->ncTable->currentColumn();
    int curRow = ui->ncTable->currentRow();

    if(curCol < 0 || curRow < 0)
    {
        return true;
    }
    int moveLen = 0;
    switch (key) {
    case Qt::Key_Up:
        moveLen = -1;
        break;
    case Qt::Key_Down:
        moveLen = 1;
        break;
    case Qt::Key_PageUp:
        moveLen = -this->pageRowNum;
        break;
    case Qt::Key_PageDown:
        moveLen = this->pageRowNum;
        break;
    default:
        break;
    }

    if(ui->ncTable->item(curRow, curCol)->data(ComboBoxStyle).toBool())
    {
        if(((NcComboBox*)ui->ncTable->cellWidget(curRow, curCol))->view()->isVisible() == false)
        {
            this->selIdx = this->selIdx + moveLen;
            this->CheckTableData();
            TableItemSetFocus();
            return true; // 处理完毕不再响应
        }
        return false; // 下拉框响应自己
    }
    else
    {
        this->selIdx = this->selIdx + moveLen;
        this->CheckTableData();
        TableItemSetFocus();
        return true; // 表格响应自己的消息，无需额外处理
    }
}

void NcTableOnePage::SetCurrentIdx(Bit32 idx)
{
    this->selIdx = idx;
    this->CheckTableData();
    TableItemSetFocus();
}
void NcTableOnePage::resizeTableRowHeight()
{
    int height = 0;
    int rowH = 0;
    int ScrollBarH = 0;
    this->pageRowNum = ui->ncTable->rowCount();

    if(this->pageRowNum <= 0)
    {
        return;
    }
    if(ui->ncTable->horizontalScrollBarPolicy() == Qt::ScrollBarAlwaysOn)
    {
        ScrollBarH = ui->ncTable->horizontalScrollBar()->height();
    }
    height = ui->ncTable->height() - ui->ncTable->horizontalHeader()->height() - ScrollBarH;
    rowH = int(height / this->pageRowNum);

    if(rowH >= MaxRowHeight)
    {
        rowH = MaxRowHeight;
    }

    for(int i = 0; i < this->pageRowNum; i++)
    {
        ui->ncTable->setRowHeight(i, rowH);
    }
}

Bit32 NcTableOnePage::GetTotalDataRowNum()
{
    return this->totalRowNum;
}

void NcTableOnePage::SetTotalDataRowNum(int row)
{
    if(row < 0 || this->totalRowNum == row)
    {
        return;
    }

    this->startIdx = 0; // 数据修改后恢复到原始值，防止出错
    this->selIdx = 0;
    this->totalRowNum = row;
    if(row == 0)
    {
        ui->ncScrollBar->setRange(0,0);
    }
    else
    {
        ui->ncScrollBar->setRange(0, row - 1);
    }
    ui->ncScrollBar->setValue(0);
}

void NcTableOnePage::SetTableRowNum(int row)
{
    if(row < 0 || this->pageRowNum == row)
    {
        return;
    }

    this->startIdx = 0; // 数据修改后恢复到原始值，防止出错
    this->selIdx = 0;
    this->pageRowNum = row;
    ui->ncTable->setRowCount(this->pageRowNum);
    resizeTableRowHeight();
}

void NcTableOnePage::GoToDataRow(int idx)
{
    this->selIdx = idx;
    this->CheckTableData();
    TableItemSetFocus();
}

QTableWidget* NcTableOnePage::GetTable()
{
    return ui->ncTable;
}

QScrollBar* NcTableOnePage::GetScrollBar()
{
    return ui->ncScrollBar;
}

/**
 * @brief NcTableOnePage::ScrollValChgResponse 响应滚动条变化
 * @param row
 */
void NcTableOnePage::ScrollValChgResponse(int row)
{
    if(row < 0)
    {
        row = 0;
    }
    else if(row >= this->totalRowNum)
    {
        row = this->totalRowNum - 1;
    }
    this->selIdx = row;
    this->CheckTableData();

    disconnect(ui->ncTable, SIGNAL(currentCellChanged(int,int,int,int)), this, SLOT(TableSelRowSet(int,int,int,int)));
    TableItemSetFocus();
    connect(ui->ncTable, SIGNAL(currentCellChanged(int,int,int,int)), this, SLOT(TableSelRowSet(int,int,int,int)));
}

/**
 * @brief NcTableOnePage::ScrollBarReset 当前选中数据序号改变后人为设置滚动条
 */
void NcTableOnePage::ScrollBarReset()
{
    disconnect(ui->ncScrollBar, SIGNAL(valueChanged(int)), this, SLOT(ScrollValChgResponse(int)));
    ui->ncScrollBar->setValue(this->selIdx);
    connect(ui->ncScrollBar, SIGNAL(valueChanged(int)), this, SLOT(ScrollValChgResponse(int)));
}

void NcTableOnePage::TableSelRowSet(int curRow, int, int previousRow, int)
{
    if(curRow == previousRow)
    {
        return;
    }

    this->selIdx = this->startIdx + curRow;
    this->CheckTableData();
}

void NcTableOnePage::CheckTableData()
{
    int oldStart = this->startIdx;

    if(this->totalRowNum <= 0 || this->pageRowNum <= 0)
    {
        return;
    }

    if(this->selIdx < 0)
    {
        this->selIdx = 0 ;
    }
    else if(this->selIdx >= this->totalRowNum)
    {
        this->selIdx = this->totalRowNum - 1;
    }

    if(this->startIdx < 0)
    {
        this->startIdx = 0;
    }
    else if(this->startIdx > this->totalRowNum - this->pageRowNum)
    {
        this->startIdx = this->totalRowNum - this->pageRowNum;
    }
    else if(this->startIdx > this->selIdx)
    {
        this->startIdx = this->selIdx;
    }
    else if(this->selIdx - this->startIdx > this->pageRowNum -1)
    {
        this->startIdx = this->selIdx - this->pageRowNum + 1;
    }


    if(oldStart != this->startIdx)
    {
        emit StartIdxChg(this->startIdx);
    }
    ScrollBarReset();
}

void NcTableOnePage::TableItemSetFocus()
{
    if(ui->ncTable->rowCount() <= 0 || ui->ncTable->columnCount() <= 0)
    {
        return;
    }
    int curCol = ui->ncTable->currentColumn();
    if(curCol < 0)
    {
        curCol = 0;
    }
    if(this->selIdx - this->startIdx < 0 || this->selIdx - this->startIdx >= ui->ncTable->rowCount())
    {
        return;
    }
    ui->ncTable->setCurrentItem(ui->ncTable->item(this->selIdx - this->startIdx, curCol));
    if(!ui->ncTable->hasFocus())
    {
        ui->ncTable->setFocus();
    }
    if(ui->ncTable->item(this->selIdx - this->startIdx, curCol)->data(ComboBoxStyle).toBool())
    {
        ((NcComboBox*)ui->ncTable->cellWidget(this->selIdx - this->startIdx, curCol))->setFocus();
    }
}

void NcTableOnePage::SetTableItemRole(int row, int col, int role, QVariant &val)
{
    if(row < 0 || row >= ui->ncTable->rowCount()
        || col < 0 || col >= ui->ncTable->columnCount())
    {
        return;
    }

    ui->ncTable->item(row, col)->setData(role, val);
}
