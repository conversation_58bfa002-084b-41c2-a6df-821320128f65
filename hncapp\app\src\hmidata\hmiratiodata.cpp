﻿#include <QFile>
#include <QProcessEnvironment>
#include <QTextStream>

#include "hncsysctrl.h"
#include "osdepend.h"

#include "common.h"
#include "apposdepend.h"

#include "hmiratiodata.h"

Bit32 HmiGetLinuxRationIndex()
{
#ifdef _LINUX
    QString strPath = "";
    QString strAll = "";
    Bit32 index = 0;

    NcSync();
    if (QProcess::execute("mount -o remount,rw /mnt/user") == 0)
    {
        strPath = "/mnt/user/boot/grub/menu.lst";
    }
    else
    {
        strPath = "/boot/grub/menu.lst";
    }
    if(strPath == "")
    {
        return -1;
    }
    QFile linuxFile(strPath);
    if (!linuxFile.open(QFile::ReadOnly | QFile::Text))
    {
        return -1;
    }
    QTextStream linuxIn(&linuxFile);
    strAll = linuxIn.readAll();
    linuxFile.close();

    for(index = 0; index < hmiRatioFlagList.count(); index++)
    {
        if(strAll.contains(hmiRatioFlagList.at(index)))
        {
            return index;
        }
    }
#endif
    return -1;

}

void HmiSetLinuxRation(Bit32 index)
{
    if(index < 0 || index >= hmiRationCount)
    {
        return;
    }
#ifdef _LINUX

    QString strPath = "";
    QString strAll = "";

    NcSync();

    if (QProcess::execute("mount -o remount,rw /mnt/user") == 0)
    {
        strPath = "/mnt/user/boot/grub/menu.lst";
    }
    else
    {
        strPath = "/boot/grub/menu.lst";
    }
    if(strPath == "")
    {
        return;
    }
    QFile linuxFile(strPath);
    if (!linuxFile.open(QFile::ReadOnly | QFile::Text))
    {
        return;
    }
    QTextStream linuxIn(&linuxFile);
    strAll = linuxIn.readAll();
    linuxFile.close();

    if (!linuxFile.open(QFile::WriteOnly | QFile::Text))
    {
        return;
    }
    for(Bit32 i = 0; i < hmiRatioFlagList.count(); i++)
    {
        strAll.replace(hmiRatioFlagList[i], hmiRatioFlagList[index]);
    }

    QTextStream linuxOut(&linuxFile);
    linuxOut << strAll;
    linuxFile.close();
    NcSync();
#endif
}

Bit32 HmiGetHmicfgRation()
{
    Bit32 index = -1;
    QDomDocument doc;
    Bit32 ret = GetXMLElement(HNC_SYS_CFG_BIN_PATH,QString("hmicfg.xml"), doc);

    if(ret < 0)
    {
        return index;
    }

    QString wid = "";
    QString hei = "";
    QString rStr = "";
    QDomElement element = doc.documentElement();
    QDomElement itemelement = element.firstChildElement();
    while (!itemelement.isNull())
    {
        if(itemelement.nodeName() == "SYSWIDTH")
        {
            wid = itemelement.attribute("index");
        }
        else if(itemelement.nodeName() == "SYSHEIGHT")
        {
            hei = itemelement.attribute("index");
        }
        if(!wid.isEmpty() && !hei.isEmpty())
        {
            break;
        }
        itemelement = itemelement.nextSiblingElement();
    }

    rStr = QString("%1*%2").arg(wid, hei);
    index = hmiRationStr.indexOf(rStr);
    return index;
}

void HmiSetHmicfgRation(Bit32 index)
{
    bool widthCheckFlag = false;
    bool heightCheckFlag = false;
    bool saveFlag = false;
    Bit8 filePathName[PATH_NAME_LEN] = {'\0'};
    Bit8 dataPath[PATH_NAME_LEN] = {'\0'};
    QDomDocument doc;

    HNC_SysCtrlGetConfig(HNC_SYS_CFG_BIN_PATH, dataPath);
    snprintf(filePathName, PATH_NAME_LEN, "%s%c%s", dataPath, DIR_SEPARATOR, "hmicfg.xml");

    Bit32 ret = GetXMLElement(filePathName, doc);
    if(ret < 0 || index < 0 || index >= hmiRationCount)
    {
        return;
    }

    QDomElement element = doc.documentElement();

    QDomElement itemelement = element.firstChildElement();
    while (!itemelement.isNull())
    {
        if(itemelement.nodeName() == "SYSWIDTH")
        {
            if(itemelement.attribute("index") != hmiRationWidList[index])
            {
                itemelement.setAttribute("index", hmiRationWidList[index]);
                saveFlag = true;
            }
            widthCheckFlag = true;
        }

        if(itemelement.nodeName() == "SYSHEIGHT")
        {
            if(itemelement.attribute("index") != hmiRationHeightList[index])
            {
                itemelement.setAttribute("index", hmiRationHeightList[index]);
                saveFlag = true;
            }
            heightCheckFlag = true;
        }
        itemelement = itemelement.nextSiblingElement();
    }

    if(!widthCheckFlag)
    {
        QDomElement item = doc.createElement("SYSWIDTH");
        itemelement.setAttribute("index", hmiRationWidList[index]);
        element.appendChild(item);
        saveFlag = true;
    }
    if(!heightCheckFlag)
    {
        QDomElement item = doc.createElement("SYSHEIGHT");
        itemelement.setAttribute("index", hmiRationHeightList[index]);
        element.appendChild(item);
        saveFlag = true;
    }

    if(saveFlag == true)
    {
        QFile file(filePathName);
        if(!file.open(QFile::WriteOnly | QFile::Text))
        {
            return;
        }
        QTextStream out(&file);
        out.setCodec(CODE_UTF8);
        doc.save(out, 4, QDomNode::EncodingFromTextStream); // indent = 4;缩进四个字符
        file.close();
        FileStrFSync(filePathName);
    }
}

QString HmiGetRationStr(Bit32 index)
{
    QString str = "";

    if(index >= 0 && index < hmiRationCount)
    {
        str = QString("%1×%2").arg(hmiRationWidList[index], hmiRationHeightList[index]);
    }

    return str;
}

QString HmiGetSysRationFlagStr(Bit32 index)
{
    QString str = "";
    if(index >= 0 && index < hmiRationCount)
    {
        str = hmiRatioFlagList.at(index);
    }

    return str;
}

QString HmiGetWidHeiRation(Bit32 index)
{
    QString rationStr = "";

    switch (index) {
    case 0:
    case 1:
        rationStr = "_0"; // 4:3
        break;
    case 2:
        rationStr = "_1"; // 5:4
        break;
    default:
        break;
    }

    return rationStr;
}
