﻿#include <QEvent>

#include "common.h"
#include "hmipaintercolor.h"

#include "ncprogress.h"
#include "ui_ncprogress.h"

#define GRID_WIDTH (10)

NcProgress::NcProgress(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::NcProgress)
{
    ui->setupUi(this);

    m_fMin = 0;
    m_fMax = 100;
    m_fVal = 0;
    m_nChunkSt = 0;
    m_nPrec = 1;

    m_nTimer = this->startTimer(50);

    this->m_bFirstFlag = false;

    m_backColor = HmiPainterColor::GetInstance()->GetProgressBack();
    m_chunkColor = HmiPainterColor::GetInstance()->GetProgressChunkBack();
    m_chunkFrontColor = HmiPainterColor::GetInstance()->GetProgressChunkFront();

    this->m_pPaint = new QPainter();
    this->m_pPaintWidget = new QPainter();

    ui->widget->installEventFilter(this);
    this->installEventFilter(this);
}

NcProgress::~NcProgress()
{
    if (this->m_pPaint != NULL)
    {
        delete this->m_pPaint;
        this->m_pPaint = NULL;
    }
    if (this->m_pPaintWidget != NULL)
    {
        delete this->m_pPaintWidget;
        this->m_pPaintWidget = NULL;
    }
}

void NcProgress::SetRange(fBit64 min, fBit64 max)
{
    m_fMin = min;
    m_fMax = max;
    SetValue(m_fVal);
}

fBit64 NcProgress::GetPer()
{
    if (m_fMax == m_fMin)
    {
        return 0.0;
    }
    return (fBit64)m_fVal / (m_fMax - m_fMin);
}

void NcProgress::SetValue(fBit64 val)
{
    if (val < m_fMin)
    {
        m_fVal = m_fMin;
    }
    else if (val > m_fMax)
    {
        m_fVal = m_fMax;
    }
    else
    {
        m_fVal = val;
    }
    UpdatePerRectWidth();

    fBit64 fval = GetPer();
    QString str = QString("%1%").arg(fval * 100, 0, 'f', m_nPrec);
    ui->label_per->setText(str);

    this->update();
    ui->widget->update();
}

void NcProgress::resizeEvent(QResizeEvent *)
{
    this->m_bFirstFlag = false;
}

bool NcProgress::eventFilter(QObject *wg, QEvent *event)
{
    if (event->type() == QEvent::Timer)
    {
        ui->widget->update();

        m_nChunkSt++;
        if (m_nChunkSt >= GRID_WIDTH * 2)
        {
            m_nChunkSt = 0;
        }
    }
    else if (event->type() == QEvent::Paint)
    {
        if  (!m_bFirstFlag)
        {
            PaintResize();
            m_bFirstFlag = true;
        }
        if (wg == this)
        {
            Redraw();
        }
        if (ui->widget == wg)
        {
            Refresh();
        }
    }

    return QObject::eventFilter(wg, event);
}

void NcProgress::UpdatePerRectWidth()
{
    fBit64 fval = GetPer();
    Bit32 width = m_paintRect.width() * fval;
    m_perRect.setWidth(width);
    ui->widget->setGeometry(m_perRect);
}

void NcProgress::PaintResize()
{
    this->m_paintRect.setRect(0, 0, this->width(), this->height());

    ui->label_per->setGeometry(m_paintRect);

    m_perRect = this->m_paintRect;

    UpdatePerRectWidth();
}

void NcProgress::Redraw()
{
    if (m_pPaint->begin(this) == false)
    {
        return;
    }

    m_pPaint->setBrush(this->m_backColor);
    m_pPaint->setPen(this->m_backColor);
    m_pPaint->drawRect(m_paintRect);

    m_pPaint->setBrush(this->m_chunkColor);
    m_pPaint->setPen(this->m_chunkColor);
    m_pPaint->drawRect(m_perRect);

    m_pPaint->end();
}

void NcProgress::DrawDiamond(Bit32 st)
{
    QPoint points[4] = {
        QPoint(st, m_perRect.top()),
        QPoint(st + GRID_WIDTH, m_perRect.bottom()),
        QPoint(st + GRID_WIDTH * 2, m_perRect.bottom()),
        QPoint(st + GRID_WIDTH, m_perRect.top())
    };

    m_pPaintWidget->drawPolygon(points, 4);
}

void NcProgress::Refresh()
{
    if (m_pPaintWidget->begin(ui->widget) == false)
    {
        return;
    }

    Bit32 totalWidth = m_perRect.width();
    Bit32 st = - GRID_WIDTH + m_nChunkSt;
    m_pPaintWidget->setBrush(this->m_chunkFrontColor);
    m_pPaintWidget->setPen(this->m_chunkFrontColor);

    while (st < totalWidth - GRID_WIDTH)
    {
        DrawDiamond(st);
        st += GRID_WIDTH * 2;
    }

    m_pPaintWidget->end();
}
