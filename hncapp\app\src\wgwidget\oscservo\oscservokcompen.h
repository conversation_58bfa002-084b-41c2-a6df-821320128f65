﻿/*!
* @file oscservokcompen.h
* @brief 前馈调试界面
* @note
*
* @version V1.00
* @date 2022/02/13
* <AUTHOR> Team
* @copyright 武汉华中数控股份有限公司软件开发部
*/
#ifndef OSCSERVOKCOMPEN_H
#define OSCSERVOKCOMPEN_H

#include "hmioscproc.h"
#include "containerwidget.h"
using namespace std;

namespace Ui {
	class OscServoKCompen;
}

QT_BEGIN_NAMESPACE
class OscList;
class QWidget;
class OscWave;
QT_END_NAMESPACE

class OscServoKCompen : public ContainerWidget
{
	Q_OBJECT

public:
	explicit OscServoKCompen(QWidget *parent = 0);
	~OscServoKCompen();
	void SetColorStyle();

protected:
	void FrameWorkMessage(QVariant messageid, QVariant messageValue);
	bool eventFilter(QObject *target, QEvent *event);
	void resizeEvent(QResizeEvent *);
private slots:

	void on_leftBtn_clicked();

	void on_rightBtn_clicked();

private:
	Ui::OscServoKCompen *ui;
	Bit32 idenAxisNo;
	Bit32 lastEndPos;

	fBit64 kcompenVal;
	fBit64 feedKCompenTime;
	fBit64 fastFeedKCompenTime;
	OscList *kcompenOscList;
	OscWave *m_pOscWaveSpeed;
	OscWave *m_pOscWaveTrackErr;

	bool m_bSampleStart;		// 采样开始

	Bit32 m_lineNum[OSC_MAX_DATA_NUM];//行号
	fBit64 m_cmdVel[OSC_MAX_DATA_NUM];//指令速度
	fBit64 m_actVel[OSC_MAX_DATA_NUM];//实际速度
	fBit64 m_trackErr[OSC_MAX_DATA_NUM];//负载电流

	void ResultSave();
	void LoadInfo();
	void Refresh();
	void Reset();
	void OnBtFlagChange();
	bool firstFlag;
	void LoadAxisVal(Bit32 type);
	void ResetInfo();
	QStringList GetParmList();

};

#endif // OSCSERVOKCOMPEN_H
