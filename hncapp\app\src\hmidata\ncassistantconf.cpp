﻿#include <QTextStream>

#include "hncsysctrl.h"

#include "hotkeycfg.h"
#include "apposdepend.h"

#include "ncassistantconf.h"

#define AssistantConfFile ("recoveryapp")

QString NcAssistantConf::m_sPath = "";
Bit32 NcAssistantConf::m_nExecFlag = 0;

NcAssistantConf::NcAssistantConf()
{
}

QString NcAssistantConf::GetFilPath()
{
    if (m_sPath.isEmpty() == false)
    {
        return m_sPath;
    }

    Bit8 path[PATH_NAME_LEN] = {'\0'};
    HNC_SysCtrlGetConfig(HNC_SYS_CFG_BIN_PATH, path);
    m_sPath = QString(path) + QString(DIR_SEPARATOR) + QString(AssistantConfFile);
    return m_sPath;
}

void NcAssistantConf::WriteAssFile()
{
    QFile f(GetFilPath());
    if (f.exists() == false)
    {
        CreateAssFile();
        return;
    }

    if (!f.open(QIODevice::ReadOnly))
    {
        return;
    }

    QTextStream in(&f);
    in.setCodec(CODE_UTF8); // 设置流的编码
    QStringList strAll;
    strAll.clear();

    while(!in.atEnd())
    {
        QString str = in.readLine();
        if (str.contains("RESETSYS="))
        {
            strAll << QString("RESETSYS=%1").arg(m_nExecFlag);
        }
        else
        {
            strAll << str;
        }
    }

    f.close();

    if (!f.open(QIODevice::WriteOnly))
    {
        return;
    }

    QTextStream out(&f);
    out.setCodec(CODE_UTF8); // 设置流的编码
    for (int i = 0; i < strAll.count(); i++)
    {
        out << strAll.at(i) + QString("\n");
    }
    f.close();
    NcSync();
    FileStrFSync(GetFilPath());
}

void NcAssistantConf::CreateAssFile()
{
    QFile f(GetFilPath());
    if (!f.open(QIODevice::WriteOnly))
    {
        return;
    }

    QTextStream textStream(&f);
    textStream.setCodec(CODE_UTF8); // 设置流的编码
    textStream << "#!/bin/sh\n"
               << "sync\n"
               << QObject::TR("#RESETSYS:0表示等待快捷键按下启动备份还原工具,1:直接启动备份还原工具\n")
               << QString("RESETSYS=%1\n").arg(m_nExecFlag)
               << "export LD_LIBRARY_PATH=../shrlib/\n"
               << "export QT_QWS_FONTDIR=../shrlib/fonts/\n"
               << QObject::TR("#T:RESETSYS为0时等待快捷键的时间(毫秒),FLAG:是否直接启动工具\n")
               << "./backandrecovery -qws -nomouse T=3000 FLAG=\"$RESETSYS\"\n"
               << "sync\n";

    f.close();

    NcSync();
    FileStrFSync(GetFilPath());
}

void NcAssistantConf::GetExecFlag()
{
    QFile f(GetFilPath());
    if (f.exists() == false)
    {
        CreateAssFile();
        return;
    }

    if (!f.open(QIODevice::ReadOnly))
    {
        return;
    }

    QTextStream in(&f);
    in.setCodec(CODE_UTF8); // 设置流的编码
    while(!in.atEnd())
    {
        QString str = in.readLine().remove(' ');
        Bit32 idx = str.indexOf("RESETSYS=");
        if (idx >= 0)
        {
            QString tmp  = str.mid(idx + QString("RESETSYS=").length(), 1);
            m_nExecFlag = tmp.toInt();
            break;
        }
    }

    f.close();
}

void NcAssistantConf::SetExecFlag(Bit32 val)
{
    if (m_nExecFlag == val)
    {
        return;
    }

    m_nExecFlag = val;
    WriteAssFile();
}

void NcAssistantConf::InitAss()
{
    QFile f(GetFilPath());
    if (f.exists() == false)
    {
        CreateAssFile();
        return;
    }
    GetExecFlag();
}
