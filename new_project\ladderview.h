#ifndef LADDERVIEW_H
#define LADDERVIEW_H

#include <QGraphicsView>
#include <QGraphicsScene>
#include <QGraphicsItem>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QWheelEvent>
#include <QTimer>

#include "ladderscene.h"
#include "ladderdata.h"

class LadderView : public QGraphicsView
{
    Q_OBJECT

public:
    explicit LadderView(QWidget *parent = nullptr);
    ~LadderView();

    // 设置数据源
    void setLadderData(LadderData *data);
    
    // 显示控制
    void setStartRow(int row);
    void setStartCol(int col);
    int getStartRow() const { return m_startRow; }
    int getStartCol() const { return m_startCol; }
    
    // 刷新显示
    void refreshDisplay();
    void forceRefresh();
    
    // 模式设置
    void setDebugMode(bool enabled);
    void setEditMode(bool enabled);
    bool isDebugMode() const { return m_debugMode; }
    bool isEditMode() const { return m_editMode; }
    
    // 选择控制
    void selectCell(int row, int col);
    void clearSelection();
    int getSelectedRow() const { return m_selectedRow; }
    int getSelectedCol() const { return m_selectedCol; }
    
    // 缩放控制
    void zoomIn();
    void zoomOut();
    void zoomReset();
    void setZoomFactor(qreal factor);
    qreal getZoomFactor() const { return m_zoomFactor; }

signals:
    void cellSelected(int row, int col);
    void cellDoubleClicked(int row, int col);
    void cellRightClicked(int row, int col, const QPoint &globalPos);
    void viewScrolled(int row, int col);

protected:
    // 事件处理
    void mousePressEvent(QMouseEvent *event) override;
    void mouseDoubleClickEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    
    // 上下文菜单
    void contextMenuEvent(QContextMenuEvent *event) override;

private slots:
    void onSceneChanged(const QList<QRectF> &region);
    void onDataChanged(int row, int col);
    void onRowDataChanged(int row);

private:
    // 初始化函数
    void setupView();
    void setupScene();
    void setupConnections();
    
    // 坐标转换
    QPoint viewToCell(const QPoint &viewPos);
    QPoint cellToView(int row, int col);
    QRectF getCellRect(int row, int col);
    
    // 显示更新
    void updateVisibleArea();
    void updateSceneRect();
    void ensureCellVisible(int row, int col);
    
    // 绘制相关
    void drawBackground(QPainter *painter, const QRectF &rect) override;
    void drawForeground(QPainter *painter, const QRectF &rect) override;
    
    // 选择处理
    void updateSelection();
    void highlightCell(int row, int col, bool highlight);

private:
    // 场景和数据
    LadderScene *m_scene;
    LadderData *m_ladderData;
    
    // 显示参数
    int m_startRow;             // 显示起始行
    int m_startCol;             // 显示起始列
    int m_visibleRows;          // 可见行数
    int m_visibleCols;          // 可见列数
    
    // 选择状态
    int m_selectedRow;          // 选中行
    int m_selectedCol;          // 选中列
    bool m_selectionVisible;    // 选择是否可见
    
    // 模式标志
    bool m_debugMode;           // 调试模式
    bool m_editMode;            // 编辑模式
    bool m_forceRefresh;        // 强制刷新标志
    
    // 缩放参数
    qreal m_zoomFactor;         // 缩放因子
    qreal m_minZoom;            // 最小缩放
    qreal m_maxZoom;            // 最大缩放
    
    // 鼠标状态
    QPoint m_lastMousePos;      // 最后鼠标位置
    bool m_dragging;            // 是否在拖拽
    
    // 性能优化
    QTimer *m_refreshTimer;     // 刷新定时器
    bool m_needsRefresh;        // 需要刷新标志
    QRect m_dirtyRect;          // 脏矩形区域
};

#endif // LADDERVIEW_H
