﻿#include <QHeaderView>

#include "mytable.h"

MyTable::MyTable(QWidget *parent):
    QTableWidget(parent)
{
    this->rowPPage = 0;
}

void MyTable::SetRowPPage(int rowNum)
{
    this->rowPPage = rowNum;
}

void MyTable::resizeEvent(QResizeEvent *)
{
    int height = 0;
    int rowH = 0;

    if(this->rowPPage <= 0 || this->rowCount() <= 0)
    {
        return;
    }

    height = this->height() - this->horizontalHeader()->height();
    rowH = int(height / this->rowPPage);

    for(int i = 0; i < this->rowCount(); i++)
    {
        this->setRowHeight(i, rowH);
    }
}
