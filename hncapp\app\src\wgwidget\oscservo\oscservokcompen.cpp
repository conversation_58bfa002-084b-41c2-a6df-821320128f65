﻿/*!
* @file oscservokcompen.cpp
* @brief 前馈调试
* @note
*
* @version V1.00
* @date 2022/02/13
* <AUTHOR> Team
* @copyright 武汉华中数控股份有限公司软件开发部
*/
#include <qmath.h>
#include <algorithm>

#include "oscwave.h"
#include "osclist.h"
#include "hmioscservo.h"
#include "hncsmpl.h"
#include "hncsys.h"
#include "hmioscproc.h"
#include "hncaxis.h"
#include "hncaxisdef.h"
#include "hmiparaman.h"

#include "oscservokcompen.h"
#include "ui_oscservokcompen.h"

using namespace std;

OscServoKCompen::OscServoKCompen(QWidget *parent) :
	ContainerWidget(parent),
	ui(new Ui::OscServoKCompen)
{
	ui->setupUi(this);

	m_bSampleStart = false;
	m_pOscWaveSpeed = new OscWave(this, HmiOscServo::OSC_SERVO_KCOMPEN, "Speed");
	m_pOscWaveTrackErr = new OscWave(this, HmiOscServo::OSC_SERVO_KCOMPEN, "TrackErr");

	ui->verticalLayout_2->addWidget(m_pOscWaveSpeed);
	ui->verticalLayout_2->addWidget(m_pOscWaveTrackErr);
	ui->verticalLayout_2->setStretch(0, 1);
	ui->verticalLayout_2->setStretch(1, 1);
	ui->verticalLayout_2->setContentsMargins(0, 0, 0, 0);
	ui->verticalLayout_2->setSpacing(5);

	memset(m_cmdVel, 0, sizeof(fBit64) * OSC_MAX_DATA_NUM);
	memset(m_actVel, 0, sizeof(fBit64) * OSC_MAX_DATA_NUM);
	memset(m_trackErr, 0, sizeof(fBit64) * OSC_MAX_DATA_NUM);
	memset(m_lineNum, 0, sizeof(Bit32) * OSC_MAX_DATA_NUM);

	m_pOscWaveSpeed->CreateGroup(m_pOscWaveTrackErr->GetWavePlot());

	// 参数初始化
	this->firstFlag = false;
	// 信息区设置
	ui->speLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
	ui->speRateLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
	ui->trackErrMaxLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
	ui->trackErrMinLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);

	// 列表设置
	kcompenOscList = new OscList(this);
	kcompenOscList->installEventFilter(this);
	ui->gridLayout->addWidget(kcompenOscList);
	kcompenOscList->SetEditAgent(true);

	ui->leftBtn->setFocusPolicy(Qt::NoFocus);
	ui->rightBtn->setFocusPolicy(Qt::NoFocus);
}

OscServoKCompen::~OscServoKCompen()
{
	delete ui;
}

void OscServoKCompen::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
	if (messageid == MsgData::SETFOCUS)
	{
		bool ret = kcompenOscList->SetTableFocus();
		if (messageValue == "CLEARFOCUS" || ret == false)
		{
			kcompenOscList->ClearTableFocus();
		}
	}
	else if (messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
	{
		QStringList strList;
		strList.clear();
		if (messageValue == "INIT") // 初始化，清除上次在该界面记住的当前行
		{
			strList = GetParmList();
			kcompenOscList->RefresWidget(strList);
		}
		this->LoadInfo();
		kcompenOscList->LoadWidget();
		this->LoadAxisVal(0);
		this->SetColorStyle();
		this->OnBtFlagChange();
		if (HmiOscServo::GetConfChgFlag())
		{
			this->Reset();
			HmiOscServo::SetConfChgFlag(false);
		}
	}
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
	else if (messageid == MsgData::GENERAL)
	{
		if (messageValue == "MSG_OSCSERVOSTART")
		{
			m_bSampleStart = true;
			this->Reset(); // 开始采样时才清除上一次的图形
		}
		else if (messageValue == "MSG_OSCSERVOSAVE")
		{
			HmiOscServo::ParmSave();
			kcompenOscList->LoadWidget();
		}
		else if (messageValue == "MSG_OSCSERVOSTOP")
		{
			if (m_bSampleStart == true)
			{
				m_bSampleStart = false;
				this->LoadInfo();
			}
		}
		else if (messageValue == "OSCSERVOCOLOR")
		{
			this->SetColorStyle();
		}
	}
	else if (messageid == MsgData::REFRESH)
	{
		this->Refresh();

		Bit32 logicAxisNo = HmiOscServo::GetCurAxesNo();
		Bit32 devAxisType = HNC_ParamanGetAxisDevType(logicAxisNo);
        if (devAxisType == DEV_ETHERCAT_AXIS || devAxisType == DEV_NCUC_AXIS)
		{
            kcompenOscList->Refresh();
		}
	}
	else if (messageid == MsgData::KEYBOARD && messageValue == "TURNTABSLEFT")
	{
		this->on_leftBtn_clicked();
	}
	else if (messageid == MsgData::KEYBOARD && messageValue == "TURNTABSRIGHT")
	{
		this->on_rightBtn_clicked();
	}
}

void OscServoKCompen::LoadInfo()
{
	Bit32 max = 0, min = 0, tmp = 1;
	Bit32 chn = 0;
	Bit32 axis_no = 0;
	Bit32 dist = 0, pulse = 0;
	Bit32 ncu_cycle = 1000, moving_unit = 0;
	fBit64 ftmp = 0;
	Bit32 num = 0;
	Bit32 ch = ActiveChan();
	Spdfluc spdfluc;
	Bit32 *startP1 = NULL;
	Bit32 *startP2 = NULL;
	Bit32 type = 0;
	Bit32 offset = 0;
	Bit32 len = 0;
	fBit64 speVal = 0.000;
	fBit64 speRateVal = 0.000;
	fBit64 trackErrMaxVal = 0.000;
	fBit64 trackErrMinVal = 0.000;
	Bit32 logicAxisNo = HmiOscServo::GetCurAxesConf();
	memset(&spdfluc, 0, sizeof(spdfluc));

	if (oscproc_get_total() > 0)
	{
		HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &moving_unit);
		ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_CYCLE, &ncu_cycle);
		//        HNC_SystemGetValue(HNC_SYS_ACTIVE_CHAN, &ch);

		chn = 0;
		Bit32 client = HmiOscServo::oscservo_get_sampl_client();
		HNC_SamplGetConfig(client, chn, type, axis_no, offset, len);
		ParaGetIntVal(PARAMAN_FILE_AXIS, axis_no, PAR_AX_PM_MUNIT, &dist);
		ParaGetIntVal(PARAMAN_FILE_AXIS, axis_no, PAR_AX_PM_PULSE, &pulse);
		if (0 != moving_unit && 0 != dist)      // 除0保护
		{
			ftmp = (1000.0 * (fBit64)pulse) / (moving_unit * dist);
		}

		startP1 = oscproc_get_smpldata(chn);
		startP2 = oscproc_get_smpldata(chn + 1);
		if (startP1 == NULL || startP2 == NULL)
		{
			return;
		}
		tmp = smpl_calc_spd_fluc(startP1, startP2, oscproc_get_total() % SMPL_DATA_NUM, ftmp, spdfluc);

		if (tmp == -1)
		{
#ifdef _DEBUG
			MessageOut(TR("速度波动无法计算!请增大配置中的行程(或降低速度,或缩短采样周期)!"));
#endif
			return;
		}

		ftmp = fabs(smpl_calc_spd_coef(axis_no, 5)); // 速度系数(m/s)
		speVal = ftmp * spdfluc.fluc * 1000 * 60; // 毫米/分(速度波动)
		speRateVal = spdfluc.perct; // 速度波动百分比

		if (min < 0)
		{
			min = -min;
		}
		max = max>min ? max : min;

		HmiOscServo::OscservoReportRecord(speRateVal, 0);//传入百分比 1.26 27389
	}
	ui->speLabel->setText(QString::number(speVal, 'f', 3));
	ui->speRateLabel->setText(QString("%1%").arg(QString::number(speRateVal, 'f', 3)));



	if (logicAxisNo < 0 || logicAxisNo >= TOTAL_AXES_NUM)
	{
		return;
	}

	if (oscproc_get_total() > 0)
	{
		for (Bit32 i = 0; i < CHAN_AXES_NUM; ++i)
		{
			if (HmiOscServo::GetIndexAxesConf(i) > -1)
			{
				num++;
			}
		}
		if (num > MAX_REPORT_AXIS)
		{
			num = MAX_REPORT_AXIS;
		}

		HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &moving_unit);
		ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_CYCLE, &ncu_cycle);
		//        HNC_SystemGetValue(HNC_SYS_ACTIVE_CHAN, &ch);

		chn = 2;
		Bit32 client = HmiOscServo::oscservo_get_sampl_client();
		HmiOscServo::SmplCalcMaxmin(chn, &max, &min, 0, oscproc_get_total());
		HNC_SamplGetConfig(client, chn, type, axis_no, offset, len);
		trackErrMaxVal = fabs(smpl_calc_follow_err_coef(axis_no))*max * 1000.0; // um
		trackErrMinVal = fabs(smpl_calc_follow_err_coef(axis_no))*min * 1000.0; // um

		if (HNC_DoubleCompare(fabs(trackErrMaxVal), fabs(trackErrMinVal)) >= 0)
		{
			HmiOscServo::OscservoReportRecord(fabs(trackErrMaxVal / (HmiOscServo::s_Conf[ch].stPosConf[logicAxisNo].axis_f / 1000.0)), 0);
		}
		else
		{
			HmiOscServo::OscservoReportRecord(fabs(trackErrMinVal / (HmiOscServo::s_Conf[ch].stPosConf[logicAxisNo].axis_f / 1000.0)), 0);
		}
	}
	ui->trackErrMaxLabel->setText(QString::number(trackErrMaxVal, 'f', 3));
	ui->trackErrMinLabel->setText(QString::number(trackErrMinVal, 'f', 3));
}

void OscServoKCompen::Refresh()
{
	QVector<fBit64> x;
	QVector<fBit64> y0;
	QVector<fBit64> y1;
	QVector<fBit64> y2;
	QVector<fBit64> y3;

	Bit32 i = 0;
	Bit32 stPos = 0;
	Bit32 edPos = 0;
	Bit32 *ch0_addr = NULL;
	Bit32 *ch1_addr = NULL;
	Bit32 *ch2_addr = NULL;
	Bit32 *ch3_addr = NULL;

	Bit32 client = HmiOscServo::oscservo_get_sampl_client();
	Bit32 type = 0;
	Bit32 offset = 0;
	Bit32 len = 0;

	fBit64 cof = 1.0;

	if (oscproc_get_stat() != OSC_PROC_START)
	{
		this->lastEndPos = 0; // 停止后需要置零
		return;
	}

	x.clear();
	y0.clear();
	y1.clear();
	y2.clear();
	y3.clear();

	stPos = this->lastEndPos;
	edPos = oscproc_get_pos();
	this->lastEndPos = edPos;

	ch0_addr = oscproc_get_smpldata(0);//指令速度
	ch1_addr = oscproc_get_smpldata(1);//实际速度
	ch2_addr = oscproc_get_smpldata(2);//跟随误差
	ch3_addr = oscproc_get_smpldata(3);//行号

	if (NULL == ch0_addr || NULL == ch1_addr || NULL == ch2_addr)
	{
		return;
	}

	HNC_SamplGetConfig(client, 0, type, idenAxisNo, offset, len);
	cof = HmiOscServo::SmplCalcLoadCoef(2, client) / 1000;

	for (i = stPos + 1; i < edPos; ++i)
	{
		x.append(i * oscproc_get_smpl_period());

		y0.append(ch0_addr[i] * smpl_calc_spd_coef(idenAxisNo, 4)*60000.0);//指令速度：m/s转化为mm/min
		y1.append(ch1_addr[i] * smpl_calc_spd_coef(idenAxisNo, 5)*60000.0);//实际速度：m/s转化为mm/min
		y2.append(ch2_addr[i] * fabs(smpl_calc_follow_err_coef(idenAxisNo)*1000.0)); // 跟踪误差：mm转化为um
		y3.append(ch3_addr[i]);//行号

							   // 实时缓存采样数据
		if (i > 1 && i < OSC_MAX_DATA_NUM)
		{
			m_cmdVel[i - 1] = ch0_addr[i] * smpl_calc_spd_coef(idenAxisNo, 4);//指令速度：m/s
			m_actVel[i - 1] = ch1_addr[i] * smpl_calc_spd_coef(idenAxisNo, 5);//实际速度：m/s
			m_trackErr[i - 1] = ch2_addr[i] * fabs(smpl_calc_follow_err_coef(idenAxisNo)*1000.0); // 跟踪误差mm转化为um
			m_lineNum[i - 1] = ch3_addr[i];//行号
		}
	}

	m_pOscWaveSpeed->LineZeroAddPoint(x, y0);
	m_pOscWaveSpeed->LineOneAddPoint(x, y1);
	m_pOscWaveSpeed->WaveReplot();

	m_pOscWaveTrackErr->LineZeroAddPoint(x, y2);
	m_pOscWaveTrackErr->WaveReplot();
}

void OscServoKCompen::Reset()
{
	// 清空图形
	memset(m_cmdVel, 0, sizeof(fBit64) * OSC_MAX_DATA_NUM);
	memset(m_actVel, 0, sizeof(fBit64) * OSC_MAX_DATA_NUM);
	memset(m_trackErr, 0, sizeof(fBit64) * OSC_MAX_DATA_NUM);
	memset(m_lineNum, 0, sizeof(Bit32) * OSC_MAX_DATA_NUM);

	m_pOscWaveSpeed->ClearPoint();
	m_pOscWaveTrackErr->ClearPoint();
	this->lastEndPos = 0;
	this->idenAxisNo = 0;
	this->kcompenVal = 0.0;
	this->feedKCompenTime = 0.0;
	this->fastFeedKCompenTime = 0.0;
}

void OscServoKCompen::SetColorStyle()
{
	// 默认黑色风格
	QColor bk(0, 0, 0); // 背景
	QColor gd(0, 0, 0); // 网格
	QColor ft(0, 0, 0); // 字体颜色
	QColor c1(0, 0, 0); // 曲线1
	QColor c2(0, 0, 0); // 曲线2
	QColor c3(0, 0, 0); // 曲线3
	QColor c4(0, 0, 0); // 曲线4

	HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

	QPalette palette;
	palette.setColor(QPalette::Background, bk);
	ui->frame->setAutoFillBackground(true);
	ui->frame->setPalette(palette);

	m_pOscWaveSpeed->SetColor(bk, gd, ft, c1, c2, c3, c4);
	m_pOscWaveTrackErr->SetColor(bk, gd, ft, c1, c2, c3, c4);
}

bool OscServoKCompen::eventFilter(QObject *target, QEvent *event)
{
	if (event->type() == QEvent::Paint && !firstFlag) // Paint事件在ReSize事件之后响应，用于图片第一次重绘
	{
		ui->leftBtn->setIconSize(ui->leftBtn->size());
		ui->rightBtn->setIconSize(ui->rightBtn->size());
		//ui->leftBtn->setIcon(PixMapToSize(ui->leftBtn->size(), "../pic/left-2.png"));
		//ui->rightBtn->setIcon(PixMapToSize(ui->rightBtn->size(), "../pic/right-1.png"));

		this->firstFlag = true;
		this->OnBtFlagChange(); // 解决初次进入界面时，redraw消息在paint事件前响应，导致界面刷新错误
	}
	return QObject::eventFilter(target, event);
}
void OscServoKCompen::OnBtFlagChange()
{
	HmiOscServo::OscservoSamplReset();
	HmiOscServo::OscservoInit();
	Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();
	if (curAxesIndex <= 0)
	{
		HmiOscServo::SetCurAxesIndex(0);
		ui->leftBtn->setProperty("valid", false);
		ui->rightBtn->setProperty("valid", true);
		ui->leftBtn->style()->polish(ui->leftBtn);
		ui->rightBtn->style()->polish(ui->rightBtn);
		return;
	}
	if (HmiOscServo::GetIndexAxesConf(curAxesIndex + 1) < 0)
	{
		ui->leftBtn->setProperty("valid", true);
		ui->rightBtn->setProperty("valid", false);
		ui->leftBtn->style()->polish(ui->leftBtn);
		ui->rightBtn->style()->polish(ui->rightBtn);
		return;
	}
	ui->leftBtn->setProperty("valid", true);
	ui->rightBtn->setProperty("valid", true);
	ui->leftBtn->style()->polish(ui->leftBtn);
	ui->rightBtn->style()->polish(ui->rightBtn);
}
void OscServoKCompen::on_leftBtn_clicked()
{
	Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();

	if (oscproc_get_stat() == OSC_PROC_START)
	{
		MessageOut(TR("采样中禁止轴切换!"));
		return;
	}

	if (curAxesIndex <= 0)
	{
		HmiOscServo::SetCurAxesIndex(0);
	}
	else
	{
		curAxesIndex--;
		HmiOscServo::SetCurAxesIndex(curAxesIndex);
	}
	this->LoadAxisVal(1);
	this->OnBtFlagChange();
	oscproc_smpldata_reset();
	//HmiOscServo::OscservoLoadGcode(1); // 重新生成并加载G代码
	this->Reset(); // 清除图形
	ResetInfo();        // 清除调机报表的显示数据
}

void OscServoKCompen::on_rightBtn_clicked()
{
	Bit32 curAxesIndex = HmiOscServo::GetCurAxesIndex();

	if (oscproc_get_stat() == OSC_PROC_START)
	{
		MessageOut(TR("采样中禁止轴切换!"));
		return;
	}

	if (curAxesIndex >= (TOTAL_AXES_PER_CHN - 1))
	{
		curAxesIndex = TOTAL_AXES_PER_CHN - 1;
		HmiOscServo::SetCurAxesIndex(curAxesIndex);
	}
	else if (HmiOscServo::GetIndexAxesConf(curAxesIndex + 1) < 0)
	{
		return;
	}
	else
	{
		curAxesIndex++;
		HmiOscServo::SetCurAxesIndex(curAxesIndex);
	}
	this->LoadAxisVal(1);
	this->OnBtFlagChange();
	oscproc_smpldata_reset();
	//HmiOscServo::OscservoLoadGcode(1); // 重新生成并加载G代码
	this->Reset(); // 清除图形
	ResetInfo();        // 清除调机报表的显示数据
}
void OscServoKCompen::ResetInfo()
{
	fBit64 speVal = 0.000;
	fBit64 speRateVal = 100.000;
	fBit64 trackErrMaxVal = 0.000;
	fBit64 trackErrMinVal = 0.000;

	ui->speLabel->setText(QString::number(speVal, 'f', 3));
	ui->speRateLabel->setText(QString("%1%").arg(QString::number(speRateVal, 'f', 3)));
	ui->trackErrMaxLabel->setText(QString::number(trackErrMaxVal, 'f', 3));
	ui->trackErrMinLabel->setText(QString::number(trackErrMinVal, 'f', 3));
}
void OscServoKCompen::LoadAxisVal(Bit32 type)
{
	QString axisName = "";
	QString unit1 = "";
	QString unit2 = "";
	QStringList strList;
	Bit32 axisType = 0;

	HNC_AxisGetValue(HNC_AXIS_TYPE, HmiOscServo::GetCurAxesConf(), &axisType);
	ui->labelImg->setText(TR("Alt+←/→切换轴"));
	strList.clear();

	axisName = HmiOscServo::OscAxisToName(HmiOscServo::GetCurAxesConf());
	ui->labelAxisName->setText(TR("%1轴").arg(axisName));

	if (axisType == 1 || axisType == 7) // 7(主轴做进给轴使用)和1一样按直线轴处理
	{
		unit1 = TR("um");
		unit2 = TR("mm");
	}
	else
	{
		unit1 = TR(" *10^-3deg");
		unit2 = TR("deg");
	}

	ui->label_14->setText(unit1);
	ui->label_16->setText(unit1);
	ui->label->setText(TR("%1/min").arg(unit2));

	m_pOscWaveSpeed->SetAxisName(QStringList() << axisName << unit2);
	m_pOscWaveTrackErr->SetAxisName(QStringList() << axisName << unit1);

	if (type == 1)//type：0表示为初始化。1表示已经初始化
	{
		strList = GetParmList();
		kcompenOscList->RefresWidget(strList);
		kcompenOscList->LoadWidget();
	}
}
void OscServoKCompen::resizeEvent(QResizeEvent *)
{
	this->firstFlag = false;
}

QStringList OscServoKCompen::GetParmList()
{
	Bit32 totalNo = 0;
	QStringList list;
	list.clear();

	totalNo = HmiOscServo::ServoParmGetCount(HmiOscServo::OSC_SERVO_KCOMPEN);

	for (Bit32 i = 0; i < totalNo; i++)
	{
		list.append(QString::number(HmiOscServo::ServoParRow2Id(i)));
	}
	return list;
}
