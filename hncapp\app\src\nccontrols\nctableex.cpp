﻿/*!
 * @file nctableex
 * @brief 自定义列表控件，集成文本、图标、下拉框、选择框、合并框功能
 * @note 说明，暂时还未找到解决第三方控件（如QCheckBox，QComboBox，QPushButton）使用委托时单击响应的方法，
 * 因此使用如上所列的控件时，直接使用QTableView的setIndexWidget函数添加。
 *
 * @version V1.00
 * @date 2018/5/4
 * <AUTHOR> HMI Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */


#include <QCheckBox>
#include <QPushButton>
#include <QMouseEvent>
#include <QLineEdit>
#include <QHeaderView>
#include <QCoreApplication>
#include <QStandardItemModel>

#include "passwd.h"

#include "nctableex.h"
#include "hmipaintercolor.h"
#include "hmicommon.h"

NcTableEx::NcTableEx(QWidget *parent, QFont font) :
    QTableView(parent)
{
    m_nTotalDataCount = -1;
    m_nRowCount = -1;
    m_nColumnCount = -1;
    m_font = font;
    m_nStartColumn = 0;
    m_nVerHeaderWidth = 0;
    m_nStartIdx = 0;
    m_nOldStartIdx = 0;
    m_nSelectIdx = 0;
    m_nOldSelectIdx = 0;
    m_enDataType = ENInvalid;
    m_nRowSpan = 1;
    m_nColumnSpan = 1;
    m_nDefaultRow = -1;
    m_nDefaultColumn = -1;
    m_nCurrentColumn = 0;
    m_bSetFocusFlag = false;
    m_enDefaultMoveDirection = NOTMOVE;
    m_nDisplayRowCount = 1;
    m_bChangeColor = false;
    m_bEditAgent = false;
    m_bEditActive = false;
    m_bFillAllFlag = false;
    m_bIsConnected = false;
    m_bDefaultSel = false;
    m_bIsDefalutContentChangedVaild = false;
    m_sOldPicPathList.clear();
    m_pTableMode = new NcTableModelEx(this);
    this->setModel(m_pTableMode);
    m_pItemDelegate = new NcItemDelegate(this);
    this->setItemDelegate(m_pItemDelegate);
    this->verticalHeader()->setVisible(false);                      // 隐藏竖直header
    this->setSelectionMode(QAbstractItemView::SingleSelection);
    this->setFocusPolicy(Qt::StrongFocus);
    // this->horizontalHeader()->setStretchLastSection(true);          // 设置充满表宽度(保留)
    this->horizontalHeader()->setHighlightSections(false);          // 点击表时不对表头行光亮（获取焦点）
    this->horizontalHeader()->setResizeMode(QHeaderView::Fixed);    // 设置列宽为固定列宽不可调整
    this->setAlternatingRowColors(true);                            // 设置交替行颜色
    this->setFont(m_font);                                          // 设置表格单元的字体大小
    this->horizontalHeader()->setFont(m_font);

    m_vecValue.clear();

    this->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);       //不使用其竖直滚动条
    this->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    m_pVerticalScrollBar = new QScrollBar(Qt::Vertical, this);
    m_pVerticalScrollBar->setVisible(false);

    m_pHorizontalScrollBar = new QScrollBar(Qt::Horizontal, this);
    m_pHorizontalScrollBar->setHidden(true);

    m_nTotalDataCount = TotalDataCount();
    m_saveIndex = currentIndex();

    this->installEventFilter(this);
    this->horizontalHeader()->installEventFilter(this);
    m_pHorizontalScrollBar->installEventFilter(this);
    m_pVerticalScrollBar->installEventFilter(this);

    connect(this, SIGNAL(doubleClicked(QModelIndex)), this, SLOT(SlotDoubleClicked(QModelIndex)));
    connect(m_pVerticalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollBarValueChanged(int)));
    connect(m_pHorizontalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollHorizontalBarValueChanged(int)));
}

void NcTableEx::resizeEvent(QResizeEvent *event)
{
    ResizeTable();
	
	if (event == NULL) // 避免编译警告
	{
		return;
	}
}

bool NcTableEx::eventFilter(QObject *target, QEvent *event)
{
    bool ret = false;

    if ((target == m_pHorizontalScrollBar || target == m_pVerticalScrollBar) &&
            (event->type() == QEvent::Resize || event->type() == QEvent::Show || event->type() == QEvent::Hide))
    {
        // 列表resize后，滚动条还要resize，在滚动条resize时，重新设置列表宽度，避免行高设置无效		
		QKeyEvent keyEvent(QEvent::Resize, Qt::Key_unknown, Qt::NoModifier);
		QCoreApplication::sendEvent(this, &keyEvent);
    }
    else if (event->type() == QEvent::MouseButtonDblClick)
    {
        QModelIndex index = currentIndex();
        int curRow = index.row() + GetStartIndex();
        int curCol = index.column() + m_nStartColumn;
        if (RoleStyle(curRow, GetDefCol(curCol), NcTableEx::DialogRole) == true || IsPicRole(index))
        {
            return EditItem(index, "");
        }
    }
    else if (target == this)
    {
        if (event->type() == QEvent::Resize)
        {
            QResizeEvent *ev = NULL;
            this->resizeEvent(ev);
        }
        else if (event->type() == QEvent::KeyPress)
        {
            // 增加enter/return按键触发设置
            QKeyEvent *keyEv = static_cast<QKeyEvent*>(event);

            switch (keyEv->key())
            {
            case Qt::Key_Enter:
            case Qt::Key_Return:
                return KeyEnter(event);
            case Qt::Key_Up:
                ret = KeyUp(event);
                emit SignalsDirKey(event);
                return ret;
            case Qt::Key_Down:
                ret = KeyDown(event);
                emit SignalsDirKey(event);
                return ret;
            case Qt::Key_Left:
                ret = KeyLeft(event);
                emit SignalsDirKey(event);
                return ret;
            case Qt::Key_Right:
                ret = KeyRight(event);
                emit SignalsDirKey(event);
                return ret;
            case Qt::Key_PageUp:
                ret = KeyPageUp(event);
                emit SignalsDirKey(event);
                return ret;
            case Qt::Key_PageDown:
                ret = KeyPageDown(event);
                emit SignalsDirKey(event);
                return ret;
            default:
#ifndef _NCUPPER_
                if (IsEditAgent() == false || (keyEv->modifiers() != Qt::NoModifier && keyEv->modifiers() != Qt::KeypadModifier))
                {
                    break;
                }
                QModelIndex index = currentIndex();
                if (IsComboBoxRole(index) == true || IsCheckBoxRole(index) == true || IsPicRole(index) == true
                || this->RoleStyle(index, NcTableEx::DialogRole) == true)
                {
                    break;
                }
                ret = VerifyKey(keyEv->text());
                if (ret)
                {
                    this->StartEdit(keyEv->text(), false);
                }
                return ret;
#else
                break;
#endif
            }
        }
        else if (event->type() == QEvent::Wheel)
        {
            QWheelEvent *wheelEvt = static_cast<QWheelEvent *>(event);
            int numDegrees = wheelEvt->delta() / 8;     // 滚动的角度，*8就是鼠标滚动的距离
            int numSteps = numDegrees / 15;             // 滚动的步数，*15就是鼠标滚动的角度

            if (wheelEvt->orientation() == Qt::Vertical) //
            {
                int currentValue = m_pVerticalScrollBar->value() - numSteps;
                if (currentValue < 0)
                {
                    currentValue = 0;
                }
                if (currentValue >= m_pVerticalScrollBar->maximum())
                {
                    currentValue = m_pVerticalScrollBar->maximum();
                }

                m_pVerticalScrollBar->setValue(currentValue);

                return true;      //接收该事件
            }
        }
        else if (event->type() == QEvent::FocusIn)
        {
            SetTableFocus();
        }
    }

    return QObject::eventFilter(target, event);
}

bool NcTableEx::GetIsDefContentChangedVaild()
{
    return m_bIsDefalutContentChangedVaild;
}

void NcTableEx::SetIsDefContentChangedVaild()
{
    m_bIsDefalutContentChangedVaild = true;
}

void NcTableEx::setStartIdx(int startIdx)
{
    Bit32 preStartIdx = m_nStartIdx;
    Bit32 curStartIdx = startIdx;

    if (m_nSelectIdx >= curStartIdx + m_nRowCount && m_nRowCount != 0)
    {
        curStartIdx = m_nSelectIdx - m_nRowCount + 1;
    }
    else if (m_nSelectIdx < curStartIdx)
    {
        curStartIdx = m_nSelectIdx;
    }

    if (curStartIdx > m_nTotalDataCount - m_nRowCount)
    {
        curStartIdx = m_nTotalDataCount - m_nRowCount;
    }
    if (curStartIdx < 0)
    {
        curStartIdx = 0;
    }

    if (m_nColumnSpan > 1 && preStartIdx != curStartIdx)
    {
        this->clearSpans();
    }
    if (m_nStartIdx != curStartIdx)
    {
        m_nStartIdx = curStartIdx;
        emit selectStartChanged(m_nStartIdx);
    }
}

Bit32 NcTableEx::IsSpanRowItem(int row, int col)
{
    if (m_nRowSpan <= 1)
    {
        return 0;
    }

    for (int i = 0; i <= row; i++)
    {
        QStringList spanList = this->RoleStyle(row - i, GetDefCol(col), SpanRole).toStringList();
        if (spanList.isEmpty() == false)
        {
            Bit32 spanRow = spanList.at(0).toInt();
            if (i == 0)
            {
                return 1;
            }
            else if(i < spanRow)
            {
                return 2;
            }
            else
            {
                return 0;
            }
        }
    }
    return 0;
}

bool NcTableEx::IsIndexSelectEnable(int row, int col)
{
    int flag = this->RoleStyle(row, col, NcTableEx::FlagRole).toInt();
    if ((flag & Qt::ItemIsEnabled) != Qt::ItemIsEnabled)
    {
        return false;
    }
    if ((flag & Qt::ItemIsSelectable) != Qt::ItemIsSelectable)
    {
        return false;
    }

    return true;
}

int NcTableEx::GetDefCol(int col)
{
    return col;
}

Bit32 NcTableEx::IsSpanColItem(int row, int col)
{
    if (m_nColumnSpan <= 1)
    {
        return 0;
    }

    for (int i = 0; i < col; i++)
    {
        QStringList spanList = this->RoleStyle(row, GetDefCol(col - i), SpanRole).toStringList();
        if (spanList.count() >= 2)
        {
            Bit32 spanCol = spanList.at(1).toInt();
            if (i == 0)
            {
                return 1;
            }
            else if(i < spanCol)
            {
                return 2;
            }
            else
            {
                return 0;
            }
        }
    }
    return 0;
}

bool NcTableEx::SelectIdxEnable(int row, int col)
{
    int flag = this->RoleStyle(row, GetDefCol(col), NcTableEx::FlagRole).toInt();
    if(Qt::NoItemFlags != flag)
    {
        return true;
    }

    return false;
}

Bit32 NcTableEx::ValidSelectIdxCheck(int row)
{
    Bit32 validRow = row;

    if (validRow >= m_nTotalDataCount)
    {
        validRow = m_nTotalDataCount - 1;
    }
    if (validRow < 0)
    {
        validRow = 0;
    }

    return validRow;
}

Bit32 NcTableEx::SelectIdxUpMove(int row, int col)
{
    Bit32 curRow = row;

    for(Bit32 ii = curRow - 1; ii > 0; ii--)
    {
        if (SelectIdxEnable(ii, col) == true)
        {
            curRow = ii;
            break;
        }
    }

    return curRow;
}

Bit32 NcTableEx::SelectIdxDownMove(int row, int col)
{
    Bit32 curRow = row;
    for(Bit32 ii = curRow + 1; ii < m_nTotalDataCount; ii++)
    {
        if (SelectIdxEnable(ii, col) == true)
        {
            curRow = ii;
            break;
        }
    }

    return curRow;
}

void NcTableEx::setSelectIdx(int nSelectIdx, Bit32 dir, int col)
{
    Bit32 preRow = m_nSelectIdx;
    Bit32 curIdx = ValidSelectIdxCheck(nSelectIdx);

    QModelIndex index = currentIndex();
    if (col == -1)
    {
        col = index.column() + m_nStartColumn;
    }
    if (SelectIdxEnable(curIdx, col) == true)
    {
        if (preRow == curIdx)
        {
            return;
        }
        m_nSelectIdx = curIdx;
        emit selectRowChanged(m_nSelectIdx, preRow);
        return;
    }

    if (dir == 0)
    {
        if (preRow > nSelectIdx)
        {
            dir = -1;
        }
        else
        {
            dir = 1;
        }
    }
    Bit32 curRow = curIdx;
    if (dir == -1)
    {
        curRow = SelectIdxUpMove(curRow, col);
        if (curRow == curIdx)
        {
            curRow = SelectIdxDownMove(curRow, col);
        }
    }
    else
    {
        curRow = SelectIdxDownMove(curRow, col);
        if (curRow == curIdx)
        {
            curRow = SelectIdxUpMove(curRow, col);
        }
    }

    curRow = ValidSelectIdxCheck(curRow);
    if (preRow == curRow)
    {
        return;
    }

    m_nSelectIdx = curRow;
    emit selectRowChanged(m_nSelectIdx, preRow);
}

ENMoveDirection NcTableEx::ContentSet(int row, int col, const QString &str)
{
	row = row;
	col = col;

	if (str.isNull())
	{
		return NOTMOVE;
	}

    return NOTMOVE;
}

void NcTableEx::ContentGet(int row, int col, QString &str) const
{
	row = row;
	col = col;
	str = str;
}

QVariant NcTableEx::RoleStyle(int row, int col, int role)
{
	row = row;
	col = col;
	role = role;

    return QVariant();
}

QVariant NcTableEx::RoleStyle(QModelIndex index, int role)
{
    int curRow = index.row() + m_nStartIdx;
    int curCol = index.column() + m_nStartColumn;
    return RoleStyle(curRow, GetDefCol(curCol), role);
}

void NcTableEx::SetHorizontalScrollBarValue()
{
    m_pHorizontalScrollBar->setValue(m_nStartColumn);
}

void NcTableEx::UpdataHorizontalScrollBarHidden()
{
    Bit32 maxVal = ColunmCount() - m_nColumnCount;
    if (maxVal > 0)
    {
        m_pHorizontalScrollBar->setHidden(false);
    }
    else
    {
        m_pHorizontalScrollBar->setHidden(true);
    }
    m_pHorizontalScrollBar->setRange(0, maxVal);
}

bool NcTableEx::IsCheckBoxRole(QModelIndex index)
{
    if (index.isValid() == false)
    {
        return false;
    }
    return (!RoleStyle(index, NcTableEx::CheckBoxRole).toStringList().isEmpty());
}

bool NcTableEx::IsComboBoxRole(QModelIndex index)
{
    if (index.isValid() == false)
    {
        return false;
    }
    return (!this->RoleStyle(index, NcTableEx::ComboBoxRole).toStringList().isEmpty());
}

bool NcTableEx::IsPicRole(QModelIndex index)
{
    if (index.isValid() == false)
    {
        return false;
    }
    return (this->RoleStyle(index, NcTableEx::PicRole).toBool());
}

NcCheckBox* NcTableEx::GetIndexCheckBox(QModelIndex index)
{
    NcCheckBox* checkBox = NULL;
    if (index.isValid() == false)
    {
        return NULL;
    }
    if (this->indexWidget(index) != NULL)
    {
        QWidget *w = this->indexWidget(index);
        QObject *o = w->findChild<NcCheckBox *>("NcTableCheckBox");
        checkBox = dynamic_cast<NcCheckBox *>(o);
    }

    return checkBox;
}

NcComboBox* NcTableEx::GetIndexComboBox(QModelIndex index)
{
    NcComboBox* comboBox = NULL;
    if (index.isValid() == false)
    {
        return NULL;
    }
    if (this->indexWidget(index) != NULL)
    {
        comboBox = dynamic_cast<NcComboBox *>(this->indexWidget(index));
    }

    return comboBox;
}

QLabel* NcTableEx::GetIndexPic(QModelIndex index)
{
    QLabel* l = NULL;
    if (index.isValid() == false)
    {
        return NULL;
    }
    if (this->indexWidget(index) != NULL)
    {
        l = dynamic_cast<QLabel *>(this->indexWidget(index));
    }

    return l;
}

void NcTableEx::CreatePicInTable(QModelIndex index)
{
    if (index.isValid() == false || this->indexWidget(index) != NULL)
    {
        return;
    }

    QLabel* l = new QLabel();
    l->setFocusPolicy(Qt::NoFocus);
    l->setObjectName("NcTableLabel");
    l->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    l->setAlignment(Qt::AlignCenter);
    l->installEventFilter((QObject*)(this->m_pTableMode));

    if (indexWidget(index) == NULL)
    {
        this->setIndexWidget(index, l);
    }

    if (this->currentIndex() == index && this->hasFocus())
    {
        this->SetFocus();
    }
    RefreshPic(index, true);
}

void NcTableEx::CreateCheckBoxInTable(QModelIndex index)
{
    if (index.isValid() == false || this->indexWidget(index) != NULL)
    {
        return;
    }

    NcCheckBox* checkBox = new NcCheckBox();
    checkBox->setFocusPolicy(Qt::NoFocus);
    checkBox->setObjectName("NcTableCheckBox");
    checkBox->setFont(this->font());
    checkBox->installEventFilter((QObject*)(this->m_pTableMode));

    if (indexWidget(index) == NULL)
    {
        this->setIndexWidget(index, new QWidget());
    }

    // 在table中对插入的object进行布局
    int align = RoleStyle(index, Qt::TextAlignmentRole).toInt();
    QLayout *pLayout = indexWidget(index)->layout();
    if (pLayout == NULL)
    {
        pLayout = new QHBoxLayout();
    }
    pLayout->setAlignment((Qt::Alignment)(align));
    pLayout->setMargin(0);
    pLayout->addWidget(checkBox);
    indexWidget(index)->setLayout(pLayout);

    if (this->currentIndex() == index && this->hasFocus())
    {
        this->SetFocus();
    }
    RefreshCheckBoxText(index);
}

void NcTableEx::RemoveItemFromTable(QModelIndex index)
{
    if (index.isValid() == false)
    {
        return;
    }

    NcCheckBox* checkBox = GetIndexCheckBox(index);
    NcComboBox* comboBox = GetIndexComboBox(index);
    QLabel* label = GetIndexPic(index);
    bool hasFocus = false;
    bool isRemoveNeeded = false;

    if (IsCheckBoxRole(index) == false && checkBox != NULL && checkBox->objectName() == QString("NcTableCheckBox"))
    {
        isRemoveNeeded = true;
        hasFocus = checkBox->hasFocus();
    }
    if (IsComboBoxRole(index) == false && comboBox != NULL && comboBox->objectName() == QString("NcTableComboBox"))
    {
        isRemoveNeeded = true;
        hasFocus = comboBox->hasFocus();
    }
    if (IsPicRole(index) == false && label != NULL && label->objectName() == QString("NcTableLabel"))
    {
        isRemoveNeeded = true;
        hasFocus = label->hasFocus();
    }


    if (isRemoveNeeded == true)
    {
        this->setIndexWidget(index, NULL);
        if (this->currentIndex() == index && hasFocus)
        {
            this->SetFocus();
        }
    }
}
void NcTableEx::InputType(int row, int col, long &ebxType, QString &info, long &len, long &prec, long &exitType)
{
	row = row;
	col = col;
	ebxType = ebxType;
	info = info;
	len = len;
	prec = prec;
	exitType = exitType;
}

void NcTableEx::InitTab(void) // 初始化标题栏(重载用)
{
	m_pTableMode->ResetModel();
	QKeyEvent keyEvent(QEvent::Resize, Qt::Key_unknown, Qt::NoModifier);
	QCoreApplication::sendEvent(this, &keyEvent);
}

int NcTableEx::TotalDataCount(void)
{
    return 0;
}


void NcTableEx::ResetIndex()
{
    m_nStartColumn = 0;
    setSelectIdx(0, 1);
    setStartIdx(0);
    m_nOldStartIdx = 0;
    m_nOldSelectIdx = 0;
    m_saveIndex = this->model()->index(-1, -1);
}

/**
 * @brief NcTableEx::SetTableFocus 设置焦点
 */
void NcTableEx::SetTableFocus()
{
    QModelIndex index = currentIndex();
    if (index.isValid())
    {
        if (IsComboBoxRole(index) == true)
        {
            NcComboBox* comboBox = GetIndexComboBox(index);
            if (comboBox != NULL && comboBox->isEnabled())
            {
                this->setCurrentIndex(index);
                comboBox->setFocus();
            }
            else // 此处设置焦点时列表显示还未重绘,当前的index.row()的下拉框可能还是disable状态
            {
                this->SetFocus();
            }
        }
        else
        {
            this->setFocus();
            this->setCurrentIndex(index);
        }
    }
    else if (m_saveIndex.isValid()
             && m_saveIndex.column() < m_nColumnCount
             && m_saveIndex.row() < m_nRowCount)
    {
        index = m_saveIndex;
        this->setFocus();
        this->setCurrentIndex(index);
    }
    if(IsCheckBoxRole(index) == true)
    {
        NcCheckBox* checkBox = GetIndexCheckBox(index);
        if(checkBox != NULL)
        {
            checkBox->setProperty("selected", true);
            checkBox->style()->polish(checkBox);
        }
    }
    setSelectIdx(index.row() + m_nStartIdx, index.column());
}

bool NcTableEx::IsEditAgent()
{
    return this->m_bEditAgent;
}

void NcTableEx::SetEditAgent(bool mode)
{
    this->m_bEditAgent = mode;
}

/**
 * @brief 获取编辑框内容验证器
 * @param row：表格行
 * @param col：表格列
 * @return
 */
QValidator* NcTableEx::GetValidator(int row, int col)
{
    UNREFERENCED_PARAM(row);
    UNREFERENCED_PARAM(col);

    return NULL;
}

/**
 * @brief 获取表格项内容信息
 * @param row：表格行
 * @param col：表格列
 * @return
 */
QString NcTableEx::GetDataInfo(int row, int col)
{
    UNREFERENCED_PARAM(row);
    UNREFERENCED_PARAM(col);

    return TR("请输入参数值:");
}

/**
 * @brief 处理表格项编辑修改
 * @param row：表格行
 * @param col：表格列
 * @param value：修改后的值
 * @return：焦点移动方向
 */
ENMoveDirection NcTableEx::ContentChanged(int row, int col, QString value)
{
    UNREFERENCED_PARAM(row);
    UNREFERENCED_PARAM(col);
    UNREFERENCED_PARAM(value);

    m_bIsDefalutContentChangedVaild = true;
    return NOTMOVE;
}

/**
 * @brief 对输入字符进行校验
 * @param row：表格行
 * @param col：表格列
 * @param key：输入字符
 * @return true: 有效 false: 无效
 */
bool NcTableEx::VerifyKey(QString key)
{
    QModelIndex index = currentIndex();
    int row = index.row() + GetStartIndex();
    int col = index.column() + m_nStartColumn;
    if (key.isNull() || key.length() > 1)
    {
        return false;
    }

    if (this->RoleStyle(row, GetDefCol(col), NcTableEx::EditDisableRole).toBool())
    {
        return false;
    }

    QValidator *pValidator = GetInputValidator(row, col);
    if (pValidator == NULL)
    {
        return false;
    }

    QIntValidator* tmpIntValid = dynamic_cast<QIntValidator*>(pValidator);
    QDoubleValidator* tmpDoubleValid = dynamic_cast<QDoubleValidator*>(pValidator);
    QRegExpValidator * tmpRegExpValid = dynamic_cast<QRegExpValidator*>(pValidator);
    if(tmpIntValid != NULL || tmpDoubleValid != NULL || tmpRegExpValid != NULL)   // 为数字类型时，"="可以激活输入框
    {
        bool IsNumType = true;
        if(tmpRegExpValid != NULL)
        {
            QString pattern = tmpRegExpValid->regExp().pattern();   // 正则表达式内容

            if(pattern.indexOf("[") > 0
                    && pattern.indexOf("]") > 0
                    && pattern.indexOf("]") > pattern.indexOf("["))    // 判断正则表达式是否有[]限制字符集合
            {
                for(int i = 0;i < pattern.length(); i++)
                {
                    if(pattern.at(i) == '[')
                    {
                        IsNumType = false;
                        continue;
                    }
                    else if(pattern.at(i) == ']')
                    {
                        IsNumType = true;
                        continue;
                    }

                    if(IsNumType == false)  // 判断正则表达式内容是否为数字类型
                    {
                        if(pattern.at(i) != '-' && pattern.at(i) != '.' && pattern.at(i).isNumber() == false)
                        {
                            break;
                        }
                    }
                }
            }
            else
            {
                IsNumType = false;
            }
        }
        if((IsNumType = true && (key == "=")))
        {
            return true;
        }
    }

    int pos = 0;
    if (pValidator->validate(key, pos) != QValidator::Invalid)
    {
        return true;
    }
    return false;
}

void NcTableEx::StartEdit(QString content, bool selStr)
{
    QModelIndex index = currentIndex();
    int currentRow = index.row() + GetStartIndex();
    int currentColumn = index.column() + m_nStartColumn;

    if (this->RoleStyle(currentRow, GetDefCol(currentColumn), NcTableEx::DialogRole) == true || IsPicRole(index))
    {
        return;
    }

    if (CommonGetInputDlg()->GetInputManageStatus())
    {
        return;
    }

    if ((RoleStyle(currentRow, GetDefCol(currentColumn), NcTableEx::FlagRole).toUInt() & Qt::ItemIsEditable) == 0)
    {
        MessageOut(QObject::TR("该参数值已固化，不可修改"));
        return;
    }

    if (RoleStyle(currentRow, GetDefCol(currentColumn), NcTableEx::EditRightRole).toInt() > passwd_get_rights())
    {
        MessageOut(QObject::TR("修改权限不足"));
        return;
    }

    if (RoleStyle(currentRow, GetDefCol(currentColumn), NcTableEx::EditDisableRole).toBool())
    {
        return;
    }

    bool isPassword = false;
    if (true == RoleStyle(currentRow, GetDefCol(currentColumn), NcTableEx::PasswordRole).toBool())
    {
        isPassword = true;
        if (selStr == true)
        {
            content = "";
        }
    }
    QString info = GetDataInfo(currentRow, GetDefCol(currentColumn));
    NcDataLimit limit = GetInputDataLimit(currentRow, currentColumn);
    QValidator *pValidator = GetInputValidator(currentRow, currentColumn);
    m_bEditActive = true;
    CommonGetInputDlg()->Show(content, limit, info, 1, pValidator, isPassword, selStr);
}

void NcTableEx::EditAccept()
{
    if (m_bEditActive == false)
    {
        return;
    }
    m_bEditActive = false;
    QModelIndex index = currentIndex();
    QString content = CommonGetInputDlg()->GetInputText();
    m_pTableMode->setData(index, content);
}

void NcTableEx::EditReject()
{
    m_bEditActive = false;
}

bool NcTableEx::KeyEnter(QEvent* event)
{
    QModelIndex index = currentIndex();

    if (index.isValid() == false)
    {
        return false;
    }

    if ((this->RoleStyle(index, NcTableEx::FlagRole).toUInt() & Qt::ItemIsEditable) == 0)
    {
        return false;
    }
    if (this->RoleStyle(index, NcTableEx::EditDisableRole).toBool())
    {
        return false;
    }

    if (state() != EditingState || hasFocus())
    {
        if (IsComboBoxRole(index) == true)
        {
            NcComboBox* comboBox = GetIndexComboBox(index);
            if (comboBox != NULL)
            {
                comboBox->showPopup();
            }
        }
        else if (IsCheckBoxRole(index) == true)
        {
            NcCheckBox* checkBox = GetIndexCheckBox(index);
            if (checkBox != NULL)
            {
                bool state = checkBox->isChecked();
                QString str = QString::number((Bit32)state);
                ENMoveDirection direction = this->ContentChanged(index.row() + m_nStartIdx, GetDefCol(index.column() + m_nStartColumn), str);
                ChangeFocus(direction);
                update(index);
            }
        }
        else if (this->RoleStyle(index, NcTableEx::DialogRole) == true
                 || IsPicRole(index))
        {
            ENMoveDirection direction = this->ContentChanged(index.row() + m_nStartIdx, GetDefCol(index.column() + m_nStartColumn), "");
            if (m_bIsDefalutContentChangedVaild == true)
            {
                direction = this->ContentSet(index.row() + m_nStartIdx, GetDefCol(index.column() + m_nStartColumn), "");
            }
            ChangeFocus(direction);
        }
        else   // 当前单元格可以编辑
        {
            if (m_bIsDefalutContentChangedVaild == true)
            {
                ENMoveDirection direction = this->ContentSet(index.row() + m_nStartIdx, GetDefCol(index.column() + m_nStartColumn), "");
                ChangeFocus(direction);
            }
            else
            {
                QString content("");
                QStringList list = RoleStyle(index, NcTableEx::InputDefValRole).toStringList();
                if (list.count() > 0)
                {
                    content = list.at(0);
                }
                else
                {
                    ContentGet(index.row() + m_nStartIdx, GetDefCol(index.column() + m_nStartColumn), content);
                }
                this->StartEdit(content, true);
            }
        }
        event->accept();

        return true;
    }

    return false;
}

bool NcTableEx::KeyUp(QEvent* event)
{
    int moveLen = -1;

    // 合并框处理（预留）
    //...
    QModelIndex index = this->currentIndex();

    if(!index.isValid())
    {
        return true;
    }

    int curCol = index.column();
    int curRow = index.row();
    m_nCurrentColumn = curCol;

    //竖直方向有合并框
    if (m_nRowSpan > 1)
    {
        if (this->rowSpan(curRow, curCol) > 1)
        {
            if (curRow == 0)
            {
                Bit32 startIdx = m_nStartIdx - m_nRowSpan;
                setSelectIdx(startIdx, -1);
                setStartIdx(startIdx);
            }
            else
            {
                setSelectIdx(m_nSelectIdx - m_nRowSpan, -1);
            }
            moveLen = 0;
        }
        else
        {
            if (curRow == 0)
            {
                moveLen = -m_nRowSpan;
            }
        }
    }
    //水平方向有合并框(预留)
    if (m_nColumnSpan > 1)
    {
        if (curRow + m_nStartIdx > 0)
        {
            for (int i = curCol; i > 0; i--)
            {
                if (!this->RoleStyle(curRow + m_nStartIdx + moveLen, GetDefCol(i), SpanRole).toStringList().isEmpty())
                {
                    QStringList slist = this->RoleStyle(curRow + m_nStartIdx + moveLen, GetDefCol(i), SpanRole).toStringList();
                    if (slist.count() != 2 || slist.at(1).toInt() <= 1 || i + slist.at(1).toInt() <= curCol)
                    {
                        return true;
                    }

                    this->setCurrentIndex(this->model()->index(curRow, i));
                    break;
                }
            }
        }
    }

    return HandleKeyPress(event, moveLen);
}

bool NcTableEx::KeyDown(QEvent* event)
{
    int moveLen = 1;

    // 合并框处理（预留）
    //...
    QModelIndex index = this->currentIndex();

    if(!index.isValid())
    {
        return true;
    }

    int curCol = index.column();
    int curRow = index.row();

    //竖直方向有合并框
    if (m_nRowSpan > 1)
    {
        if (this->rowSpan(curRow, curCol) > 1)
        {
            if (curRow == m_nRowCount - m_nRowSpan)
            {
                if (m_nSelectIdx != m_nTotalDataCount - m_nRowSpan)
                {
                    Bit32 startIdx = m_nStartIdx + m_nRowSpan;
                    setSelectIdx(startIdx + m_nRowCount - m_nRowSpan, 1);
                    setStartIdx(startIdx);
                }
            }
            else
            {
                setSelectIdx(m_nSelectIdx + m_nRowSpan, 1);
            }
            moveLen = 0;
        }
        else
        {
            if (curRow == m_nRowCount - 1 && m_nSelectIdx != m_nTotalDataCount - 1)
            {
                moveLen = m_nRowSpan;
            }
        }
    }

    //水平方向有合并框(预留)
    if (m_nColumnSpan > 1)
    {
        if (curRow + m_nStartIdx > 0)
        {
            for (int i = curCol; i > 0; i--)
            {
                if (!this->RoleStyle(curRow + m_nStartIdx + moveLen, GetDefCol(i), SpanRole).toStringList().isEmpty())
                {
                    QStringList slist = this->RoleStyle(curRow + m_nStartIdx + moveLen, GetDefCol(i), SpanRole).toStringList();
                    if (slist.count() != 2 || slist.at(1).toInt() <= 1 || i + slist.at(1).toInt() <= curCol)
                    {
                        return true;
                    }

                    this->setCurrentIndex(this->model()->index(curRow , i));
                    break;
                }
            }
        }
    }

    return HandleKeyPress(event, moveLen);
}

void NcTableEx::SetStartColumn(Bit32 stCol)
{
    if (stCol < 0)
    {
        stCol = 0;
        for (int i = stCol; i < ColunmCount(); i++)
        {
            if (SelectIdxEnable(m_nSelectIdx, i) == true)
            {
                stCol = i;
                break;
            }
        }
    }
    else if (stCol >= ColunmCount())
    {
        stCol = ColunmCount() - 1;
        for (int i = stCol; i >= 0; i--)
        {
            if (SelectIdxEnable(m_nSelectIdx, i) == true)
            {
                stCol = i;
                break;
            }
        }
    }
    if (m_nRowSpan > 1 && stCol != m_nStartColumn)
    {
        this->clearSpans();
    }
    m_nStartColumn = stCol;
}

Bit32 NcTableEx::GetNextCol(Bit32 row, Bit32 stCol, Bit32 dir)
{
    for (int i = stCol; (i >= 0 && i < ColunmCount()); i += dir)
    {
        if (SelectIdxEnable(row, i) == true)
        {
            if (IsSpanColItem(row, i) != 2)
            {
                return i;
            }
        }
    }
    return -1;
}

bool NcTableEx::KeyLeft(QEvent* event)
{
    UNREFERENCED_PARAM(event);
    // 合并框处理（预留）
    //...
    QModelIndex index = this->currentIndex();

    if(!index.isValid())
    {
        return true;
    }

    int curCol = index.column();
    int curRow = index.row();
    int oldCol = curCol + m_nStartColumn;
    int nextCol = GetNextCol(m_nStartIdx + curRow, oldCol - 1, -1);
    if (nextCol < 0)
    {
        nextCol = GetNextCol(m_nStartIdx + curRow, oldCol, 1);
    }
    if (nextCol < 0)
    {
        nextCol = oldCol;
    }

    if (nextCol < m_nStartColumn)
    {
        SetStartColumn(nextCol);
        SetHorizontalScrollBarValue();
    }
    if (IsSpanRowItem(curRow, nextCol) != 0)
    {
        curRow = (curRow / m_nRowSpan) * m_nRowSpan;
    }
    setSelectIdx(m_nStartIdx + curRow, 1);
    this->setCurrentIndex(this->model()->index(m_nSelectIdx - m_nStartIdx, nextCol - m_nStartColumn));

    if (oldCol == nextCol)
    {
        return false;
    }
    return true;
}

bool NcTableEx::KeyRight(QEvent* event)
{
    UNREFERENCED_PARAM(event);
    //合并框处理（预留）
    //...
    QModelIndex index = this->currentIndex();

    if(!index.isValid())
    {
        return true;
    }

    int curCol = index.column();
    int curRow = index.row();
    int oldCol = curCol + m_nStartColumn;
    int nextCol = GetNextCol(m_nStartIdx + curRow, oldCol + 1, 1);
    if (nextCol < 0)
    {
        nextCol = GetNextCol(m_nStartIdx + curRow, oldCol, -1);
    }
    if (nextCol < 0)
    {
        nextCol = oldCol;
    }

    if (nextCol >= m_nStartColumn + m_nColumnCount)
    {
        SetStartColumn(nextCol - m_nColumnCount + 1);
        SetHorizontalScrollBarValue();
    }
    if (IsSpanRowItem(m_nStartIdx + curRow, nextCol) != 0)
    {
        curRow = (curRow / m_nRowSpan) * m_nRowSpan;
    }
    setSelectIdx(m_nStartIdx + curRow, 1);
    this->setCurrentIndex(this->model()->index(m_nSelectIdx - m_nStartIdx, nextCol - m_nStartColumn));
    if (oldCol == nextCol)
    {
        return false;
    }
    return true;
}

bool NcTableEx::KeyPageUp(QEvent* event)
{
    int moveLen = -m_nRowCount;

    // 合并框处理（预留）
    //...
    QModelIndex index = this->currentIndex();

    if(!index.isValid())
    {
        return true;
    }

    int curCol = index.column();
    int curRow = index.row();

	int targetRow = curRow + m_nStartIdx + moveLen;
	if (targetRow < 0)
	{
		targetRow = 0;
	}

    //水平方向有合并框(预留)
    if (m_nColumnSpan > 1)
    {
        if (curRow + m_nStartIdx > 0)
        {
            for (int i = curCol; i > 0; i--)
            {
                if (!this->RoleStyle(targetRow, GetDefCol(i), SpanRole).toStringList().isEmpty())
                {
                    QStringList slist = this->RoleStyle(targetRow, GetDefCol(i), SpanRole).toStringList();
                    if (slist.count() != 2 || slist.at(1).toInt() <= 1 || i + slist.at(1).toInt() <= curCol)
                    {
                        return true;
                    }

                    if(targetRow >= 0)
                    {
                        if (this->model()->index(targetRow, i).isValid() && IsIndexSelectEnable(targetRow, i))
                        {
                            this->setCurrentIndex(this->model()->index(targetRow, i));
                        }
                        break;
                    }
                }
            }
        }
    }

    return HandleKeyPress(event, moveLen);
}

bool NcTableEx::KeyPageDown(QEvent* event)
{
    int moveLen = m_nRowCount;

    // 合并框处理（预留）
    //...
    QModelIndex index = this->currentIndex();

    if(!index.isValid())
    {
        return true;
    }

    int curCol = index.column();
    int curRow = index.row();

    //竖直方向有合并框
    if (m_nRowSpan > 1 && m_nSelectIdx + moveLen >= m_nTotalDataCount - 1)
    {
        if (this->rowSpan(m_nRowCount - 1, curCol) > 1)
        {
            setSelectIdx(m_nTotalDataCount - m_nRowSpan, 1);
            moveLen = 0;
        }
    }

    //水平方向有合并框(预留)
    if (m_nColumnSpan > 1)
    {
        if (curRow + m_nStartIdx > 0)
        {
            for (int i = curCol; i > 0; i--)
            {
                if (!this->RoleStyle(curRow + m_nStartIdx + moveLen, GetDefCol(i), SpanRole).toStringList().isEmpty())
                {
                    QStringList slist = this->RoleStyle(curRow + m_nStartIdx + moveLen, GetDefCol(i), SpanRole).toStringList();
                    if (slist.count() != 2 || slist.at(1).toInt() <= 1 || i + slist.at(1).toInt() <= curCol)
                    {
                        return true;
                    }

                    if (this->model()->index(curRow + m_nStartIdx + moveLen, i).isValid() && IsIndexSelectEnable(curRow + m_nStartIdx + moveLen, i))
                    {
                        this->setCurrentIndex(this->model()->index(curRow + m_nStartIdx + moveLen , i));
                    }
                    break;
                }
            }
        }
    }

    return HandleKeyPress(event, moveLen);
}

bool NcTableEx::HandleKeyPress(QEvent* event ,int moveLen)
{
    QModelIndex index = currentIndex();
    if (!index.isValid())
    {
        event->accept();
        return true;
    }

    if (IsComboBoxRole(index) == true)
    {
        NcComboBox* comboBox = GetIndexComboBox(index);
        if (comboBox != NULL)
        {
            CheckTableData(moveLen, event);
            event->accept();
            return true;
        }
        return false;
    }
    else if (IsCheckBoxRole(index) == true)
    {
        NcCheckBox* checkBox = GetIndexCheckBox(index);
        if (checkBox != NULL)
        {
            CheckTableData(moveLen, event);
            event->accept();
            return true;
        }
        return false;
    }
    else
    {
        CheckTableData(moveLen, event);
        event->accept();
        return true;    // 表格响应自己的消息，无需额外处理
    }

    return false;
}

/**
 * @brief NcTableEx::currentChanged 在鼠标点击，上、下、左、右按键时响应
 * @param current
 * @param previous
 */
void NcTableEx::currentChanged(const QModelIndex & current, const QModelIndex & previous)
{
    if (IsComboBoxRole(previous) == true)
    {
        NcComboBox* comboBox = GetIndexComboBox(previous);
        if (comboBox != NULL)
        {
            comboBox->clearFocus();
        }
    }
    if(IsCheckBoxRole(previous) == true)
    {
        NcCheckBox* checkBox = GetIndexCheckBox(previous);
        if(checkBox != NULL)
        {
            checkBox->setProperty("selected", false);
            checkBox->style()->polish(checkBox);
        }
    }
    if(IsCheckBoxRole(current) == true)
    {
        NcCheckBox* checkBox = GetIndexCheckBox(current);
        if(checkBox != NULL)
        {
            checkBox->setProperty("selected", true);
            checkBox->style()->polish(checkBox);
        }
    }
    m_saveIndex = current;

    SetTableFocus();

    emit SignalItemChanged();
}

int NcTableEx::SelectOn(int row, int col)
{
    if (this->focusPolicy() == Qt::NoFocus)
    {
        return 0;
    }

    if (col < 0)
    {
        col = 0;
    }
    if (col >= this->m_nColumnCount)
    {
        col = m_nColumnCount - 1;
    }
    setSelectIdx(row, 0);
    setStartIdx(m_nStartIdx);

    Refresh();                                  // 翻页后数据刷新

    return 0;
}

int NcTableEx::ColunmCount()
{
    return this->m_pTableMode->m_sHTitle.count();
}

int NcTableEx::FocusOn(int row, int col)
{
    if (this->focusPolicy() == Qt::NoFocus)
    {
        return 0;
    }

    if (col < 0)
    {
        col = 0;
    }
    if (col >= m_nStartColumn + m_nColumnCount && col < m_pTableMode->m_sHTitle.count())
    {
        SetStartColumn(col - m_nColumnCount + 1);
    }
    if (col < m_nStartColumn)
    {
        SetStartColumn(col);
    }
    SetHorizontalScrollBarValue();
    this->setFocus();
    setSelectIdx(row, col - m_nStartColumn, col);
    setStartIdx(m_nStartIdx);
    disconnect(m_pVerticalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollBarValueChanged(int)));
    if (m_nRowSpan != 0)
    {
        m_pVerticalScrollBar->setValue(m_nStartIdx / m_nRowSpan);
    }
    else
    {
        m_pVerticalScrollBar->setValue(m_nStartIdx);
    }
    connect(m_pVerticalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollBarValueChanged(int)));

    QModelIndex index = this->model()->index(m_nSelectIdx - m_nStartIdx, col - m_nStartColumn);
    if (IsComboBoxRole(index) == true)
    {
        NcComboBox* comboBox = GetIndexComboBox(index);
        if (comboBox != NULL && comboBox->isEnabled())
        {
            this->setCurrentIndex(index);
            comboBox->setFocus();
        }
        else // 此处设置焦点时列表当前的index的下拉框可能还未new出
        {
            this->setCurrentIndex(index);
            this->SetFocus();
        }
    }
    else
    {
        this->setCurrentIndex(index);
        if(IsCheckBoxRole(index) == true)
        {
            NcCheckBox* checkBox = GetIndexCheckBox(index);
            if(checkBox != NULL)
            {
                checkBox->setProperty("selected", true);
                checkBox->style()->polish(checkBox);
            }
        }
    }

    update();                                  // 翻页后数据刷新

    return 0;
}

void NcTableEx::SetFocus()                                // 设置焦点
{
    if (this->focusPolicy() != Qt::NoFocus)
    {
        m_bSetFocusFlag = true;
        this->setFocus(); // QAbstractItemView::focusInEvent中对于0行0列ItemIsEnabled的情况,会将焦点设到0行0列
    }
}

void NcTableEx::SetDefaultFocus(int row, int column)         // 设置默认的焦点位置（第一次启动时的焦点位置）
{
    if (row >=0 && column >= 0)
    {
        m_nDefaultRow = row;
        m_nDefaultColumn = column;
    }
}

int NcTableEx::GetStartIndex()
{
    return m_nStartIdx;
}

void NcTableEx::SetSpanRole(int rowSpan, int columnSpan)
{
    if (rowSpan >= 1 && columnSpan >= 1)
    {
        m_nRowSpan = rowSpan;
        m_nColumnSpan = columnSpan;
    }
}

void NcTableEx::GetSpanRole(int &rowSpan, int &columnSpan)
{
     rowSpan = m_nRowSpan;
     columnSpan = m_nColumnSpan;
}

int NcTableEx::GetCurrentRow()
{
    return m_nSelectIdx;
}

int NcTableEx::GetCurrentColumn()
{
    return GetDefCol(this->currentIndex().column() + m_nStartColumn);
}

void NcTableEx::ChangeFocus(ENMoveDirection direction)                // 按照指定方向切换焦点
{
    Bit32 keyVal = 0;

    if(direction == MOVE_DEFAULT)
    {
        if (TableNoDefaultMove() == true)
        {
            return;
        }
        switch (m_enDefaultMoveDirection)
        {
        case NOTMOVE:
            return;
            break;
        case MOVE_UP:
            keyVal = Qt::Key_Up;
            break;
        case MOVE_DOWN:
            keyVal = Qt::Key_Down;
            break;
        case MOVE_RIGHT:
            keyVal = Qt::Key_Right;
            break;
        default:
            break;
        }
    }
    else if (direction == MOVE_DOWN)
    {
        keyVal = Qt::Key_Down;
    }
    else if(direction == MOVE_UP)
    {
        keyVal = Qt::Key_Up;
    }
    else
    {
        return;
    }

    QKeyEvent keyEvent(QEvent::KeyPress, keyVal, Qt::NoModifier);
    QCoreApplication::sendEvent(this, &keyEvent);
}

/**
 * @brief NcTableEx::SetDefaultMoveDirection 设置默认移动方向
 * @param [in] direction 方向
 */
void NcTableEx::SetDefaultMoveDirection(ENMoveDirection direction)
{
    m_enDefaultMoveDirection = direction;
}

/**
 * @brief NcTableEx::SetDisplayRowCount 设置每行显示文字的行数
 * @param [in] count 行数
 */
void NcTableEx::SetDisplayRowCount(Bit32 count)
{
    if (count > 1)
    {
        m_nDisplayRowCount = count;
    }
}

void NcTableEx::SetComboBoxAlignment(Qt::Alignment alignment)
{
    m_Alignment = alignment;
}

/**
 * @brief NcTableEx::SetChangeColorFlag
 * @param flag
 */
void NcTableEx::SetChangeColorFlag(bool flag)                         // 设置m_bChangeColor标志
{
    m_bChangeColor = flag;
}

/**
 * @brief NcTableEx::GetChangeColorFlag
 * @return
 */
bool NcTableEx::GetChangeColorFlag()                                  // 获取m_bChangeColor标志
{
    return m_bChangeColor;
}

/**
 * @brief NcTableExt::SetTotalDataCount 设置数据总数
 * @param count 数据总数
 * @param refresh （默认为true）
 * 1、true：将起始索引与选择索引原始为0；
 * 2、false：保留原始索引
 */
void NcTableEx::SetTotalDataCount(int count, bool refresh)
{
    if(count <= 0)
    {
        return;
    }

    if (refresh)        // 初始化起始索引及选择项索引
    {
        setSelectIdx(0, 1);
        setStartIdx(0); // 数据修改后恢复到原始值，防止出错
    }

    if (m_nTotalDataCount != count)
    {
        m_nTotalDataCount = count;
    }

}

/**
 * @brief NcTableEx::GetTotalDataCount 获取数据总数
 * @return
 */
int NcTableEx::GetTotalDataCount() const
{
    return m_nTotalDataCount;
}

/**
 * @brief NcTableEx::InitProp 初始化列表
 * @param dataType 数据类型
 * @param vecWidth 列宽
 * @param title 水平表头
 * @param showMaxCol 显示列数最大值
 */
void NcTableEx::InitProp(EN_DataType dataType, const QVector<int> vecWidth, const QStringList horTitle, const Bit32 showMaxCol)
{
    if (vecWidth.count() != horTitle.count())
    {
        return;
    }
    m_enDataType = dataType;
    m_vecValue = vecWidth;
    if (horTitle.count() < showMaxCol || showMaxCol == 0)
    {
        m_nColumnCount = horTitle.count();
    }
    else
    {
        m_nColumnCount = showMaxCol;
    }
    m_pTableMode->SetColumnCount(m_nColumnCount);
    m_pTableMode->SetHorizontalHeaderLabels(horTitle);
}

void NcTableEx::SetVerticalHeaderLabels(const QStringList vecTitle, Bit32 width)
{
    if (vecTitle.isEmpty() == false)
    {
        this->verticalHeader()->setVisible(true);
        m_pTableMode->SetVerticalHeaderLabels(vecTitle);
        m_nVerHeaderWidth = width;
        QAbstractButton *btn = this->findChild<QAbstractButton *>();
        if (btn)
        {
            QPalette p = btn->palette();
            QColor c = HmiPainterColor::GetInstance()->GeNcTableexCornerButton();
            p.setColor(QPalette::Button, c);
            p.setColor(QPalette::Shadow, c);
            btn->setPalette(p);
        }
    }
}

void NcTableEx::ResetTableTitle(const QStringList &title)
{
    if (ColunmCount() != title.count())
    {
        return;
    }
    m_pTableMode->SetHorizontalHeaderLabels(title);
}

/**
 * @brief NcTableEx::Refresh 刷新
 * 注意：当有数据变动时调用此函数刷新显示数据
 */
void NcTableEx::Refresh()
{
    m_pTableMode->UpdateView(0, 0, m_nRowCount - 1, m_nColumnCount - 1);
}

/**
 * @brief NcTableEx::RefreshSingleItem 单行刷新（预留，暂未实现）
 * @param row
 * @param col
 */
void NcTableEx::RefreshSingleItem(int row, int col)
{
	row = row;
	col = col;
}
/**
 * @brief NcTableEx::Redraw 列表重绘
 * 当数据总行数有变动时调用此函数进行列重绘
 * @param refresh true:恢复起始索引及选择索引；false:保留起始索引及选择索引
 */
void NcTableEx::Redraw(bool refresh)
{
	m_pTableMode->ResetModel();
	TableUpdate(refresh);
}

void NcTableEx::TableUpdate(bool refresh)
{
	Bit32 totalCount = TotalDataCount();
	if (totalCount != m_nTotalDataCount)
	{
		m_nTotalDataCount = totalCount;
		QKeyEvent keyEvent(QEvent::Resize, Qt::Key_unknown, Qt::NoModifier);
		QCoreApplication::sendEvent(this, &keyEvent);
	}

	if (refresh)
	{
		setSelectIdx(0, 1);
		setStartIdx(0); // 数据修改后恢复到原始值，防止出错
		m_nOldStartIdx = 0;
        m_nStartColumn = 0;
		disconnect(m_pVerticalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollBarValueChanged(int)));
        disconnect(m_pHorizontalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollHorizontalBarValueChanged(int)));
        m_pVerticalScrollBar->setValue(0);        
        SetHorizontalScrollBarValue();
        connect(m_pVerticalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollBarValueChanged(int)));
        connect(m_pHorizontalScrollBar, SIGNAL(valueChanged(int)), this, SLOT(SlotScrollHorizontalBarValueChanged(int)));

	}
	m_pTableMode->ResetModel();
	if (m_nColumnSpan > 1)
	{
		this->clearSpans();
	}
    if ((!m_bIsConnected) && (CommonGetInputDlg() != NULL))
    {
        m_bIsConnected = true;
        connect(CommonGetInputDlg(), SIGNAL(accepted()), this, SLOT(EditAccept()));
        connect(CommonGetInputDlg(), SIGNAL(rejected()), this, SLOT(EditReject()));
    }
}

bool NcTableEx::EditItem(const QModelIndex &index, QString val)
{
    int curRow = index.row() + GetStartIndex();
    int curCol = index.column() + m_nStartColumn;
    int flagRole = RoleStyle(curRow, GetDefCol(curCol), NcTableEx::FlagRole).toInt();
    if ((flagRole & Qt::ItemIsSelectable) != Qt::ItemIsSelectable)
    {
        return false;
    }
    if ((flagRole & Qt::ItemIsEditable) != Qt::ItemIsEditable)
    {
        return false;
    }
    if (RoleStyle(curRow, GetDefCol(curCol), NcTableEx::EditDisableRole) == true)
    {
        return false;
    }

    FocusOn(curRow, curCol);
    ENMoveDirection direction = ContentChanged(curRow, GetDefCol(curCol), val);
    if (GetIsDefContentChangedVaild() == true)
    {
        direction = ContentSet(curRow, GetDefCol(curCol), val);
    }
    return true;
}

/**
 * @brief NcTableEx::CalculateTableRowCount
 * 计算每行的高度及table页的行数
 */
void NcTableEx::CalculateTableRowCount()
{
    int tableHeight = this->height();
    int headerHeight = this->horizontalHeader()->height();
    Bit32 fontHeight = (this->fontMetrics().height() + 5) * m_nDisplayRowCount;

    int horizontalScrollBarHeight = 0;

    UpdataHorizontalScrollBarHidden();
    if (m_pHorizontalScrollBar->isHidden() == false)
    {
        horizontalScrollBarHeight = m_pHorizontalScrollBar->height();
    }

    // 显示数据
    m_nRowCount = (tableHeight - headerHeight - horizontalScrollBarHeight) / fontHeight;

    // 处理有合并框时的情况
    if (m_nRowSpan > 1)
    {
        m_nRowCount = (m_nRowCount / m_nRowSpan) * m_nRowSpan;
    }

    // 每行的高度
    if (m_nRowCount <= 0)
    {
        m_nTableRowHeight = 0;
    }
    else
    {
        m_nTableRowHeight = (tableHeight - headerHeight - horizontalScrollBarHeight) / m_nRowCount;
    }
}

/**
 * @brief 根据实际显示行数重新计算每行高度
 */
void NcTableEx::CalculateTableRowHeight()
{
    int tableHeight = this->height();
    int headerHeight = this->horizontalHeader()->height();

    int horizontalScrollBarHeight = 0;

    UpdataHorizontalScrollBarHidden();
    if (m_pHorizontalScrollBar->isHidden() == false)
    {
        horizontalScrollBarHeight = m_pHorizontalScrollBar->height();
    }

    //每行的高度
    if (m_nRowCount > 0)
    {
        m_nTableRowHeight = (tableHeight - headerHeight - horizontalScrollBarHeight) / m_nRowCount;
    }
    else
    {
        m_nTableRowHeight = 0;
    }
}

/**
 * @brief NcTableEx::SetVerticalScrollBar 设置竖直滚动条参数
 */
void NcTableEx::SetVerticalScrollBar()
{
    int horizontalScrollBarHeight = 0;

    UpdataHorizontalScrollBarHidden();
    if (m_pHorizontalScrollBar->isHidden() == false)
    {
        horizontalScrollBarHeight = m_pHorizontalScrollBar->height(); // 当竖直滚动条和水平滚动条同时存在，竖直滚动条高度应减去水平滚动条高度
    }

    if (m_nRowCount <= 0 || m_nTotalDataCount <= 0 || m_nRowSpan <= 0)
    {
        return;
    }

    if (m_nTotalDataCount - m_nRowCount < 0)
    {
        m_pVerticalScrollBar->setRange(0, 0);
        ResetIndex();
    }
    else
    {
        m_pVerticalScrollBar->setRange(0, (m_nTotalDataCount - m_nRowCount) / m_nRowSpan);
    }

    if(m_nTotalDataCount > m_nRowCount)
    {
        m_pVerticalScrollBar->setVisible(true);
        //m_pVerticalScrollBar->setValue(0); // setRange会调整超出范围ScrollBar的value值

        double width = ((double)(this->width() - m_pVerticalScrollBar->width()) - 1);
        m_pVerticalScrollBar->setGeometry((int)width, 0, m_pVerticalScrollBar->width(),
                                          this->geometry().height() - horizontalScrollBarHeight);
    }
    else
    {
        m_pVerticalScrollBar->setVisible(false);
    }
}

void NcTableEx::SetHorizontalScrollBar()
{
    if (m_nColumnCount <= 0 || ColunmCount() <= 0 || m_nColumnSpan <= 0)
    {
        return;
    }

    UpdataHorizontalScrollBarHidden();

    if (m_pHorizontalScrollBar->isHidden() == false)
    {
        double height = ((double)(this->height() - m_pHorizontalScrollBar->height()) - 1);
        m_pHorizontalScrollBar->setGeometry(0, height, this->geometry().width(), m_pHorizontalScrollBar->height());
    }
}
/**
 * @brief NcTableEx::ResizeTable 调整表格
 */
void NcTableEx::ResizeTable()
{
    SetHorizontalScrollBar();
    //第一次计算行高
    CalculateTableRowCount();
    //设置列宽
    ResizeTableColumnWidth();
    //再次计算行高
    CalculateTableRowCount();
    //设置显示行数
    SetDisplayRow();
    //设置行高
    ResizeTableRowHeight();
    //设置滚动条
    SetVerticalScrollBar();
    //列表重绘
    TableUpdate();
}

/**
 * @brief NcTableEx::ResizeTableRowHeight   调整行高
 */
void NcTableEx::ResizeTableRowHeight()
{
    if (m_nRowCount <= 0 || m_nTotalDataCount <= 0)
    {
        return;
    }

    if (m_bFillAllFlag == true)
    {
        CalculateTableRowHeight();
    }

    for (int i = 0; i < m_nRowCount && i < m_nTotalDataCount; i++)
    {
        this->setRowHeight(i, m_nTableRowHeight);
    }
}

/**
 * @brief NcTableEx::ResizeTableColumnWidth 调整列宽
 */
void NcTableEx::ResizeTableColumnWidth()
{
    if (m_enDataType == ENInvalid)
    {
        return;
    }

    int tableWidth = this->width();
    int verticalScrollBarWidth = 0;
    if (m_nTotalDataCount > m_nRowCount)
    {
        m_pVerticalScrollBar->setVisible(true);
        verticalScrollBarWidth = m_pVerticalScrollBar->width();
    }
    else
    {
        m_pVerticalScrollBar->setVisible(false);
    }

    long long int total = 0;
    for (int i = m_nStartColumn; i < m_nStartColumn + m_nColumnCount; i++)
    {
        total += m_vecValue.at(i);
    }
    if (m_enDataType == ENScaleType)
    {
        double width = tableWidth - verticalScrollBarWidth - 1;
        double verticalHeaderWidth = (double)width / (double)(total + m_nVerHeaderWidth) * (double)m_nVerHeaderWidth;
#ifndef _NCUPPER_  // 代码逻辑错误，九型不使用，八型需修改
        if(m_nVerHeaderWidth != 0)
        {
            this->verticalHeader()->setFixedWidth(m_nVerHeaderWidth);

        }
        else
#endif
        {
            this->verticalHeader()->setFixedWidth(verticalHeaderWidth);
        }
    }

    if (total == 0)
    {
        return;
    }

    //数据具体数值
    if (m_enDataType == ENValueType)
    {
        for (int i = m_nStartColumn; i < m_nColumnCount + m_nStartColumn; i++)
        {
            if (this->isColumnHidden(i))
            {
                continue;
            }
            this->setColumnWidth(i - m_nStartColumn, m_vecValue.at(i));
        }
    }
    else if (m_enDataType == ENScaleType)
    { //数据具体比例
        double width = tableWidth - verticalScrollBarWidth - this->verticalHeader()->width() - 1;
        for (int i = m_nStartColumn; i < m_nColumnCount + m_nStartColumn; i++)
        {
            if (this->isColumnHidden(i))
            {
                continue;
            }
            double colWidth = (double)width / (double)total * m_vecValue.at(i);
            this->setColumnWidth(i - m_nStartColumn, colWidth);
        }
    }
}

/**
 * @brief NcTableEx::SetDisplayRow 设置显示的行数
 */
void NcTableEx::SetDisplayRow()
{
    if (m_nRowCount <= 0 || m_nTotalDataCount < 0)
    {
        return;
    }

    if (m_nRowCount >= m_nTotalDataCount)
    {
        m_nRowCount = m_nTotalDataCount;
    }

    m_pTableMode->SetRowCount(m_nRowCount);
    m_pTableMode->ResetModel();
}

void NcTableEx::SlotScrollHorizontalBarValueChanged(int value)
{
    Bit32 startIdx = 0;
    if(value < 0)
    {
        value = 0;
    }
    else if(value >= ColunmCount() - m_nColumnCount)
    {
        value = ColunmCount() - m_nColumnCount;
    }

    //有合并框
    if (m_nColumnSpan != 0)
    {
        startIdx = value * m_nColumnSpan;
    }
    else
    {
        startIdx = value;                // 改变开始索引号
    }
    m_nStartColumn = startIdx;
    Refresh();                                  // 翻页后数据刷新
    if (this->horizontalHeader()->isVisible())
    {
        this->horizontalHeader()->update();
    }
}

/**
 * @brief NcTableEx::SlotScrollBarValueChanged 响应竖直滚动条
 * @param value
 */
void NcTableEx::SlotScrollBarValueChanged(int value)
{
    Bit32 startIdx = 0;
    if(value < 0)
    {
        value = 0;
    }
    else if(value >= m_nTotalDataCount - m_nRowCount)
    {
        value = m_nTotalDataCount - m_nRowCount;
    }

    int row = m_nSelectIdx - m_nStartIdx;     //计算焦点所在行

    //有合并框
    if (m_nRowSpan != 0)
    {
        startIdx = value * m_nRowSpan;
    }
    else
    {
        startIdx = value;                // 改变开始索引号
    }
    setSelectIdx(startIdx + row, 0);         // 计算当前选择行
    setStartIdx(startIdx);
    ChangeCurrentIndex();                       // 设置当前索引
    Refresh();                                  // 翻页后数据刷新
    if (this->verticalHeader()->isVisible())
    {
        this->verticalHeader()->update();
    }
}

void NcTableEx::SlotDoubleClicked(QModelIndex index)
{
    if (RoleStyle(index, NcTableEx::DialogRole) == true || IsPicRole(index))
    {
        EditItem(index, "");
    }
}

/**
 * @brief NcTableEx::CheckTableData 检查数据
 * @param moveLen 移动步数
 */
void NcTableEx::CheckTableData(Bit32 moveLen, QEvent* event)
{
    Bit32 idx = m_nSelectIdx + moveLen;
    Bit32 preRow = m_nSelectIdx;
    Bit32 dir = 0;
    if(m_nTotalDataCount <= 0 || m_nRowCount <= 0)
    {
        return;
    }

    if(idx < 0)
    {
        idx = 0;
    }
    else if(idx >= m_nTotalDataCount)
    {
        idx = m_nTotalDataCount - 1;
    }

    if (event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEv = static_cast<QKeyEvent*>(event);
        if (keyEv->key() == Qt::Key_PageDown || keyEv->key() == Qt::Key_Down)
        {
            dir = 1;
            setSelectIdx(idx, dir);
            if (keyEv->key() == Qt::Key_PageDown)
            {
                setStartIdx(m_nStartIdx + m_nRowCount);
            }
        }
        else if (keyEv->key() == Qt::Key_PageUp || keyEv->key() == Qt::Key_Up)
        {
            dir = -1;
            setSelectIdx(idx, dir);
            if (keyEv->key() == Qt::Key_PageDown)
            {
                setStartIdx(m_nStartIdx - m_nRowCount);
            }
        }
        //else if (keyEv->key() == Qt::Key_Ri)
    }

    Bit32 startIdx = m_nStartIdx;
    QModelIndex index = currentIndex();
    if (preRow == m_nSelectIdx && SelectIdxEnable(idx, index.column() + m_nStartColumn) == false)
    {
        startIdx += dir;
    }
    if(m_nTotalDataCount - m_nRowCount >= 0 && m_nStartIdx > m_nTotalDataCount - m_nRowCount)
    {
        startIdx = m_nTotalDataCount - m_nRowCount;
    }
    else if(idx - m_nStartIdx > m_nRowCount -1)// || m_nSelectIdx - m_nStartIdx < 0)
    {
        startIdx = idx - m_nRowCount + 1;
    }
    else if (idx - m_nStartIdx < 0)
    {
        if (qAbs(moveLen) == m_nRowCount)
        {
            startIdx = m_nStartIdx + moveLen;
        }
        else
        {
            startIdx = idx;
        }
    }
    setStartIdx(startIdx);

    if (m_nRowSpan != 0)
    {
        m_pVerticalScrollBar->setValue(m_nStartIdx / m_nRowSpan);
    }
    else
    {
        m_pVerticalScrollBar->setValue(m_nStartIdx);
    }
    ChangeCurrentIndex();
}

/**
 * @brief NcTableEx::ChangeCurrentIndex 改变当前索引
 * （同时保存起始索引及选择索引）
 */
void NcTableEx::ChangeCurrentIndex()
{
    int oldTableRow = m_nOldSelectIdx - m_nOldStartIdx;
    int tableRow = m_nSelectIdx - m_nStartIdx;
    m_nOldStartIdx = m_nStartIdx;
    m_nOldSelectIdx = m_nSelectIdx;

    if (tableRow != oldTableRow)
    {
        QModelIndex index = this->model()->index(tableRow, this->currentIndex().column());
        this->setCurrentIndex(index);
    }
    else
    {
        QModelIndex index = this->model()->index(oldTableRow, this->currentIndex().column());
        this->setCurrentIndex(index);
    }
}

void NcTableEx::SetFocus(const QModelIndex& index)          // 设置焦点
{
    if (m_bSetFocusFlag)
    {
        if (m_saveIndex.isValid())
        {
            if (SelectIdxEnable(m_saveIndex.row() + m_nStartIdx, m_saveIndex.column() + m_nStartColumn) == false)
            {
                m_pVerticalScrollBar->setValue(0);
                m_saveIndex = this->model()->index(m_nDefaultRow, m_nDefaultColumn);
            }

            if (m_saveIndex == index)
            {
                this->setCurrentIndex(index);
                SetTableFocus();
                if (m_nRowSpan != 0)
                {
                    m_pVerticalScrollBar->setValue(m_nStartIdx / m_nRowSpan);
                }
                m_bSetFocusFlag = false;
            }
        }
        else if (index.row() == m_nDefaultRow && index.column() == m_nDefaultColumn)
        {
            this->setCurrentIndex(index);
            SetTableFocus();
            if (m_nRowSpan != 0)
            {
                m_pVerticalScrollBar->setValue(m_nStartIdx / m_nRowSpan);
            }
            m_bSetFocusFlag = false;
        }

        return;

        if ((m_saveIndex.isValid() && m_saveIndex == index) ||
            (index.row() == m_nDefaultRow && index.column() == m_nDefaultColumn))
        {
            this->setCurrentIndex(index);
            SetTableFocus();
            m_bSetFocusFlag = false;
        }
    }
}

/**
 * @brief NcTableEx::GetPageCount 获取Table总页数
 * @param
 */
int NcTableEx::GetPageCount()
{
    int ret = 1;
    int tem = 0;

    if(m_nRowCount <= 0)
    {
        return ret;
    }

    ret = m_nTotalDataCount / m_nRowCount;
    tem = m_nTotalDataCount % m_nRowCount;
    if (tem != 0)
    {
        ret++;
    }

    return ret;
}

/**
 * @brief NcTableEx::PageTo 翻到指定页
 * @param pageNO 页号
 */
bool NcTableEx::PageTo(int pageNO)
{
    int maxPage = GetPageCount();                               //最大页数
    int canNotViewCount = m_nTotalDataCount - m_nRowCount;      //看不见的行数
    int maxValue = this->m_pVerticalScrollBar->maximum();		// 当前SCROLLER最大显示值

    if(pageNO > maxPage || pageNO < 0)
    {
        return false;
    }

    if(canNotViewCount <= 0)
    {
        return false;
    }

    if(maxValue == 0)
    {
        return false;
    }

    int pageValue = (maxValue * m_nRowCount) / canNotViewCount;
    int posValue = pageValue * (pageNO - 1);
    if(posValue > maxValue)
    {
        posValue = maxValue;
    }

    this->m_pVerticalScrollBar->setSliderPosition(posValue);

    return true;
}

void NcTableEx::SetFillAllFlag(bool flag)
{
    m_bFillAllFlag = flag;
}

void NcTableEx::RefreshCheckBoxText(QModelIndex index)
{
    NcCheckBox* checkBox = GetIndexCheckBox(index);
    if (checkBox == NULL)
    {
        return;
    }
    QStringList strList = RoleStyle(index, NcTableEx::CheckBoxRole).toStringList();
    if(strList.count() == 0)
    {
        return;
    }
    QString str = strList.at(0);
    if (str != checkBox->text())
    {
        checkBox->setText(str);
    }
}

void NcTableEx::InitPicPathList()
{
    m_sOldPicPathList.clear();
    for (int i = 0; i < m_nTotalDataCount; i++)
    {
        QStringList list;
        for (int j = 0; j < ColunmCount(); j++)
        {
            list.append("");
        }
        m_sOldPicPathList.append(list);
    }
}

void NcTableEx::RefreshPic(QModelIndex index, bool flag)
{
    QLabel* l = GetIndexPic(index);
    if (l == NULL || m_nTotalDataCount <= 0)
    {
        return;
    }
    QString path("");
    ContentGet(index.row() + m_nStartIdx, GetDefCol(index.column() + m_nStartColumn), path);

    if (m_sOldPicPathList.count() != m_nTotalDataCount)
    {
        InitPicPathList();
    }
    if (m_sOldPicPathList.at(0).count() != ColunmCount())
    {
        InitPicPathList();
    }

    QStringList list = m_sOldPicPathList.at(index.row() + m_nStartIdx);
    QString oldPath = list.at(index.column() + m_nStartColumn);
    if (oldPath == path && flag == false)
    {
        return;
    }
    list.replace(index.column() + m_nStartColumn, path);
    m_sOldPicPathList.replace(index.row() + m_nStartIdx, list);
    if (path.isEmpty())
    {
        l->clear();
        return;
    }
    QPixmap p("");
    p.load(TransPicName(path));
    if (p.isNull())
    {
        l->clear();
    }
    else
    {
        p = p.scaled(l->size(), Qt::KeepAspectRatio, Qt::SmoothTransformation); // 图片适应label大小
        l->setPixmap(p);
    }
}

NcDataLimit NcTableEx::InputDataLimit(int row, int col)
{
    UNREFERENCED_PARAM(row);
    UNREFERENCED_PARAM(col);

    return DataTypeLimit(DTYPE_NULL, -1, -1);
}

NcDataLimit NcTableEx::GetInputDataLimit(const int row, const int col)
{
    NcDataLimit limit = InputDataLimit(row, GetDefCol(col));
    if (limit.dateType == DTYPE_NULL)
    {
        Bit32 ebxType = RoleStyle(row, GetDefCol(col), NcTableEx::DataTypeRole).toInt();
        limit = DataTypeLimit(ebxType, -1, -1);
    }
    return limit;
}

QValidator *NcTableEx::GetInputValidator(const int row, const int col)
{
    QValidator *pValidator = NULL;
    pValidator = GetValidator(row, GetDefCol(col));
    if (pValidator == NULL)
    {
        NcDataLimit limit = GetInputDataLimit(row, col);
        pValidator = CommonGetInputDlg()->TransDataLimitToValidator(limit);
    }
    return pValidator;
}

bool NcTableEx::UseSelBackgrd(QModelIndex index)
{
    if (this->hasFocus())
    {
        return false;
    }
    QModelIndex currentIndex = this->currentIndex();
    int curRow = m_nSelectIdx - m_nStartIdx;
    int curCol = currentIndex.column();
    if (curRow < 0 && m_nRowCount > 0)
    {
        curRow = 0;
    }
    if (curCol == -1 && m_nColumnCount > 0)
    {
        curCol = 0;
    }
    if (this->selectionBehavior() == SelectRows)
    {
        if (curRow != index.row())
        {
            return false;
        }
    }
    else
    {
        if (curRow != index.row() || curCol != index.column())
        {
            return false;
        }
    }

    if (m_bDefaultSel)
    {
        return true;
    }


    return false;
}

void NcTableEx::SetDefaultSel(bool defaultSel)
{
    m_bDefaultSel = defaultSel;
}

void NcTableEx::ClearFocusAndSelection()
{
    QModelIndex index = this->currentIndex();
    if (index.isValid())
    {
        if (this->indexWidget(index) != NULL)
        {
            QWidget* widget = dynamic_cast<QWidget*>(this->indexWidget(index));
            if (widget != NULL)
            {
                widget->clearFocus();
            }
        }
    }

    this->clearSelection();
    this->clearFocus();
}

void NcTableEx::UpdateHorTitle(const QStringList horTitle)
{
    if (m_nColumnCount != horTitle.count())
    {
        return;
    }
    m_pTableMode->SetHorizontalHeaderLabels(horTitle);
    Redraw();
}

bool NcTableEx::IsIndexInTableSpan(int row, int col)
{
    if (row < 0 || row >= m_nRowCount || col < 0 || col >= m_nColumnCount)
    {
        return false;
    }

    int tmpRow = row;
    int tmpCol = col;

    if (m_nRowSpan > 1)
    {
        if (this->rowSpan(row, col) != m_nRowSpan)
        {
            for (int i = 1; i < m_nRowSpan; i++)
            {
                tmpRow = row - i;
                if (tmpRow >= 0)
                {
                    if (this->rowSpan(tmpRow, col) == m_nRowSpan)
                    {
                        return false;
                    }
                }
            }
        }
    }

    if (m_nColumnSpan > 1)
    {
		if (this->columnSpan(row, col) != m_nColumnCount)
		{
			for (int i = 1; i < m_nColumnCount; i++)
			{
                tmpCol = col - i;
				if (tmpCol >= 0)
				{
					if (this->columnSpan(row, tmpCol) == m_nColumnCount)
					{
						return false;
					}
				}
			}
		}
    }

    return true;
}

//*****************************************************************************************************************************
//************NcTableModel
//*****************************************************************************************************************************
void NcTableModelEx::UpdateView(int tlrow, int tlcol, int brrow, int brcol)
{
    QModelIndex t1 = this->index(tlrow, tlcol);
    QModelIndex t2 = this->index(brrow, brcol);
    emit dataChanged(t1, t2);
}

/**
 * @brief NcTableModelEx::rowCount 获取行数
 * @return
 */
int NcTableModelEx::rowCount(const QModelIndex &) const
{
    return m_nRowCount;
}

/**
 * @brief NcTableModelEx::columnCount 获取列数
 * @return
 */
int NcTableModelEx::columnCount(const QModelIndex &) const
{
    return m_nColumnCount;
}

void NcTableModelEx::SetIndexWidgetAlignment(const QModelIndex index) const
{
    QLayout *pLayout = m_pTable->indexWidget(index)->layout();
    if (pLayout == NULL)
    {
        return;
    }

    int align = m_pTable->RoleStyle(index, Qt::TextAlignmentRole).toInt();
    Qt::Alignment align0 = pLayout->alignment();
    if (align0 != (Qt::Alignment)(align))
    {
        pLayout->setAlignment((Qt::Alignment)(align));
        m_pTable->indexWidget(index)->setLayout(pLayout);
    }
}

QVariant NcTableModelEx::data(const QModelIndex &index, int role) const
{
    int flagRole = m_pTable->RoleStyle(index, NcTableEx::FlagRole).toInt();
    bool editableFlag = false;
    if ((flagRole & Qt::ItemIsEditable) == Qt::ItemIsEditable)
    {
        editableFlag = true;
    }
    bool selectableFlag = false;
    if ((flagRole & Qt::ItemIsSelectable) == Qt::ItemIsSelectable)
    {
        selectableFlag = true;
    }
    bool noFlag = false;
    if (flagRole == Qt::NoItemFlags)
    {
        noFlag = true;
    }

    bool changeColor = m_pTable->GetChangeColorFlag();

    if (role == Qt::TextAlignmentRole)
    {
        if (m_pTable->IsCheckBoxRole(index) == true)
        {
            SetIndexWidgetAlignment(index);
            return QVariant();
        }
    }
    if (role == Qt::BackgroundRole)
    {
        if (m_pTable->UseSelBackgrd(index) == true)
        {
            return HmiPainterColor::GetInstance()->GeNcTableexFoucsColor();
        }
    }
    if (role == Qt::ForegroundRole)
    {
        if (changeColor == true && selectableFlag == true && editableFlag == false && noFlag == false)
        {
            return HmiPainterColor::GetInstance()->GetTableDiseditableColor();
        }
        else
        {
            return m_pTable->RoleStyle(index, Qt::ForegroundRole);
        }
    }
    else if (role == Qt::BackgroundRole)
    {
        return m_pTable->RoleStyle(index, Qt::BackgroundRole);
    }
    if (role == Qt::DisplayRole)
    {
        QString val;
        m_pTable->ContentGet(index.row() + m_pTable->GetStartIndex(), m_pTable->GetDefCol(index.column() + m_pTable->m_nStartColumn), val);
        m_pTable->SetFocus(index);
        if (m_pTable->IsCheckBoxRole(index) == false && m_pTable->IsPicRole(index) == false)
        {
            return val;
        }
    }
    else
    {
        if (!m_pTable->RoleStyle(index, NcTableEx::SpanRole).toStringList().isEmpty())
        {   // 合并框处理
            QStringList slist = m_pTable->RoleStyle(index, NcTableEx::SpanRole).toStringList();
            if (slist.count() != 2)
            {
                return QVariant();
            }

            int rowSpan = slist.at(0).toInt();
            int columnSpan = slist.at(1).toInt();
            QModelIndex tmp;
            if (index.column() + columnSpan > columnCount(tmp))
            {
                columnSpan = columnCount(tmp) - index.column();
            }
            if (index.row() + rowSpan > rowCount(tmp))
            {
                rowSpan = rowCount(tmp) - index.row();
            }
            if (rowSpan != m_pTable->rowSpan(index.row(), index.column())
             || columnSpan != m_pTable->columnSpan(index.row(), index.column()))
            {
                m_pTable->SetSpanRole(rowSpan, columnSpan);
                m_pTable->setSpan(index.row(), index.column(), rowSpan, columnSpan);
            }
        }


        if (m_pTable->indexWidget(index) != NULL)
        {
            m_pTable->RemoveItemFromTable(index);
        }

        // 处理下拉框
        if (m_pTable->IsComboBoxRole(index) == true)
        {
//             int rowSpan = 1;
//             int columnSpan = 1;
//             m_pTable->GetSpanRole(rowSpan, columnSpan);
// 
//             if (index.row() % rowSpan != 0 || index.column() % columnSpan != 0)
//             {
//                 return QVariant();
//             }

            if (!m_pTable->IsIndexInTableSpan(index.row(), index.column()))
            {
                return QVariant();
            }
            if (m_pTable->indexWidget(index) == NULL)
            {
                NcComboBox* comboBox = new NcComboBox();
                comboBox->setObjectName("NcTableComboBox");
                m_pTable->setIndexWidget(index, comboBox);
                comboBox->setFont(m_pTable->font());
                comboBox->addItems(m_pTable->RoleStyle(index, NcTableEx::ComboBoxRole).toStringList());
                bool disableFlag = m_pTable->RoleStyle(index, NcTableEx::ComboBoxDisEnableFlag).toBool();
                comboBox->setDisabled(disableFlag);
                comboBox->installEventFilter((QObject*)this);
                if (m_pTable->currentIndex() == index && m_pTable->hasFocus())
                {
                    m_pTable->SetFocus();
                }
            }
            else
            {
                QString value;
                m_pTable->ContentGet(index.row() + m_pTable->GetStartIndex(), m_pTable->GetDefCol(index.column() + m_pTable->m_nStartColumn), value);
                NcComboBox* comboBox = (NcComboBox*)m_pTable->indexWidget(index);
                QStringList list = m_pTable->RoleStyle(index, NcTableEx::ComboBoxRole).toStringList();
                QStringList oldList;
                for (int i = 0; i < comboBox->count(); i++)
                {
                    oldList << comboBox->itemText(i);
                }
                int currentIndex = list.indexOf(value);
                if (oldList != list)
                {
                    disconnect(comboBox,SIGNAL(currentIndexChanged(QString)), this, SLOT(SlotComboBoxCurrentIndexChanged(QString)));
                    comboBox->clear();
                    comboBox->addItems(list);
                    connect(comboBox,SIGNAL(currentIndexChanged(QString)), this, SLOT(SlotComboBoxCurrentIndexChanged(QString)));
                }
                if (currentIndex >= 0 && currentIndex < list.count())
                {
                    disconnect(comboBox,SIGNAL(currentIndexChanged(QString)), this, SLOT(SlotComboBoxCurrentIndexChanged(QString)));
                    comboBox->setCurrentIndex(currentIndex);
                    bool disableFlag = m_pTable->RoleStyle(index, NcTableEx::ComboBoxDisEnableFlag).toBool();
                    comboBox->setDisabled(disableFlag);
                    connect(comboBox,SIGNAL(currentIndexChanged(QString)), this, SLOT(SlotComboBoxCurrentIndexChanged(QString)));

                    QStandardItemModel* pItemModel = static_cast<QStandardItemModel*>(comboBox->view()->model());
                    for(int i = 0; i < list.count(); i++)
                    {
                        pItemModel->item(i)->setTextAlignment(m_pTable->m_Alignment);
                    }
                }
            }
            NcComboBox* comboBox = (NcComboBox*)m_pTable->indexWidget(index);
            if (comboBox != NULL)
            {
                if (comboBox->isEnabled() != selectableFlag)
                {
                    comboBox->setEnabled(selectableFlag);
                }
                if (comboBox->property("Editable") != editableFlag)
                {
                    comboBox->setProperty("Editable", editableFlag);
                    comboBox->style()->polish(comboBox);
                }
            }
            return QVariant();
        }
        else if (m_pTable->IsCheckBoxRole(index) == true)
        {
//             int rowSpan = 1;
//             int columnSpan = 1;
//             m_pTable->GetSpanRole(rowSpan, columnSpan);
//             if (index.row() % rowSpan == 0 && index.column() % columnSpan == 0)
//             {
//                 
//             }

			if (!m_pTable->IsIndexInTableSpan(index.row(), index.column()))
			{
                return QVariant();
            }

            if (m_pTable->indexWidget(index) == NULL)
            {
                m_pTable->CreateCheckBoxInTable(index);
            }

            NcCheckBox* checkBox = m_pTable->GetIndexCheckBox(index);
            if (checkBox != NULL)
            {
                QString value("");
                m_pTable->ContentGet(index.row() + m_pTable->GetStartIndex(), m_pTable->GetDefCol(index.column() + m_pTable->m_nStartColumn), value);
                bool ok = false;
                Bit32 val = value.toInt(&ok);
                bool state = false;
                if (ok == true && val == 1)
                {
                    state = true;
                }
                if (state != checkBox->isChecked())
                {
                    checkBox->setChecked(state);
                    m_pTable->RefreshCheckBoxText(index);
                }
            }
            return QVariant();
        }
        else if (m_pTable->IsPicRole(index) == true)
        {
            if (m_pTable->indexWidget(index) == NULL)
            {
                m_pTable->CreatePicInTable(index);
            }

            if (m_pTable->IsPicRole(index))
            {
                m_pTable->RefreshPic(index);
            }
            return QVariant();
        }

        m_pTable->SetFocus(index);
        return m_pTable->RoleStyle(index, role);
    }

    return QVariant();
}

QVariant NcTableModelEx::headerData(int section, Qt::Orientation orientation, int role) const
{
    if (orientation == Qt::Horizontal && role == Qt::DisplayRole)
    {
        Bit32 row = m_pTable->m_nStartColumn + section;
        if (row >= 0 && row < m_sHTitle.count())
        {
            return m_sHTitle.at(row);
        }
    }
    else if (section == 0 && orientation == Qt::Horizontal && role == Qt::TextAlignmentRole)
    {
        return int(Qt::AlignCenter);
    }

    if (orientation == Qt::Vertical && role == Qt::DisplayRole)
    {
        Bit32 col = m_pTable->m_nStartIdx + section;
        if (col >= 0 && col < m_sVTitle.count())
        {
            return m_sVTitle.at(col);
        }
    }
    else if (orientation == Qt::Vertical && role == Qt::TextAlignmentRole)
    {
        return int(Qt::AlignCenter);
    }
    if (orientation == Qt::Horizontal && role == Qt::ForegroundRole)
    {
        Bit32 col = m_pTable->m_nStartColumn + section;
        return m_pTable->RoleStyle(0, m_pTable->GetDefCol(col), NcTableEx::HorHeadColorRole);
    }
    if (orientation == Qt::Horizontal && role == Qt::FontRole)
    {
        Bit32 col = m_pTable->m_nStartColumn + section;
        return m_pTable->RoleStyle(0, m_pTable->GetDefCol(col), NcTableEx::HorHeadFontRole);
    }


    return QVariant();
}

bool NcTableModelEx::setData(const QModelIndex &index, const QVariant &value, int role)
{
    ENMoveDirection direction = MOVE_DEFAULT;

    if (index.isValid() && role == Qt::EditRole)
    {
        NcDataLimit limit = m_pTable->GetInputDataLimit(index.row() + m_pTable->GetStartIndex(), index.column() + m_pTable->m_nStartColumn);
        if (limit.dateType == DTYPE_INT)
        {
            if (value.toInt() < limit.mixInt || value.toInt() > limit.maxInt)
            {
                MessageOut(QObject::TR("输入数据超出范围：%1~%2").arg(limit.mixInt).arg(limit.maxInt));
                return false;
            }
            MessageOut(""); // 输入正确后要消除提示
        }
        else if (limit.dateType == DTYPE_FLOAT)
        {
            // 只要CheckRangeRole返回真，就要检查范围，除了整形外，其他的按浮点检查（无数据类型或默认的类型，都按浮点检查）
            if (value.toDouble() < limit.mixFloat || value.toDouble() > limit.maxFloat)
            {
                MessageOut(QObject::TR("输入数据超出范围：%1~%2").arg(limit.mixFloat, 0, 'f', limit.prec).arg(limit.maxFloat, 0, 'f', limit.prec));
                return false;
            }
            MessageOut(""); // 输入正确后要消除提示
        }

        direction = m_pTable->ContentChanged(index.row() + m_pTable->GetStartIndex(), m_pTable->GetDefCol(index.column() + m_pTable->m_nStartColumn), value.toString());

        if (direction == MOVE_DEFAULT)
        {
            int exType = CommonGetInputDlg()->GetExitRet();
            if (exType == 0)
            {
                m_pTable->ChangeFocus(direction);
            }
            if (exType == 1)
            {
                 m_pTable->ChangeFocus(MOVE_UP);
            }
            else if (exType == 2)
            {
                m_pTable->ChangeFocus(MOVE_DOWN);
            }
        }
        else
        {
            m_pTable->ChangeFocus(direction);
        }

        emit dataChanged(index, index);
    }

    return false;
}

/**
 * @brief NcTableModelEx::SetRowCount 设置显示行数
 * @param rowCount
 */
void NcTableModelEx::SetRowCount(int rowCount)
{
    m_nRowCount = rowCount;
}

/**
 * @brief NcTableModelEx::SetColumnCount 设置显示列数
 * @param columnCount
 */
void NcTableModelEx::SetColumnCount(int columnCount)
{
    m_nColumnCount = columnCount;
}

void NcTableModelEx::SetVerticalHeaderLabels(const QStringList labels)
{
    m_sVTitle = labels;
}

void NcTableModelEx::SetHorizontalHeaderLabels(const QStringList labels)
{
    m_sHTitle = labels;
}

Qt::ItemFlags NcTableModelEx::flags(const QModelIndex &index) const
{
    if (!index.isValid())
        return 0;

    int flag = m_pTable->RoleStyle(index, NcTableEx::FlagRole).toInt();
    return (Qt::ItemFlag)flag;
}

/**
 * @brief NcTableModelEx::ResetModel 重置Model
 */
void NcTableModelEx::ResetModel()
{
    this->beginResetModel();
    this->reset();
    this->endResetModel();
}

/**
 * @brief NcTableModelEx::SlotComboBoxCurrentIndexChanged 响应下拉框
 * @param text
 */
void NcTableModelEx::SlotComboBoxCurrentIndexChanged(const QString & text)
{
    QModelIndex index;
    GetModelIndex(sender(), index);

    m_pTable->ContentChanged(index.row() + m_pTable->GetStartIndex(), m_pTable->GetDefCol(index.column() + m_pTable->m_nStartColumn), text);
    if (m_pTable->GetIsDefContentChangedVaild() == true)
    {
        m_pTable->ContentSet(index.row() + m_pTable->GetStartIndex(), m_pTable->GetDefCol(index.column() + m_pTable->m_nStartColumn), text);
    }
   //m_pTable->ChangeFocus(0);
}

/**
 * @brief NcTableModelEx::GetModelIndex 根据object对象，寻找其对应的modelindex
 * @param object
 * @param index
 */
void NcTableModelEx::GetModelIndex(QObject* object, QModelIndex& index)
{
    for (int row = 0; row < m_nRowCount; row++)
    {
        for (int column = 0; column < m_nColumnCount; column++)
        {
            QWidget *w = m_pTable->indexWidget(this->index(row, column));
            if (w != NULL && (w == object || w->children().contains(object)))
            {
                index = this->index(row, column);
                return;
            }
        }
    }
}

bool NcTableModelEx::eventFilter(QObject *target, QEvent *event)
{
    if (event->type() == QEvent::KeyPress)
    {
        QModelIndex index = m_pTable->currentIndex();
        Bit32 curRow = m_pTable->GetCurrentRow();
        QKeyEvent* keyEvent = static_cast<QKeyEvent*>(event);
        QList<QVariant> hotKeyList = m_pTable->RoleStyle(curRow, m_pTable->GetDefCol(index.column() + m_pTable->m_nStartColumn), NcTableEx::HotKeyRole).toList();
        switch(keyEvent->key())
        {
        case Qt::Key_Up:
        case Qt::Key_Down:
        case Qt::Key_PageUp:
        case Qt::Key_PageDown:
            //有下拉框时，上、下、上翻页及下翻页会在此响应，但此时需要在其父类响应，使用ignore忽略后在其父类中可以收到按键响应
            event->ignore();
            return true;
            break;
        default:
            if (!hotKeyList.isEmpty())
            {
                if (hotKeyList.contains(keyEvent->key()))
                {
                    event->ignore();
                    return true;
                }
            }
            return false;
            break;
        }
    }
    //鼠标单击下拉框或者在下拉框上滑动鼠标滚轮时，nctableex需要知道当前下拉框对应的modelindex，并设置为当前index，否则鼠标与键盘交互时不正确
    if (event->type() == QEvent::MouseButtonPress || event->type() == QEvent::Wheel)
    {
        QModelIndex index;
        GetModelIndex(target, index);
        if (index.isValid())
        {
            m_pTable->setCurrentIndex(index);
        }
        if (m_pTable->IsCheckBoxRole(index) == true && event->type() == QEvent::MouseButtonPress)
        {
            NcCheckBox* checkBox = m_pTable->GetIndexCheckBox(index);
            bool state = checkBox->isChecked();
            QString str = QString::number((Bit32)state);
            return m_pTable->EditItem(index, str);
        }
    }

    return QObject::eventFilter(target, event);
}


bool NcItemDelegate::editorEvent(QEvent *event, QAbstractItemModel *, const QStyleOptionViewItem &, const QModelIndex &)
{
    if (event->type() == QEvent::KeyPress || event->type() == QEvent::MouseButtonDblClick)
    {
        return true;
    }
    return false;
}
