#ifndef LADDERSCENE_H
#define LADDERSCENE_H

#include <QGraphicsScene>
#include <QGraphicsItem>
#include <QPainter>
#include <QFont>
#include <QFontMetrics>
#include <QTimer>
#include <QMap>

#include "ladderdata.h"
#include "ladderitem.h"

class LadderScene : public QGraphicsScene
{
    Q_OBJECT

public:
    explicit LadderScene(QObject *parent = nullptr);
    ~LadderScene();

    // 数据设置
    void setLadderData(LadderData *data);
    
    // 显示区域设置
    void setVisibleArea(int startRow, int startCol, int visibleRows, int visibleCols);
    
    // 模式设置
    void setDebugMode(bool enabled);
    void setEditMode(bool enabled);
    
    // 选择设置
    void setSelection(int row, int col);
    void highlightCell(int row, int col, bool highlight);
    
    // 刷新控制
    void refreshDisplay();
    void forceRefresh();

protected:
    void drawBackground(QPainter *painter, const QRectF &rect) override;
    void drawForeground(QPainter *painter, const QRectF &rect) override;

private slots:
    void onDataChanged(int row, int col);
    void onRowDataChanged(int row);

private:
    // 初始化
    void setupScene();
    void setupFonts();
    
    // 绘制函数
    void drawGrid(QPainter *painter, const QRectF &rect);
    void drawRowNumbers(QPainter *painter, const QRectF &rect);
    void drawTitle(QPainter *painter);
    void drawCells(QPainter *painter, const QRectF &rect);
    void drawCell(QPainter *painter, int row, int col, const SLadCell &cell);
    void drawSelection(QPainter *painter);
    
    // 元件绘制
    void drawContact(QPainter *painter, const QRectF &rect, const SLadCell &cell, CellState state);
    void drawCoil(QPainter *painter, const QRectF &rect, const SLadCell &cell, CellState state);
    void drawFunction(QPainter *painter, const QRectF &rect, const SLadCell &cell, CellState state);
    void drawLine(QPainter *painter, const QRectF &rect, const SLadCell &cell, CellState state);
    void drawEmpty(QPainter *painter, const QRectF &rect);
    
    // 辅助绘制
    void drawCellBorder(QPainter *painter, const QRectF &rect, const SLadCell &cell);
    void drawCellParameter(QPainter *painter, const QRectF &rect, const SLadCell &cell);
    void drawCellComment(QPainter *painter, const QRectF &rect, const SLadCell &cell);
    void drawForceState(QPainter *painter, const QRectF &rect, CellState forceState);
    
    // 工具函数
    QRectF getCellRect(int row, int col) const;
    QColor getCellColor(const SLadCell &cell, CellState state, bool selected = false) const;
    QString getCellParameterText(const SLadCell &cell) const;
    QString getCellCommentText(const SLadCell &cell) const;
    CellType getCellType(uBit16 cmdID) const;
    
    // 坐标转换
    QPointF cellToScene(int row, int col) const;
    QPoint sceneToCell(const QPointF &scenePos) const;

private:
    // 数据源
    LadderData *m_ladderData;
    
    // 显示参数
    int m_startRow;
    int m_startCol;
    int m_visibleRows;
    int m_visibleCols;
    
    // 选择状态
    int m_selectedRow;
    int m_selectedCol;
    
    // 模式标志
    bool m_debugMode;
    bool m_editMode;
    bool m_forceRefresh;
    
    // 字体设置
    QFont m_normalFont;
    QFont m_boldFont;
    QFont m_smallFont;
    QFontMetrics *m_normalMetrics;
    QFontMetrics *m_boldMetrics;
    QFontMetrics *m_smallMetrics;
    
    // 颜色配置
    QColor m_backgroundColor;
    QColor m_gridColor;
    QColor m_textColor;
    QColor m_borderColor;
    QColor m_selectionColor;
    
    // 性能优化
    QMap<int, bool> m_rowCache;         // 行缓存
    QRect m_lastDrawRect;               // 上次绘制区域
    bool m_needsFullRedraw;             // 需要完全重绘
};

#endif // LADDERSCENE_H
