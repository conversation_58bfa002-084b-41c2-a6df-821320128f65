﻿#ifndef ROUNDSCALE_H
#define ROUNDSCALE_H

#include "containerwidget.h"

namespace Ui {
class RoundScale;
}

class RoundScaleGraph:public QObject
{
public:
    enum
    {
        NoOperation = 0x0000,
        AddPoint = 0x0001,
        ClearPoint = 0x0002,
    };
    explicit RoundScaleGraph(QObject *parent = 0):QObject(parent){}
    ~RoundScaleGraph(){}
//    QVector<double>x;
//    QVector<double>y;
    QVector<QVector<double>> vecX;
    QVector<QVector<double>> vecY;
    QColor curColor;
    QVector<double>addx;
    QVector<double>addy;

};

class RoundScale : public ContainerWidget
{
    Q_OBJECT

public:
    explicit RoundScale(QWidget *parent = 0);
    ~RoundScale();

    void paintEvent(QPaintEvent *);
    void SetColor(const QColor &bg, const QColor &ft, const QColor &c1, const QColor &c2, const QColor& c3);
    void replot();
    void resizeEvent(QResizeEvent *ev);
    void LineSetRadius(double r);
    void LineZeroAddPoint(const QVector<QVector<double>> vecx, const QVector<QVector<double>> vecy);
    void LineOneAddPoint(const QVector<QVector<double>> vecx, const QVector<QVector<double>> vecy);
    void LineCrossAddPoint(const QVector<QVector<double>> vecx, const QVector<QVector<double>> vecy);
    void ClearData();
    void SetScaleDist(double dist);
private:
    Ui::RoundScale *ui;
    QColor crdcolor; // 背景色
    QColor fontcolor; // 文本色
    QColor curveAct; // 实际位置曲线颜色
    QColor curveCmd; // 指令位置曲线颜色

    QPoint centrePoint;
    Bit32 resizeCof; // 比例系数，所有图上的点根据

    double mc(double val){return val * resizeCof;} // 乘以比例系数,适应不同分辨率
    double mcX(double val){return (val * resizeCof + centrePoint.x());}
    double mcY(double val){return (val * resizeCof + centrePoint.y());}

    bool m_bShowScaleAreaFlag;      // 显示
    double radius;
    double m_dbScaleDist; // 单位刻度代表距离（单位um）

    RoundScaleGraph *graph0;
    RoundScaleGraph *graph1;
    RoundScaleGraph *graph2;
    QList<RoundScaleGraph *>gList;
    void DrawScaleArea(QPainter &painter);
    void GraphRedraw(QPainter &painter);
    bool CrdTran(double x, double y, double &outx, double &outy);
    void CordRedraw(QPainter &painter);
    void ScaleRedraw(QPainter &painter);
};

#endif // ROUNDSCALE_H
