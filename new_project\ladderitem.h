#ifndef LADDERITEM_H
#define LADDERITEM_H

#include <QGraphicsItem>
#include <QPainter>
#include <QStyleOptionGraphicsItem>
#include <QWidget>
#include <QRectF>
#include <QPointF>

#include "lad_def_compat.h"

class LadderItem : public QGraphicsItem
{
public:
    explicit LadderItem(QGraphicsItem *parent = nullptr);
    ~LadderItem();

    // 设置元件数据
    void setCellData(const SLadCell &cell);
    void setCellState(CellState state);
    void setSelected(bool selected);
    void setForced(bool forced, CellState forceState = CELL_STATE_OFF);
    
    // 获取元件数据
    const SLadCell& getCellData() const { return m_cell; }
    CellState getCellState() const { return m_state; }
    bool isSelected() const { return m_selected; }
    bool isForced() const { return m_forced; }
    
    // 设置显示参数
    void setParameterText(const QString &text);
    void setCommentText(const QString &text);
    void setValueText(const QString &text);
    void setDebugMode(bool enabled);
    
    // QGraphicsItem接口
    QRectF boundingRect() const override;
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;
    QPainterPath shape() const override;
    
    // 碰撞检测
    bool contains(const QPointF &point) const override;
    
    // 类型识别
    enum { Type = UserType + 1 };
    int type() const override { return Type; }

protected:
    // 鼠标事件
    void mousePressEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event) override;
    void hoverEnterEvent(QGraphicsSceneHoverEvent *event) override;
    void hoverLeaveEvent(QGraphicsSceneHoverEvent *event) override;

private:
    // 绘制函数
    void drawContact(QPainter *painter, const QRectF &rect);
    void drawCoil(QPainter *painter, const QRectF &rect);
    void drawFunction(QPainter *painter, const QRectF &rect);
    void drawLine(QPainter *painter, const QRectF &rect);
    void drawEmpty(QPainter *painter, const QRectF &rect);
    
    // 辅助绘制
    void drawBorder(QPainter *painter, const QRectF &rect);
    void drawParameter(QPainter *painter, const QRectF &rect);
    void drawComment(QPainter *painter, const QRectF &rect);
    void drawValue(QPainter *painter, const QRectF &rect);
    void drawForceIndicator(QPainter *painter, const QRectF &rect);
    void drawSelection(QPainter *painter, const QRectF &rect);
    
    // 工具函数
    QColor getStateColor() const;
    CellType getCellType() const;
    QString getFunctionName() const;
    QRectF getContentRect() const;

private:
    // 元件数据
    SLadCell m_cell;
    CellState m_state;
    
    // 显示状态
    bool m_selected;
    bool m_forced;
    CellState m_forceState;
    bool m_hovered;
    bool m_debugMode;
    
    // 显示文本
    QString m_parameterText;
    QString m_commentText;
    QString m_valueText;
    
    // 几何参数
    QRectF m_boundingRect;
    
    // 颜色配置
    static const QColor s_defaultColor;
    static const QColor s_activeColor;
    static const QColor s_inactiveColor;
    static const QColor s_selectedColor;
    static const QColor s_forceOnColor;
    static const QColor s_forceOffColor;
    static const QColor s_hoverColor;
};

#endif // LADDERITEM_H
