﻿#include <QKeyEvent>
#include "nccombobox.h"

NcComboBox::NcComboBox(QWidget *parent):
    QComboBox(parent)
{
}

void NcComboBox::keyPressEvent(QKeyEvent *e)
{
    if(e == NULL)
    {
        return;
    }
    switch (e->key()) {
    case Qt::Key_Up:
    case Qt::Key_Down:
    case Qt::Key_PageUp:
    case Qt::Key_PageDown:
        //QComboBox::keyPressEvent(e);
        break;
    default:
        e->ignore();
        break;
    }
}
