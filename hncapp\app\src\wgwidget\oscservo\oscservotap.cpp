﻿/*
* Copyright (c) 2017, 武汉华中数控股份有限公司软件开发部
* All rights reserved.
*
* 文件名称：oscservotap.cpp
* 文件标识：根据配置管理计划书
* 摘    要：伺服调整-刚性攻丝界面
* 运行平台：linux/winxp
*
* 版    本：1.00
* 作    者：Hnc8-Team
* 日    期：2017年5月24日
* 说    明：
*/

#include <qmath.h>

#include "hncaxis.h"
#include "hncchan.h"
#include "hncchandef.h"
#include "hncmath.h"
#include "hncsys.h"
#include "hncparaman.h"
#include "hncsmpl.h"
#include "smplcalc.h"
#include "ncassert.h"

#include "hmicommon.h"
#include "hotkeycfg.h"
#include "hmioscproc.h"
#include "hmioscservo.h"
#include "oscwave.h"
#include "osclist.h"

#include "oscservotap.h"
#include "ui_oscservotap.h"

OscServoTap::OscServoTap(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscServoTap)
{
    ui->setupUi(this);

    pOscWaveSyncErr = new OscWave(this, HmiOscServo::OSC_SERVO_TAP, "SyncErr");
    pOscWaveSpe = new OscWave(this, HmiOscServo::OSC_SERVO_TAP, "SPE");

    ui->verticalLayout_2->addWidget(pOscWaveSyncErr);
    ui->verticalLayout_2->addWidget(pOscWaveSpe);
    ui->verticalLayout_2->setStretch(0, 1);
    ui->verticalLayout_2->setStretch(1, 1);
    ui->verticalLayout_2->setContentsMargins(0, 0, 0, 0);
    ui->verticalLayout_2->setSpacing(5);

    pOscWaveSpe->CreateGroup(pOscWaveSyncErr->GetWavePlot());

    // 参数初始化
    this->firstFlag = false;

    // 信息区设置
    ui->syncErrMaxLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    ui->syncErrMinLabel->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    ui->labelAxisName->setText("Z");
    ui->labelAxisName->setAlignment(Qt::AlignCenter);
    ui->labelImg->setAlignment(Qt::AlignCenter);
    ui->labelImg->setText(HotKeyCfg::GetDirChgMsg());
    ui->labelImg->setFont(QFont(FONT_TYPE, 10));

    // 列表设置
    posOscList = new OscList(this);
    posOscList->installEventFilter(this);
    ui->gridLayout->addWidget(posOscList);
    posOscList->SetEditAgent(true);

//    ui->leftBtn->setShortcut(QKeySequence(Qt::AltModifier + Qt::Key_Left));
//    ui->rightBtn->setShortcut(QKeySequence(Qt::AltModifier + Qt::Key_Right));
    ui->leftBtn->setFocusPolicy(Qt::NoFocus);
    ui->rightBtn->setFocusPolicy(Qt::NoFocus);
    m_ax = 0;
}

OscServoTap::~OscServoTap()
{
    delete ui;
}

void OscServoTap::LoadInfo()
{
    Bit32 axis_no = 0;
    Bit32 ncu_cycle = 1000, moving_unit = 0;
    Bit32 ch = ActiveChan();
    Bit32 macType = GetOscprocCurCh(ch);
    Bit32 type = 0;
    Bit32 offset = 0;
    Bit32 len = 0;
    fBit64 syncErrMaxVal = 0.000;
    fBit64 syncErrMinVal = 0.000;

    Bit64 *chn_addr1 = NULL, *chn_addr2 = NULL;
    Bit32 i = 0, smpl_period = oscproc_get_smpl_period(), tmp = 0;
    fBit64 fdelta = 0.0;
    fBit64 tmpThread = 0.0;
    Bit32 client = HmiOscServo::oscservo_get_sampl_client();
    Bit32 stop_pos = oscproc_get_total();
    fBit64 ftmp = 0;

    if(oscproc_get_total() > 0)
    {
        HNC_SystemGetValue(HNC_SYS_MOVE_UNIT, &moving_unit);
        ParaGetIntVal(PARAMAN_FILE_NCU, 0, PAR_NCU_CYCLE, &ncu_cycle);

        if (macType == 1 && HmiOscServo::s_Conf[ch].stTapConf.tapDir == 1)
        {
            tmpThread = -(HmiOscServo::s_Conf[ch].stTapConf.thread_lead);
        }
        else
        {
            tmpThread = HmiOscServo::s_Conf[ch].stTapConf.thread_lead;
        }
        HNC_SamplGetConfig(client, 0, type, axis_no, offset, len);
        chn_addr1 = oscproc_get_smpldata(0);
        chn_addr2 = oscproc_get_smpldata(1);
        if (chn_addr1 == NULL || chn_addr2 == NULL)
        {
            return;
        }
//        for (ftmp = 0, fdelta = 0, i = 0; i < start_pos; i++)
//        {
//            ftmp = smpl_calc_spd_coef(axis_no,5)*chn_addr1[i] - smpl_calc_tapvel_coef(1, tmpThread, samplClient)*chn_addr2[i]; // 速度差
//            fdelta = ftmp*smpl_period*1000 + fdelta; // um
//        }
        tmp = i;
        ftmp = HmiOscServo::SmplCalcTapvelCoef(1, tmpThread, client)*chn_addr2[tmp] - smpl_calc_spd_coef(axis_no,5)*chn_addr1[tmp]; // 速度差
        fdelta = ftmp*smpl_period*1000 + ftmp; // um
        syncErrMaxVal = syncErrMinVal = fdelta;
        for (tmp = i+1; tmp < stop_pos; tmp++)
        {
            ftmp = HmiOscServo::SmplCalcTapvelCoef(1, tmpThread, client)*chn_addr2[tmp] - smpl_calc_spd_coef(axis_no,5)*chn_addr1[tmp];
            fdelta = ftmp*smpl_period*1000 + fdelta;
            if (fdelta > syncErrMaxVal)
            {
                syncErrMaxVal = fdelta;
            }
            else if (fdelta < syncErrMinVal)
            {
                syncErrMinVal = fdelta;
            }
        }

        if (HNC_DoubleCompare(fabs(syncErrMaxVal), fabs(syncErrMinVal)) >= 0)
        {
            HmiOscServo::OscservoReportRecord(fabs(syncErrMaxVal), 0);
        }
        else
        {
            HmiOscServo::OscservoReportRecord(fabs(syncErrMinVal), 0);
        }
    }
    ui->syncErrMaxLabel->setText(QString::number(syncErrMaxVal, 'f',3));
    ui->syncErrMinLabel->setText(QString::number(syncErrMinVal, 'f',3));
}

QStringList OscServoTap::GetParmList()
{
    Bit32 totalNo = 0;
	Bit32 paramId = 0;
    QStringList list;
    list.clear();

    totalNo = HmiOscServo::ServoParmGetCount(HmiOscServo::OSC_SERVO_TAP);

    for(Bit32 i = 0; i < totalNo; i++)
    {
		paramId = HmiOscServo::ServoParRow2Id(i);
		if (IsParamanVisible(paramId))
		{
			list.append(QString::number(paramId));
		}
    }
    return list;
}

void OscServoTap::LoadAxisVal()
{
    Bit32 ch = ActiveChan();
    QString axisName = "";
    Bit32 cur2Axis = HmiOscServo::GetCur2AxisIndex();
    Bit32 ax = 0;
    QStringList strList;

    strList.clear();

    if(cur2Axis == 1)
    {
        ax = HmiOscServo::s_Conf[ch].stTapConf.sync_axis;
    }
    else
    {
        ax = HmiOscServo::s_Conf[ch].stTapConf.tap_axis;
    }

    m_ax = ax;
    axisName = HmiOscServo::OscAxisToName(ax);
    ui->labelAxisName->setText(TR("%1轴").arg(axisName));

    strList = GetParmList();
    posOscList->RefresWidget(strList);
    posOscList->LoadWidget();
}

void OscServoTap::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    if(messageid == MsgData::SETFOCUS)
    {
        bool ret = posOscList->SetTableFocus();
        if(messageValue == "CLEARFOCUS" || ret == false)
        {
            posOscList->ClearTableFocus();
        }
    }
    else if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        QStringList strList;
        QString axisName = "";
        Bit32 cur2Axis = HmiOscServo::GetCur2AxisIndex();
        Bit32 ax = 0;
        Bit32 ch = ActiveChan();
        strList.clear();
		if(cur2Axis == 1)
        {
            ax = HmiOscServo::s_Conf[ch].stTapConf.sync_axis;
        }
        else
        {
            ax = HmiOscServo::s_Conf[ch].stTapConf.tap_axis;
        }

        if(messageValue == "INIT" || m_ax != ax) // 初始化，清除上次在该界面记住的当前行
        {
            strList = GetParmList();
            posOscList->RefresWidget(strList);
			m_ax = ax;
        }
        this->LoadInfo();
        this->OnBtFlagChange();
        posOscList->LoadWidget();
        axisName = HmiOscServo::OscAxisToName(ax);
        ui->labelAxisName->setText(TR("%1轴").arg(axisName));
        this->SetColorStyle();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::GENERAL)
    {
        if (messageValue == "MSG_OSCSERVOSTART")
        {
            this->Reset(); // 开始采样时才清除上一次的图形
        }
        else if(messageValue == "MSG_OSCSERVOSAVE")
        {
            HmiOscServo::ParmSave();
            posOscList->LoadWidget();
        }
        else if (messageValue == "MSG_OSCSERVOSTOP")
        {
            this->LoadInfo();
        }
        else if(messageValue == "OSCSERVOCOLOR")
        {
            this->SetColorStyle();
        }

    }
    else if (messageid == MsgData::REFRESH)
    {
        this->Refresh();

		Bit32 devAxisType = HNC_ParamanGetAxisDevType(m_ax);

		if (devAxisType == DEV_ETHERCAT_AXIS || devAxisType == DEV_NCUC_AXIS)
		{
            posOscList->Refresh();
		}
    }
    else if(messageid == MsgData::KEYBOARD && messageValue == "TURNTABSLEFT")
    {
        this->on_leftBtn_clicked();
    }
    else if(messageid == MsgData::KEYBOARD && messageValue == "TURNTABSRIGHT")
    {
        this->on_rightBtn_clicked();
    }
}

void OscServoTap::resizeEvent(QResizeEvent *)
{
    this->firstFlag = false;
}

bool OscServoTap::eventFilter(QObject *target, QEvent *event)
{
    if(event->type() == QEvent::Paint && !firstFlag) // Paint事件在ReSize事件之后响应，用于图片第一次重绘
    {
        ui->leftBtn->setIconSize(ui->leftBtn->size());
        ui->rightBtn->setIconSize(ui->rightBtn->size());
        //ui->leftBtn->setIcon(PixMapToSize(ui->leftBtn->size(), "../pic/left-2.png"));
        //ui->rightBtn->setIcon(PixMapToSize(ui->rightBtn->size(), "../pic/right-1.png"));

        this->firstFlag = true;
        this->OnBtFlagChange(); // 解决初次进入界面时，redraw消息在paint事件前响应，导致界面刷新错误
    }
    return QObject::eventFilter(target, event);
}

void OscServoTap::OnBtFlagChange()
{
    Bit32 curAxesIndex = HmiOscServo::GetCur2AxisIndex();
    if(curAxesIndex == 0)
    {
        HmiOscServo::SetCurAxesIndex(0);
        ui->leftBtn->setProperty("valid",false);
        ui->rightBtn->setProperty("valid",true);
        ui->leftBtn->style()->polish(ui->leftBtn);
        ui->rightBtn->style()->polish(ui->rightBtn);
        return;
    }
    else if(curAxesIndex == 1)
    {
        ui->leftBtn->setProperty("valid",true);
        ui->rightBtn->setProperty("valid",false);
        ui->leftBtn->style()->polish(ui->leftBtn);
        ui->rightBtn->style()->polish(ui->rightBtn);
        return;
    }
}

void OscServoTap::on_leftBtn_clicked()
{
    Bit32 cur2AxisIndex = HmiOscServo::GetCur2AxisIndex();

    if(cur2AxisIndex == 1)
    {
        HmiOscServo::SetCur2AxisIndex(0);
        this->LoadAxisVal();
        this->OnBtFlagChange();
    }
}

void OscServoTap::on_rightBtn_clicked()
{
    Bit32 curAxesIndex = HmiOscServo::GetCur2AxisIndex();

    if(curAxesIndex == 0)
    {
        HmiOscServo::SetCur2AxisIndex(1);
        this->LoadAxisVal();
        this->OnBtFlagChange();
    }
}

void OscServoTap::Reset()
{ // 清空图形
    pOscWaveSpe->ClearPoint();
    pOscWaveSyncErr->ClearPoint();
    this->lastEndPos = 0;
}

void OscServoTap::Refresh()
{
    QVector<double> x;
    QVector<double> y0;
    QVector<double> y1;
    QVector<double> y2;

    Bit32 i = 0;
    Bit32 stPos = 0;
    Bit32 edPos= 0;
    Bit64 *ch0_addr = NULL;
    Bit64 *ch1_addr = NULL;

    Bit32 client = HmiOscServo::oscservo_get_sampl_client();
    Bit32 ch = ActiveChan();
    Bit32 macType = GetMacType(ch);
    Bit32 axisNo = 0;
    Bit32 type = 0;
    Bit32 offset = 0;
    Bit32 len = 0;

    Bit32 smpl_period = 1;
    fBit64 ftmp = 0.0;
    fBit64 deltaVal = 0.0;
    fBit64 compVal = 0.0;

    Bit32 tmp = 0;
    fBit64 yConf = 0;
    Bit32 off = 0;

    if (oscproc_get_stat() != OSC_PROC_START)
    {
        this->lastEndPos = 0; // 停止后需要置零
        return;
    }

    x.clear();
    y0.clear();
    y1.clear();
    y2.clear();

    stPos = this->lastEndPos;
    edPos = oscproc_get_pos();
    this->lastEndPos = edPos;

    ch0_addr = oscproc_get_smpldata(0);
    ch1_addr = oscproc_get_smpldata(1);
    smpl_period = oscproc_get_smpl_period();
    if (NULL == ch0_addr || NULL == ch1_addr)
    {
        return;
    }

    HNC_AxisGetValue(HNC_AXIS_WCS_ZERO, axisNo, &tmp);
    HNC_SamplGetConfig(client, 0, type, axisNo, offset, len);

    if (macType == 1 && HmiOscServo::s_Conf[ch].stTapConf.tapDir == 1)
    {
        ftmp = -(HmiOscServo::s_Conf[ch].stTapConf.thread_lead);
    }
    else
    {
        ftmp = HmiOscServo::s_Conf[ch].stTapConf.thread_lead;
    }

    for(i = 0; i < stPos; i++)
    {
        deltaVal = HmiOscServo::SmplCalcTapvelCoef(1, ftmp, client)*ch1_addr[i] - smpl_calc_spd_coef(axisNo, 5)*ch0_addr[i];
        compVal = deltaVal*smpl_period*1000 + compVal;
    }

    for (i = stPos+1; i < edPos; ++i)
    {
        deltaVal = HmiOscServo::SmplCalcTapvelCoef(1, ftmp, client)*ch1_addr[i] - smpl_calc_spd_coef(axisNo, 5)*ch0_addr[i];
        compVal = deltaVal*smpl_period*1000 + compVal;
        y0.append(compVal); // 同步误差：um
        yConf = smpl_calc_spd_coef(axisNo, 5)*60000.0;
        off = 0;
        y1.append(ch0_addr[i] * yConf + off); // 攻丝轴速度：mm/min
        yConf = HmiOscServo::smpl_calc_tapvel_coef(1, client)*60000.0;
        off = 0;
        y2.append(ch1_addr[i] *yConf + off); // 旋转轴速度： r/min

        x.append(i * oscproc_get_smpl_period());
    }

    pOscWaveSyncErr->LineZeroAddPoint(x, y0);
    pOscWaveSyncErr->WaveReplot();
    pOscWaveSpe->LineZeroAddPoint(x, y1);
    pOscWaveSpe->LineOneAddPoint(x, y2);
    pOscWaveSpe->WaveReplot();
}

void OscServoTap::SetColorStyle()
{
    // 默认黑色风格
    QColor bk(0, 0, 0); // 背景
    QColor gd(0, 0, 0); // 网格
    QColor ft(0, 0, 0); // 字体颜色
    QColor c1(0, 0, 0); // 曲线1
    QColor c2(0, 0, 0); // 曲线2
    QColor c3(0, 0, 0); // 曲线3
    QColor c4(0, 0, 0); // 曲线4

    HmiOscServo::GetColor(bk, gd, ft, c1, c2, c3, c4);

    QPalette palette;
    palette.setColor(QPalette::Background, bk);
    ui->frame->setAutoFillBackground(true);
    ui->frame->setPalette(palette);

    pOscWaveSyncErr->SetColor(bk, gd, ft, c1, c4, c3, c4);
    pOscWaveSpe->SetColor(bk, gd, ft, c2, c3, c3, c4);
}
