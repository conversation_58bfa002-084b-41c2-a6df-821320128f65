﻿<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OscServoSync</class>
 <widget class="QWidget" name="OscServoSync">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>706</width>
    <height>446</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="2,1">
   <property name="spacing">
    <number>2</number>
   </property>
   <property name="leftMargin">
    <number>2</number>
   </property>
   <property name="topMargin">
    <number>2</number>
   </property>
   <property name="rightMargin">
    <number>2</number>
   </property>
   <property name="bottomMargin">
    <number>2</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="backgrd3" native="true">
     <property name="selected" stdset="0">
      <bool>false</bool>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3" stretch="1">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="widget_3" native="true">
        <layout class="QVBoxLayout" name="verticalLayout_5">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QWidget" name="widget_5" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>5</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="labelTitle">
              <property name="font">
               <font>
                <family>微软雅黑</family>
                <pointsize>12</pointsize>
                <weight>75</weight>
                <bold>true</bold>
               </font>
              </property>
              <property name="text">
               <string>信息</string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <property name="leftMargin">
            <number>10</number>
           </property>
           <item>
            <widget class="QLabel" name="label_9">
             <property name="font">
              <font>
               <family>微软雅黑</family>
               <pointsize>12</pointsize>
               <weight>50</weight>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>最大位置偏差</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="3,1">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>50</number>
           </property>
           <item>
            <widget class="QLabel" name="labelMaxPosErr">
             <property name="sizePolicy">
              <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
               <horstretch>0</horstretch>
               <verstretch>0</verstretch>
              </sizepolicy>
             </property>
             <property name="font">
              <font>
               <family>微软雅黑</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>0.000</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label">
             <property name="font">
              <font>
               <family>微软雅黑</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>mm</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_6">
           <property name="leftMargin">
            <number>10</number>
           </property>
           <item>
            <widget class="QLabel" name="label_12">
             <property name="font">
              <font>
               <family>微软雅黑</family>
               <pointsize>12</pointsize>
               <weight>50</weight>
               <bold>false</bold>
              </font>
             </property>
             <property name="text">
              <string>最大电流偏差</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="3,1">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>20</number>
           </property>
           <property name="rightMargin">
            <number>50</number>
           </property>
           <item>
            <widget class="QLabel" name="labelMaxCurrentErr">
             <property name="font">
              <font>
               <family>微软雅黑</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>0.000</string>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_2">
             <property name="font">
              <font>
               <family>微软雅黑</family>
               <pointsize>12</pointsize>
              </font>
             </property>
             <property name="text">
              <string>A</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
