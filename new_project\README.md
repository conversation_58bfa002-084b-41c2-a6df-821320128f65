# PLC梯形图监控系统

基于Qt5+QGraphicsView重构的PLC梯形图监控系统，完全兼容原有hzcnc项目的梯形图模块功能。

## 项目特点

- **完全重构**: 使用Qt5+QGraphicsView架构重新设计
- **功能兼容**: 对外表现与原有系统完全一致
- **性能优化**: 采用现代Qt技术，提供更好的性能和用户体验
- **模块化设计**: 清晰的代码结构，便于维护和扩展

## 主要功能

### 1. 梯形图显示
- 支持标准PLC梯形图元件显示
- 实时状态监控和颜色指示
- 网格对齐和行号显示
- 支持缩放和滚动操作

### 2. 调试模式
- 实时显示元件通断状态
- 寄存器值监控
- 强制允许/禁止功能
- 变化检测和高亮显示

### 3. 编辑模式
- 元件选择和编辑
- 参数修改
- 注释管理
- 符号表支持

### 4. 数据管理
- 兼容原有数据格式
- 支持文件加载和保存
- 多子程序支持
- 符号表管理

## 技术架构

### 核心类结构

```
MainWindow          - 主窗口，负责UI布局和用户交互
├── LadderView      - 梯形图视图，基于QGraphicsView
│   └── LadderScene - 梯形图场景，负责绘制和事件处理
│       └── LadderItem - 梯形图元件项，可选的图形项实现
└── LadderData      - 数据管理，负责数据存储和状态计算
```

### 数据兼容性

- `SLadCell`: 梯形图元件单元结构
- `SLadRow`: 梯形图行结构  
- `SLadReg`: 寄存器结构
- 完全兼容原有的数据类型定义

### 绘制系统

- 使用QGraphicsView/QGraphicsScene架构
- 支持硬件加速渲染
- 优化的重绘机制
- 响应式布局设计

## 编译和运行

### 环境要求
- Qt 5.12 或更高版本
- C++11 支持的编译器
- Windows/Linux 平台

### 编译步骤
```bash
cd new_project
qmake plc.pro
make
```

### 运行
```bash
./plc_ladder_viewer
```

## 使用说明

### 基本操作
- **F9**: 切换调试模式
- **F2**: 切换编辑模式  
- **F5**: 强制刷新显示
- **Ctrl+滚轮**: 缩放视图
- **方向键**: 移动选择
- **鼠标拖拽**: 滚动视图

### 调试功能
- 红色: 元件接通状态
- 灰色: 元件断开状态
- 绿色: 强制接通状态
- 橙色: 强制断开状态
- 蓝色: 选中状态

### 编辑功能
- 单击选择元件
- 双击编辑元件参数
- 右键显示上下文菜单
- 支持键盘快捷操作

## 扩展开发

### 添加新元件类型
1. 在`LAD_CMDID_COMPAT`枚举中添加新指令ID
2. 在`LadderScene::getCellType()`中添加类型判断
3. 实现对应的绘制函数
4. 更新状态计算逻辑

### 自定义绘制
- 重写`LadderScene::drawCell()`方法
- 实现自定义的`LadderItem`子类
- 扩展颜色和样式配置

### 数据接口
- 实现`LadderData`的虚拟接口
- 连接到实际的PLC数据源
- 添加网络通信支持

## 注意事项

1. **数据兼容性**: 确保与原有系统的数据格式完全兼容
2. **性能优化**: 大量数据时注意绘制性能优化
3. **线程安全**: 数据访问使用互斥锁保护
4. **内存管理**: 注意Qt对象的生命周期管理

## 版本历史

- v1.0.0: 初始版本，基本功能实现
- 计划中: 网络通信、高级编辑功能、插件系统

## 联系方式

如有问题或建议，请联系开发团队。
