<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>800</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>PLC梯形图监控</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/icons/ladder.png</normaloff>:/icons/ladder.png</iconset>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <property name="spacing">
     <number>2</number>
    </property>
    <property name="leftMargin">
     <number>2</number>
    </property>
    <property name="topMargin">
     <number>2</number>
    </property>
    <property name="rightMargin">
     <number>2</number>
    </property>
    <property name="bottomMargin">
     <number>2</number>
    </property>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1200</width>
     <height>22</height>
    </rect>
   </property>
   <widget class="QMenu" name="menuFile">
    <property name="title">
     <string>文件(&amp;F)</string>
    </property>
    <addaction name="actionOpen"/>
    <addaction name="actionSave"/>
    <addaction name="actionSaveAs"/>
    <addaction name="separator"/>
    <addaction name="actionExit"/>
   </widget>
   <widget class="QMenu" name="menuView">
    <property name="title">
     <string>视图(&amp;V)</string>
    </property>
    <addaction name="actionZoomIn"/>
    <addaction name="actionZoomOut"/>
    <addaction name="actionZoomReset"/>
    <addaction name="separator"/>
    <addaction name="actionRefresh"/>
   </widget>
   <widget class="QMenu" name="menuMode">
    <property name="title">
     <string>模式(&amp;M)</string>
    </property>
    <addaction name="actionDebugMode"/>
    <addaction name="actionEditMode"/>
   </widget>
   <widget class="QMenu" name="menuHelp">
    <property name="title">
     <string>帮助(&amp;H)</string>
    </property>
    <addaction name="actionAbout"/>
   </widget>
   <addaction name="menuFile"/>
   <addaction name="menuView"/>
   <addaction name="menuMode"/>
   <addaction name="menuHelp"/>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>工具栏</string>
   </property>
   <property name="toolButtonStyle">
    <enum>Qt::ToolButtonTextUnderIcon</enum>
   </property>
   <attribute name="toolBarArea">
    <enum>TopToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
  </widget>
  <action name="actionOpen">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/open.png</normaloff>:/icons/open.png</iconset>
   </property>
   <property name="text">
    <string>打开(&amp;O)</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+O</string>
   </property>
  </action>
  <action name="actionSave">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/save.png</normaloff>:/icons/save.png</iconset>
   </property>
   <property name="text">
    <string>保存(&amp;S)</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="actionSaveAs">
   <property name="text">
    <string>另存为(&amp;A)</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+Shift+S</string>
   </property>
  </action>
  <action name="actionExit">
   <property name="text">
    <string>退出(&amp;X)</string>
   </property>
   <property name="shortcut">
    <string>Alt+F4</string>
   </property>
  </action>
  <action name="actionZoomIn">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/zoom_in.png</normaloff>:/icons/zoom_in.png</iconset>
   </property>
   <property name="text">
    <string>放大</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+=</string>
   </property>
  </action>
  <action name="actionZoomOut">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/zoom_out.png</normaloff>:/icons/zoom_out.png</iconset>
   </property>
   <property name="text">
    <string>缩小</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+-</string>
   </property>
  </action>
  <action name="actionZoomReset">
   <property name="text">
    <string>重置缩放</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+0</string>
   </property>
  </action>
  <action name="actionRefresh">
   <property name="icon">
    <iconset>
     <normaloff>:/icons/refresh.png</normaloff>:/icons/refresh.png</iconset>
   </property>
   <property name="text">
    <string>刷新</string>
   </property>
   <property name="shortcut">
    <string>F5</string>
   </property>
  </action>
  <action name="actionDebugMode">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset>
     <normaloff>:/icons/debug.png</normaloff>:/icons/debug.png</iconset>
   </property>
   <property name="text">
    <string>调试模式</string>
   </property>
   <property name="shortcut">
    <string>F9</string>
   </property>
  </action>
  <action name="actionEditMode">
   <property name="checkable">
    <bool>true</bool>
   </property>
   <property name="icon">
    <iconset>
     <normaloff>:/icons/edit.png</normaloff>:/icons/edit.png</iconset>
   </property>
   <property name="text">
    <string>编辑模式</string>
   </property>
   <property name="shortcut">
    <string>F2</string>
   </property>
  </action>
  <action name="actionAbout">
   <property name="text">
    <string>关于</string>
   </property>
  </action>
 </widget>
 <resources/>
 <connections/>
</ui>
