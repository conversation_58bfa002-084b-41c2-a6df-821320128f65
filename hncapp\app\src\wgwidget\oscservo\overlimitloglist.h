﻿/*!
 * @file overlimitloglist.h
 * @brief 主轴全生命周期负荷超限日志列表控件
 * @note
 *
 * @version V1.00
 * @date 2021/3/6
 * <AUTHOR> Team
 * @copyright 武汉华中数控股份有限公司软件开发部
 */

#ifndef OVERLIMITLOGLIST_H
#define OVERLIMITLOGLIST_H

#include "containerwidget.h"

namespace Ui {
class OverLimitLogList;
}

enum _OVERLIMIT_LOG_TYPE
{
    POWER_LIMIT_LOG = 0,        // 功率超限
    ROTATE_SPEED_LIMIT_LOG,     // 转速超限
    TEMPERATURE_LIMIT_LOG,		// 温度超限
    VIBRATE_LIMIT_LOG,			// 振动超限
    OVERLIMIT_LOGTYPE_NUM
};

class OverLimitLogList : public ContainerWidget
{
    Q_OBJECT

public:
    explicit OverLimitLogList(Bit32 type, QWidget *parent = 0);
    ~OverLimitLogList();

    void FrameWorkMessage(QVariant messageid, QVariant messageValue);
    void ClearSelection();

protected:
    bool eventFilter(QObject *wg, QEvent *event);
    void keyPressEvent(QKeyEvent *ev);
    void wheelEvent(QWheelEvent *wheelEvt);

private:
    Ui::OverLimitLogList *ui;

    bool firstFlag;
    Bit32 m_nListType;
    Bit32 curFirstRow;
    Bit32 oldFirstRow;

    QVector<QString> logData;

    void ImportLogFileData();
    void OnVScrollBarRefresh(Bit32 maxNum);
    void GetLogListContent(Bit32 index, Bit32 *pLogNum, QString *stTimeStr, QString *peakStr, QString *durationStr);
    void resizeEvent(QResizeEvent *);

private slots:
    void OnWidgetRefresh(Bit32 firstRow, Bit32 flag = 0);
    void on_verticalScrollBar_valueChanged(int value);
};

#endif // OVERLIMITLOGLIST_H
