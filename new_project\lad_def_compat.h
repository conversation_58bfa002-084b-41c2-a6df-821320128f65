#ifndef LAD_DEF_COMPAT_H
#define LAD_DEF_COMPAT_H

#include <QtCore/QtGlobal>

// 兼容原有数据类型定义
typedef quint8  Bit8;
typedef qint8   sBit8;
typedef quint16 Bit16;
typedef qint16  sBit16;
typedef quint32 Bit32;
typedef qint32  sBit32;
typedef quint16 uBit16;
typedef quint32 uBit32;

// 梯形图常量定义
#define CELL_PER_ROW    10      // 梯形图每一行元件个数
#define CELL_PER_COL    20      // 梯形图每页显示行数
#define CELL_WIDTH      60      // 元件单元宽度
#define CELL_HEIGHT     40      // 元件单元高度
#define LEFT_BLANK      80      // 左边空白区域宽度
#define TOP_BLANK       40      // 顶部空白区域高度

// 梯形图指令ID枚举（简化版本）
enum LAD_CMDID_COMPAT
{
    I_VOR = 0,      // 空
    I_LINE,         // 直线
    I_LDT,          // 
    I_LD,           // 常开触点
    I_LDI,          // 常闭触点
    I_LDC,          // 
    I_LDNC,         // 
    I_OUT,          // 输出线圈
    I_OOUT,         // 
    I_SET,          // 置位
    I_RST,          // 复位
    I_AND,          // 与
    I_ANI,          // 与非
    I_ANC,          // 
    I_ANNC,         // 
    I_OR,           // 或
    I_ORI,          // 或非
    I_ORC,          // 
    I_ORNC,         // 
    I_TMR = 36,     // 定时器
    I_CTR = 44,     // 计数器
    I_ADD = 70,     // 加法
    I_SUB,          // 减法
    I_MUL,          // 乘法
    I_DIV,          // 除法
    I_MOV = 82,     // 传送
    I_CMP = 80,     // 比较
    I_TOTAL
};

// 寄存器结构
struct SLadReg
{
    Bit32 index;        // 寄存器下标
    Bit8 reg_type;      // 寄存器类型
    Bit8 bit;           // 寄存器位号
    Bit8 reserv[2];     // 预留字段
};

// 梯形图元件单元结构
struct SLadCell
{
    Bit8 parmmask;      // 参数掩码
    Bit8 parallel;      // 并线
    Bit8 rectmask;      // 边框掩码
    Bit8 reserv;
    Bit8 w_pos, h_pos;  // 位置
    uBit16 cmdID;       // 指令ID
    Bit32 cmd_idx;      // 指令索引
    SLadReg reg;        // 寄存器
};

// 梯形图行结构
struct SLadRow
{
    SLadCell dft_cell[CELL_PER_ROW];
};

// 颜色定义
enum LadderColors
{
    LAD_COLOR_DEFAULT = 0,      // 默认颜色
    LAD_COLOR_BACKGROUND,       // 背景颜色
    LAD_COLOR_ACTIVE,           // 激活状态颜色
    LAD_COLOR_INACTIVE,         // 非激活状态颜色
    LAD_COLOR_ALLOW,            // 允许状态颜色
    LAD_COLOR_FORBID,           // 禁止状态颜色
    LAD_COLOR_SELECTED          // 选中状态颜色
};

// 元件类型定义
enum CellType
{
    CELL_TYPE_EMPTY = 0,        // 空元件
    CELL_TYPE_CONTACT,          // 触点
    CELL_TYPE_COIL,             // 线圈
    CELL_TYPE_FUNCTION,         // 功能块
    CELL_TYPE_LINE_H,           // 水平线
    CELL_TYPE_LINE_V            // 垂直线
};

// 元件状态定义
enum CellState
{
    CELL_STATE_OFF = 0,         // 断开
    CELL_STATE_ON,              // 接通
    CELL_STATE_FORCE_ON,        // 强制接通
    CELL_STATE_FORCE_OFF        // 强制断开
};

#endif // LAD_DEF_COMPAT_H
