﻿
#include <qmath.h>
#include <QPainter>

#include "hncmath.h"

#include "common.h"
#include "hmioscservo.h"
#include "staticdata.h"
#include "hmipaintercolor.h"

#include "roundwave.h"
#include "ui_roundwave.h"

const Bit32 DEFAULT_STD_RADIUS = 25;            // 默认

#define OSC_SERVO_RASTER_VALID_MAX (1.14705882)
#define OSC_SERVO_RASTER_VALID_MIN (0.85294118)

RoundWave::RoundWave(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::RoundWave)
{
    ui->setupUi(this);
    this->centrePoint=QPoint(size().width() / 2, this->size().height() / 2);

    graph0 = new RoundwaveGraph(this);
    graph1 = new RoundwaveGraph(this);
    gList.append(graph0);
    gList.append(graph1);

    fontcolor = HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_BLACK_FONT);

    this->radius = 1;
    //m_dbStdCirRadius = DEFAULT_STD_RADIUS;
    scaleAreaText = "";
    if (HmiOscServo::GetCurOscWidget() == HmiOscServo::OSC_SERVO_CIR)
    {
        m_dbStdCirRadius = DEFAULT_STD_RADIUS;
    }
    else
    {
        m_dbStdCirRadius = DEFAULT_STD_RADIUS + 15;
    }
    m_dbScale = 3;
    m_dbScaleDist = 10;

    axisNameX = "X";
    axisNameY = "Y";
    m_nShowScaleAreaRadius = -1;
    m_bDotRoundShow = true;
    m_bZoomFlag = true;
    m_bShowScaleAreaFlag = false;

    m_nScaleX = 0;
    m_nScaleY = 0;
}

RoundWave::~RoundWave()
{
    delete ui;
}

void RoundWave::paintEvent(QPaintEvent *)
{
    QPainter painter;
    painter.begin(this);
    painter.setRenderHint(QPainter::Antialiasing);

    CordRedraw(painter);
    if (HmiOscServo::GetCurOscWidget() == HmiOscServo::OSC_SERVO_CIR)
    {
        ScaleRedraw(painter);
        RadAndSpeRedraw(painter);
		
		if (m_bShowScaleAreaFlag)
	    {
	        DrawScaleArea(painter);
	    }
    }
    else if (HmiOscServo::GetCurOscWidget() == HmiOscServo::OSC_SERVO_RASTER)
    {
        CordRedraw(painter);
        RasterScaleRedraw(painter);
    }

//    ScaleRedraw(painter);
//    CordRedraw(painter);
//    RadAndSpeRedraw(painter);

    GraphRedraw(painter);

    painter.end();
}

void RoundWave::resizeEvent(QResizeEvent *)
{
    this->resizeCof = this->height() / 100;
    this->centrePoint=QPoint(size().width() / 2, this->size().height() / 2);
}

void RoundWave::replot()
{
    this->repaint();
}

void RoundWave::SetColor(const QColor &bg, const QColor &ft,
                         const QColor &c1, const QColor &c2)
{
    crdcolor = bg; // 背景色
    fontcolor = ft; // 文本色
    graph0->curColor = c1; // 实际位置曲线颜色
    graph1->curColor = c2; // 指令位置曲线颜色
}

void RoundWave::CordRedraw(QPainter &painter, double rate)
{ // 坐标绘制
    painter.save();
    if(m_bDotRoundShow)
    {
        if (HmiOscServo::GetCurOscWidget() == HmiOscServo::OSC_SERVO_RASTER)
        {
            painter.setPen(QPen(HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_BLACK_CURVE3), 1 / rate, Qt::SolidLine, Qt::RoundCap));
        }
        else
        {
             painter.setPen(QPen(HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_CIRGRID), 1 / rate, Qt::DotLine, Qt::RoundCap));
        }

//        painter.drawEllipse(mcX(-25), mcY(-25), mc(25) * 2, mc(25)*2);
        painter.drawEllipse(mcX(0-m_dbStdCirRadius), mcY(0-m_dbStdCirRadius), mc(m_dbStdCirRadius) * 2, mc(m_dbStdCirRadius)*2);
    }

    // 中心点
    painter.setPen(QPen(HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_CIRGRID), 0.5, Qt::SolidLine, Qt::RoundCap));
    painter.drawLine(mcX(1), mcY(0),mcX(-1), mcY(0));
    painter.drawLine(mcX(0), mcY(1),mcX(0), mcY(-1));

    Bit32 cirR = (Bit32)m_dbStdCirRadius;
    Bit32 gap = (Bit32)m_dbScale; // 短线之间间隔3
    Bit32 harfLen = 2; // 短线一半长度2
    for (Bit32 i = 0; i <= 5; i++)
    { // 4个象限、8个方向
        if (i == 5)
        {// 最后一根线较长
            harfLen = 3;
        }
        else
        {
            harfLen = 2;
        }
        painter.drawLine(mcX(-cirR + gap*i), mcY(-harfLen), mcX(-cirR+gap*i), mcY(harfLen));
        painter.drawLine(mcX(cirR + gap*i), mcY(-harfLen), mcX(cirR+gap*i), mcY(harfLen));
        painter.drawLine(mcX(-harfLen), mcY(-cirR+gap*i), mcX(harfLen), mcY(-cirR+gap*i));
        painter.drawLine(mcX(-harfLen), mcY(cirR+gap*i), mcX(harfLen), mcY(cirR+gap*i));

        if (0 != i)
        { // 避免第一条线重复绘制
            painter.drawLine(mcX(-cirR - gap*i), mcY(-harfLen), mcX(-cirR-gap*i), mcY(harfLen));
            painter.drawLine(mcX(cirR - gap*i), mcY(-harfLen), mcX(cirR-gap*i), mcY(harfLen));
            painter.drawLine(mcX(-harfLen), mcY(-cirR-gap*i), mcX(harfLen), mcY(-cirR-gap*i));
            painter.drawLine(mcX(-harfLen), mcY(cirR-gap*i), mcX(harfLen), mcY(cirR-gap*i));
        }
    }

    // 字母xy
    painter.setFont(QFont(FONT_TYPE, 12));
    painter.drawText(mcX(-5), mcY(-10), mc(10), mc(10),Qt::AlignCenter, axisNameY);
    painter.drawText(mcX(0), mcY(-5), mc(10), mc(10), Qt::AlignCenter, axisNameX);

    painter.restore();
}

void RoundWave::RadAndSpeRedraw(QPainter &painter)
{
    painter.save();
    painter.setPen(QPen(fontcolor, 1, Qt::SolidLine, Qt::RoundCap));
    //
    painter.setFont(QFont(FONT_TYPE, 10));
    painter.drawText(mcX(-50), mcY(43), mc(50), mc(5),Qt::AlignVCenter|Qt::AlignLeft, parm1st);
    painter.drawText(mcX(-50), mcY(43) + 20, mc(60), mc(5),Qt::AlignVCenter|Qt::AlignLeft, parm2nd);

    if(m_bZoomFlag)
    {
        painter.drawText(mcX(20), mcY(43), mc(50), mc(5),Qt::AlignVCenter|Qt::AlignLeft, TR("放大：Alt+PgUp"));
        painter.drawText(mcX(20), mcY(43) + 20, mc(50), mc(5),Qt::AlignVCenter|Qt::AlignLeft, TR("缩小：Alt+PgDn"));
    }

    painter.restore();
}

void RoundWave::ScaleRedraw(QPainter &painter)
{
    painter.save();
    painter.setPen(QPen(fontcolor, 0.5, Qt::SolidLine, Qt::RoundCap));
    painter.setFont(QFont(FONT_TYPE, 12));
    painter.drawText(mcX(-25), mcY(-50), mc(50), mc(5),Qt::AlignCenter, QString("%1 um/div").arg(this->m_dbScaleDist));

    painter.restore();
}

void RoundWave::RasterScaleRedraw(QPainter &painter)
{
    painter.setPen(QPen(HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_BLACK_CURVE2), 0 ,Qt::DashLine, Qt::RoundCap));
    painter.drawEllipse(mcX(0-m_dbStdCirRadius * OSC_SERVO_RASTER_VALID_MAX), mcY(0-m_dbStdCirRadius * OSC_SERVO_RASTER_VALID_MAX), mc(m_dbStdCirRadius * OSC_SERVO_RASTER_VALID_MAX) * 2, mc(m_dbStdCirRadius * OSC_SERVO_RASTER_VALID_MAX)*2);
    painter.setPen(QPen(HmiPainterColor::GetInstance()->GetColor(HmiPainterColor::OSCSERVO_BLACK_CURVE6), 0 ,Qt::DashLine, Qt::RoundCap));
    painter.drawEllipse(mcX(0-m_dbStdCirRadius * OSC_SERVO_RASTER_VALID_MIN), mcY(0-m_dbStdCirRadius * OSC_SERVO_RASTER_VALID_MIN), mc(m_dbStdCirRadius * OSC_SERVO_RASTER_VALID_MIN) * 2, mc(m_dbStdCirRadius * OSC_SERVO_RASTER_VALID_MIN)*2);
//    painter.drawEllipse(mcX(0-m_dbStdCirRadius), mcY(0-m_dbStdCirRadius), mc(m_dbStdCirRadius) * 2, mc(m_dbStdCirRadius)*2);
    painter.save();
//    painter.setPen(QPen(fontcolor, 1, Qt::SolidLine, Qt::RoundCap));
//    painter.setFont(QFont(FONT_TYPE, 10));
//    painter.drawText(mcX(-110), mcY(-50), mc(100), mc(10),Qt::AlignVCenter|Qt::AlignLeft, TR("半径：%1V").arg(radius, 0, 'f', 1));
//    painter.drawText(mcX(-110), mcY(-50)+20, mc(100), mc(10),Qt::AlignVCenter|Qt::AlignLeft, TR("刻度：0.1V/div"));
    DrawScaleAreaText(painter);

    painter.restore();
}

void RoundWave::GraphRedraw(QPainter &painter, double rate)
{
    painter.save();

    // 曲线1
    for (Bit32 i = 0; i < gList.count(); i++)
    {
        if (HmiOscServo::GetCurOscWidget() == HmiOscServo::OSC_SERVO_RASTER)
        {
            painter.setPen(QPen(gList.at(i)->curColor, 1 / rate, Qt::CustomDashLine, Qt::RoundCap));
        }
        else
        {
            painter.setPen(QPen(gList.at(i)->curColor, 1 / rate, Qt::SolidLine, Qt::RoundCap));
        }
        Bit32 xcount = gList.at(i)->x.count();
        Bit32 ycount = gList.at(i)->y.count();
        double plotx = 0.0;
        double ploty = 0.0;
        double plotx1 = 0.0;
        double ploty1 = 0.0;
        if (gList.at(i)->x.count()>0 && gList.at(i)->y.count()>0)
        {
            CrdTran(gList.at(i)->x.at(0), gList.at(i)->y.at(0), plotx, ploty);
        }

        for (Bit32 j = 0; j<xcount && j < ycount;j++)
        {
            CrdTran(gList.at(i)->x.at(j), gList.at(i)->y.at(j), plotx1, ploty1);
            painter.drawLine(QPointF(plotx, ploty), QPointF(plotx1, ploty1));
            plotx = plotx1;
            ploty = ploty1;
        }
    }

    painter.restore();
}

void RoundWave::DrawScaleArea(QPainter &painter)
{
    painter.save();

    painter.setPen(QPen(fontcolor, 1, Qt::SolidLine, Qt::RoundCap));
//    painter.drawRect(mcX(-10 + m_nScaleX), mcY(-10 + m_nScaleY), mc(20), mc(20));
    painter.drawRect(mcX(-m_dbScale * 2 + m_nScaleX), mcY(-m_dbScale * 2 + m_nScaleY), mc(m_dbScale * 4), mc(m_dbScale * 4));

    painter.restore();
}

void RoundWave::DrawScaleAreaText(QPainter &painter)
{
    painter.setPen(QPen(fontcolor, 1, Qt::SolidLine, Qt::RoundCap));
    painter.setFont(QFont(FONT_TYPE, 10));

    if (HmiOscServo::GetCurOscWidget() == HmiOscServo::OSC_SERVO_RASTER)
    {
        painter.drawText(mcX(-50), mcY(-50), mc(100), mc(10),Qt::AlignVCenter|Qt::AlignLeft, scaleAreaText);
    }
    else
    {
        if (m_nShowScaleAreaRadius == -1)
        {
            painter.drawText(mcX(-110), mcY(-50), mc(100), mc(10),Qt::AlignVCenter|Qt::AlignLeft, TR("半径：%1V").arg(radius, 0, 'f', 1));// 修改绘图半径和显示半径文本
        }
        else
        {
            painter.drawText(mcX(-110), mcY(-50), mc(100), mc(10),Qt::AlignVCenter|Qt::AlignLeft, TR("半径：%1V").arg(m_nShowScaleAreaRadius, 0, 'f', 1));// 仅修改显示半径文本
        }

        painter.drawText(mcX(-110), mcY(-50)+20, mc(100), mc(10),Qt::AlignVCenter|Qt::AlignLeft, TR("刻度：0.1V/div"));
    }
}

void RoundWave::SetParm(const QString &str1, const QString &str2)
{
    parm1st = str1;
    parm2nd = str2;
}

void RoundWave::LineZeroAddPoint(const QVector<double>x, const QVector<double>y)
{
    this->graph0->x +=x;
    this->graph0->y +=y;
    if (HmiOscServo::GetCurOscWidget() == HmiOscServo::OSC_SERVO_RASTER)
    {
        if (this->graph0->x.count() > 500)
        {
            this->graph0->x.remove(0, 300);
            this->graph0->y.remove(0, 300);
        }
    }
}

void RoundWave::LineOneAddPoint(const QVector<double>x, const QVector<double>y)
{
    this->graph1->x += x;
    this->graph1->y += y;
}

void RoundWave::LineSetRadius(double r)
{ // 设置圆半径
    this->radius = r;
}

void RoundWave::ClearData()
{
    this->graph0->x.clear();
    this->graph0->y.clear();
    this->graph1->x.clear();
    this->graph1->y.clear();
    this->repaint();

}

bool RoundWave::CrdTran(double x, double y, double &outx, double &outy)
{
    double r = sqrt(x * x + y * y);
    double diffDist = (r - this->radius) * 1000; // 半径偏差，单位微米
    double diff = 0.0;
    double plotR = 0.0; // 绘图半径

    if (this->m_dbScaleDist == 0 || r == 0)
    {
        return false;
    }

    if (diffDist > this->m_dbScaleDist * 5)
    {
        diffDist = this->m_dbScaleDist * 5;
    }
    if (diffDist < this->m_dbScaleDist * (-5))
    {
        diffDist = this->m_dbScaleDist * (-5);
    }

    diff = diffDist * this->m_dbScale / this->m_dbScaleDist;

    plotR = diff + this->m_dbStdCirRadius;

    outx = mcX(plotR * x / r);
    outy =  mcY(plotR * -y / r);

    return true;
}

void RoundWave::SetScale(double val)
{
    this->m_dbScaleDist = val;
    this->repaint();
}

void RoundWave::SetAxiNameX(QString str)
{
    axisNameX = str;
}

void RoundWave::SetAxiNameY(QString str)
{
    axisNameY = str;
}

void RoundWave::SetDotRoundShowFlag(bool flag)
{
    m_bDotRoundShow = flag;
}

void RoundWave::SetZoomFlag(bool flag)
{
    m_bZoomFlag = flag;
}

void RoundWave::SetStdCircleRadius(fBit64 r)
{
    // 设置标圆半径
    m_dbStdCirRadius = r;
}

void RoundWave::ResetStdCircleRadius()
{
    m_dbStdCirRadius = DEFAULT_STD_RADIUS;
}

void RoundWave::ShowScaleArea(bool flag)
{
    m_bShowScaleAreaFlag = flag;
}

void RoundWave::SetScaleAreaRadius(double radius)
{
    m_nShowScaleAreaRadius = radius;
}

void RoundWave::SetScaleAreaText(QString str)
{
    scaleAreaText = str;
}

void RoundWave::UpdateScaleArea(Bit32 x, Bit32 y)
{
    if (mcX(m_nScaleX + x) >= 0 && mcX(m_nScaleX + x) < centrePoint.x() * 2)
    {
        m_nScaleX += x;
    }

    if (mcY(m_nScaleY + y) >= 0 && mcY(m_nScaleY + y) < centrePoint.y() * 2)
    {
        m_nScaleY += y;
    }

    this->repaint();
}

bool RoundWave::GetScaleData(fBit64 xValue, fBit64 yValue, fBit64 &xOut, fBit64 &yOut)
{
    bool ret = false;

    fBit64 xTemp = 0;
    fBit64 yTemp = 0;
    ret = CrdTran(xValue, yValue, xTemp, yTemp);
    if (!ret)
    {
        return false;
    }

//    mcX(-10 + m_nScaleX), mcY(-10 + m_nScaleY), mc(20), mc(20)

    fBit64 hMin = mcX(-m_dbScale * 2 + m_nScaleX);
    fBit64 vMin = mcY(-m_dbScale * 2 + m_nScaleY);

//    fBit64 hMax = hMin + mc(m_dbScale * 4);
//    fBit64 vMax = vMin + mc(m_dbScale * 4);

//    if (HNC_DoubleCompare(xTemp, hMin) < 0 || HNC_DoubleCompare(xTemp, hMax) > 0
//            || HNC_DoubleCompare(yTemp, vMin) < 0 || HNC_DoubleCompare(yTemp, vMax) > 0)
//    {
//        return false;
//    }

    xOut = (xTemp - hMin) / mc(m_dbScale * 4);
    yOut = (yTemp - vMin) / mc(m_dbScale * 4);

    return true;
}

void RoundWave::GetCenterCross(Bit32 type, QVector<double>& x, QVector<double>& y)
{

	fBit64 hMin = mcX(-m_dbScale * 2 + m_nScaleX);
	fBit64 vMin = mcY(-m_dbScale * 2 + m_nScaleY);
	fBit64 tempx = 0;
	fBit64 tempy = 0;
    fBit64 xTemp = 0;
    fBit64 yTemp = 0;
    Bit32 ret = 0;

    if (type == 0)              // 横线
    {
        ret = CrdTran(-5 * DEFAULT_STD_RADIUS, 0, xTemp, yTemp);
        if (!ret)
        {
            return;
        }

        tempx = (xTemp - hMin) / mc(m_dbScale * 4);
        tempy = (yTemp - vMin) / mc(m_dbScale * 4);
        x.append(tempx);
        y.append(tempy);

        ret = CrdTran(5 * DEFAULT_STD_RADIUS, 0, xTemp, yTemp);
        if (!ret)
        {
            return;
        }

        tempx = (xTemp - hMin) / mc(m_dbScale * 4);
        tempy = (yTemp - vMin) / mc(m_dbScale * 4);
        x.append(tempx);
        y.append(tempy);
    }
    else                        // 竖线
    {
        ret = CrdTran(0, -5 * DEFAULT_STD_RADIUS, xTemp, yTemp);
        if (!ret)
        {
            return;
        }
        tempx = (xTemp - hMin) / mc(m_dbScale * 4);
        tempy = (yTemp - vMin) / mc(m_dbScale * 4);
        x.append(tempx);
        y.append(tempy);

        ret = CrdTran(0, 5 * DEFAULT_STD_RADIUS, xTemp, yTemp);
        if (!ret)
        {
            return;
        }
        tempx = (xTemp - hMin) / mc(m_dbScale * 4);
        tempy = (yTemp - vMin) / mc(m_dbScale * 4);
        x.append(tempx);
        y.append(tempy);
    }

//    if (type == 0)              // 横线
//    {
//		tempx = (0 - hMin) / mc(m_dbScale * 4);
//		tempy = (this->height() / 2.0 - vMin) / mc(m_dbScale * 4);
//		x.append(tempx);
//		y.append(tempy);

//		tempx = (this->width() - hMin) / mc(m_dbScale * 4);
//		tempy = (this->height() / 2.0 - vMin) / mc(m_dbScale * 4);
//		x.append(tempx);
//		y.append(tempy);
//    }
//    else                        // 竖线
//    {
//		tempx = (this->width() / 2.0 - hMin) / mc(m_dbScale * 4);
//		tempy = (0 - vMin) / mc(m_dbScale * 4);
//		x.append(tempx);
//		y.append(tempy);

//		tempx = (this->width() / 2.0 - hMin) / mc(m_dbScale * 4);
//		tempy = (this->height() - vMin) / mc(m_dbScale * 4);
//		x.append(tempx);
//		y.append(tempy);
//    }
}

bool RoundWave::IsValid(fBit64 xMin, fBit64 xMax, fBit64 yMin, fBit64 yMax)
{
    fBit64 hMin = mcX(-10 + m_nScaleX);
    fBit64 vMin = mcY(-10 + m_nScaleY);

    fBit64 hMax = hMin + mc(20);
    fBit64 vMax = vMin + mc(20);

    if (HNC_DoubleCompare(xMin, hMax) >= 0 || HNC_DoubleCompare(xMax, hMin) <= 0
            || HNC_DoubleCompare(yMin, vMax) >= 0 || HNC_DoubleCompare(yMax, vMin) <= 0)
    {
        return false;
    }

    if ((HNC_DoubleCompare(xMin, hMin) < 0 && HNC_DoubleCompare(xMax, hMax) > 0)
            && (HNC_DoubleCompare(yMin, vMin) < 0 && HNC_DoubleCompare(yMax, vMax) > 0))
    {
        return false;
    }

    return true;
}
