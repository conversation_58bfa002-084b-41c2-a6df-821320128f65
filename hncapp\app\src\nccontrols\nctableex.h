﻿#ifndef NCTABLEEX_H
#define NCTABLEEX_H

#include <QTableView>
#include <QVector>
#include <QItemDelegate>
#include <QScrollBar>
#include <QEvent>
#include <QModelIndex>
#include <QMap>
#include <QFont>
#include <QLayout>
#include "nccheckbox.h"

#include "common.h"
#include "nccombobox.h"
#include "nccheckbox.h"


class NcTableEx;
class NcTableModelEx: public QAbstractTableModel
{
    Q_OBJECT
public:
    NcTableModelEx(QObject *parent = 0):QAbstractTableModel(parent), m_pTable((NcTableEx *)parent)
    {
        m_nRowCount = 0;
        m_nColumnCount = 0;

        this->installEventFilter(this);
    }

protected:
    bool eventFilter(QObject *, QEvent *);
    //void mousePressEvent(QMouseEvent*);

private:
    int m_nRowCount;            // 显示行数（默认为0）
    int m_nColumnCount;         // 显示列数（默认为0）
    QStringList m_sHTitle;      // 水平列表头
    QStringList m_sVTitle;      // 竖直列表头
    NcTableEx *m_pTable;
    QVector<NcComboBox*> m_vecComboBox;

    void GetModelIndex(QObject *object, QModelIndex &index);        // 获取索引
    void SetIndexWidgetAlignment(const QModelIndex index) const;
    int rowCount(const QModelIndex &parent) const;
    int columnCount(const QModelIndex &parent) const;
    QVariant data(const QModelIndex &index, int role) const;
    QVariant headerData(int section, Qt::Orientation orientation, int role) const;
    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole);
    Qt::ItemFlags flags(const QModelIndex &index) const;

    void SetRowCount(int rowCount);
    void SetColumnCount(int columnCount);
    void SetVerticalHeaderLabels(const QStringList labels);
    void SetHorizontalHeaderLabels(const QStringList labels);

    void UpdateView(int tlrow, int tlcol, int brrow, int brcol);
    void ResetModel();

private slots:
    void SlotComboBoxCurrentIndexChanged(const QString & text);

    friend class NcTableEx;
};

class NcItemDelegate:public QItemDelegate
{
    Q_OBJECT
public:
    NcItemDelegate(QWidget *parent = 0): QItemDelegate(parent)
    {
    }
private:
    bool editorEvent(QEvent *event,
                                    QAbstractItemModel *,
                                    const QStyleOptionViewItem &,
                                    const QModelIndex &);
};

class NcTableEx : public QTableView
{
    Q_OBJECT
public:
    enum EN_NcTableStyle
    {
        ComboBoxRole = Qt::UserRole + 1,    // 下拉框
        ComboBoxDisEnableFlag,              // 下拉框去使能标志(默认使能)
        CheckBoxRole,                       // 选择框
        PicRole,                     // 图片
        DialogRole,                         // 对话框
        SpanRole,                           // 合并框
        FlagRole,                           // 是否可编辑，是否可选择
        EditDisableRole,                    // 标志有效，不可编辑
        DataTypeRole,                       // 数据类型，目前先只支持浮点、整形的输入
        EditRightRole,                      // 编辑权限
        HotKeyRole,
        PasswordRole,                       // 密文模式
        InputDefValRole,                    // 回车弹框显示的默认值设置
        HorHeadColorRole,                      // 表头字体颜色
        HorHeadFontRole,
    };

    //在设置列宽的时候使用
    enum EN_DataType
    {
        ENInvalid = -1,                     //无效模式

        //在设置列宽的时候使用
        ENScaleType = 0,                    //数据为比例类型（宽度相对表格宽的比例）
        ENValueType,                         //数据为具体宽度（设定每一列的具体宽度）

        //用于CheckBox
        ENFalse = false,                    // 没有勾选
        ENTrue = true,                      // 勾选
    };

    explicit NcTableEx(QWidget *parent = 0, QFont font = QFont(FONT_TYPE, TABLE_FONT));
    virtual ENMoveDirection ContentSet(int row, int col, const QString &str);
    virtual void ContentGet(int row, int col, QString &str) const;
    virtual QVariant RoleStyle(int row, int col, int role);
    virtual void InputType(int row, int col, long &ebxType, QString &info, long &len, long &prec, long &exitType);
    virtual void InitTab(void); // 初始化标题栏(重载用)
    virtual int TotalDataCount(void);

    void ResetIndex();          // 复位索引号

    void SetTotalDataCount(int count, bool refresh = true);    // 设置数据总数
    int GetTotalDataCount() const;                             // 获取数据总数
    void InitProp(EN_DataType dataType, const QVector<int> vecWidth, const QStringList horTitle, const Bit32 showMaxCol = 0);

    void Refresh();                                 // 刷新整个列表
    void RefreshSingleItem(int row, int col);       // 刷新单行
    void Redraw(bool refresh = false);              // 重绘
    virtual int FocusOn(int row, int col);                  // 设置焦点索引
    int SelectOn(int row, int col);
    void SetFocus();                                // 设置焦点
    void SetFocus(const QModelIndex& index);        // 设置焦点
    void SetDefaultFocus(int row, int col);         // 设置默认的焦点位置（第一次启动时的焦点位置）
    int GetStartIndex();                            // 获取起始索引
    //如果表格中单元格合并，在初始化的时候必需设定合并类型
    virtual void SetSpanRole(int rowSpan, int columnSpan);
    void GetSpanRole(int &rowSpan, int &columnSpan);
    int GetCurrentRow();
    int GetCurrentColumn();
    void ChangeFocus(ENMoveDirection direction);                // 按照指定方向切换焦点
    void SetDefaultMoveDirection(ENMoveDirection direction);    // 设置默认输入完成后的焦点移动方向
    void SetDisplayRowCount(Bit32 count);                       // 设置单元格的显示行数
    void SetComboBoxAlignment(Qt::Alignment alignment);         // 设置下拉框对齐方式
    void SetChangeColorFlag(bool flag);                         // 设置m_bChangeColor标志
    bool GetChangeColorFlag();                                  // 获取m_bChangeColor标志
    void SetFillAllFlag(bool flag);                             // 设置是否自动调整行高

    int GetPageCount();            // 总页数
    bool PageTo(int pageNO);       // 翻到指定页

    bool IsEditAgent();            // 是否用编辑代理
    void SetEditAgent(bool mode);  // 设置编辑代理

    virtual QValidator* GetValidator(int row, int col);
    virtual QString GetDataInfo(int row, int col);
    virtual ENMoveDirection ContentChanged(int row, int col, QString value);
    virtual NcDataLimit InputDataLimit(int row, int col);

    Qt::Alignment m_Alignment;

    void ResetTableTitle(const QStringList &title);

    // 临时使用,待表格替换完成后和ContentSet()一起删除
    bool GetIsDefContentChangedVaild();

    void SetDefaultSel(bool defaultSel);

    void ClearFocusAndSelection();

    void SetVerticalHeaderLabels(const QStringList vecTitle, Bit32 width);
    Bit32 GetColumnCount() { return m_nColumnCount; }
    void UpdateHorTitle(const QStringList horTitle);

	bool IsIndexInTableSpan(int row, int col);
signals:
    void selectRowChanged(int curRow, int preRow); // 当前选择行改变
    void selectStartChanged(int startIdx);         // 当前起始行改变
    void SignalsDirKey(QEvent* event);
	void SignalItemChanged();

public slots:

protected:
    void resizeEvent(QResizeEvent *event);
    bool eventFilter(QObject *target, QEvent *event);
    // 临时使用,待表格替换完成后和ContentSet()一起删除
    void SetIsDefContentChangedVaild();

protected:
    NcTableModelEx *m_pTableMode;
    NcItemDelegate *m_pItemDelegate;
    int m_nTotalDataCount;                  // 数据总数（默认值为-1）
    int m_nRowCount;                        // 每页显示的行数（默认值为-1）
    int m_nStartColumn;                     // 显示列数（默认值为-1）
    int m_nColumnCount;                     // 显示列数（默认值为-1）
    int m_nSelectIdx;                       // 当前选择行在总行数中的索引（默认为0）
    int m_nOldSelectIdx;                    // 上一次保存的选择行（默认为0）
    int m_nStartIdx;                        // 当前起始行在总行数中的索引（默认为0）
    int m_nOldStartIdx;                     // 上一次保存的起始行（默认为0）
    int m_nTableRowHeight;                  // 行高
    int m_nDefaultRow;                      // 默认焦点行(初始值为-1)
    int m_nDefaultColumn;                   // 默认焦点列(初始值为-1)
    int m_nCurrentColumn;                   // 当前列号
    QFont m_font;                           // 字体（默认为FONT_TYPE, 12）
    QScrollBar* m_pVerticalScrollBar;       // 竖直滚动条
    QScrollBar* m_pHorizontalScrollBar;     // 水平滚动条
    EN_DataType m_enDataType;               // 数据类型(默认为Invalid，无效模式)
    QVector<int> m_vecValue;
    int m_nVerHeaderWidth;
    Bit32 m_nColumnSpan;                    // 水平竖直框合并的个数（默认为1，没有合并框）
    Bit32 m_nRowSpan;                       // 竖直框合并的个数（默认为1，没有合并框）
    QModelIndex m_saveIndex;                // 保存焦点索引
    bool m_bSetFocusFlag;                   // 设置焦点标志
    ENMoveDirection m_enDefaultMoveDirection;   // 默认的焦点移动方向(默认值为NOTMOVE，不移动)
    Bit32 m_nDisplayRowCount;               // 每行的显示行数（默认为1，默认显示1行）
    bool m_bChangeColor;                    // 用于表格没Edit属性但有enable与select属性时(默认true，改变颜色)
    bool m_bEditAgent;                      // 输入方式优化
    bool m_bEditActive;
    bool m_bKeyPressed;
    bool m_bFillAllFlag;                    // 自动调整行高
    bool m_bIsDefalutContentChangedVaild;   // 是否调用了NcTableEx默认的ContentChanged接口(临时使用,待表格替换完成后和ContentSet()一起删除)
    bool m_bIsConnected;
    bool m_bDefaultSel;                     // 失焦后选择行是否有背景色
    QList<QStringList> m_sOldPicPathList;

    void CalculateTableRowHeight();         // 根据实际显示行数计算每行高度
    void CalculateTableRowCount();          // 计算每行的高度及table页的行数
    void ResizeTable();                     // 调整大小
    void ResizeTableRowHeight();            // 调整行高
    void ResizeTableColumnWidth();          // 调整列宽
    void SetVerticalScrollBar();            // 设置竖直滚动条
    void SetHorizontalScrollBar();
    void SetDisplayRow();                   // 设置显示行数
    void SetTableFocus();                   // 设置焦点
    bool KeyEnter(QEvent* event);

    bool KeyUp(QEvent* event);
    bool KeyDown(QEvent* event);
    bool KeyLeft(QEvent* event);
    bool KeyRight(QEvent* event);
    bool KeyPageUp(QEvent* event);
    bool KeyPageDown(QEvent* event);
    void CheckTableData(Bit32 moveLen, QEvent *event);   // 检查索引数据
    void ChangeCurrentIndex();                           // 改变当前索引
    void SetCurrentIndex(QModelIndex &index, Bit32 oldTableRow);
    bool HandleKeyPress(QEvent *event, int moveLen);     // 处理按键按下

    bool SelectIdxEnable(int row, int col);       // 检测row行col列是否可以设置焦点
    Bit32 ValidSelectIdxCheck(int row);           // 检测row行是否在0- m_nTotalDataCount范围内,不在则修正
    Bit32 SelectIdxUpMove(int row, int col);      // 获取row行col列向上多少行可以设置焦点
    Bit32 SelectIdxDownMove(int row, int col);    // 获取row行col列向下多少行可以设置焦点
    void setSelectIdx(int nSelectIdx, Bit32 dir, int col = -1); // 设置m_nSelectIdx,若m_nSelectIdx不可设置焦点,默认向dir方向修正焦点位置
    void setStartIdx(int startIdx); // 若需同时设置m_nSelectIdx和m_nStartIdx,需先进行setSelectIdx(),m_nStartIdx的值会根据m_nSelectIdx进行调整

    bool IsCheckBoxRole(QModelIndex index);
    NcCheckBox *GetIndexCheckBox(QModelIndex index); // 获取index的勾选框

    bool IsComboBoxRole(QModelIndex index);
    NcComboBox *GetIndexComboBox(QModelIndex index);

    void CreateCheckBoxInTable(QModelIndex index);
    void RemoveItemFromTable(QModelIndex index);

    bool IsPicRole(QModelIndex index);
    QLabel *GetIndexPic(QModelIndex index);
    void CreatePicInTable(QModelIndex index);
    void InitPicPathList();

    bool EditItem(const QModelIndex &index, QString val);
    void RefreshCheckBoxText(QModelIndex index);
    void RefreshPic(QModelIndex index, bool flag = false);
    NcDataLimit GetInputDataLimit(const int row, const int col);
    QValidator *GetInputValidator(const int row, const int col);
    void TableUpdate(bool refresh = false);
    bool VerifyKey(QString key);
    void StartEdit(QString content, bool selStr);
    bool UseSelBackgrd(QModelIndex index);
    int ColunmCount();
    void SetStartColumn(Bit32 stCol);
    Bit32 GetNextCol(Bit32 row, Bit32 stCol, Bit32 dir);
    Bit32 IsSpanColItem(int row, int col);
    Bit32 IsSpanRowItem(int row, int col);
    bool IsIndexSelectEnable(int row, int col);
    Bit32 GetNextRow(Bit32 stRow, Bit32 col, Bit32 dir);
    virtual int GetDefCol(int col);
    QVariant RoleStyle(QModelIndex index, int role);
    virtual void SetHorizontalScrollBarValue();
    virtual void UpdataHorizontalScrollBarHidden();
private slots:
    void currentChanged(const QModelIndex & current, const QModelIndex & previous);
    void SlotScrollBarValueChanged(int value);
    virtual void SlotScrollHorizontalBarValueChanged(int value);
    void SlotDoubleClicked(QModelIndex index);
    void EditAccept();
    void EditReject();

    friend class NcTableModelEx;
    friend class NcItemDelegate;
};

#endif // NCTABLEEX_H
