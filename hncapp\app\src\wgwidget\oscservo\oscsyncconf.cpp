﻿
#include <QKeyEvent>

#include "chaxispos.h"
#include "common.h"
#include "hmioscservo.h"
#include "msgchan.h"

#include "oscsyncconf.h"
#include "ui_oscsyncconf.h"

OscSyncConf::OscSyncConf(QWidget *parent) :
    ContainerWidget(parent),
    ui(new Ui::OscSyncConf)
{
    ui->setupUi(this);

    ui->labelPosBegin->setAlignment(Qt::AlignVCenter | Qt::AlignRight);
    ui->labelPosEnd->setAlignment(Qt::AlignVCenter | Qt::AlignRight);
//    ui->labelPeriod->setAlignment(Qt::AlignVCenter | Qt::AlignRight);
    ui->labelSpe->setAlignment(Qt::AlignVCenter | Qt::AlignRight);
    ui->labelAxis1->setAlignment(Qt::AlignVCenter | Qt::AlignRight);
    ui->labelAxis2->setAlignment(Qt::AlignVCenter | Qt::AlignRight);

    wList.clear();
    wList.append(ui->labelPosBegin);
    wList.append(ui->labelPosEnd);
    wList.append(ui->labelSpe);
    wList.append(ui->labelPeriodMulti);
    wList.append(ui->labelAxis1);
    wList.append(ui->labelAxis2);

    this->curFocusIndex = 0;
    for(int i = 0; i < wList.count(); i++)
    {
        wList.at(i)->setFocusPolicy(Qt::StrongFocus);
        wList.at(i)->installEventFilter(this);
    }

    intValidator = new QIntValidator(this);
    this->dirLayout = new DirMoveLayout(this);
    dirLayout->SetDirMoveLayout(6, 1, this->wList);
}

OscSyncConf::~OscSyncConf()
{
    delete ui;
}

bool OscSyncConf::eventFilter(QObject *wg, QEvent *event)
{
    if(event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        QWidget *tmp = dynamic_cast<QWidget *>(wg);
        if(this->wList.contains(tmp) && tmp != NULL)
        {
            this->curFocusIndex = this->wList.indexOf(tmp);
            if(keyEvent->key() == Qt::Key_Right || keyEvent->key() == Qt::Key_Left
              || keyEvent->key() == Qt::Key_Down || keyEvent->key() == Qt::Key_Up)
            {
                dirLayout->DirMoveOnKey(tmp, keyEvent->key());
                this->curFocusIndex = dirLayout->GetCurFocusIdx();
                return true;
            }
            else if(keyEvent->key() == Qt::Key_Enter || keyEvent->key() == Qt::Key_Return)
            {
                this->CurValSet(this->curFocusIndex);
                return true;
            }

        }
    }
    else if(event->type() == QEvent::FocusIn)
    {
        QWidget *tmp = dynamic_cast<QWidget *>(wg);
        if(this->wList.contains(tmp) && tmp != NULL)
        {
            this->curFocusIndex = this->wList.indexOf(tmp);
        }
    }
    return QObject::eventFilter(wg, event);
}

void OscSyncConf::LoadData()
{
    Bit32 ch = ActiveChan();
    QString ax1Name = ChAxisPos::GetInstance()->GetAxName(HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1);
    QString ax2Name = ChAxisPos::GetInstance()->GetAxName(HmiOscServo::s_Conf[ch].stSyncConf.sync_axis2);

    ui->labelPosBegin->setText(QString::number(HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos));
    ui->labelPosEnd->setText(QString::number(HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos));
//    ui->labelPeriod->setText(QString::number(HmiOscServo::s_Conf[ch].stSyncConf.sync_period));
    ui->labelSpe->setText(QString::number(HmiOscServo::s_Conf[ch].stSyncConf.axis_f));
    ui->labelAxis1->setText(QString::number(HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1));
    ui->labelAxis2->setText(QString::number(HmiOscServo::s_Conf[ch].stSyncConf.sync_axis2));
    ui->label_Axis1Name->setText(QString("(%1)").arg(ax1Name));
    ui->label_Axis2Name->setText(QString("(%1)").arg(ax2Name));

    Bit32 ncuCycle = 0;
    Bit32 multi = 0;
    Bit32 ret = HmiOscServo::GetNcuCycleMulti(HmiOscServo::s_Conf[ch].stSyncConf.sync_period, ncuCycle, multi);
    ncuCycle = ncuCycle / 1000;

    if(ret == -1)
    {
        ui->labelPeriodMulti->setText("");
        ui->labelNcuCycle->setText(QString::number(ncuCycle));
        ui->labelPeriodResult->setText(QString::number(HmiOscServo::s_Conf[ch].stSyncConf.sync_period));

        MessageOut(QObject::TR("采样周期不是插补周期整数倍，请手动设置采样周期"));
    }
    else
    {
        ui->labelPeriodMulti->setText(QString::number(multi));
        ui->labelNcuCycle->setText(QString::number(ncuCycle));
        ui->labelPeriodResult->setText(QString::number(ncuCycle * multi));
    }
}

void OscSyncConf::FrameWorkMessage(QVariant messageid, QVariant messageValue)
{
    UNREFERENCED_PARAM(messageValue);
    if(messageid == MsgData::REDRAWALL || messageid == MsgData::CHANCHANGE)
    {
        this->LoadData();
        this->wList.at(this->curFocusIndex)->setFocus();
    }
    else if (messageid == MsgData::REDRAW)
    {
        FrameWorkMessage(MsgData::REDRAWALL, messageValue);
        return;
    }
    else if(messageid == MsgData::SETFOCUS)
    {
        this->LoadData();
        this->wList.at(this->curFocusIndex)->setFocus();
    }
}

void OscSyncConf::CurValSet(Bit32 row)
{
    QString editStr = "";
    Bit32 ret = 0;
    Bit32 max = 0;
    Bit32 min = 0;
    fBit64 fTmp = 0.0;
    Bit32 tmp = 0;
    Bit32 ch = ActiveChan();
    bool ok = false;
    Bit32 cycle = HmiOscServo::GetNcuCycle() / 1000;
    if(cycle == 0)
    {
        cycle = 1;
    }

    MessageOut("");

    if(row < 0 || row >= this->wList.count())
    {
        return;
    }

    switch (row) {
    case 0:  // 行程起点
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos);
        ParaGetFloatVal(PARAMAN_FILE_AXIS, HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1, PAR_AX_NLMT, &fTmp);
        min = (Bit32)fTmp + 10;
        ParaGetFloatVal(PARAMAN_FILE_AXIS, HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1, PAR_AX_PLMT, &fTmp);
        max = (Bit32)fTmp - 10;
        if(MessageInput(&editStr, DTYPE_INT, TR("请输入[行程起点]:"), 8/*-1, -1, 0, intValidator*/) == 0)
        {
            Bit32 intTmp = editStr.toInt(&ok);
            if (ok == false || intTmp < min || intTmp > max)
            {
                ret = -1;
            }
            else
            {
                ret = 0;
                HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos = intTmp;
            }
        }
        break;
    case 1: // 行程终点
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos);
        ParaGetFloatVal(PARAMAN_FILE_AXIS, HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1, PAR_AX_NLMT, &fTmp);
        min = (Bit32)fTmp + 10;
        ParaGetFloatVal(PARAMAN_FILE_AXIS, HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1, PAR_AX_PLMT, &fTmp);
        max = (Bit32)fTmp - 10;
        if(MessageInput(&editStr, DTYPE_INT, TR("请输入[行程终点]:"), 8/*-1, -1, 0, intValidator*/) == 0)
        {
            Bit32 intTmp = editStr.toInt(&ok);
            if (ok == false || intTmp < min || intTmp > max)
            {
                ret = -1;
            }
            else
            {
                ret = 0;
                HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos = intTmp;
            }
        }
        break;
    case 2: // 速度(F)
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSyncConf.axis_f);
        min = 10;
        max = 100000;
        intValidator->setBottom(min);
        intValidator->setTop(max);
        ret = MessageInput(&editStr, DTYPE_UINT, TR("请输入[速度(F)]:"), -1, -1, 0, intValidator);
        if(ret == 0)
        {
            Bit32 intTmp = editStr.toInt(&ok);
            if (ok == false || intTmp < min || intTmp > max)
            {
                ret = -1;
            }
            else
            {
                ret = 0;
                HmiOscServo::s_Conf[ch].stSyncConf.axis_f = intTmp;
            }
        }
        break;
    case 3: // 采样周期
        max = 1000 / cycle;
        min = 1;

        intValidator->setBottom(min);
        intValidator->setTop(max);

        editStr = ui->labelPeriodMulti->text();
        if (MessageInput(&editStr, DTYPE_UINT, TR("请输入[采样周期/插补周期 倍率]:"), -1, -1, 0, intValidator) == 0)
        {
            tmp = editStr.toInt(&ok);
            if (ok == false || tmp < min || tmp > max)
            {
                ret = -1;
            }
            else
            {
                ret = 0;
                HmiOscServo::s_Conf[ch].stSyncConf.sync_period = tmp * cycle;
            }
        }
        break;
    case 4: // 引导轴轴号
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1);
        intValidator->setBottom(0);
        intValidator->setTop(31);
        if(MessageInput(&editStr, DTYPE_UINT, TR("请输入[引导轴轴号]:"), -1, -1, 0, intValidator) == 0)
        {
            Bit32 intTmp = editStr.toInt(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效!"));
                break;
            }
            Bit32 type = 0;
            if(intTmp == HmiOscServo::s_Conf[ch].stSyncConf.sync_axis2)
            {
                MessageOut(TR("两同步轴轴号不能相同!"));
                break;
            }
            HNC_AxisGetValue(HNC_AXIS_TYPE, intTmp, &type);
            if (type != 1 && type != 7) //  7(主轴做进给轴使用)和1一样按直线轴处理
            {
                MessageOut(TR("轴配置错误!"));
                break;
            }
            else
            {
                if (intTmp != HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1)
                {
                    // 重新获取龙门同步行程起点和终点
                    ParaGetFloatVal(PARAMAN_FILE_AXIS, intTmp, PAR_AX_NLMT, &fTmp);
                    HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos = (Bit32)fTmp + 10;
                    ParaGetFloatVal(PARAMAN_FILE_AXIS, intTmp, PAR_AX_PLMT, &fTmp);
                    HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos = (Bit32)fTmp - 10;
                }
                HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1 = intTmp;
                this->LoadData();
            }
        }
        break;
    case 5: // 从动轴轴号
        editStr = QString::number(HmiOscServo::s_Conf[ch].stSyncConf.sync_axis2);
        intValidator->setBottom(0);
        intValidator->setTop(31);
        if(MessageInput(&editStr, DTYPE_UINT, TR("请输入[从动轴轴号]:"), -1, -1, 0, intValidator) == 0)
        {
            Bit32 intTmp = editStr.toInt(&ok);
            if(ok == false)
            {
                MessageOut(TR("输入数据无效!"));
                break;
            }
            Bit32 type = 0;
            if(intTmp == HmiOscServo::s_Conf[ch].stSyncConf.sync_axis1)
            {
                MessageOut(TR("两同步轴轴号不能相同!"));
                break;
            }
            HNC_AxisGetValue(HNC_AXIS_TYPE, intTmp, &type);
            if (type != 1 && type != 7) //  7(主轴做进给轴使用)和1一样按直线轴处理
            {
                MessageOut(TR("轴配置错误!"));
                break;
            }
            else
            {
                HmiOscServo::s_Conf[ch].stSyncConf.sync_axis2 = intTmp;
                this->LoadData();
            }
        }
        break;
    default:
        break;
    }

    if (ret == -1)
    {
        MessageOut(TR("输入数据无效，范围:%1~%2").arg(min).arg(max));
        this->LoadData();
    }
    else
    {
        //Bit32 row = this->curFocusIndex;
        if (row == 0 || row == 1 || row == 2 || row == 4)
        {
            Bit32 wishPeriod = 1;
            fBit64 runTime = 0.0;

            if (HmiOscServo::s_Conf[ch].stSyncConf.axis_f <= 0) // 防止除0
            {
                HmiOscServo::s_Conf[ch].stSyncConf.axis_f = 10;
            }
            runTime = 2.0*(HmiOscServo::s_Conf[ch].stSyncExtraConf.ePos - HmiOscServo::s_Conf[ch].stSyncExtraConf.sPos)/HmiOscServo::s_Conf[ch].stSyncConf.axis_f*60000+1000; // +1000ms考虑到额外时间
            wishPeriod = (Bit32)(runTime / SMPL_DATA_NUM + 0.99); // +0.99相当于向上圆整

            if (wishPeriod < 1)
            {
                wishPeriod = 1;
            }
            else if (wishPeriod > 1000)
            {
                wishPeriod = 1000;
            }

            if (wishPeriod != HmiOscServo::s_Conf[ch].stSyncConf.sync_period)
            {
                HmiOscServo::s_Conf[ch].stSyncConf.sync_period = wishPeriod;
            }
        }
        HmiOscServo::OscServoDataSave();
    }

    this->LoadData();
    //this->wList.at(row)->setFocus();
    MsgChan::Instance().TranMsg(MsgData::SETFOCUS, ""); // 设置焦点
}

void OscSyncConf::FocusRedraw()
{
    for (Bit32 i = 0; i < wList.count(); i++)
    {
        if (i == this->curFocusIndex)
        {
            wList.at(i)->setProperty("selected", true);
            wList.at(i)->style()->polish(wList.at(i));
        }
        else
        {
            wList.at(i)->setProperty("selected", false);
            wList.at(i)->style()->polish(wList.at(i));
        }
    }
}
